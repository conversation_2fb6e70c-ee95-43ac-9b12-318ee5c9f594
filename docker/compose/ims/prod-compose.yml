version: "2"

services:
  ims-apiservice:
    image: "${DOCKER_REGISTRY}/${DOCKER_TAGNAME}:${VERSION}"
    ports:
      - "${HOST_PORT}:8000"
    env_file: "${ENV_FILE}"
    volumes:
      - "${HOST_LOG_ROOT}:/var/log/ims"
      - "${CONFIG_ROOT}/ims/config_files:/usr/src/app/config_files"
    container_name: "ims_app_service"
    hostname: "${HOSTNAME}"
    restart: always
    entrypoint: /usr/src/app/gunicorn.sh
    tty: true
    logging:
      driver: "json-file"
      options:
        max-size: "200k"
        max-file: "10"

  room-upgrade-worker:
    image: "${DOCKER_REGISTRY}/${DOCKER_TAGNAME}:${VERSION}"
    env_file: "${ENV_FILE}"
    volumes:
      - "${HOST_LOG_ROOT}:/var/log/ims"
      - "${CONFIG_ROOT}/ims/config_files:/usr/src/app/config_files"
    container_name: "room_upgrade_worker"
    hostname: "${HOSTNAME}"
    entrypoint: /usr/src/app/inventory_management/workers/upgrade_room_worker.sh
    tty: true
    logging:
      driver: "json-file"
      options:
        max-size: "200k"
        max-file: "10"