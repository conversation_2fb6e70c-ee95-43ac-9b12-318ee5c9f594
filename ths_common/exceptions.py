# coding=utf-8
"""
Exceptions
"""
from ths_common.constants.base_enum import BaseEnum


class CRSError(BaseEnum):
    @property
    def error_code(self):
        return self.values[0]

    @property
    def message(self):
        return self.values[1]


class CRSException(Exception):
    error_code = "0001"
    message = "Something went wrong. Please contact escalations team"
    APP_CODE = {"crs": "01", "pos": "02"}

    def __init__(self, description=None, extra_payload=None, message=None):
        self.description = description
        self.extra_payload = extra_payload
        if message is not None:
            self.message = message

    def __str__(self):
        return str(
            dict(
                error_code=self.error_code,
                message=self.message,
                description=self.description,
                extra_payload=self.extra_payload,
            )
        )

    def with_description(self, description):
        self.description = description
        return self

    def code(self, app_name="crs"):
        pod_code = "04"
        app_code = self.APP_CODE.get(app_name, "00")
        # https://treebo.atlassian.net/wiki/spaces/PFM/pages/201490819/Error+Handling+Guidelines
        return "{pod_code}{app_code}{error_code}".format(
            pod_code=pod_code, app_code=app_code, error_code=self.error_code
        )


class ValidationException(CRSException):
    error_code = "0002"
    message = "Validation Exception"

    def __init__(
        self,
        error=None,
        description=None,
        message=None,
        extra_payload=None,
        format_dict=None,
    ):
        if error:
            self.error_code = error.error_code
            self.message = (
                error.message.format(**format_dict) if format_dict else error.message
            )
        else:
            self.error_code = self.error_code
            self.message = message if message else self.message
        super(ValidationException, self).__init__(
            description=description, extra_payload=extra_payload
        )


class DatabaseError(CRSException):
    error_code = "0003"
    message = "Something went wrong with the database"


class ResourceNotFound(CRSException):
    error_code = "0004"
    message = "{} not found. Please contact escalations."

    def __init__(self, resource_name, description=None, extra_payload=None):
        super(ResourceNotFound, self).__init__(
            message=self.message.format(resource_name),
            description=description,
            extra_payload=extra_payload,
        )


class OutdatedVersion(CRSException):
    error_code = "0005"
    message = "You're operating on old version of data, please refresh and retry."

    def __init__(self, class_name, requested_version, current_version, entity_id=None):
        super(OutdatedVersion, self).__init__(description="", extra_payload="")


class ApiValidationException(CRSException):
    error_code = "0006"

    def __init__(self, error_messages=None):
        self.error_messages = error_messages
        super(ApiValidationException, self).__init__(
            description=None, extra_payload=None
        )

    def __str__(self):
        return "exception: error_code=%s messages=%s" % (
            self.error_code,
            self.error_messages,
        )


class AggregateNotFound(CRSException):
    error_code = "0007"

    def __init__(self, class_name, id):
        self.message = "Aggregate: {} with id: {} missing.".format(class_name, id)
        super(AggregateNotFound, self).__init__(description="", extra_payload="")


class JobNotRegistered(CRSException):
    error_code = "0008"

    def __init__(self, job_name):
        self.message = "Job {} not registered".format(job_name)
        super(JobNotRegistered, self).__init__(description="", extra_payload="")


class InvalidOptionsError(CRSException):
    error_code = "0009"

    def __init__(self, message):
        self.message = message
        super(InvalidOptionsError, self).__init__(description="", extra_payload="")


class InvalidOperationError(CRSException):
    error_code = "0010"
    message = "Invalid Operation Error"

    def __init__(self, error=None, description=None, extra_payload=None):
        super(InvalidOperationError, self).__init__(
            description=description, extra_payload=extra_payload
        )
        if error:
            self.error_code = error.error_code
            self.message = error.message


class AuthorizationError(CRSException):
    error_code = "0011"
    message = "You're not authorized to perform this operation"

    def __init__(self, description=None, extra_payload=None):
        super(AuthorizationError, self).__init__(
            description=description, extra_payload=extra_payload
        )


class PolicyException(CRSException):
    error_code = "0012"
    message = "Something went wrong while evaluating rules in policy engine"


class MissingFactsException(PolicyException):
    error_code = "0013"
    message = "Required fact missing for evaluating the rule"

    def __init__(self, description=None, extra_payload=None):
        super(MissingFactsException, self).__init__(
            description=description, extra_payload=extra_payload
        )


class PolicyAuthException(AuthorizationError):
    error_code = "0014"

    def __init__(self, error=None, message=None, description=None, extra_payload=None):
        if error:
            self.error_code = error.error_code
            self.message = error.message
        else:
            self.error_code = self.error_code
            self.message = message if message else self.message
        super(PolicyAuthException, self).__init__(
            description=description, extra_payload=extra_payload
        )


class DownstreamSystemFailure(CRSException):
    error_code = "0015"
    message = "Downstream system failed."

    def __init__(self, message=None, description=None, extra_payload=None):
        self.message = message
        super(DownstreamSystemFailure, self).__init__(
            description=description, extra_payload=extra_payload
        )


class InternalServerException(CRSException):
    error_code = "0016"
    message = "Something went wrong. Please contact Escalations team"

    def __init__(self, error=None, description=None, message=None, extra_payload=None):
        if error:
            self.error_code = error.error_code
            self.message = error.message
        else:
            self.error_code = self.error_code
            self.message = message if message else self.message
        super(InternalServerException, self).__init__(
            description=description, extra_payload=extra_payload
        )


class UserTypeHeaderInformationMissing(CRSException):
    error_code = "0017"
    message = (
        "You're not authorised to perform this operation. Please login and try again."
    )

    def __init__(self):
        super(UserTypeHeaderInformationMissing, self).__init__(
            description="X-User-Type header is missing to serve the request"
        )


class PrimaryKeyCollision(DatabaseError):
    error_code = "0018"
    message = "Collision in id"


class BookingIdCollision(DatabaseError):
    error_code = "0019"
    message = "Collision in booking id"


class DomainEventMergerException(CRSException):
    error_code = "0020"
    message = "Domain event cannot be merged"


class DatabaseLockError(DatabaseError):
    error_code = "0021"
    message = (
        "The entity that you're trying to modify is being currently modified by another user or operation. "
        "Please try again in sometime."
    )


class BookingReferenceIdCollision(DatabaseError):
    error_code = "0022"
    message = "Duplicate data found with the provided reference id"


class NotModifiedException(CRSException):
    error_code = "0023"
    message = "Entity in server was not modified since last version was fetched from the client"


class UserTypeHeaderInformationIncorrect(CRSException):
    error_code = "0024"
    message = (
        "You're not authorised to perform this operation. Please login and try again."
    )

    def __init__(self):
        super(UserTypeHeaderInformationIncorrect, self).__init__(
            description="X-User-Type header is incorrect to serve the request"
        )


class ERegCardIdCollision(DatabaseError):
    error_code = "0025"
    message = "Collision in e reg card id"


class InvalidTenantException(CRSException):
    error_code = "0030"
    message = "Tenant ID is either not passed in the request, or is invalid."

    def __init__(self, description=None):
        super(InvalidTenantException, self).__init__(description=description)


class NoOpenCashierSessionException(CRSException):
    error_code = "0031"
    message = "Cashier session is not open"


class NoBillPaymentCancellationInCashierException(CRSException):
    error_code = "0032"
    message = "Please cancel this payment from Frontdesk module"


class MissingCurrentBusinessDate(CRSException):
    error_code = "0034"
    message = "Current Business Date not configured for hotel"


class UniqueIdCollision(PrimaryKeyCollision):
    error_code = "0035"
    message = "Collision in unique id"


class CurrencyExchangePaymentCancellationNotAllowed(CRSException):
    error_code = "0036"
    message = "Cancellation of payment done via Currency Exchange is not allowed"


class UnableToObtainLockOnBooking(DatabaseLockError):
    error_code = "0037"
    message = (
        "The booking that you're trying to modify is currently opened or being modified by another user or "
        "operation. Please refresh the page and try again."
    )


class UnableToObtainLockOnBill(DatabaseLockError):
    error_code = "0038"
    # Keeping the message as booking only, as for PMS users, bill is not an entity. They only know about booking.
    message = (
        "The booking that you're trying to modify is currently opened or being modified by another user or "
        "operation. Please refresh the page and try again."
    )


class SellerIdHeaderInformationMissing(CRSException):
    error_code = "0039"
    message = "You're not authorised to perform this operation."

    def __init__(self):
        super(SellerIdHeaderInformationMissing, self).__init__(
            description="X-Seller-Id header is missing"
        )


class PayoutLinkGenerationFailed(CRSException):
    error_code = "0040"
    message = "Payout Link Creation Failed"

    def __init__(self, message=None):
        super(PayoutLinkGenerationFailed, self).__init__(message=message)


class RefundFailed(CRSException):
    error_code = "0041"
    message = "Refund Failed, Please Contact Escalation Team"

    def __init__(self, message=None):
        super(RefundFailed, self).__init__(message=message)


class MissingInventoryBlock(CRSException):
    error_code = "0042"
    message = "No Inventory BlockS found"

    def __init__(self, message=None):
        super(MissingInventoryBlock, self).__init__(message=message)


class InvalidInventoryBlock(CRSException):
    error_code = "0043"
    message = "Invalid inventory block"

    def __init__(self, message=None):
        super(InvalidInventoryBlock, self).__init__(message=message)
