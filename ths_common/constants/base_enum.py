from aenum import MultiValueEnum


class BaseEnum(MultiValueEnum):
    @classmethod
    def all(cls, exclude=None):
        if exclude:
            return [enum.value for enum in cls if enum not in exclude]
        else:
            return [enum.value for enum in cls]

    @classmethod
    def all_options(cls, as_set=False):
        return [enum for enum in cls] if not as_set else {enum for enum in cls}

    def __str__(self):
        return self.value

    @property
    def label(self):
        if len(self.values) > 1:
            return self.values[1]
        else:
            return self.value

    @classmethod
    def get_enums(cls, user_type):
        return [enum for enum in cls]

    @classmethod
    def all_values(cls):
        list_of_tuples = [enum.values for enum in cls]
        return [value for tup in list_of_tuples for value in tup]
