# coding=utf-8
"""
Catalog Constants
"""

from ths_common.constants.base_enum import BaseEnum


class HotelStatus(BaseEnum):
    """
    Enum for Hotel Status
    """

    ACTIVE = 'active'
    INACTIVE = 'inactive'


class RoomTypeStatus(BaseEnum):
    """
    Enum for Room Type Status
    """

    ACTIVE = 'active'
    INACTIVE = 'inactive'


class RoomStatus(BaseEnum):
    """
    Enum for Room Status
    """

    ACTIVE = 'active'
    INACTIVE = 'inactive'


class SkuCategoryStatus(BaseEnum):
    """
    Enum for Sku item Status
    """

    ACTIVE = 'ACTIVE'
    INACTIVE = 'INACTIVE'


class SkuCategory(BaseEnum):
    STAY = "stay"
    CURRENCY_EXCHANGE = "currency-exchange"


class SkuCategoryHSN(BaseEnum):
    STAY = "HSN:996311"


class SellerType(BaseEnum):
    MARKETPLACE = "MARKETPLACE"
    RESELLER = "RESELLER"


class SkuName(BaseEnum):
    TRANSFERRED_CHARGE = 'TRANSFERRED_CHARGE'


KERALA_STATE_ID = 9
SIKKIM_STATE_CODE = '11'


class NightAuditStatus(BaseEnum):
    SCHEDULED = 'scheduled'
    COMPLETED = 'completed'
    BLOCKED_BY_CRITICAL_TASK = 'blocked_by_critical_task'
    FAILED = 'failed'
    IN_PROGRESS = 'in_progress'


class RoomType(BaseEnum):
    HOUSE_ACCOUNTS = 'House Accounts'
