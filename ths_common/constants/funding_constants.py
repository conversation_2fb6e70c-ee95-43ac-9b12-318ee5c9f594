from ths_common.constants.base_enum import BaseEnum
from ths_common.value_objects import PhoneNumber

TREEBO_FUNDING_CUSTOMER_LEGAL_NAME = "Treebo Hospitality Ventures Private Limited"
TREEBO_PHONE_NUMBER = PhoneNumber("9322800100", "+91")
TREEBO_EMAIL_ID = "<EMAIL>"


class FundingStrategy(BaseEnum):
    LOWEST_FIRST = 'lowest_first'
    EQUAL_DISTRIBUTION = 'equal_distribution'


class FundingExpenseItem(BaseEnum):
    TREEBO_AUTO_FUNDING = "TariffSupport::A"
    TREEBO_MANUAL_FUNDING = "TariffSupport::B"


class FundingStatus(BaseEnum):
    CREATED = 'created'
    LOCKED = 'locked'
    REFUNDED = 'refunded'
    VOIDED = 'voided'


class FundingType(BaseEnum):
    MANUAL_FUNDING = 'manual_funding'
    AUTO_FUNDING = 'auto_funding'


class FundingExpenseTypes(BaseEnum):
    STAY = 'stay'
