"""empty message

Revision ID: 5b1f261c5914
Revises: b3852c8c06f1
Create Date: 2018-09-05 15:38:10.754878

"""
import sqlalchemy as sa
from alembic import op

import prometheus

# revision identifiers, used by Alembic.
revision = '5b1f261c5914'
down_revision = 'b3852c8c06f1'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('sku_category',
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
    sa.Column('modified_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
    sa.Column('deleted', sa.<PERSON>(), nullable=True),
    sa.Column('item_code', prometheus.infrastructure.database.common_models.ItemCodeDBType(), nullable=True),
    sa.Column('name', sa.String(), nullable=True),
    sa.Column('sku_category_id', sa.String(), nullable=False),
    sa.Column('status', sa.String(), nullable=True),
    sa.PrimaryKeyConstraint('sku_category_id')
    )
    op.add_column('hotel', sa.Column('city_id', sa.Integer(), nullable=True))
    op.add_column('hotel', sa.Column('country', sa.String(), nullable=True))
    op.add_column('hotel', sa.Column('legal_address', sa.String(), nullable=True))
    op.add_column('hotel', sa.Column('state_id', sa.Integer(), nullable=True))
    op.drop_column('hotel', 'address')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('hotel', sa.Column('address', sa.VARCHAR(), autoincrement=False, nullable=True))
    op.drop_column('hotel', 'state_id')
    op.drop_column('hotel', 'legal_address')
    op.drop_column('hotel', 'country')
    op.drop_column('hotel', 'city_id')
    op.drop_table('sku_category')
    # ### end Alembic commands ###
