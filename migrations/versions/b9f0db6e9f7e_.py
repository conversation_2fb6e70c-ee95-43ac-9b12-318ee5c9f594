"""empty message

Revision ID: b9f0db6e9f7e
Revises: 5f8109c2bb8c
Create Date: 2019-09-17 12:36:21.955321

"""
import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = 'b9f0db6e9f7e'
down_revision = '5f8109c2bb8c'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('booking_customer', sa.Column('has_lut', sa.<PERSON><PERSON>(), nullable=True))
    op.add_column('booking_customer', sa.Column('is_sez', sa.<PERSON>(), nullable=True))
    op.add_column('hotel', sa.Column('has_lut', sa.<PERSON>(), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('hotel', 'has_lut')
    op.drop_column('booking_customer', 'is_sez')
    op.drop_column('booking_customer', 'has_lut')
    # ### end Alembic commands ###
