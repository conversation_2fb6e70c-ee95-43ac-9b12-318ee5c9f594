"""empty message

Revision ID: 9e8662bf0edb
Revises: 
Create Date: 2018-05-16 17:15:00.669029

"""
import sqlalchemy as sa
from alembic import op

import prometheus

# revision identifiers, used by Alembic.
revision = '9e8662bf0edb'
down_revision = None
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('addon',
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=False),
    sa.Column('modified_at', sa.DateTime(timezone=True), nullable=False),
    sa.Column('deleted', sa.Boolean(), nullable=True),
    sa.Column('addon_id', sa.Integer(), nullable=False),
    sa.Column('booking_id', sa.Integer(), nullable=False),
    sa.Column('room_stay_id', sa.Integer(), nullable=False),
    sa.Column('expense_item_id', sa.String(), nullable=False),
    sa.Column('name', sa.String(), nullable=True),
    sa.Column('pretax_price', sa.DECIMAL(precision=9, scale=4), nullable=True),
    sa.Column('posttax_price', sa.DECIMAL(precision=9, scale=4), nullable=True),
    sa.Column('quantity', sa.Integer(), nullable=True),
    sa.Column('charge_checkin', sa.Boolean(), nullable=True),
    sa.Column('charge_checkout', sa.Boolean(), nullable=True),
    sa.Column('charge_other_days', sa.Boolean(), nullable=True),
    sa.Column('status', sa.String(), nullable=True),
    sa.PrimaryKeyConstraint('addon_id', 'booking_id', 'room_stay_id')
    )
    op.create_table('addon_expense',
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=False),
    sa.Column('modified_at', sa.DateTime(timezone=True), nullable=False),
    sa.Column('deleted', sa.Boolean(), nullable=True),
    sa.Column('addon_id', sa.Integer(), nullable=False),
    sa.Column('booking_id', sa.Integer(), nullable=False),
    sa.Column('room_stay_id', sa.Integer(), nullable=False),
    sa.Column('addon_expense_id', sa.Integer(), nullable=False),
    sa.Column('expense_id', sa.Integer(), nullable=False),
    sa.Column('expense_date', sa.DateTime(timezone=True), nullable=False),
    sa.Column('status', sa.String(), nullable=True),
    sa.PrimaryKeyConstraint('addon_id', 'booking_id', 'room_stay_id', 'addon_expense_id')
    )
    op.create_table('bill',
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=False),
    sa.Column('modified_at', sa.DateTime(timezone=True), nullable=False),
    sa.Column('deleted', sa.Boolean(), nullable=True),
    sa.Column('bill_id', sa.String(), nullable=False),
    sa.Column('vendor_id', sa.String(), nullable=True),
    sa.Column('vendor_details', sa.JSON(), nullable=True),
    sa.Column('bill_date', sa.DateTime(timezone=True), nullable=True),
    sa.Column('grace_period', sa.Integer(), nullable=True),
    sa.Column('gstin', sa.String(), nullable=True),
    sa.Column('version', sa.Integer(), nullable=True),
    sa.PrimaryKeyConstraint('bill_id')
    )
    op.create_table('booking',
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=False),
    sa.Column('modified_at', sa.DateTime(timezone=True), nullable=False),
    sa.Column('deleted', sa.Boolean(), nullable=True),
    sa.Column('booking_id', sa.String(), nullable=False),
    sa.Column('reference_number', sa.String(), nullable=True),
    sa.Column('booking_owner', sa.String(), nullable=True),
    sa.Column('checkin_date', sa.DateTime(timezone=True), nullable=True),
    sa.Column('checkout_date', sa.DateTime(timezone=True), nullable=True),
    sa.Column('actual_checkin_date', sa.DateTime(timezone=True), nullable=True),
    sa.Column('actual_checkout_date', sa.DateTime(timezone=True), nullable=True),
    sa.Column('status', sa.String(), nullable=True),
    sa.Column('channel_code', sa.String(), nullable=True),
    sa.Column('subchannel_code', sa.String(), nullable=True),
    sa.Column('application_code', sa.String(), nullable=True),
    sa.Column('hotel_id', sa.String(), nullable=True),
    sa.Column('hold_till', sa.DateTime(timezone=True), nullable=True),
    sa.Column('version_id', sa.Integer(), nullable=False),
    sa.Column('comments', sa.Text(), nullable=True),
    sa.Column('bill_id', sa.String(), nullable=True),
    sa.Column('extra_information', sa.JSON(), nullable=True),
    sa.Column('guests', sa.ARRAY(sa.String()), nullable=True),
    sa.PrimaryKeyConstraint('booking_id')
    )
    op.create_table('booking_action',
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=False),
    sa.Column('modified_at', sa.DateTime(timezone=True), nullable=False),
    sa.Column('deleted', sa.Boolean(), nullable=True),
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('action_type', sa.String(), nullable=True),
    sa.Column('created_by', sa.String(), nullable=True),
    sa.Column('entity', sa.String(), nullable=True),
    sa.Column('entity_id', sa.String(), nullable=True),
    sa.Column('status', sa.String(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('charge',
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=False),
    sa.Column('modified_at', sa.DateTime(timezone=True), nullable=False),
    sa.Column('deleted', sa.Boolean(), nullable=True),
    sa.Column('charge_id', sa.Integer(), nullable=False),
    sa.Column('bill_id', sa.String(), nullable=False),
    sa.Column('pretax_amount', sa.DECIMAL(precision=9, scale=4), nullable=True),
    sa.Column('tax_details', sa.ARRAY(prometheus.infrastructure.database.common_models.TaxDetailDBType()), nullable=True),
    sa.Column('tax_amount', sa.DECIMAL(precision=9, scale=4), nullable=True),
    sa.Column('posttax_amount', sa.DECIMAL(precision=9, scale=4), nullable=True),
    sa.Column('type', sa.String(), nullable=True),
    sa.Column('bill_to_type', sa.String(), nullable=True),
    sa.Column('created_by', sa.String(), nullable=True),
    sa.Column('item_name', sa.String(), nullable=True),
    sa.Column('sku_category_id', sa.String(), nullable=True),
    sa.Column('charge_item_detail', sa.JSON(), nullable=True),
    sa.Column('status', sa.String(), nullable=True),
    sa.Column('applicable_date', sa.DateTime(timezone=True), nullable=True),
    sa.Column('comment', sa.String(), nullable=True),
    sa.Column('is_refundable', sa.Boolean(), nullable=True),
    sa.PrimaryKeyConstraint('charge_id', 'bill_id')
    )
    op.create_table('charge_split',
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=False),
    sa.Column('modified_at', sa.DateTime(timezone=True), nullable=False),
    sa.Column('deleted', sa.Boolean(), nullable=True),
    sa.Column('charge_split_id', sa.Integer(), nullable=False),
    sa.Column('charge_id', sa.Integer(), nullable=False),
    sa.Column('bill_id', sa.String(), nullable=False),
    sa.Column('charge_to', sa.String(), nullable=True),
    sa.Column('tax', sa.DECIMAL(precision=9, scale=4), nullable=True),
    sa.Column('pre_tax', sa.DECIMAL(precision=9, scale=4), nullable=True),
    sa.Column('post_tax', sa.DECIMAL(precision=9, scale=4), nullable=True),
    sa.Column('invoice_id', sa.String(), nullable=True),
    sa.Column('tax_details', sa.ARRAY(prometheus.infrastructure.database.common_models.TaxDetailDBType()), nullable=True),
    sa.PrimaryKeyConstraint('charge_split_id', 'charge_id', 'bill_id')
    )
    op.create_table('customer',
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=False),
    sa.Column('modified_at', sa.DateTime(timezone=True), nullable=False),
    sa.Column('deleted', sa.Boolean(), nullable=True),
    sa.Column('customer_id', sa.String(), nullable=False),
    sa.Column('external_ref_id', sa.String(), nullable=True),
    sa.Column('profile_type', sa.String(), nullable=True),
    sa.Column('first_name', sa.String(), nullable=True),
    sa.Column('last_name', sa.String(), nullable=True),
    sa.Column('gender', sa.String(), nullable=True),
    sa.Column('age', sa.Integer(), nullable=True),
    sa.Column('nationality', sa.String(), nullable=True),
    sa.Column('addr_field1', sa.String(), nullable=True),
    sa.Column('addr_field2', sa.String(), nullable=True),
    sa.Column('addr_city', sa.String(), nullable=True),
    sa.Column('addr_state', sa.String(), nullable=True),
    sa.Column('addr_country', sa.String(), nullable=True),
    sa.Column('pincode', sa.String(), nullable=True),
    sa.Column('country_code', sa.String(), nullable=True),
    sa.Column('phone', sa.String(), nullable=True),
    sa.Column('email', sa.String(), nullable=True),
    sa.Column('image_url', sa.String(), nullable=True),
    sa.Column('id_proof_type', sa.String(), nullable=True),
    sa.Column('id_kyc_url', sa.String(), nullable=True),
    sa.Column('id_number', sa.String(), nullable=True),
    sa.Column('status', sa.String(), nullable=True),
    sa.Column('legal_name', sa.String(), nullable=True),
    sa.Column('address', sa.String(), nullable=True),
    sa.Column('gstin_num', sa.String(), nullable=True),
    sa.PrimaryKeyConstraint('customer_id')
    )
    op.create_table('dnr',
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=False),
    sa.Column('modified_at', sa.DateTime(timezone=True), nullable=False),
    sa.Column('deleted', sa.Boolean(), nullable=True),
    sa.Column('id', sa.String(), nullable=False),
    sa.Column('hotel_id', sa.String(), nullable=False),
    sa.Column('room_id', sa.Integer(), nullable=False),
    sa.Column('start_date', sa.DateTime(timezone=True), nullable=True),
    sa.Column('end_date', sa.DateTime(timezone=True), nullable=True),
    sa.Column('type', sa.String(), nullable=True),
    sa.Column('sub_type', sa.String(), nullable=True),
    sa.Column('source', sa.String(), nullable=False),
    sa.Column('status', sa.String(), nullable=False),
    sa.Column('assigned_by', sa.String(), nullable=True),
    sa.Column('comments', sa.Text(), nullable=True),
    sa.Column('date_inactivated', sa.DateTime(timezone=True), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('expense',
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=False),
    sa.Column('modified_at', sa.DateTime(timezone=True), nullable=False),
    sa.Column('deleted', sa.Boolean(), nullable=True),
    sa.Column('booking_id', sa.String(), nullable=False),
    sa.Column('room_stay_id', sa.Integer(), nullable=False),
    sa.Column('expense_id', sa.Integer(), nullable=False),
    sa.Column('expense_item_id', sa.String(), nullable=True),
    sa.Column('assigned_to', sa.ARRAY(sa.String()), nullable=True),
    sa.Column('status', sa.String(), nullable=True),
    sa.Column('comment', sa.String(), nullable=True),
    sa.Column('charge_id', sa.Integer(), nullable=True),
    sa.Column('applicable_date', sa.DateTime(timezone=True), nullable=False),
    sa.PrimaryKeyConstraint('booking_id', 'room_stay_id', 'expense_id')
    )
    op.create_table('expense_item',
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=False),
    sa.Column('modified_at', sa.DateTime(timezone=True), nullable=False),
    sa.Column('deleted', sa.Boolean(), nullable=True),
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('name', sa.String(), nullable=True),
    sa.Column('description', sa.String(), nullable=True),
    sa.Column('short_name', sa.String(), nullable=True),
    sa.Column('sku_category_id', sa.String(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('guest_allocation',
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=False),
    sa.Column('modified_at', sa.DateTime(timezone=True), nullable=False),
    sa.Column('deleted', sa.Boolean(), nullable=True),
    sa.Column('booking_id', sa.String(), nullable=False),
    sa.Column('room_stay_id', sa.Integer(), nullable=False),
    sa.Column('guest_stay_id', sa.Integer(), nullable=False),
    sa.Column('guest_allocation_id', sa.Integer(), nullable=False),
    sa.Column('guest_id', sa.String(), nullable=True),
    sa.Column('assigned_by', sa.String(), nullable=True),
    sa.Column('checkin_date', sa.DateTime(timezone=True), nullable=True),
    sa.Column('checkout_date', sa.DateTime(timezone=True), nullable=True),
    sa.Column('is_current', sa.Boolean(), nullable=True),
    sa.PrimaryKeyConstraint('booking_id', 'room_stay_id', 'guest_stay_id', 'guest_allocation_id')
    )
    op.create_table('guest_stay',
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=False),
    sa.Column('modified_at', sa.DateTime(timezone=True), nullable=False),
    sa.Column('deleted', sa.Boolean(), nullable=True),
    sa.Column('booking_id', sa.String(), nullable=False),
    sa.Column('room_stay_id', sa.Integer(), nullable=False),
    sa.Column('guest_stay_id', sa.Integer(), nullable=False),
    sa.Column('status', sa.String(), nullable=True),
    sa.Column('checkin_date', sa.DateTime(timezone=True), nullable=True),
    sa.Column('checkout_date', sa.DateTime(timezone=True), nullable=True),
    sa.Column('actual_checkin_date', sa.DateTime(timezone=True), nullable=True),
    sa.Column('actual_checkout_date', sa.DateTime(timezone=True), nullable=True),
    sa.Column('age_group', sa.String(), nullable=True),
    sa.PrimaryKeyConstraint('booking_id', 'room_stay_id', 'guest_stay_id')
    )
    op.create_table('hotel',
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=False),
    sa.Column('modified_at', sa.DateTime(timezone=True), nullable=False),
    sa.Column('deleted', sa.Boolean(), nullable=True),
    sa.Column('hotel_id', sa.String(), nullable=False),
    sa.Column('name', sa.String(), nullable=True),
    sa.Column('area', sa.String(), nullable=True),
    sa.Column('status', sa.String(), nullable=False),
    sa.Column('legal_name', sa.String(), nullable=True),
    sa.Column('address', sa.String(), nullable=True),
    sa.Column('gstin_num', sa.String(), nullable=True),
    sa.Column('night_audit_date', sa.Date(), nullable=True),
    sa.Column('switch_over_time', sa.String(), nullable=True),
    sa.Column('checkin_grace_time', sa.Integer(), nullable=True),
    sa.Column('checkout_grace_time', sa.Integer(), nullable=True),
    sa.Column('checkin_time', sa.String(), nullable=True),
    sa.Column('checkout_time', sa.String(), nullable=True),
    sa.Column('early_checkin_fee', sa.String(), nullable=True),
    sa.Column('late_checkout_fee', sa.String(), nullable=True),
    sa.Column('free_early_checkin_time', sa.String(), nullable=True),
    sa.Column('free_late_checkout_time', sa.String(), nullable=True),
    sa.PrimaryKeyConstraint('hotel_id')
    )
    op.create_table('hotel_config',
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=False),
    sa.Column('modified_at', sa.DateTime(timezone=True), nullable=False),
    sa.Column('deleted', sa.Boolean(), nullable=True),
    sa.Column('hotel_id', sa.String(), nullable=False),
    sa.Column('migration_start_date', sa.DateTime(timezone=True), nullable=True),
    sa.Column('migration_end_date', sa.DateTime(timezone=True), nullable=True),
    sa.Column('live_date', sa.DateTime(timezone=True), nullable=True),
    sa.Column('managed_by', sa.String(), nullable=True),
    sa.PrimaryKeyConstraint('hotel_id')
    )
    op.create_table('hotel_room_type_config',
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=False),
    sa.Column('modified_at', sa.DateTime(timezone=True), nullable=False),
    sa.Column('deleted', sa.Boolean(), nullable=True),
    sa.Column('room_type_config_id', sa.Integer(), nullable=False),
    sa.Column('hotel_id', sa.String(), nullable=False),
    sa.Column('room_type_id', sa.String(), nullable=True),
    sa.Column('count', sa.Integer(), nullable=True),
    sa.Column('base_pax', sa.Integer(), nullable=True),
    sa.Column('max_pax', sa.Integer(), nullable=True),
    sa.Column('max_child', sa.Integer(), nullable=True),
    sa.Column('max_adult_plus_children', sa.Integer(), nullable=True),
    sa.Column('status', sa.String(), nullable=False),
    sa.PrimaryKeyConstraint('room_type_config_id')
    )
    op.create_table('integration_event',
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=False),
    sa.Column('modified_at', sa.DateTime(timezone=True), nullable=False),
    sa.Column('deleted', sa.Boolean(), nullable=True),
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('hotel_id', sa.String(), nullable=False),
    sa.Column('booking_id', sa.String(), nullable=False),
    sa.Column('version', sa.String(), nullable=False),
    sa.Column('event_type', sa.String(), nullable=False),
    sa.Column('generated_at', sa.DateTime(timezone=True), nullable=False),
    sa.Column('body', sa.JSON(), nullable=False),
    sa.Column('status', sa.String(), nullable=False),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('invoice',
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=False),
    sa.Column('modified_at', sa.DateTime(timezone=True), nullable=False),
    sa.Column('deleted', sa.Boolean(), nullable=True),
    sa.Column('bill_id', sa.String(), nullable=True),
    sa.Column('vendor_id', sa.String(), nullable=True),
    sa.Column('vendor_details', sa.JSON(), nullable=True),
    sa.Column('invoice_id', sa.String(), nullable=False),
    sa.Column('invoice_number', sa.String(), nullable=True),
    sa.Column('invoice_date', sa.DateTime(timezone=True), nullable=False),
    sa.Column('invoice_due_date', sa.DateTime(timezone=True), nullable=False),
    sa.Column('parent_info', sa.JSON(), nullable=True),
    sa.Column('bill_to', sa.String(), nullable=True),
    sa.Column('status', sa.String(), nullable=True),
    sa.Column('generated_by', sa.String(), nullable=True),
    sa.Column('generation_channel', sa.String(), nullable=True),
    sa.Column('pretax_amount', sa.DECIMAL(precision=9, scale=4), nullable=True),
    sa.Column('tax_amount', sa.DECIMAL(precision=9, scale=4), nullable=True),
    sa.Column('posttax_amount', sa.DECIMAL(precision=9, scale=4), nullable=True),
    sa.Column('invoice_url', sa.String(), nullable=True),
    sa.Column('version', sa.Integer(), nullable=True),
    sa.PrimaryKeyConstraint('invoice_id'),
    sa.UniqueConstraint('invoice_number')
    )
    op.create_table('invoice_charges',
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=False),
    sa.Column('modified_at', sa.DateTime(timezone=True), nullable=False),
    sa.Column('deleted', sa.Boolean(), nullable=True),
    sa.Column('invoice_charge_id', sa.Integer(), nullable=False),
    sa.Column('invoice_id', sa.String(), nullable=False),
    sa.Column('charge_id', sa.Integer(), nullable=True),
    sa.Column('charge_split_ids', sa.ARRAY(sa.Integer()), nullable=True),
    sa.Column('type', sa.String(), nullable=True),
    sa.Column('bill_to_type', sa.String(), nullable=True),
    sa.Column('created_by', sa.String(), nullable=True),
    sa.Column('pretax_amount', sa.DECIMAL(precision=9, scale=4), nullable=True),
    sa.Column('tax_details', sa.ARRAY(prometheus.infrastructure.database.common_models.TaxDetailDBType()), nullable=True),
    sa.Column('tax_amount', sa.DECIMAL(precision=9, scale=4), nullable=True),
    sa.Column('posttax_amount', sa.DECIMAL(precision=9, scale=4), nullable=True),
    sa.Column('item_name', sa.String(), nullable=True),
    sa.Column('item_code', prometheus.infrastructure.database.common_models.ItemCodeDBType(), nullable=True),
    sa.Column('charge_item_detail', sa.JSON(), nullable=True),
    sa.Column('applicable_date', sa.DateTime(timezone=True), nullable=True),
    sa.Column('comment', sa.String(), nullable=True),
    sa.Column('is_refundable', sa.Boolean(), nullable=True),
    sa.Column('charge_to', sa.ARRAY(sa.String()), nullable=True),
    sa.PrimaryKeyConstraint('invoice_charge_id', 'invoice_id')
    )
    op.create_table('invoice_number_count',
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=False),
    sa.Column('modified_at', sa.DateTime(timezone=True), nullable=False),
    sa.Column('deleted', sa.Boolean(), nullable=True),
    sa.Column('vendor_id', sa.Integer(), nullable=False),
    sa.Column('sequence_number', sa.Integer(), nullable=True),
    sa.PrimaryKeyConstraint('vendor_id')
    )
    op.create_table('job',
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=False),
    sa.Column('modified_at', sa.DateTime(timezone=True), nullable=False),
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('hotel_id', sa.String(), nullable=False),
    sa.Column('booking_id', sa.String(), nullable=False),
    sa.Column('serialized_job', sa.JSON(), nullable=False),
    sa.Column('job_name', sa.String(), nullable=False),
    sa.Column('status', sa.String(), nullable=False),
    sa.Column('generated_at', sa.DateTime(timezone=True), nullable=False),
    sa.Column('picked_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('failure_message', sa.String(), nullable=True),
    sa.Column('total_tries', sa.Integer(), nullable=False),
    sa.Column('eta', sa.DateTime(timezone=True), nullable=False),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('payment',
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=False),
    sa.Column('modified_at', sa.DateTime(timezone=True), nullable=False),
    sa.Column('deleted', sa.Boolean(), nullable=True),
    sa.Column('bill_id', sa.String(), nullable=False),
    sa.Column('payment_id', sa.Integer(), nullable=False),
    sa.Column('amount', sa.DECIMAL(), nullable=True),
    sa.Column('date_of_payment', sa.DateTime(timezone=True), nullable=True),
    sa.Column('payment_mode', sa.String(), nullable=True),
    sa.Column('payment_type', sa.String(), nullable=True),
    sa.Column('payment_details', sa.JSON(), nullable=True),
    sa.Column('status', sa.String(), nullable=True),
    sa.Column('paid_by', sa.String(), nullable=True),
    sa.Column('paid_to', sa.String(), nullable=True),
    sa.Column('payment_channel', sa.String(), nullable=True),
    sa.Column('payment_ref_id', sa.String(), nullable=True),
    sa.PrimaryKeyConstraint('bill_id', 'payment_id')
    )
    op.create_table('room',
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=False),
    sa.Column('modified_at', sa.DateTime(timezone=True), nullable=False),
    sa.Column('deleted', sa.Boolean(), nullable=True),
    sa.Column('room_id', sa.Integer(), nullable=False),
    sa.Column('hotel_id', sa.String(), nullable=False),
    sa.Column('room_type_id', sa.String(), nullable=False),
    sa.Column('room_number', sa.String(), nullable=False),
    sa.Column('status', sa.String(), nullable=False),
    sa.PrimaryKeyConstraint('room_id')
    )
    op.create_table('room_allocation',
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=False),
    sa.Column('modified_at', sa.DateTime(timezone=True), nullable=False),
    sa.Column('deleted', sa.Boolean(), nullable=True),
    sa.Column('booking_id', sa.String(), nullable=False),
    sa.Column('room_stay_id', sa.Integer(), nullable=False),
    sa.Column('room_allocation_id', sa.Integer(), nullable=False),
    sa.Column('room_id', sa.Integer(), nullable=False),
    sa.Column('room_no', sa.String(), nullable=False),
    sa.Column('room_type_id', sa.String(), nullable=False),
    sa.Column('assigned_by', sa.String(), nullable=True),
    sa.Column('checkin_date', sa.DateTime(timezone=True), nullable=True),
    sa.Column('checkout_date', sa.DateTime(timezone=True), nullable=True),
    sa.Column('is_current', sa.Boolean(), nullable=True),
    sa.PrimaryKeyConstraint('booking_id', 'room_stay_id', 'room_allocation_id')
    )
    op.create_table('room_inventory_availability',
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=False),
    sa.Column('modified_at', sa.DateTime(timezone=True), nullable=False),
    sa.Column('deleted', sa.Boolean(), nullable=True),
    sa.Column('hotel_id', sa.String(), nullable=False),
    sa.Column('room_id', sa.Integer(), nullable=False),
    sa.Column('date', sa.Date(), nullable=False),
    sa.Column('status', sa.String(), nullable=True),
    sa.PrimaryKeyConstraint('hotel_id', 'room_id', 'date')
    )
    op.create_table('room_inventory_current_status',
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=False),
    sa.Column('modified_at', sa.DateTime(timezone=True), nullable=False),
    sa.Column('deleted', sa.Boolean(), nullable=True),
    sa.Column('hotel_id', sa.String(), nullable=False),
    sa.Column('room_id', sa.Integer(), nullable=False),
    sa.Column('status', sa.String(), nullable=True),
    sa.PrimaryKeyConstraint('hotel_id', 'room_id')
    )
    op.create_table('room_stay',
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=False),
    sa.Column('modified_at', sa.DateTime(timezone=True), nullable=False),
    sa.Column('deleted', sa.Boolean(), nullable=True),
    sa.Column('booking_id', sa.String(), nullable=False),
    sa.Column('room_stay_id', sa.Integer(), nullable=False),
    sa.Column('room_type_id', sa.String(), nullable=False),
    sa.Column('type', sa.String(), nullable=True),
    sa.Column('status', sa.String(), nullable=True),
    sa.Column('checkin_date', sa.DateTime(timezone=True), nullable=True),
    sa.Column('checkout_date', sa.DateTime(timezone=True), nullable=True),
    sa.Column('actual_checkin_date', sa.DateTime(timezone=True), nullable=True),
    sa.Column('actual_checkout_date', sa.DateTime(timezone=True), nullable=True),
    sa.Column('charge_id_map', sa.JSON(), nullable=True),
    sa.PrimaryKeyConstraint('booking_id', 'room_stay_id')
    )
    op.create_table('room_type',
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=False),
    sa.Column('modified_at', sa.DateTime(timezone=True), nullable=False),
    sa.Column('deleted', sa.Boolean(), nullable=True),
    sa.Column('room_type_id', sa.String(), nullable=False),
    sa.Column('type', sa.String(), nullable=True),
    sa.PrimaryKeyConstraint('room_type_id')
    )
    op.create_table('room_type_inventory_availability',
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=False),
    sa.Column('modified_at', sa.DateTime(timezone=True), nullable=False),
    sa.Column('deleted', sa.Boolean(), nullable=True),
    sa.Column('hotel_id', sa.String(), nullable=False),
    sa.Column('room_type_id', sa.String(), nullable=False),
    sa.Column('date', sa.Date(), nullable=False),
    sa.Column('count', sa.Integer(), nullable=False),
    sa.PrimaryKeyConstraint('hotel_id', 'room_type_id', 'date')
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('room_type_inventory_availability')
    op.drop_table('room_type')
    op.drop_table('room_stay')
    op.drop_table('room_inventory_current_status')
    op.drop_table('room_inventory_availability')
    op.drop_table('room_allocation')
    op.drop_table('room')
    op.drop_table('payment')
    op.drop_table('job')
    op.drop_table('invoice_number_count')
    op.drop_table('invoice_charges')
    op.drop_table('invoice')
    op.drop_table('integration_event')
    op.drop_table('hotel_room_type_config')
    op.drop_table('hotel_config')
    op.drop_table('hotel')
    op.drop_table('guest_stay')
    op.drop_table('guest_allocation')
    op.drop_table('expense_item')
    op.drop_table('expense')
    op.drop_table('dnr')
    op.drop_table('customer')
    op.drop_table('charge_split')
    op.drop_table('charge')
    op.drop_table('booking_action')
    op.drop_table('booking')
    op.drop_table('bill')
    op.drop_table('addon_expense')
    op.drop_table('addon')
    # ### end Alembic commands ###
