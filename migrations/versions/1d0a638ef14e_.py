"""empty message

Revision ID: 1d0a638ef14e
Revises: 275727aa9ed7
Create Date: 2019-05-16 12:01:17.018820

"""
import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = '1d0a638ef14e'
down_revision = '275727aa9ed7'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('booking_room_stay_overflow',
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.Column('modified_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.Column('hotel_id', sa.String(), nullable=False),
        sa.Column('booking_id', sa.String(), nullable=False),
        sa.Column('room_stay_id', sa.Integer(), nullable=False),
        sa.Column('room_type_id', sa.String(), nullable=False),
        sa.Column('start_date', sa.Date(), nullable=False),
        sa.Column('end_date', sa.Date(), nullable=False),
        sa.PrimaryKeyConstraint('hotel_id', 'booking_id', 'room_stay_id', 'start_date', 'end_date')
    )
    op.create_index('idx_booking_room_stay_overflow_dates', 'booking_room_stay_overflow', ['start_date', sa.text('end_date DESC')], unique=False)
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index('idx_booking_room_stay_overflow_dates', table_name='booking_room_stay_overflow')
    op.drop_table('booking_room_stay_overflow')
    # ### end Alembic commands ###
