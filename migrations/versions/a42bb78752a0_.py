"""empty message

Revision ID: a42bb78752a0
Revises: 5f150134314d
Create Date: 2018-06-26 17:37:01.257971

"""
import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = 'a42bb78752a0'
down_revision = '5f150134314d'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('customer', sa.Column('id_proof_country_code', sa.String(), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('customer', 'id_proof_country_code')
    # ### end Alembic commands ###
