-- revision: '20210325131337_added_inventory_audit_trail'
-- down_revision: '20210129152634_rate_plan_addon_in_addon'

-- upgrade

CREATE TABLE inventory_audit_trail(
audit_id character varying PRIMARY KEY,
hotel_id character varying not null,
room_type_id character varying not null,
date Date not null,
available_count Integer not null,
integration_event_id character varying,
user_type character varying,
application character varying,
user_action character varying,
booking_id character varying,
created_at timestamp with time zone default now(),
modified_at timestamp with time zone default now());

CREATE INDEX ix_hotel_id ON inventory_audit_trail USING btree (hotel_id, date, room_type_id);
CREATE INDEX ix_integration_event_id ON inventory_audit_trail USING btree (integration_event_id);
CREATE INDEX ix_booking_id on inventory_audit_trail USING btree (booking_id)

-- downgrade

drop table inventory_audit_trail;