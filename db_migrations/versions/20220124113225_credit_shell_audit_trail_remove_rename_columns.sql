-- revision: '20220124113225_credit_shell_audit_trail_remove_rename_columns'
-- down_revision: '20211221004510_credit_shell_audit_trail_table'

-- upgrade
ALTER TABLE credit_shell_audit_trail
DROP COLUMN action_performed,
DROP COLUMN auth_id,
DROP COLUMN application,
DROP COLUMN remarks,
DROP COLUMN booking_id,
DROP COLUMN bill_id,
DROP COLUMN "user";

ALTER   TABLE credit_shell_audit_trail
RENAME COLUMN booking_reference_number
           TO target_booking_reference_number;

-- downgrade
ALTER TABLE credit_shell_audit_trail
ADD COLUMN action_performed VARCHAR,
ADD COLUMN "user" VARCHAR,
ADD COLUMN auth_id VARCHAR,
ADD COLUMN application VARCHAR,
ADD COLUMN remarks TEXT,
ADD COLUMN booking_id VARCHAR,
ADD COLUMN bill_id  VARCHAR
;

ALTER   TABLE credit_shell_audit_trail
RENAME COLUMN target_booking_reference_number
           TO booking_reference_number;
