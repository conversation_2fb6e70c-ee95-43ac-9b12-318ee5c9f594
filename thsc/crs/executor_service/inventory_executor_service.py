from thsc.crs.request_templates.inventory.dnr import (
    GetDNR,
    MarkDNR,
    ResolveDNR,
    SearchDNR,
    UpdateDNR,
)
from thsc.crs.request_templates.inventory.inventory import (
    GetRoomTypeInventoryAvailabilities,
)


class InventoryExecutorService(object):
    def __init__(self, executor):
        self.executor = executor

    def get_dnr(self, hotel_id, dnr_id):
        request = GetDNR(hotel_id, dnr_id)
        return self.executor.execute(request)

    def mark_dnr(self, dnr):
        request = MarkDNR(dnr.hotel_id, dnr)
        return self.executor.execute(request)

    def update_dnr(self, dnr, resource_version):
        request = UpdateDNR(dnr.hotel_id, dnr.dnr_id, resource_version, dnr)
        return self.executor.execute(request)

    def remove_dnr(self, dnr, resource_version):
        request = ResolveDNR(dnr.hotel_id, dnr.dnr_id, resource_version)
        return self.executor.execute(request)

    def search_dnr(self, hotel_id, dnr_search_query):
        request = SearchDNR(hotel_id, dnr_search_query)
        return self.executor.execute(request)

    def get_room_type_inventories(
        self, hotel_id, room_type_inventory_availability_get_query
    ):
        request = GetRoomTypeInventoryAvailabilities(
            hotel_id, room_type_inventory_availability_get_query
        )
        return self.executor.execute(request)
