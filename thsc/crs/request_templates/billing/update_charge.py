from thsc.crs.convertors.billing_convertors import UpdateChargeConvertor
from thsc.crs.request_templates.request import Request


class UpdateCharge(Request):
    _convertor = UpdateChargeConvertor

    def __init__(self, bill_id, charge_id, version, charge):
        super(UpdateCharge, self).__init__(
            '/bills/%s/charges/%s' % (bill_id, charge_id), 'PATCH', charge
        )
        self.bill_id = bill_id
        self.charge_id = charge_id
        self.resource_version = version
