from thsc.crs.convertors.billing_convertors import PaymentConvertor
from thsc.crs.request_templates.billing.payment import Payment


class AddPayment(Payment):
    _convertor = PaymentConvertor

    def __init__(self, bill_id, version, payment):
        super(AddPayment, self).__init__(
            ('/bills/%s/payments') % bill_id, 'POST', payment
        )
        self.bill_id = bill_id
        self.resource_version = version
