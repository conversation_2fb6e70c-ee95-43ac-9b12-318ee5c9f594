from thsc.crs.convertors.booking_convertors import (
    CustomerConvertor,
    GuestStayConvertor,
    RatePlanInclusionConvertor,
    UpdateCustomerConvertor,
)
from thsc.crs.convertors.value_object_convertors import PriceConvertor
from thsc.crs.request_templates.request import Request


class Guest(Request):
    _convertor = CustomerConvertor

    def __init__(self, url, request_method, data=None):
        super(Guest, self).__init__(url, request_method, data)

    def get_uri(self):
        return super(Guest, self).get_uri()


class AddGuest(Guest):
    _convertor = GuestStayConvertor

    def __init__(
        self, booking_id, room_id, guest, new_room_stay_prices, resource_version
    ):
        super(AddGuest, self).__init__(
            url='/bookings/{booking_id}/room-stays/{room_stay_id}/guest-stays',
            request_method='POST',
            data=guest,
        )
        self.booking_id = booking_id
        self.room_id = room_id
        self.new_room_stay_prices = new_room_stay_prices
        self.resource_version = resource_version

    def get_uri(self):
        return (
            super(AddGuest, self)
            .get_uri()
            .format(booking_id=self.booking_id, room_stay_id=self.room_id)
        )

    def to_dict(self):
        response = super(AddGuest, self).to_dict()
        prices = PriceConvertor().to_dict(self.new_room_stay_prices, many=True)
        response['data']['new_room_stay_prices'] = prices
        response['resource_version'] = self.resource_version
        return response


class AddGuests(Guest):
    _convertor = GuestStayConvertor
    _many = True

    def __init__(
        self,
        booking_id,
        room_id,
        guests,
        new_room_stay_prices,
        rate_plan_inclusions,
        resource_version,
    ):
        super(AddGuests, self).__init__(
            url='/bookings/{booking_id}/room-stays/{room_stay_id}/guest-stays-list',
            request_method='POST',
            data=guests,
        )
        self.booking_id = booking_id
        self.room_id = room_id
        self.new_room_stay_prices = new_room_stay_prices
        self.rate_plan_inclusions = rate_plan_inclusions
        self.resource_version = resource_version

    def get_uri(self):
        return (
            super(AddGuests, self)
            .get_uri()
            .format(booking_id=self.booking_id, room_stay_id=self.room_id)
        )

    def to_dict(self):
        response = super(AddGuests, self).to_dict()
        prices = PriceConvertor().to_dict(self.new_room_stay_prices, many=True)
        rate_plan_inclusions = RatePlanInclusionConvertor().to_dict(
            self.rate_plan_inclusions, many=True
        )
        response['data'] = dict(
            new_room_stay_prices=prices,
            guest_stays=response["data"],
            rate_plan_inclusions=rate_plan_inclusions,
        )
        response['resource_version'] = self.resource_version
        return response


class UpdateGuest(Guest):
    _convertor = UpdateCustomerConvertor

    def __init__(self, booking_id, guest_id, guest):
        super(UpdateGuest, self).__init__(
            url='/bookings/{booking_id}/guests/{guest_id}',
            request_method='PUT',
            data=guest,
        )
        self.guest_id = guest_id
        self.booking_id = booking_id

    def get_uri(self):
        return (
            super(UpdateGuest, self)
            .get_uri()
            .format(booking_id=self.booking_id, guest_id=self.guest_id)
        )


class DeleteGuest(Guest):
    _payload = True

    def __init__(self, booking_id, room_id, id):
        super(DeleteGuest, self).__init__(
            url='/bookings/{booking_id}/room-stays/{room_stay_id}/guest-stays/{guest_stay_id}',
            request_method='DELETE',
        )
        self.room_id = room_id
        self.id = id
        self.booking_id = booking_id

    def get_uri(self):
        return (
            super(DeleteGuest, self)
            .get_uri()
            .format(
                booking_id=self.booking_id,
                guest_stay_id=self.id,
                room_stay_id=self.room_id,
            )
        )

    def to_dict(self):
        '''
        Expected Request:
        {
            "new_room_stay_charges": [
                {
                    "applicable_date": "2018-04-18T16:07:07.164Z",
                    "bill_to_type": "COMPANY",
                    "posttax_amount": 0,
                    "pretax_amount": 0,
                    "type": "CREDIT"
                }
            ]
        }

        '''
        return []
