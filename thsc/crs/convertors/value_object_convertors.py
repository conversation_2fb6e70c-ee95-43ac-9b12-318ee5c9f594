from ths_common.constants.billing_constants import ChargeBillToTypes, ChargeTypes
from ths_common.constants.booking_constants import GuaranteeTypes, TACommissionTypes
from ths_common.value_objects import (
    AccountDetails,
    Address,
    BillingInstructions,
    CommissionTax,
    CompanyDetails,
    CompanyMetadata,
    Discount,
    DiscountDetail,
    ExpenseServiceContext,
    GSTDetails,
    GuaranteeInformation,
    GuestMetadata,
    IDProof,
    LegalDetails,
    LoyaltyProgramDetails,
    Name,
    PhoneNumber,
    RoomRent,
    Segment,
    SegmentValue,
    TACommissionDetails,
    TADetails,
    TaxDetail,
)
from thsc.crs.convertors.base_convertor import BaseConvertor
from thsc.crs.convertors.scalar_type_convertors import (
    DateConvertor,
    DateTimeConvertor,
    EnumConvertor,
    MoneyConvertor,
)
from thsc.crs.entities.booking import Price


class AddressConvertor(BaseConvertor):
    convertor = {
        "city": (None, "city"),
        "country": (None, "country"),
        "field_1": (None, "field_1"),
        "field_2": (None, "field_2"),
        "pincode": (None, "pincode"),
        "state": (None, "state"),
    }
    reverse_convertor = {
        "city": (None, "city"),
        "country": (None, "country"),
        "field_1": (None, "field_1"),
        "field_2": (None, "field_2"),
        "pincode": (None, "pincode"),
        "state": (None, "state"),
    }
    object_class = Address


class GSTDetailsConvertor(BaseConvertor):
    convertor = {
        "address": (AddressConvertor, "address"),
        "gstin_num": (None, "gstin_num"),
        "legal_name": (None, "legal_name"),
        "is_sez": (None, "is_sez"),
        "has_lut": (None, "has_lut"),
    }
    object_class = GSTDetails
    reverse_convertor = {
        "address": (AddressConvertor, "address"),
        "gstin_num": (None, "gstin_num"),
        "legal_name": (None, "legal_name"),
        "is_sez": (None, "is_sez"),
        "has_lut": (None, "has_lut"),
    }


class DiscountConvertor(BaseConvertor):
    object_class = Discount
    convertor = {
        "discount_detail_reference_id": (None, "discount_detail_reference_id"),
        "discount_value": (MoneyConvertor, "discount_value"),
    }
    reverse_convertor = convertor


class PriceConvertor(BaseConvertor):
    object_class = Price
    convertor = {
        "applicable_date": (DateTimeConvertor, "applicable_date"),
        "bill_to_type": (EnumConvertor(ChargeBillToTypes), "bill_to_type"),
        "posttax_amount": (MoneyConvertor, "posttax_amount"),
        "pretax_amount": (MoneyConvertor, "pretax_amount"),
        "type": (EnumConvertor(ChargeTypes), "type"),
        "rate_plan_reference_id": (None, "rate_plan_reference_id"),
        "discounts": (DiscountConvertor, "discounts"),
    }

    reverse_convertor = convertor


class UpdatePriceConvertor(PriceConvertor):
    convertor = {
        "bill_to_type": (EnumConvertor(ChargeBillToTypes), "bill_to_type"),
        "posttax_amount": (MoneyConvertor, "posttax_amount"),
        "pretax_amount": (MoneyConvertor, "pretax_amount"),
        "type": (EnumConvertor(ChargeTypes), "type"),
        "charge_id": (None, "charge_id"),
        "charge_to": (None, "charge_to"),
        "rate_plan_reference_id": (None, "rate_plan_reference_id"),
    }


class IDProofConvertor(BaseConvertor):
    object_class = IDProof
    convertor = {
        "id_proof_type": (None, "id_proof_type"),
        "id_number": (None, "id_number"),
        "id_kyc_url": (None, "id_kyc_url"),
        "id_proof_country_code": (None, "id_proof_country_code"),
    }
    reverse_convertor = convertor


class NameConverter(BaseConvertor):
    object_class = Name
    convertor = {
        'first_name': (None, 'first_name'),
        'middle_name': (None, 'middle_name'),
        'last_name': (None, 'last_name'),
        'salutation': (None, 'salutation'),
    }
    reverse_convertor = convertor


class PhoneConvertor(BaseConvertor):
    object_class = PhoneNumber
    convertor = {'number': (None, 'number'), 'country_code': (None, 'country_code')}
    reverse_convertor = convertor


class TaxDetailConvertor(BaseConvertor):
    object_class = TaxDetail
    reverse_convertor = {
        'tax_type': (None, 'tax_type'),
        'amount': (MoneyConvertor, 'amount'),
        'percentage': (None, 'percentage'),
    }


class AccountDetailsConverter(BaseConvertor):
    convertor = {
        "account_id": (None, "account_id"),
        "version": (None, "version"),
    }
    object_class = AccountDetails
    reverse_convertor = convertor


class GuestMetadataConvertor(BaseConvertor):
    convertor = {
        "privacy_options": (None, "privacy_options"),
        "primary_language_id": (None, "primary_language_id"),
        "mfname_code": (None, "mfname_code"),
        "profile_certification_details": (None, "profile_certification_details"),
    }
    object_class = GuestMetadata
    reverse_convertor = convertor


class LegalDetailsConvertor(BaseConvertor):
    convertor = {
        "legal_name": (None, "legal_name"),
        "email": (None, "email"),
        "phone": (PhoneConvertor, "phone"),
        "tin": (None, "tin"),
        "address": (AddressConvertor, "address"),
        "is_sez": (None, "is_sez"),
        "has_lut": (None, "has_lut"),
        "client_internal_code": (None, "client_internal_code"),
        "external_reference_id": (None, "external_reference_id"),
    }
    object_class = LegalDetails
    reverse_convertor = {
        "legal_name": (None, "legal_name"),
        "email": (None, "email"),
        "phone": (PhoneConvertor, "phone"),
        "tin": (None, "tin"),
        "address": (AddressConvertor, "address"),
        "is_sez": (None, "is_sez"),
        "has_lut": (None, "has_lut"),
        "client_internal_code": (None, "client_internal_code"),
        "external_reference_id": (None, "external_reference_id"),
    }


class TACommissionTaxConvertor(BaseConvertor):
    convertor = {
        "tax": (None, "tax"),
        "tcs": (None, "tcs"),
        "tds": (None, "tds"),
        "rcm": (None, "rcm"),
    }
    object_class = CommissionTax
    reverse_convertor = convertor


class TACommissionDetailsConvertor(BaseConvertor):
    convertor = {
        "commission_type": (EnumConvertor(TACommissionTypes), "commission_type"),
        "commission_value": (None, "commission_value"),
        "post_commission_amount": (None, "post_commission_amount"),
        "commission_tax": (TACommissionTaxConvertor, "commission_tax"),
    }
    object_class = TACommissionDetails
    reverse_convertor = convertor


class CompanyMetadataConvertor(BaseConvertor):
    convertor = {
        "privacy_options": (None, "privacy_options"),
        "primary_language_id": (None, "primary_language_id"),
        "mfname_code": (None, "mfname_code"),
        "profile_certification_details": (None, "profile_certification_details"),
        "nationality": (None, "nationality"),
    }
    object_class = CompanyMetadata
    reverse_convertor = convertor


class TADetailsConvertor(BaseConvertor):
    convertor = {
        "legal_details": (LegalDetailsConvertor, "legal_details"),
        "ta_commission_details": (
            TACommissionDetailsConvertor,
            "ta_commission_details",
        ),
        "billed_entity_id": (None, "billed_entity_id"),
        "metadata": (CompanyMetadataConvertor, "metadata"),
    }
    object_class = TADetails
    reverse_convertor = convertor


class CompanyDetailsConvertor(BaseConvertor):
    convertor = {
        "legal_details": (LegalDetailsConvertor, "legal_details"),
        "billed_entity_id": (None, "billed_entity_id"),
        "metadata": (CompanyMetadataConvertor, "metadata"),
    }
    object_class = CompanyDetails
    reverse_convertor = {
        "legal_details": (LegalDetailsConvertor, "legal_details"),
        "billed_entity_id": (None, "billed_entity_id"),
        "metadata": (CompanyMetadataConvertor, "metadata"),
    }


class LoyaltyProgramDetailsConverter(BaseConvertor):
    convertor = {
        "program_name": (None, "program_name"),
        "program_id": (None, "program_id"),
        "membership_number": (None, "membership_number"),
        "membership_level": (None, "membership_level"),
        "current_points_balance": (None, "current_points_balance"),
        "external_url": (None, "external_url"),
        "program_start_date": (None, "program_start_date"),
        "program_end_date": (None, "program_end_date"),
    }
    object_class = LoyaltyProgramDetails
    reverse_convertor = {
        "program_name": (None, "program_name"),
        "program_id": (None, "program_id"),
        "membership_number": (None, "membership_number"),
        "membership_level": (None, "membership_level"),
        "current_points_balance": (None, "current_points_balance"),
        "external_url": (None, "external_url"),
        "program_start_date": (None, "program_start_date"),
        "program_end_date": (None, "program_end_date"),
    }


class SegmentValueConvertor(BaseConvertor):
    convertor = {
        "code": (None, "code"),
        "name": (None, "name"),
    }
    object_class = SegmentValue
    reverse_convertor = {
        "code": (None, "code"),
        "name": (None, "name"),
    }


class SegmentConvertor(BaseConvertor):
    convertor = {
        "name": (None, "name"),
        "value": (SegmentValueConvertor, "value"),
        "group_name": (None, "group_name"),
    }
    object_class = Segment
    reverse_convertor = {
        "name": (None, "name"),
        "value": (SegmentValueConvertor, "value"),
        "group_name": (None, "group_name"),
    }


class RoomRentConverter(BaseConvertor):
    convertor = {
        "posttax_amount": (MoneyConvertor, 'posttax_amount'),
        "applicable_date": (DateConvertor, 'applicable_date'),
    }
    object_class = RoomRent
    reverse_convertor = {
        "posttax_amount": (MoneyConvertor, 'posttax_amount'),
        "applicable_date": (DateConvertor, 'applicable_date'),
    }


class BillingInstructionsConverter(BaseConvertor):
    convertor = {
        "default_billed_entity_category": (None, "default_billed_entity_category"),
        "default_payment_instruction": (None, "default_payment_instruction"),
        "default_billed_entity_category_for_extras": (
            None,
            "default_billed_entity_category_for_extras",
        ),
        "default_payment_instruction_for_extras": (
            None,
            "default_payment_instruction_for_extras",
        ),
        "update_existing_charges": (None, "update_existing_charges"),
    }
    object_class = BillingInstructions


class GuaranteeInformationConverter(BaseConvertor):
    convertor = {
        "guarantee_type": (EnumConvertor(GuaranteeTypes), "guarantee_type"),
        "guarantee_details": (None, "guarantee_details"),
    }
    reverse_convertor = convertor
    object_class = GuaranteeInformation


class DiscountDetailsConverter(BaseConvertor):
    convertor = {
        "code": (None, "code"),
        "description": (None, "description"),
        "implementation": (None, "implementation"),
        "name": (None, "name"),
        "reference_id": (None, "reference_id"),
        "total_discount_value": (MoneyConvertor, "total_discount_value"),
        "type": (None, "type"),
        "debit_mode": (None, "debit_mode"),
        "debit_percentage": (None, "debit_percentage"),
        "reward_transaction_id": (None, "reward_transaction_id"),
        "reward_source_name": (None, "reward_source_name"),
    }
    reverse_convertor = convertor
    object_class = DiscountDetail


class ExpenseServiceContextConverter(BaseConvertor):
    convertor = {
        "service_type": (None, "service_type"),
        "service_details": (None, "service_details"),
    }

    reverse_convertor = convertor
    object_class = ExpenseServiceContext

    def to_dict(self, obj, **kwargs):
        if obj is None:
            return None
        return obj.to_dict()

    def from_dict(self, data_dict, **kwargs):
        if data_dict is None:
            return None
        return ExpenseServiceContext.from_dict(data_dict)
