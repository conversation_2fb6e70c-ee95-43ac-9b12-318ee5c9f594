from pos.integration_tests.utilities import excel_utils
from pos.integration_tests.utilities.common_utils import sanitize_test_data


class ReservationRequest(object):
    def __init__(self, sheet_name, test_case_id, booking_id=None):
        test_data = excel_utils.get_test_case_data(sheet_name, test_case_id)[0]
        self.data = ReservationData(test_data, booking_id).__dict__


class EditReservationRequest(object):
    def __init__(self, sheet_name, test_case_id, booking_id=None):
        test_data = excel_utils.get_test_case_data(sheet_name, test_case_id)[0]
        self.data = ReservationData(test_data, booking_id).__dict__


class ReservationData(object):
    def __init__(self, test_data, booking_id=None):
        self.allergens = sanitize_test_data(test_data['allergens'])
        self.duration = sanitize_test_data(test_data['duration'])
        self.guests = sanitize_test_data(test_data['guests'])
        self.guest_count = sanitize_test_data(test_data['guest_count'])
        self.occasion = sanitize_test_data(test_data['occasion'])
        self.special_requests = sanitize_test_data(str(test_data['special_requests']))
        self.start_datetime = sanitize_test_data(str(test_data['start_datetime']))
        self.table_id = sanitize_test_data(str(test_data['table_id']))
