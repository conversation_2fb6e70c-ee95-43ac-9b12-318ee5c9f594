import os

import pytest
from treebo_commons.multitenancy.sqlalchemy import db_engine
from treebo_commons.multitenancy.sqlalchemy.db_engine import Base

from pos.application.decorators import session_manager
from pos.integration_tests.utilities import excel_utils

dir_path = os.path.dirname(os.path.abspath(__file__))

print("Module loaded: %s" % __name__)


@pytest.fixture(scope="session", autouse=True)
def excel_data():
    excel_utils.extract_excel_data(dir_path + '/resources/PosTestData.xlsx')


@pytest.fixture(scope="session", autouse=True)
@session_manager(commit=True)
def seed_data():
    Base.metadata.drop_all(bind=db_engine.get_engine(None))
    Base.metadata.create_all(bind=db_engine.get_engine(None))
    with open(dir_path + '/resources/seed_data.sql', 'r') as s:
        db_engine.get_session(None).execute(s.read())
    print("Data seeded")


@pytest.fixture(scope="function", autouse=True)
def reporting():
    yield
    print("-------------TC ended ------------")
