import json

from pos.integration_tests.builders import (
    edit_order_builder,
    orders_builder,
    settle_order_builder,
)
from pos.integration_tests.builders.pos_v2_builders import (
    edit_order_v2_builder,
    order_v2_builder,
    settle_order_v2_builder,
)
from pos.integration_tests.config.common_config import *
from pos.integration_tests.config.request_uris import *
from pos.integration_tests.config.sheet_names import *
from pos.integration_tests.external_clients.pos_clients import PosClient
from pos.integration_tests.requests.base_request import BaseRequest
from pos.integration_tests.utilities.common_utils import del_none


class OrderRequestsV2(BaseRequest):
    def create_order_request(
        self, client, test_case_id, status_code, user_type, seller_id, booking_id
    ):
        request_json = json.dumps(
            del_none(
                order_v2_builder.CreateOrderBuilder(
                    NEW_ORDER_SHEET_NAME, test_case_id, seller_id, booking_id
                ).__dict__
            )
        )
        response = self.request_processor(
            client, 'POST', ORDER_URI, status_code, request_json, user_type
        )
        if status_code in SUCCESS_CODES:
            self.bill_id = response.json['data']['bill_id']
            self.order_id = response.json['data']['order_id']
        return response.json

    def edit_order_request(self, client, test_case_id, status_code, user_type):
        request_json = json.dumps(
            del_none(
                edit_order_v2_builder.EditOrderRequestV2(
                    EDIT_ORDER_SHEET_NAME, test_case_id
                ).__dict__
            )
        )
        uri = EDIT_ORDER_URI.format(self.order_id)
        response = self.request_processor(
            client, 'PATCH', uri, status_code, request_json, user_type
        )
        return response.json

    def settle_order_request(
        self,
        client,
        test_case_id,
        status_code,
        order_id,
        bill_id,
        user_type,
        booking_id=None,
    ):
        request_json = json.dumps(
            del_none(
                settle_order_v2_builder.SettleOrderRequestV2(
                    SETTLE_ORDER_SHEET_NAME, test_case_id, booking_id
                ).__dict__
            )
        )
        uri = SETTLE_ORDER_URI.format(order_id, bill_id)
        response = self.request_processor(
            client, 'POST', uri, status_code, request_json, user_type
        )
        if status_code in SUCCESS_CODES:
            self.invoice_id = response.json['data']['invoice_details'][0]['invoice_id']
            self.bill_id = response.json['data']['order']['bill_id']
        return response.json

    def settle_order_request_for_booking(
        self,
        client,
        test_case_id,
        status_code,
        order_id,
        bill_id,
        user_type,
        booking_id=None,
    ):
        request_json = json.dumps(
            del_none(
                settle_order_v2_builder.SettleOrderRequestV2(
                    SETTLE_ORDER_SHEET_NAME, test_case_id, booking_id
                ).__dict__
            )
        )
        uri = SETTLE_ORDER_URI.format(order_id, bill_id)
        response = self.request_processor(
            client, 'POST', uri, status_code, request_json, user_type
        )
        if status_code in SUCCESS_CODES:
            self.invoice_id = response.json['data']['invoice_details'][0]['invoice_id']
            self.bill_id = response.json['data']['order']['bill_id']
        return response.json

    def void_order_request(self, client, test_case_id, status_code, user_type):
        uri = VOID_ORDER_URI.format(self.order_id)
        response = self.request_processor(client, 'POST', uri, status_code, user_type)
        return response.json

    def new_order_for_booking_request(self, test_case_id, seller_id):
        request_json = json.dumps(
            del_none(
                orders_builder.CreateOrderRequest(
                    NEW_ORDER_SHEET_NAME, test_case_id, seller_id
                ).__dict__
            )
        )
        response = PosClient().create_order_for_booking(request_json)
        self.bill_id = response.json['data']['bill_id']
        self.order_id = response.json['data']['order_id']
        return response

    def edit_order_for_booking_request(self, test_case_id):
        request_json = json.dumps(
            del_none(
                edit_order_builder.EditOrderRequest(
                    EDIT_ORDER_SHEET_NAME, test_case_id
                ).__dict__
            )
        )
        response = PosClient().edit_order_for_booking(request_json, self.order_id)
        return response

    def settle_order_for_booking_request(self, test_case_id, booking_id):
        request_json = json.dumps(
            del_none(
                settle_order_builder.SettleOrderRequest(
                    SETTLE_ORDER_SHEET_NAME, test_case_id, booking_id
                ).__dict__
            )
        )
        response = PosClient().settle_order_for_booking(request_json, self.order_id)
        return response

    def get_orders_request(self, client_, order_id, status_code):
        url = GET_ORDERS_URI.format(order_id)
        response = self.request_processor(client_, 'GET', url, status_code)
        return response.json

    def get_order_request(self, client_, order_id, status_code):
        url = GET_ORDER_URI.format(order_id)
        response = self.request_processor(client_, 'GET', url, status_code)
        return response.json
