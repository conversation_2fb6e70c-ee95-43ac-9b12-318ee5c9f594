from object_registry import register_instance
from pos.application.decorators import session_manager
from pos.application.dtos.seller_table_dtos import EditSellerTableDto
from pos.infrastructure.database.repositories.order.order_repository import (
    OrderRepository,
)
from pos.pos_restaurant.entities.seller_table import SellerTable
from pos.pos_restaurant.repositories.seller_table_repository import (
    SellerTableRepository,
)
from ths_common.pos.constants.order_constants import SellerTableStatus
from ths_common.value_objects import NotAssigned


@register_instance(dependencies=[SellerTableRepository, OrderRepository])
class SellerTableService(object):
    def __init__(self, seller_table_repository, order_repository):
        self.seller_table_repository = seller_table_repository
        self.order_repository = order_repository

    @session_manager(commit=False)
    def get_table(self, seller_id, seller_table_id):
        return self.seller_table_repository.load(seller_id, seller_table_id)

    @session_manager(commit=False)
    def get_tables_for_seller(self, seller_id, table_ids):
        return self.seller_table_repository.get_tables(seller_id, table_ids)

    @session_manager(commit=True)
    def edit_table(self, dto_object: EditSellerTableDto, seller_id, seller_table_id):
        seller_table = self.seller_table_repository.load(seller_id, seller_table_id)

        if (
            dto_object.current_status != NotAssigned
            and seller_table.current_status != dto_object.current_status
        ):
            seller_table.update_current_status(
                SellerTableStatus(dto_object.current_status)
            )
            seller_table.set_status_updated_at()

        return self.seller_table_repository.update(seller_table)
