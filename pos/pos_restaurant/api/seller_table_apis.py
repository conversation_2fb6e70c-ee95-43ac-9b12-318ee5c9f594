from object_registry import inject
from pos.api import pos_bp
from pos.api.serializers.request.order import (
    EditSellerTableSchema,
    SellerTableGetSchema,
)
from pos.api.serializers.response.order import SellerTableResponseSchema
from pos.application.dtos.seller_table_dtos import EditSellerTableDto
from pos.core.api_docs import swag_route
from pos.pos_restaurant.application.seller_table_service import SellerTableService
from shared_kernel.api_helpers.request_parsers import (
    RequestTypes,
    schema_wrapper_parser,
)
from shared_kernel.api_response import ApiResponse


@swag_route
@pos_bp.route('/sellers/<seller_id>/tables/<table_id>', methods=['PATCH'])
@schema_wrapper_parser(EditSellerTableSchema)
@inject(seller_table_service=SellerTableService)
def edit_seller_table(seller_table_service, parsed_request, seller_id, table_id):
    """edit seller table
    ---
    operationId: edit_seller_table
    consumes:
        - application/json
    produces:
        - application/json
    schemes: ['http', 'https']
    deprecated: false
    get:
        description: edit seller table
        tags:
            - Seller Table
        parameters:
            - in: path
              name: seller_id
              description: The seller_id of the seller whose table is being updated
              required: True
              type: string
            - in: body
              name: seller_table
              description: The seller_table object properties which need to be edited
              required: True
              schema:
                type: object
                properties:
                    data:
                        $ref: "#/definitions/EditSellerTableSchema"
        responses:
            200:
                description: Reservation object.
                schema:
                    type: object
                    properties:
                        data:
                            $ref: "#/definitions/SellerTableResponseSchema"
                        meta:
                            type: object
                            additionalProperties: {}
                        errors:
                            type: array
                            items:
                                $ref: "#/definitions/ApiErrorSchema"
    """
    seller_table = seller_table_service.edit_table(parsed_request, seller_id, table_id)
    seller_table_response_schema = SellerTableResponseSchema()
    response = seller_table_response_schema.dump(seller_table)
    return ApiResponse.build(data=response.data, status_code=200)


@swag_route
@pos_bp.route('/sellers/<seller_id>/tables/<table_id>', methods=['GET'])
@inject(seller_table_service=SellerTableService)
def get_table(seller_table_service, seller_id, table_id):
    """Returns the given Seller Table
    ---
    operationId: get_seller_table
    consumes:
        - application/json
    produces:
        - application/json
    schemes: ['http', 'https']
    deprecated: false
    get:
        description: Gets Seller Table
        tags:
            - Seller Table
        parameters:
            - in: path
              name: table_id
              description: The table_id of the seller table that needs to be fetched
              required: True
              type: string
            - in: path
              name: seller_id
              description: The seller_id which will be matched against the seller_id of Seller Table
              required: True
              type: string
        responses:
            200:
                description: Seller Table object.
                schema:
                    type: object
                    properties:
                        data:
                            $ref: "#/definitions/SellerTableResponseSchema"
                        meta:
                            type: object
                            additionalProperties: {}
                        errors:
                            type: array
                            items:
                                $ref: "#/definitions/ApiErrorSchema"
    """
    seller_table = seller_table_service.get_table(seller_id, table_id)
    seller_table_response_schema = SellerTableResponseSchema()
    response = seller_table_response_schema.dump(seller_table)
    return ApiResponse.build(data=response.data, status_code=200)


@swag_route
@pos_bp.route('/sellers/<seller_id>/tables', methods=['GET'])
@schema_wrapper_parser(SellerTableGetSchema, param_type=RequestTypes.ARGS)
@inject(seller_table_service=SellerTableService)
def list_tables(seller_table_service, seller_id, parsed_request):
    """List Seller Tables
    ---
    operationId: list_seller_tables
    consumes:
        - application/json
    produces:
        - application/json
    schemes: ['http', 'https']
    deprecated: false
    get:
        description: list seller tables
        tags:
            - Seller Tables
        parameters:
            - in: url
              name: seller_id
              description: Seller id for which seller tables list to be fetched
              required: True
        responses:
            200:
                description: Seller Table array.
                schema:
                    type: array
                    properties:
                        data:
                            $ref: "#/definitions/SellerTableResponseSchema"
                        meta:
                            type: object
                            additionalProperties: {}
                        errors:
                            type: array
                            items:
                                $ref: "#/definitions/ApiErrorSchema"
    """
    table_ids = parsed_request.get("table_ids")
    restaurant_tables = seller_table_service.get_tables_for_seller(
        seller_id, table_ids=table_ids
    )
    restaurant_table_response_schema = SellerTableResponseSchema()
    response = restaurant_table_response_schema.dump(restaurant_tables, many=True)
    return ApiResponse.build(data=response.data, status_code=200)
