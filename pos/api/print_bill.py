from object_registry import inject
from pos.api import pos_bp
from pos.application.services.order_application_service import OrderApplicationService
from pos.common.request_parsers import (
    read_user_data_from_request_header_and_set_context,
)
from pos.core.api_docs import swag_route
from shared_kernel.api_response import ApiResponse


@swag_route
@pos_bp.route('/orders/<string:order_id>/bills/<string:bill_id>/print', methods=['GET'])
@inject(order_app_service=OrderApplicationService)
def print_bill(order_app_service, order_id, bill_id):
    """Print Bill
    ---
    operationId: Print Bill
    consumes:
        - application/json
    produces:
        - application/json
    schemes: ['http', 'https']
    deprecated: false
    post:
        description: Print Bill
        tags:
            - Order
        parameters:
            - in: path
              name: order_id
              description: The order_id of the pos order the bill should be printed for
              required: True
              type: string
            - in: path
              name: bill_id
              description: The bill_id of the pos order the bill should be printed for
              required: True
              type: string
        responses:
            200:
                description: Url of Bill Template
                schema:
                    type: object
                    properties:
                        data:
                            type: string
                        meta:
                            type: object
                            additionalProperties: {}
                        errors:
                            type: array
                            items:
                                $ref: "#/definitions/ApiErrorSchema"
    """
    user_data = read_user_data_from_request_header_and_set_context(
        default="backend-system"
    )
    url = order_app_service.print_bill(user_data, order_id, bill_id)
    return ApiResponse.build(data=dict(url=url.url), status_code=201)
