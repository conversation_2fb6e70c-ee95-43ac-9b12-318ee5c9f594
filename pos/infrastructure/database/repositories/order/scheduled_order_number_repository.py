from object_registry import register_instance
from pos.infrastructure.database.base_repository import BaseRepository
from pos.models import ScheduledOrderNumberSequenceModel


@register_instance()
class ScheduledOrderNumberRepository(BaseRepository):
    def from_aggregate(self, aggregate=None):
        pass

    def to_aggregate(self, **kwargs):
        pass

    def get_next_order_number(self, seller_id, scheduled_order_date):
        sequence = self.get_for_update(
            ScheduledOrderNumberSequenceModel,
            seller_id=seller_id,
            scheduled_order_date=scheduled_order_date,
        )
        if not sequence:
            order_number = 1
            new_sequence = ScheduledOrderNumberSequenceModel(
                seller_id=seller_id,
                scheduled_order_date=scheduled_order_date,
                last_scheduled_order_number=order_number,
            )
            self._save(new_sequence)
        else:
            order_number = sequence.last_scheduled_order_number + 1
            sequence.last_scheduled_order_number = order_number
            self._update(sequence)

        self.flush_session()
        return order_number
