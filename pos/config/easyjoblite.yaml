app_id: crs-job
async_timeout: 120
dead_message_log_file: /var/tmp/ths_easyjoblite_dl.log
default_dl_consumer_count: 1
default_retry_consumer_count: 1
default_worker_count: 3
eqc_sleep_duration: 5
import_paths: .
max_retries: 2
max_worker_count: 10
pid_file_path: /var/tmp/ths_easyjoblite.pid
rabbitmq_url: amqp://crs:Mb500@localhost:5672/crs
workers_log_file_path: /var/tmp/ths_easyjoblite.log
health_check_interval: 2
rmq_config:
  heartbeat_interval: 0
  prefetch_count: 10
  retry: True
  retry_policy:
    interval_start: 0
    interval_step: 2
    interval_max: 30
    max_retries: 3

