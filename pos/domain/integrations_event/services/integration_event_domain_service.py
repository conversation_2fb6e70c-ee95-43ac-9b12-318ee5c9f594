import logging

from ths_common.constants.integration_event_constants import IntegrationEventStatus

logger = logging.getLogger(__name__)


class IntegrationEventDomainService:
    def __init__(self, integration_event_publisher):
        self.integration_event_publisher = integration_event_publisher

    def publish_to_queue(self, event_aggregate):
        event_entity = event_aggregate.pos_integration_event

        try:
            self.integration_event_publisher.publish(event_entity)
            event_entity.status = IntegrationEventStatus.PUBLISHED
            return event_aggregate
        except Exception as e:
            logger.exception(
                'Error while publishing integration event with id: %s',
                event_entity.event_id,
            )
            event_entity.status = IntegrationEventStatus.FAILED
            return event_aggregate
