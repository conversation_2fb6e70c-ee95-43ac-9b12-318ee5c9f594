from pos.domain.order.errors import OrderError
from ths_common.exceptions import CRSException


class PosOrderException(CRSException):
    error = OrderError.DEFAULT_ERROR

    def __init__(self, error=None, description=None, extra_payload=None):
        if error:
            self.error_code = error.error_code
            self.message = error.message
        else:
            self.error_code = self.error.error_code
            self.message = self.error.message
        super().__init__(description=description, extra_payload=extra_payload)


class OrderInvarianceException(PosOrderException):
    def __init__(self, error, description=None, extra_payload=None):
        super().__init__(error, description=description, extra_payload=extra_payload)
