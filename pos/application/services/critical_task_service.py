from treebo_commons.utils import dateutils

from object_registry import register_instance
from pos.domain.critical_task.entities.critical_task import CriticalTask
from pos.infrastructure.database.repositories.catalog.seller_repository import (
    SellerRepository,
)
from pos.infrastructure.database.repositories.order.order_repository import (
    OrderRepository,
)
from ths_common.constants.critical_task_constants import CriticalTaskType


@register_instance(dependencies=[OrderRepository, SellerRepository])
class CriticalTaskService(object):
    def __init__(
        self, order_repository: OrderRepository, seller_repository: SellerRepository
    ):
        self.order_repository = order_repository
        self.seller_repository = seller_repository

    def search_critical_unsettled_orders(self, seller_id):
        seller_aggregate = self.seller_repository.load(seller_id)
        order_ids = self.order_repository.load_order_ids_with_critical_unsettled_orders(
            seller_id, seller_aggregate.seller.current_business_date
        )
        return order_ids

    def search_critical_tasks(self, search_query):
        critical_tasks = []
        if search_query.critical_unsettled_orders:
            entity_ids = self.search_critical_unsettled_orders(search_query.seller_id)
            critical_tasks.append(
                CriticalTask(
                    CriticalTaskType.CRITICAL_UNSETTLED_ORDER, "order", entity_ids
                )
            )

        return critical_tasks
