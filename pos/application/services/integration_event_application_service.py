import logging

import newrelic.agent
from flask.cli import with_appcontext

from object_registry import inject, register_instance
from pos.application.decorators import session_manager
from pos.application.factories.event_payload_generator import EventPayloadGenerator
from pos.application.factories.integration_event_writer_factory import (
    IntegrationEventWriterFactory,
)
from pos.common.decorators import consumer_middleware
from pos.domain.integrations_event.publisher import IntegrationEventPublisher
from pos.domain.integrations_event.repositories.integration_event_repository import (
    IntegrationEventRepository,
)
from pos.domain.integrations_event.services.integration_event_domain_service import (
    IntegrationEventDomainService,
)
from pos.infrastructure.alerting.newrelic_service_client import NewrelicServiceClient
from ths_common.constants.integration_event_constants import (
    IntegrationEventStatus,
    IntegrationEventType,
)

logger = logging.getLogger(__name__)


@inject(integration_event_writer_factory=IntegrationEventWriterFactory)
def write_event(event_dto, ignore_context=False, integration_event_writer_factory=None):
    int_evt_writer = integration_event_writer_factory.create_integration_event_writer()
    int_evt_writer.write(event_dto)
    int_evt_writer.flush()


@register_instance(
    dependencies=[
        IntegrationEventPublisher,
        IntegrationEventRepository,
        NewrelicServiceClient,
    ]
)
class IntegrationEventApplicationService(object):
    def __init__(
        self,
        integration_event_publisher,
        pos_integration_event_repository,
        alerting_service,
    ):
        self.integration_event_publisher = integration_event_publisher
        self.pos_integration_event_repository = pos_integration_event_repository
        self.alerting_service = alerting_service

    @newrelic.agent.background_task()
    @with_appcontext
    @consumer_middleware
    @session_manager(commit=True)
    def publish_oldest_unpublished_event_to_queue(self):
        event_aggregate = (
            self.pos_integration_event_repository.get_oldest_unpublished_event()
        )
        if not event_aggregate:
            return False
        pending_event_count = (
            self.pos_integration_event_repository.count_unpublished_events()
        )
        self.alerting_service.record_event(
            "pending_pos_integration_events", {"event_count": pending_event_count}
        )
        try:
            logger.info(
                'Fetched event_id: %s to be published',
                event_aggregate.pos_integration_event.event_id,
            )
            integration_event_domain_service = IntegrationEventDomainService(
                self.integration_event_publisher
            )
            event_aggregate = integration_event_domain_service.publish_to_queue(
                event_aggregate
            )
        except Exception as ex:
            self.alerting_service.record_event(
                "pos_integration_event_publish_failure",
                {"event_id": event_aggregate.pos_integration_event.event_id},
            )
            logger.exception(
                'Error while publishing integration event with id: %s',
                event_aggregate.integration_event.event_id,
            )
            raise

        self.pos_integration_event_repository.update(event_aggregate)

        return (
            event_aggregate.pos_integration_event.status
            == IntegrationEventStatus.PUBLISHED
        )

    @staticmethod
    def create_order_event(order_aggregate, user_action):
        event_dto = EventPayloadGenerator.generate_event_dto(
            event_type=IntegrationEventType.ORDER_CREATED,
            order_aggregate=order_aggregate,
        )
        event_dto.user_action = user_action
        write_event(event_dto)

    @staticmethod
    def edit_order_event(order_aggregate, user_action):
        event_dto = EventPayloadGenerator.generate_event_dto(
            event_type=IntegrationEventType.ORDER_UPDATED,
            order_aggregate=order_aggregate,
        )
        event_dto.user_action = user_action
        write_event(event_dto)

    @staticmethod
    def send_to_kitchen_event(order_aggregate, user_action):
        event_dto = EventPayloadGenerator.generate_event_dto(
            event_type=IntegrationEventType.ORDER_SENT_TO_KITCHEN,
            order_aggregate=order_aggregate,
        )
        event_dto.user_action = user_action
        write_event(event_dto)

    @staticmethod
    def settle_bill_event(order_aggregate, user_action):
        event_dto = EventPayloadGenerator.generate_event_dto(
            event_type=IntegrationEventType.ORDER_BILL_SETTLED,
            order_aggregate=order_aggregate,
        )
        event_dto.user_action = user_action
        write_event(event_dto)
