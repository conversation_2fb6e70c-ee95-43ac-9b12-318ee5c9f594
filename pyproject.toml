[tool.black]
line-length = 88
skip-string-normalization = true
safe = true
exclude = '''
/(
      .git
    | .hg
    | .mypy_cache
    | .tox
    | .venv
    | logs
    | .idea
    | migrations
    | prometheus/integration_tests
)/
'''
force-exclude='''
/(
    prometheus/integration_tests
)/
'''

[tool.isort]
profile = 'black'
multi_line_output = 3
skip = ["__init__.py"]
[tool.poetry.extras]
pipfile_deprecated_finder = ["pip-shims<=0.3.4"]