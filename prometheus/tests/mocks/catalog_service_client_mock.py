from prometheus.infrastructure.external_clients.core.base_client import (
    BaseExternalClient,
)
from ths_common.constants.catalog_constants import SellerType


class CatalogServiceClientMock(BaseExternalClient):
    def get_seller_type(self, hotel_id, date):
        def _create_seller_type_response(hotel_id, date):
            return dict(seller_type=SellerType.MARKETPLACE.value)

        return _create_seller_type_response(hotel_id, date)

    def fetch_hotel(self, hotel_id):
        def fetch_hotel_response(hotel_id):
            bank_details = {
                "account_name": "World",
                "account_number": "**********",
                "bank": "Ramesh",
                "branch": None,
                "id": 1463,
                "ifsc_code": "ABCD1234567",
                "type": "CURRENT",
            }
            property_details = {
                "bank_details": bank_details,
                "building_type": "INDEPENDENT_SINGLE",
                "construction_year": 2016,
                "external_provider": {
                    "code": "treebo",
                    "provider_hotel_code": None,
                    "provider_name": "TREEBO",
                },
                "floor_count": 5,
                "gstin": None,
                "id": 1536,
                "legal_signature": None,
                "navision_code": None,
                "neighbourhood_detail": None,
                "neighbourhood_type": "RESIDENTIAL",
                "pan": None,
                "previously_different_franchise": False,
                "property_type": "HOTEL",
                "reception_landline": "080 ********",
                "reception_mobile": "**********",
                "sold_as": "RESELLER",
                "star_rating": 3,
                "style": "MODERN",
                "style_detail": None,
            }
            location = {
                "city": {
                    "aliases": [{"id": 13, "name": "Bengaluru"}],
                    "id": 1,
                    "latitude": 12.9667,
                    "longitude": 77.5667,
                    "name": "Bangalore",
                },
                "id": 1689,
                "latitude": 12.926032,
                "legal_address": "8TH E - MAIN, NO. 302, 3RD A CROSS, HRBR 1ST BLOCK, KALYANAGAR, Bengaluru (Bangalore) Urban, Karnataka,",
                "legal_city": None,
                "legal_pincode": None,
                "legal_state": None,
                "locality": {
                    "id": 440,
                    "latitude": 12.921611,
                    "longitude": 77.666514,
                    "name": "Bellandur",
                },
                "longitude": 77.670951,
                "maps_link": "https://www.google.co.in/maps/place/Hotel+Worldtree/@12.9260333,77.6703748,19z/data=!3m1!4b1!4m5!3m4!1s0x3bae139e072f3547:0xda4216b16d33ed93!8m2!3d12.926032!4d77.670922?hl=en",
                "micro_market": {"id": 405, "name": "Bellandur"},
                "pincode": 560103,
                "postal_address": "# 90, Margosa Avenue, 50ft Road, Green Glen Layout, Bellandur",
                "state": {"code": 29, "id": 1, "name": "Karnataka"},
            }
            name = {
                "legal_name": "Hotel Worldtree",
                "new_name": "Treebo Hotel Worldtree Bellandur",
                "old_name": "Hotel Worldtree",
            }
            guest_facing_details = {
                "checkin_grace_time": 540,
                "checkin_time": "12:00:00",
                "checkout_grace_time": 360,
                "checkout_time": "11:00:00",
                "early_checkin_fee": "Full day charge",
                "free_early_checkin_time": "10:00:00",
                "free_late_checkout_time": "13:00:00",
                "id": 1389,
                "late_checkout_fee": "Full day charge",
                "switch_over_time": "06:00:00",
            }
            return dict(
                id=hotel_id,
                status="LIVE",
                property_details=property_details,
                location=location,
                name=name,
                guest_facing_details=guest_facing_details,
                signed_date="2018-02-22",
                contractual_launch_date="2018-03-15",
                launched_date="2018-03-30",
            )

        return fetch_hotel_response(hotel_id)
