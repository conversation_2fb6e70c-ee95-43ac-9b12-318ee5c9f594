from collections import defaultdict

from prometheus.domain.billing.models import InvoiceSequenceModel


class CRSInvoiceNumberGenerationStrategy(object):
    @staticmethod
    def generate_invoice_number(vendor_id, prefix, sequence_number):
        invoice_number = "{vendor_id_7_digit}-{serial_number_8_digit}".format(
            vendor_id_7_digit=str(vendor_id).rjust(7, '0'),
            serial_number_8_digit=str(sequence_number).rjust(8, '0'),
        )
        return invoice_number


sequence_strategy_map = dict(crs_strategy=CRSInvoiceNumberGenerationStrategy)


class InvoiceSeriesRepositoryMock(object):
    DEFAULT_PREFIX = "INV"
    DEFAULT_SEQUENCE_STRATEGY = "crs_strategy"

    def __init__(self):
        self.invoice_map = dict()
        self.vendor_invoice_no_map = defaultdict(lambda: 0)

    def get_next_invoice_number(self, issued_by_type, vendor_id, gstin):
        sequence = self.invoice_map.get((issued_by_type.value, vendor_id, gstin))
        if not sequence:
            sequence_number = 1
            new_sequence = InvoiceSequenceModel(
                issued_by_type=issued_by_type.value,
                vendor_id=vendor_id,
                gstin=gstin,
                prefix=self.DEFAULT_PREFIX,
                last_sequence_number=sequence_number,
                sequence_generation_strategy=self.DEFAULT_SEQUENCE_STRATEGY,
            )
            current_sequence = self.save(new_sequence)
        else:
            sequence_number = sequence.last_sequence_number + 1
            sequence.last_sequence_number = sequence_number
            self.save(sequence)
            current_sequence = sequence

        sequence_strategy_cls = sequence_strategy_map.get(
            current_sequence.sequence_generation_strategy
        )
        invoice_number = sequence_strategy_cls.generate_invoice_number(
            vendor_id, current_sequence.prefix, sequence_number
        )

        return invoice_number

    def save(self, sequence_model):
        self.invoice_map[
            (
                sequence_model.issued_by_type,
                sequence_model.vendor_id,
                sequence_model.gstin,
            )
        ] = sequence_model
        return sequence_model
