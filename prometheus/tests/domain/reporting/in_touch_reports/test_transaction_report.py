import pytest

from prometheus.domain.billing.entities.folio import Folio
from prometheus.reporting.in_touch_reports.transaction_report.transaction_report_generator import (
    InTouchTransactionReportGenerator,
)
from ths_common.constants.reporting_constants import TransactionTypes


@pytest.fixture()
def folios(bill_aggregate):
    return [Folio(bill_aggregate.bill_id, 1, 2, 1)]


def test_transaction_report(
    valid_booking_aggregate_with_one_room_stay,
    active_hotel_aggregate,
    bill_aggregate,
    folios,
):
    booking_aggregate = valid_booking_aggregate_with_one_room_stay
    hotel_aggregate = active_hotel_aggregate
    bill_aggregate = bill_aggregate
    folios_for_bill = folios
    transaction_reports = InTouchTransactionReportGenerator(
        booking_aggregate, bill_aggregate, hotel_aggregate, folios_for_bill
    ).generate()

    charge_type_transaction_report = [
        tr
        for tr in transaction_reports
        if tr.transaction_type == TransactionTypes.CHARGE
    ]
    charge_id_to_charge_map = {ch.charge_id: ch for ch in bill_aggregate.charges}
    for report in charge_type_transaction_report:
        charge = charge_id_to_charge_map.get(report.transaction_reference)
        assert report.hotel_code == hotel_aggregate.hotel_id
        assert (
            report.pms_transaction_id == f"{bill_aggregate.bill_id}_{charge.charge_id}"
        )
        assert report.pms_visit_id == booking_aggregate.booking.booking_id
        assert report.hotel_code == hotel_aggregate.hotel_id
        assert report.revenue == charge.charge_splits[0].pre_tax.amount
        assert report.tax == charge.charge_splits[0].tax.amount

        is_rate_plan_charge = (
            True if charge.item.details.get('rate_plan_code') else False
        )
        assert report.package_flag == is_rate_plan_charge
