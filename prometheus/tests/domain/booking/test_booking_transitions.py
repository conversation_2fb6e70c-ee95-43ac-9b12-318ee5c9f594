from ths_common.constants.booking_constants import BookingStatus


def test_booking_part_checkin_transition(valid_booking_aggregate_with_one_room_stay):
    if not valid_booking_aggregate_with_one_room_stay.is_future_booking():
        valid_booking_aggregate_with_one_room_stay.part_checkin()
        assert (
            valid_booking_aggregate_with_one_room_stay.booking.status
            == BookingStatus.PART_CHECKIN
        )
