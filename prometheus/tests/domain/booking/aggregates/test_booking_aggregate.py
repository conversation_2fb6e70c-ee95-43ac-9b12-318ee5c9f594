import pytest
from treebo_commons.money import Money
from treebo_commons.money.constants import CurrencyType
from treebo_commons.utils import dateutils

from prometheus.domain.billing.entities.charge import Charge
from prometheus.domain.booking.dtos.expense_dto import ExpenseDto
from prometheus.domain.booking.dtos.guest_checkin_data import GuestCheckinData
from prometheus.domain.booking.dtos.guest_checkout_data import (
    GuestCheckoutRequestData,
    RoomCheckoutRequestData,
)
from prometheus.domain.booking.dtos.guest_stay_data import GuestStayData
from prometheus.domain.booking.dtos.room_allocation_data import RoomAllocationData
from prometheus.domain.booking.dtos.room_stay_data import RoomStayData
from prometheus.domain.booking.entities.customer import Customer
from prometheus.domain.booking.entities.guest_stay import GuestStay
from prometheus.domain.booking.entities.room_allocation import RoomAllocation
from prometheus.domain.booking.entities.room_stay import RoomStay
from prometheus.domain.booking.exceptions import (
    BookingInvarianceCheckError,
    CustomerNotFound,
    InvalidActionError,
    InvalidExpenseDate,
    InvalidStayDatesError,
    PriceError,
    RoomStayAdditionError,
)
from prometheus.domain.catalog.entities.room import Room
from prometheus.tests.factories.aggregate_factories import BookingAggregateFactory
from prometheus.tests.factories.entity_factories import GuestAllocationFactory
from prometheus.tests.test_utils import items_equal, today, today_plus_days, tomorrow
from ths_common.constants.billing_constants import (
    ChargeBillToTypes,
    ChargeStatus,
    ChargeTypes,
)
from ths_common.constants.booking_constants import (
    AgeGroup,
    BookingStatus,
    Genders,
    RoomStayType,
)
from ths_common.constants.catalog_constants import RoomStatus
from ths_common.exceptions import OutdatedVersion, ResourceNotFound
from ths_common.utils.dateutils import get_settlement_date
from ths_common.value_objects import (
    BookingSideEffect,
    GuestAllocationSideEffect,
    GuestStaySideEffect,
    RoomStaySideEffect,
)


@pytest.fixture
def settlement_date():
    return get_settlement_date(today(), next_month=True)


@pytest.fixture
def checked_in_booking(request, active_hotel_aggregate):
    from prometheus import crs_context

    crs_context.set_hotel_context(active_hotel_aggregate)
    checked_in_booking = BookingAggregateFactory(
        booking__status=BookingStatus.CHECKED_IN, with_2_guest_stays=True
    )
    cust1_allocation = GuestAllocationFactory(
        checkin_date=checked_in_booking.booking.checkin_date,
        checkout_date=checked_in_booking.booking.checkout_date,
        guest_id='123',
    )
    guest_stay = checked_in_booking.room_stays[0].guest_stays[0]
    guest_stay.checkin()
    guest_stay.guest_allocation = cust1_allocation

    def clear_threadlocal_context():
        crs_context.clear()

    request.addfinalizer(clear_threadlocal_context)
    return checked_in_booking


@pytest.fixture
def charge_id_map_for_five_days():
    charge_id_map = {
        dateutils.date_to_ymd_str(dateutils.to_date(today())): 1,
        dateutils.date_to_ymd_str(dateutils.to_date(today_plus_days(1))): 2,
        dateutils.date_to_ymd_str(dateutils.to_date(today_plus_days(2))): 4,
        dateutils.date_to_ymd_str(dateutils.to_date(today_plus_days(3))): 5,
        dateutils.date_to_ymd_str(dateutils.to_date(today_plus_days(4))): 6,
    }
    return charge_id_map


def test_get_room_stay_charges_should_return_all_charges(
    valid_booking_aggregate_with_one_room_stay, charge_id_map_for_five_days
):
    valid_booking_aggregate_with_one_room_stay.room_stays[
        0
    ].charge_id_map = charge_id_map_for_five_days
    actual_charge_ids = (
        valid_booking_aggregate_with_one_room_stay.get_room_stay_charges([1])
    )
    expected_charge_ids = [1, 2, 4, 5, 6]
    assert items_equal(actual_charge_ids, expected_charge_ids)


def test_add_guest_stay_assign_guest_stay_id_equal_one_for_first_guest_stay(
    valid_booking_aggregate_with_one_room_stay,
):
    room_stays = valid_booking_aggregate_with_one_room_stay.room_stays
    room_stay = room_stays[0]
    room_stay.guest_stays = []
    guest_stay_data = GuestStayData(
        status=BookingStatus.RESERVED,
        age_group=AgeGroup.ADULT,
        checkin_date=today(),
        checkout_date=today_plus_days(3),
    )
    valid_booking_aggregate_with_one_room_stay.add_guest_stay(
        room_stay.room_stay_id, guest_stay_data
    )
    guest_stays = valid_booking_aggregate_with_one_room_stay.room_stays[0].guest_stays
    assert len(guest_stays) == 1
    assert guest_stays[0].guest_stay_id == 1


def test_add_guest_stay_assign_next_guest_stay_id_as_max_guest_id_plus_one(
    valid_booking_aggregate_with_one_room_stay,
):
    room_stays = valid_booking_aggregate_with_one_room_stay.room_stays
    room_stay = room_stays[0]
    room_stay.guest_stays = [
        GuestStay(
            1, BookingStatus.RESERVED, AgeGroup.ADULT, today(), today_plus_days(3)
        )
    ]
    guest_stay_data = GuestStayData(
        status=BookingStatus.RESERVED,
        age_group=AgeGroup.ADULT,
        checkin_date=today(),
        checkout_date=today_plus_days(3),
    )
    valid_booking_aggregate_with_one_room_stay.add_guest_stay(
        room_stay.room_stay_id, guest_stay_data
    )
    guest_stays = valid_booking_aggregate_with_one_room_stay.room_stays[0].guest_stays
    assert len(guest_stays) == 2
    assert max(g.guest_stay_id for g in guest_stays) == 2


def test_add_guest_stay_update_charges_in_room_stay(
    valid_booking_aggregate_with_one_room_stay,
):
    room_stays = valid_booking_aggregate_with_one_room_stay.room_stays
    room_stay = room_stays[0]
    room_stay.charge_id_map = dict()
    guest_stay_data = GuestStayData(
        status=BookingStatus.RESERVED,
        age_group=AgeGroup.ADULT,
        checkin_date=today(),
        checkout_date=today_plus_days(3),
    )

    applicable_date = today()
    charges = [
        Charge(
            charge_id=4,
            pretax_amount=Money(100, CurrencyType('INR')),
            tax_amount=Money(10, CurrencyType('INR')),
            tax_details=None,
            posttax_amount=Money(110, CurrencyType('INR')),
            charge_type=ChargeTypes.CREDIT,
            bill_to_type=ChargeBillToTypes.COMPANY,
            status=ChargeStatus.CREATED,
            recorded_time=None,
            applicable_date=applicable_date,
            comment=None,
            created_by=None,
            charge_item=None,
            charge_splits=None,
        )
    ]
    valid_booking_aggregate_with_one_room_stay.add_guest_stay(
        room_stay.room_stay_id, guest_stay_data, charges=charges
    )
    charge_id_map = room_stay.charge_id_map
    assert charge_id_map.get(dateutils.date_to_ymd_str(applicable_date)) == 4


def test_cancel_guest_stay_should_mark_guest_stay_status_as_cancelled(
    valid_booking_aggregate_with_one_room_stay,
):
    room_stays = valid_booking_aggregate_with_one_room_stay.room_stays
    room_stay = room_stays[0]
    room_stay.guest_stays = [
        GuestStay(
            1,
            BookingStatus.RESERVED,
            AgeGroup.ADULT,
            today_plus_days(1),
            today_plus_days(3),
        ),
        GuestStay(
            2,
            BookingStatus.RESERVED,
            AgeGroup.ADULT,
            today_plus_days(1),
            today_plus_days(3),
        ),
    ]
    valid_booking_aggregate_with_one_room_stay._cancel_guest_stay(1, 1)
    guest_stay = valid_booking_aggregate_with_one_room_stay.room_stays[0].guest_stays[0]
    assert guest_stay.status == BookingStatus.CANCELLED


def test_cancel_last_guest_stay_throws_error(
    valid_booking_aggregate_with_one_room_stay,
):
    room_stays = valid_booking_aggregate_with_one_room_stay.room_stays
    room_stay = room_stays[0]
    room_stay.guest_stays = [
        GuestStay(
            1, BookingStatus.RESERVED, AgeGroup.ADULT, today(), today_plus_days(3)
        )
    ]
    with pytest.raises(InvalidActionError):
        valid_booking_aggregate_with_one_room_stay._cancel_guest_stay(1, 1)


def test_cancel_last_adult_guest_stay_throws_error(
    valid_booking_aggregate_with_one_room_stay,
):
    room_stays = valid_booking_aggregate_with_one_room_stay.room_stays
    room_stay = room_stays[0]
    room_stay.guest_stays = [
        GuestStay(
            1, BookingStatus.RESERVED, AgeGroup.ADULT, today(), today_plus_days(3)
        ),
        GuestStay(
            2, BookingStatus.RESERVED, AgeGroup.INFANT, today(), today_plus_days(3)
        ),
    ]
    with pytest.raises(InvalidActionError):
        valid_booking_aggregate_with_one_room_stay._cancel_guest_stay(1, 1)


def test_cancel_room_stay_for_last_room_should_raise_error(
    valid_booking_aggregate_with_one_room_stay,
):
    room_stay_id = 1
    with pytest.raises(InvalidActionError):
        valid_booking_aggregate_with_one_room_stay._cancel_room_stay(room_stay_id)


def test_cancel_room_stay_raise_error_if_room_stay_is_checked_in(
    valid_booking_aggregate_with_one_room_stay,
):
    room_stay = RoomStay(
        2,
        1,
        RoomStayType.NIGHT,
        BookingStatus.RESERVED,
        today(),
        today_plus_days(5),
        [],
        None,
        None,
        None,
        [],
        [],
    )
    valid_booking_aggregate_with_one_room_stay._room_stays.append(room_stay)

    room_stay_id = 1
    room_stay = valid_booking_aggregate_with_one_room_stay.get_room_stay(room_stay_id)

    checkin_success = room_stay.checkin()
    if checkin_success:
        with pytest.raises(InvalidActionError):
            valid_booking_aggregate_with_one_room_stay._cancel_room_stay(room_stay_id)

        room_stay.status = BookingStatus.PART_CHECKIN
        with pytest.raises(InvalidActionError):
            valid_booking_aggregate_with_one_room_stay._cancel_room_stay(room_stay_id)


def test_cancel_room_stay_should_mark_room_stay_as_cancelled(
    valid_booking_aggregate_with_one_room_stay,
):
    checkin_date = today()
    checkout_date = today_plus_days(3)
    guest_stays = [
        GuestStayData(
            status=BookingStatus.RESERVED,
            age_group=AgeGroup.ADULT,
            checkin_date=checkin_date,
            checkout_date=checkout_date,
        ),
        GuestStayData(
            status=BookingStatus.RESERVED,
            age_group=AgeGroup.CHILD,
            checkin_date=checkin_date,
            checkout_date=checkout_date,
        ),
    ]
    new_room_stay = RoomStayData(
        room_type_id=1,
        type=RoomStayType.NIGHT,
        checkin_date=checkin_date,
        checkout_date=checkout_date,
        charges=None,
        guest_stays=guest_stays,
    )
    valid_booking_aggregate_with_one_room_stay.add_room_stay(new_room_stay)
    room_stay_id = 2
    room_stay, _ = valid_booking_aggregate_with_one_room_stay._cancel_room_stay(
        room_stay_id
    )
    assert room_stay.is_cancelled()


def test_cancel_room_stay_should_refresh_booking_checkin_checkout_dates(
    valid_booking_aggregate_with_one_room_stay,
):
    # NOTE: Setting checkin date after today, because cancel operation is not allowed on same date
    valid_booking_aggregate_with_one_room_stay.room_stays[
        0
    ].checkin_date = today_plus_days(1)
    valid_booking_aggregate_with_one_room_stay.room_stays[0].guest_stays[
        0
    ].checkin_date = today_plus_days(1)

    checkin_date = today()
    checkout_date = today_plus_days(3)
    guest_stays = [
        GuestStayData(
            status=BookingStatus.RESERVED,
            age_group=AgeGroup.ADULT,
            checkin_date=checkin_date,
            checkout_date=checkout_date,
        ),
        GuestStayData(
            status=BookingStatus.RESERVED,
            age_group=AgeGroup.CHILD,
            checkin_date=checkin_date,
            checkout_date=checkout_date,
        ),
    ]
    new_room_stay = RoomStayData(
        room_type_id=1,
        type=RoomStayType.NIGHT,
        checkin_date=checkin_date,
        checkout_date=checkout_date,
        charges=None,
        guest_stays=guest_stays,
    )
    valid_booking_aggregate_with_one_room_stay.add_room_stay(new_room_stay)
    room_stay_id = 1
    valid_booking_aggregate_with_one_room_stay._cancel_room_stay(room_stay_id)

    expected_checkin_date = new_room_stay.checkin_date
    expected_checkout_date = new_room_stay.checkout_date
    assert (
        valid_booking_aggregate_with_one_room_stay.booking.checkin_date
        == expected_checkin_date
    )
    assert (
        valid_booking_aggregate_with_one_room_stay.booking.checkout_date
        == expected_checkout_date
    )


def test_update_charges_merge_charge_id_map_on_room_stay(
    valid_booking_aggregate_with_one_room_stay, charge_id_map_for_five_days
):
    valid_booking_aggregate_with_one_room_stay.room_stays[
        0
    ].charge_id_map = charge_id_map_for_five_days

    charges = [
        Charge(
            charge_id=8,
            pretax_amount=Money(100, CurrencyType('INR')),
            tax_amount=Money(10, CurrencyType('INR')),
            tax_details=None,
            posttax_amount=Money(110, CurrencyType('INR')),
            charge_type=ChargeTypes.CREDIT,
            bill_to_type=ChargeBillToTypes.COMPANY,
            status=ChargeStatus.CREATED,
            recorded_time=None,
            applicable_date=today_plus_days(3),
            comment=None,
            created_by=None,
            charge_item=None,
            charge_splits=None,
        )
    ]
    valid_booking_aggregate_with_one_room_stay.update_room_rents(1, charges)
    actual_charge_ids = (
        valid_booking_aggregate_with_one_room_stay.get_room_stay_charges([1])
    )
    expected_charge_ids = [1, 2, 4, 8, 6]
    assert items_equal(actual_charge_ids, expected_charge_ids)


def test_update_charges_should_raise_PriceOutOfStayDatesError_when_charge_date_is_outside_of_room_stay_dates(
    valid_booking_aggregate_with_one_room_stay, charge_id_map_for_five_days
):
    valid_booking_aggregate_with_one_room_stay.room_stays[
        0
    ].charge_id_map = charge_id_map_for_five_days
    charges = [
        Charge(
            charge_id=7,
            pretax_amount=Money(100, CurrencyType('INR')),
            tax_amount=Money(10, CurrencyType('INR')),
            tax_details=None,
            posttax_amount=Money(110, CurrencyType('INR')),
            charge_type=ChargeTypes.CREDIT,
            bill_to_type=ChargeBillToTypes.COMPANY,
            status=ChargeStatus.CREATED,
            recorded_time=None,
            applicable_date=today_plus_days(7),
            comment=None,
            created_by=None,
            charge_item=None,
            charge_splits=None,
        )
    ]
    with pytest.raises(PriceError):
        valid_booking_aggregate_with_one_room_stay.update_room_rents(1, charges)


def test_add_room_stays_reassign_next_room_stay_id_starting_max_room_stay_id_plus_one(
    valid_booking_aggregate_with_one_room_stay,
):
    checkin_date = today()
    checkout_date = today_plus_days(5)
    room_stays = [
        RoomStayData(
            room_type_id=1,
            type=RoomStayType.NIGHT,
            checkin_date=checkin_date,
            checkout_date=checkout_date,
            guest_stays=[
                GuestStayData(
                    status=BookingStatus.RESERVED,
                    age_group=AgeGroup.ADULT,
                    checkin_date=checkin_date,
                    checkout_date=checkout_date,
                )
            ],
            charges=None,
        ),
        RoomStayData(
            room_type_id=1,
            type=RoomStayType.NIGHT,
            checkin_date=checkin_date,
            checkout_date=checkout_date,
            guest_stays=[
                GuestStayData(
                    status=BookingStatus.RESERVED,
                    age_group=AgeGroup.ADULT,
                    checkin_date=checkin_date,
                    checkout_date=checkout_date,
                )
            ],
            charges=None,
        ),
    ]
    valid_booking_aggregate_with_one_room_stay.add_room_stays(room_stays)
    room_stays = valid_booking_aggregate_with_one_room_stay.room_stays
    assert len(room_stays) == 3
    assert max(r.room_stay_id for r in room_stays) == 3


def test_allocate_room_moves_current_room_allocation_to_history(
    valid_booking_aggregate_with_one_room_stay,
):
    room_stay_id = 1
    room_type_id = 1
    room_id = 2
    room_allocation = RoomAllocation(
        1, room_stay_id, room_type_id, room_id, None, today(), None, room_no="101"
    )
    valid_booking_aggregate_with_one_room_stay.room_stays[
        0
    ].room_allocation = room_allocation

    room = Room(
        room_id=3,
        hotel_id='1',
        room_type_id=1,
        room_number="102",
        status=RoomStatus.ACTIVE,
        deleted=False,
    )
    valid_booking_aggregate_with_one_room_stay.allocate_room(
        room_stay_id, room, room.room_id, today_plus_days(3)
    )
    room_allocation_history = valid_booking_aggregate_with_one_room_stay.room_stays[
        0
    ].room_allocation_history

    assert room_allocation in room_allocation_history


def test_allocate_room_assigns_new_allocation_with_next_id(
    valid_booking_aggregate_with_one_room_stay,
):
    room_allocation_id = 1
    room_stay_id = 1
    room_type_id = 1
    room_id = 2
    room_allocation = RoomAllocation(
        room_allocation_id,
        room_stay_id,
        room_type_id,
        room_id,
        None,
        today(),
        None,
        room_no="101",
    )
    valid_booking_aggregate_with_one_room_stay.room_stays[
        0
    ].room_allocation = room_allocation

    room = Room(
        room_id=3,
        hotel_id='1',
        room_type_id=1,
        room_number="102",
        status=RoomStatus.ACTIVE,
        deleted=False,
    )
    valid_booking_aggregate_with_one_room_stay.allocate_room(
        room_stay_id, room, room.room_id, today_plus_days(3)
    )

    new_room_allocation = valid_booking_aggregate_with_one_room_stay.room_stays[
        0
    ].room_allocation

    expected_room_allocation_id = 2
    assert new_room_allocation.room_id == room.room_id
    assert new_room_allocation.room_allocation_id == expected_room_allocation_id


def test_add_expense(checked_in_booking, some_expense, settlement_date):
    assert checked_in_booking.expenses == []
    some_expense['guests'] = [checked_in_booking.booking.owner_id]
    # TODO (rohit): Breaks at midnight
    # checked_in_booking.add_expense(ExpenseDto.from_dict(some_expense), settlement_date)
    # assert len(checked_in_booking.expenses) == 1


def test_add_room_stays_should_fail_for_0_ADULT_guest_stay_in_any_room_stay(
    valid_booking_aggregate_with_one_room_stay,
):
    guest_stays = [
        GuestStayData(
            status=BookingStatus.RESERVED,
            age_group=AgeGroup.INFANT,
            checkin_date=today(),
            checkout_date=today_plus_days(3),
        ),
        GuestStayData(
            status=BookingStatus.RESERVED,
            age_group=AgeGroup.CHILD,
            checkin_date=today(),
            checkout_date=today_plus_days(3),
        ),
    ]
    new_room_stay = RoomStayData(
        room_type_id=1,
        type=RoomStayType.NIGHT,
        checkin_date=min(gs.checkin_date for gs in guest_stays),
        checkout_date=max(gs.checkout_date for gs in guest_stays),
        guest_stays=guest_stays,
        charges=None,
    )

    with pytest.raises(RoomStayAdditionError):
        valid_booking_aggregate_with_one_room_stay.add_room_stays([new_room_stay])


def test_given_only_2_rooms_in_booking_check_invariance_should_not_raise_HollowBookingStayDatesCreationError_if_checkin_of_second_room_is_same_as_checkout_of_first_room(
    valid_booking_aggregate_with_one_room_stay,
):
    guest_stays = [
        GuestStayData(
            status=BookingStatus.RESERVED,
            age_group=AgeGroup.ADULT,
            checkin_date=today_plus_days(5),
            checkout_date=today_plus_days(7),
        ),
    ]
    new_room_stay = RoomStayData(
        room_type_id=1,
        type=RoomStayType.NIGHT,
        checkin_date=min(gs.checkin_date for gs in guest_stays),
        checkout_date=max(gs.checkout_date for gs in guest_stays),
        guest_stays=guest_stays,
        charges=None,
    )
    valid_booking_aggregate_with_one_room_stay.add_room_stays([new_room_stay])

    valid_booking_aggregate_with_one_room_stay.check_invariance()


def test_given_only_2_guests_in_room_stay_check_invariance_should_raise_BookingInvarianceCheck_if_checkin_of_second_guest_is_greater_than_checkout_of_first_guest(
    valid_booking_aggregate_with_one_room_stay,
):
    guest_stays = [
        GuestStayData(
            status=BookingStatus.RESERVED,
            age_group=AgeGroup.ADULT,
            checkin_date=today_plus_days(1),
            checkout_date=today_plus_days(3),
        ),
        GuestStayData(
            status=BookingStatus.RESERVED,
            age_group=AgeGroup.ADULT,
            checkin_date=today_plus_days(5),
            checkout_date=today_plus_days(7),
        ),
    ]
    new_room_stay = RoomStayData(
        room_type_id=1,
        type=RoomStayType.NIGHT,
        checkin_date=min(gs.checkin_date for gs in guest_stays),
        checkout_date=max(gs.checkout_date for gs in guest_stays),
        guest_stays=guest_stays,
        charges=None,
    )
    valid_booking_aggregate_with_one_room_stay.add_room_stay(new_room_stay)
    with pytest.raises(BookingInvarianceCheckError):
        valid_booking_aggregate_with_one_room_stay.check_invariance()


def test_given_only_2_guests_in_room_stay_check_invariance_should_not_raise_HollowRoomStayDatesError_if_checkin_of_second_guest_is_same_as_checkout_of_first_guest(
    valid_booking_aggregate_with_one_room_stay,
):
    guest_stays = [
        GuestStayData(
            status=BookingStatus.RESERVED,
            age_group=AgeGroup.ADULT,
            checkin_date=today_plus_days(1),
            checkout_date=today_plus_days(5),
        ),
        GuestStayData(
            status=BookingStatus.RESERVED,
            age_group=AgeGroup.ADULT,
            checkin_date=today_plus_days(5),
            checkout_date=today_plus_days(7),
        ),
    ]
    new_room_stay = RoomStayData(
        room_type_id=1,
        type=RoomStayType.NIGHT,
        checkin_date=min(gs.checkin_date for gs in guest_stays),
        checkout_date=max(gs.checkout_date for gs in guest_stays),
        guest_stays=guest_stays,
        charges=None,
    )
    valid_booking_aggregate_with_one_room_stay.add_room_stay(new_room_stay)
    valid_booking_aggregate_with_one_room_stay.check_invariance()


def test_given_2_guests_in_room_stay_add_room_stay_InvalidStayDatesError_if_any_guest_stay_dates_is_beyond_room_stay_dates(
    valid_booking_aggregate_with_one_room_stay,
):
    guest_stays = [
        GuestStayData(
            status=BookingStatus.RESERVED,
            age_group=AgeGroup.ADULT,
            checkin_date=today_plus_days(1),
            checkout_date=today_plus_days(5),
        ),
        GuestStayData(
            status=BookingStatus.RESERVED,
            age_group=AgeGroup.ADULT,
            checkin_date=today_plus_days(5),
            checkout_date=today_plus_days(8),
        ),
    ]

    new_room_stay = RoomStayData(
        room_type_id=1,
        type=RoomStayType.NIGHT,
        checkin_date=min(gs.checkin_date for gs in guest_stays),
        checkout_date=today_plus_days(7),
        guest_stays=guest_stays,
        charges=None,
    )
    with pytest.raises(InvalidStayDatesError):
        valid_booking_aggregate_with_one_room_stay.add_room_stay(new_room_stay)


def test_given_2_guests_in_room_stay_check_invariance_should_not_raise_GuestStayBeyondRoomStayError_if_guest_stay_dates_are_within_room_stay_dates(
    valid_booking_aggregate_with_one_room_stay,
):
    guest_stays = [
        GuestStayData(
            status=BookingStatus.RESERVED,
            age_group=AgeGroup.ADULT,
            checkin_date=today_plus_days(1),
            checkout_date=today_plus_days(5),
        ),
        GuestStayData(
            status=BookingStatus.RESERVED,
            age_group=AgeGroup.ADULT,
            checkin_date=today_plus_days(4),
            checkout_date=today_plus_days(7),
        ),
    ]

    new_room_stay = RoomStayData(
        room_type_id=1,
        type=RoomStayType.NIGHT,
        checkin_date=min(gs.checkin_date for gs in guest_stays),
        checkout_date=max(gs.checkout_date for gs in guest_stays),
        guest_stays=guest_stays,
        charges=None,
    )
    valid_booking_aggregate_with_one_room_stay.add_room_stay(new_room_stay)
    valid_booking_aggregate_with_one_room_stay.check_invariance()


def test_get_room_stay_should_raise_ResourceNotFound_if_room_stay_id_is_not_found(
    valid_booking_aggregate_with_one_room_stay,
):
    with pytest.raises(ResourceNotFound):
        valid_booking_aggregate_with_one_room_stay.get_room_stay(10)


def test_fail_if_outdated_version_should_raise_OutdatedVersion_error_if_old_version(
    valid_booking_aggregate_with_one_room_stay,
):
    current_version = valid_booking_aggregate_with_one_room_stay.current_version()
    with pytest.raises(OutdatedVersion):
        valid_booking_aggregate_with_one_room_stay.fail_if_outdated_version(
            current_version + 1
        )


def test_get_room_stays_method_doesnot_give_deleted_room_stay(
    valid_booking_aggregate_with_one_room_stay,
):
    # Mark all room stay as deleted first
    for room_stay in valid_booking_aggregate_with_one_room_stay.room_stays:
        room_stay.delete()

    guest_stays = [
        GuestStayData(
            status=BookingStatus.RESERVED,
            age_group=AgeGroup.ADULT,
            checkin_date=today_plus_days(1),
            checkout_date=today_plus_days(5),
        ),
        GuestStayData(
            status=BookingStatus.RESERVED,
            age_group=AgeGroup.ADULT,
            checkin_date=today_plus_days(5),
            checkout_date=today_plus_days(8),
        ),
    ]

    added_room_stays = []
    # Add a new room stay
    new_room_stay = RoomStayData(
        room_type_id=1,
        type=RoomStayType.NIGHT,
        checkin_date=min(gs.checkin_date for gs in guest_stays),
        checkout_date=max(gs.checkout_date for gs in guest_stays),
        guest_stays=guest_stays,
        charges=None,
    )
    added_room_stays.append(
        valid_booking_aggregate_with_one_room_stay.add_room_stay(new_room_stay)
    )

    # Add a new room stay in REVERSED state
    new_room_stay = RoomStayData(
        room_type_id=1,
        type=RoomStayType.NIGHT,
        checkin_date=min(gs.checkin_date for gs in guest_stays),
        checkout_date=max(gs.checkout_date for gs in guest_stays),
        guest_stays=guest_stays,
        charges=None,
    )
    added_room_stays.append(
        valid_booking_aggregate_with_one_room_stay.add_room_stay(new_room_stay)
    )
    # Cancel second room stay
    added_room_stays[1].mark_cancel()
    actual_room_stays = valid_booking_aggregate_with_one_room_stay.room_stays
    expected_room_stays = added_room_stays
    assert items_equal(
        [rs.room_stay_id for rs in expected_room_stays],
        [rs.room_stay_id for rs in actual_room_stays],
    )


def test_get_active_room_stays_method_doesnot_give_deleted_or_cancelled_room_stay(
    valid_booking_aggregate_with_one_room_stay,
):
    # Mark all room stay as deleted first
    for room_stay in valid_booking_aggregate_with_one_room_stay.room_stays:
        room_stay.delete()

    guest_stays = [
        GuestStayData(
            status=BookingStatus.RESERVED,
            age_group=AgeGroup.ADULT,
            checkin_date=today_plus_days(1),
            checkout_date=today_plus_days(5),
        ),
        GuestStayData(
            status=BookingStatus.RESERVED,
            age_group=AgeGroup.ADULT,
            checkin_date=today_plus_days(5),
            checkout_date=today_plus_days(8),
        ),
    ]

    added_room_stays = []
    # Add a new room stay
    new_room_stay = RoomStayData(
        room_type_id=1,
        type=RoomStayType.NIGHT,
        checkin_date=min(gs.checkin_date for gs in guest_stays),
        checkout_date=max(gs.checkout_date for gs in guest_stays),
        guest_stays=guest_stays,
        charges=None,
    )
    added_room_stays.append(
        valid_booking_aggregate_with_one_room_stay.add_room_stay(new_room_stay)
    )

    # Add a new room stay in REVERSED state
    new_room_stay = RoomStayData(
        room_type_id=1,
        type=RoomStayType.NIGHT,
        checkin_date=min(gs.checkin_date for gs in guest_stays),
        checkout_date=max(gs.checkout_date for gs in guest_stays),
        guest_stays=guest_stays,
        charges=None,
    )
    added_room_stays.append(
        valid_booking_aggregate_with_one_room_stay.add_room_stay(new_room_stay)
    )

    added_room_stays[1].mark_cancel()
    actual_room_stays = (
        valid_booking_aggregate_with_one_room_stay.get_active_room_stays()
    )
    expected_room_stays = [added_room_stays[0]]
    assert items_equal(expected_room_stays, actual_room_stays)


def test_booking_cancellation_cancels_all_active_room_stays(
    valid_booking_aggregate_with_one_room_stay,
):
    # NOTE: Setting checkin date after today, because cancel operation is not allowed on same date
    valid_booking_aggregate_with_one_room_stay.room_stays[
        0
    ].checkin_date = today_plus_days(1)
    valid_booking_aggregate_with_one_room_stay.room_stays[0].guest_stays[
        0
    ].checkin_date = today_plus_days(1)
    guest_stays = [
        GuestStayData(
            status=BookingStatus.RESERVED,
            age_group=AgeGroup.ADULT,
            checkin_date=today_plus_days(1),
            checkout_date=today_plus_days(5),
        ),
        GuestStayData(
            status=BookingStatus.RESERVED,
            age_group=AgeGroup.ADULT,
            checkin_date=today_plus_days(5),
            checkout_date=today_plus_days(8),
        ),
    ]

    # Add a new room stay
    new_room_stay = RoomStayData(
        room_type_id=1,
        type=RoomStayType.NIGHT,
        checkin_date=min(gs.checkin_date for gs in guest_stays),
        checkout_date=max(gs.checkout_date for gs in guest_stays),
        guest_stays=guest_stays,
        charges=None,
    )
    valid_booking_aggregate_with_one_room_stay.add_room_stay(new_room_stay)
    valid_booking_aggregate_with_one_room_stay.cancel_booking(
        cancellation_reason="Test"
    )
    assert all(
        rs.is_cancelled()
        for rs in valid_booking_aggregate_with_one_room_stay.get_active_room_stays()
    )


def test_checkin_guests_changes_guest_stay_status_to_checked_in(
    valid_booking_aggregate_with_one_room_stay,
):
    checkin_date = today()
    checkout_date = today_plus_days(8)
    guest_stays = [
        GuestStayData(
            status=BookingStatus.RESERVED,
            age_group=AgeGroup.ADULT,
            checkin_date=checkin_date,
            checkout_date=checkout_date,
        ),
        GuestStayData(
            status=BookingStatus.RESERVED,
            age_group=AgeGroup.ADULT,
            checkin_date=checkin_date,
            checkout_date=checkout_date,
        ),
    ]

    # Add a new room stay
    new_room_stay = RoomStayData(
        room_type_id=1,
        type=RoomStayType.NIGHT,
        checkin_date=checkin_date,
        checkout_date=checkout_date,
        guest_stays=guest_stays,
        charges=None,
    )
    room_stay = valid_booking_aggregate_with_one_room_stay.add_room_stay(new_room_stay)

    customer = Customer(
        customer_id="test_guest_id",
        external_ref_id=None,
        profile_type=None,
        first_name=None,
        last_name=None,
        gender=Genders.MALE,
        age=None,
        address=None,
        phone=None,
        email=None,
    )
    valid_booking_aggregate_with_one_room_stay.append_customer(customer)
    guest_checkin_data = [
        GuestCheckinData(
            guest_stay_id=1, guest_id="test_guest_id", checkin_date=today()
        )
    ]
    room = Room(
        room_id=1,
        hotel_id='1',
        room_type_id=1,
        room_number="101",
        status=RoomStatus.ACTIVE,
        deleted=False,
    )
    room_allocation_checkin_date = min(gc.checkin_date for gc in guest_checkin_data)
    room_allocation_data = RoomAllocationData(
        room=room, room_id=1, checkin_date=room_allocation_checkin_date
    )

    if not valid_booking_aggregate_with_one_room_stay.is_future_booking():
        valid_booking_aggregate_with_one_room_stay.checkin_guests(
            room_stay.room_stay_id,
            guest_checkin_data,
            room_allocation_data=room_allocation_data,
        )
        guest_stay = room_stay.get_guest_stay(guest_stay_id=1)
        assert guest_stay.is_checked_in()


def test_checkin_one_guest_changes_room_stay_status_to_part_checked_in(
    valid_booking_aggregate_with_one_room_stay,
):
    checkin_date = today()
    checkout_date = today_plus_days(8)
    guest_stays = [
        GuestStayData(
            status=BookingStatus.RESERVED,
            age_group=AgeGroup.ADULT,
            checkin_date=checkin_date,
            checkout_date=checkout_date,
        ),
        GuestStayData(
            status=BookingStatus.RESERVED,
            age_group=AgeGroup.ADULT,
            checkin_date=checkin_date,
            checkout_date=checkout_date,
        ),
    ]

    # Add a new room stay
    new_room_stay = RoomStayData(
        room_type_id=1,
        type=RoomStayType.NIGHT,
        checkin_date=checkin_date,
        checkout_date=checkout_date,
        guest_stays=guest_stays,
        charges=None,
    )
    room_stay = valid_booking_aggregate_with_one_room_stay.add_room_stay(new_room_stay)

    customer = Customer(
        customer_id="test_guest_id",
        external_ref_id=None,
        profile_type=None,
        first_name=None,
        last_name=None,
        gender=Genders.MALE,
        age=None,
        address=None,
        phone=None,
        email=None,
    )
    valid_booking_aggregate_with_one_room_stay.append_customer(customer)
    guest_checkin_data = [
        GuestCheckinData(
            guest_stay_id=1, guest_id="test_guest_id", checkin_date=today()
        )
    ]
    room_allocation_checkin_date = min(gc.checkin_date for gc in guest_checkin_data)
    room = Room(
        room_id=1,
        hotel_id='1',
        room_type_id=1,
        room_number="101",
        status=RoomStatus.ACTIVE,
        deleted=False,
    )
    room_allocation_data = RoomAllocationData(
        room=room, room_id=1, checkin_date=room_allocation_checkin_date
    )

    if not valid_booking_aggregate_with_one_room_stay.is_future_booking():
        valid_booking_aggregate_with_one_room_stay.checkin_guests(
            room_stay.room_stay_id,
            guest_checkin_data,
            room_allocation_data=room_allocation_data,
        )
        assert room_stay.is_part_checked_in()


def test_checkin_all_guests_changes_room_stay_status_to_checked_in(
    valid_booking_aggregate_with_one_room_stay,
):
    checkin_date = today()
    checkout_date = today_plus_days(8)
    guest_stays = [
        GuestStayData(
            status=BookingStatus.RESERVED,
            age_group=AgeGroup.ADULT,
            checkin_date=checkin_date,
            checkout_date=checkout_date,
        ),
        GuestStayData(
            status=BookingStatus.RESERVED,
            age_group=AgeGroup.ADULT,
            checkin_date=checkin_date,
            checkout_date=checkout_date,
        ),
    ]

    # Add a new room stay
    new_room_stay = RoomStayData(
        room_type_id=1,
        type=RoomStayType.NIGHT,
        checkin_date=checkin_date,
        checkout_date=checkout_date,
        guest_stays=guest_stays,
        charges=None,
    )
    room_stay = valid_booking_aggregate_with_one_room_stay.add_room_stay(new_room_stay)

    customer = Customer(
        customer_id="test_guest_id",
        external_ref_id=None,
        profile_type=None,
        first_name=None,
        last_name=None,
        gender=Genders.MALE,
        age=None,
        address=None,
        phone=None,
        email=None,
    )
    valid_booking_aggregate_with_one_room_stay.append_customer(customer)
    customer = Customer(
        customer_id="test_guest_id_2",
        external_ref_id=None,
        profile_type=None,
        first_name=None,
        last_name=None,
        gender=Genders.MALE,
        age=None,
        address=None,
        phone=None,
        email=None,
    )
    valid_booking_aggregate_with_one_room_stay.append_customer(customer)
    guest_checkin_data = [
        GuestCheckinData(
            guest_stay_id=1, guest_id="test_guest_id", checkin_date=today()
        ),
        GuestCheckinData(
            guest_stay_id=2, guest_id="test_guest_id_2", checkin_date=today()
        ),
    ]
    room_allocation_checkin_date = min(gc.checkin_date for gc in guest_checkin_data)
    room = Room(
        room_id=1,
        hotel_id='1',
        room_type_id=1,
        room_number="101",
        status=RoomStatus.ACTIVE,
        deleted=False,
    )
    room_allocation_data = RoomAllocationData(
        room=room, room_id=1, checkin_date=room_allocation_checkin_date
    )

    if not valid_booking_aggregate_with_one_room_stay.is_future_booking():
        valid_booking_aggregate_with_one_room_stay.checkin_guests(
            room_stay.room_stay_id,
            guest_checkin_data,
            room_allocation_data=room_allocation_data,
        )
        assert room_stay.is_checked_in()


def test_checkin_guests_changes_booking_status_to_part_checked_in_if_only_one_room_is_checked_in(
    valid_booking_aggregate_with_one_room_stay,
):
    checkin_date = today()
    checkout_date = today_plus_days(8)
    guest_stays = [
        GuestStayData(
            status=BookingStatus.RESERVED,
            age_group=AgeGroup.ADULT,
            checkin_date=checkin_date,
            checkout_date=checkout_date,
        ),
        GuestStayData(
            status=BookingStatus.RESERVED,
            age_group=AgeGroup.ADULT,
            checkin_date=checkin_date,
            checkout_date=checkout_date,
        ),
    ]

    # Add a new room stay
    new_room_stay = RoomStayData(
        room_type_id=1,
        type=RoomStayType.NIGHT,
        checkin_date=checkin_date,
        checkout_date=checkout_date,
        guest_stays=guest_stays,
        charges=None,
    )
    room_stay = valid_booking_aggregate_with_one_room_stay.add_room_stay(new_room_stay)

    customer = Customer(
        customer_id="test_guest_id",
        external_ref_id=None,
        profile_type=None,
        first_name=None,
        last_name=None,
        gender=Genders.MALE,
        age=None,
        address=None,
        phone=None,
        email=None,
    )
    valid_booking_aggregate_with_one_room_stay.append_customer(customer)
    customer = Customer(
        customer_id="test_guest_id_2",
        external_ref_id=None,
        profile_type=None,
        first_name=None,
        last_name=None,
        gender=Genders.MALE,
        age=None,
        address=None,
        phone=None,
        email=None,
    )
    valid_booking_aggregate_with_one_room_stay.append_customer(customer)

    guest_checkin_data = [
        GuestCheckinData(
            guest_stay_id=1, guest_id="test_guest_id", checkin_date=today()
        ),
        GuestCheckinData(
            guest_stay_id=2, guest_id="test_guest_id_2", checkin_date=today()
        ),
    ]
    room_allocation_checkin_date = min(gc.checkin_date for gc in guest_checkin_data)
    room = Room(
        room_id=1,
        hotel_id='1',
        room_type_id=1,
        room_number="101",
        status=RoomStatus.ACTIVE,
        deleted=False,
    )
    room_allocation_data = RoomAllocationData(
        room=room, room_id=1, checkin_date=room_allocation_checkin_date
    )
    if not valid_booking_aggregate_with_one_room_stay.is_future_booking():
        valid_booking_aggregate_with_one_room_stay.checkin_guests(
            room_stay.room_stay_id,
            guest_checkin_data,
            room_allocation_data=room_allocation_data,
        )
        assert valid_booking_aggregate_with_one_room_stay.is_part_checked_in()


def test_checkin_guests_changes_booking_status_to_checked_in_if_all_rooms_are_fully_checked_in(
    valid_booking_aggregate_with_one_room_stay,
):
    existing_room_stay_id = 1

    customer = Customer(
        customer_id="test_guest_id",
        external_ref_id=None,
        profile_type=None,
        first_name=None,
        last_name=None,
        gender=Genders.MALE,
        age=None,
        address=None,
        phone=None,
        email=None,
    )
    valid_booking_aggregate_with_one_room_stay.append_customer(customer)

    guest_stay = valid_booking_aggregate_with_one_room_stay.get_room_stay(
        existing_room_stay_id
    ).get_guest_stay(1)
    guest_checkin_data = [
        GuestCheckinData(
            guest_stay_id=1,
            guest_id="test_guest_id",
            checkin_date=guest_stay.checkin_date,
        ),
    ]
    room_allocation_checkin_date = min(gc.checkin_date for gc in guest_checkin_data)
    room = Room(
        room_id=1,
        hotel_id='1',
        room_type_id=1,
        room_number="101",
        status=RoomStatus.ACTIVE,
        deleted=False,
    )
    room_allocation_data = RoomAllocationData(
        room=room, room_id=1, checkin_date=room_allocation_checkin_date
    )
    if not valid_booking_aggregate_with_one_room_stay.is_future_booking():
        valid_booking_aggregate_with_one_room_stay.checkin_guests(
            existing_room_stay_id,
            guest_checkin_data,
            room_allocation_data=room_allocation_data,
        )
        assert valid_booking_aggregate_with_one_room_stay.is_checked_in()


def test_checkin_guests_room_should_not_be_checked_in_without_room_allocation(
    valid_booking_aggregate_with_one_room_stay,
):
    existing_room_stay_id = 1
    customer = Customer(
        customer_id="test_guest_id",
        external_ref_id=None,
        profile_type=None,
        first_name=None,
        last_name=None,
        gender=Genders.MALE,
        age=None,
        address=None,
        phone=None,
        email=None,
    )
    valid_booking_aggregate_with_one_room_stay.append_customer(customer)

    guest_checkin_data = [
        GuestCheckinData(
            guest_stay_id=1, guest_id="test_guest_id", checkin_date=today_plus_days(4)
        ),
    ]
    with pytest.raises(InvalidActionError):
        valid_booking_aggregate_with_one_room_stay.checkin_guests(
            existing_room_stay_id, guest_checkin_data
        )


def test_add_expense_to_booking_with_invalid_assigned_to_should_throw_error(
    valid_booking_aggregate_with_one_room_stay, some_expense
):
    booking_aggregate = valid_booking_aggregate_with_one_room_stay
    some_expense['guests'] = "some guest not in booking"
    expense = ExpenseDto.from_dict(some_expense)
    with pytest.raises(CustomerNotFound):
        booking_aggregate.add_expense(expense, None)


def test_add_expense_to_booking_with_invalid_room_stay_id_should_throw_error(
    valid_booking_aggregate_with_one_room_stay, some_expense_with_valid_guest
):
    booking_aggregate = valid_booking_aggregate_with_one_room_stay

    room_stay_id = 10  # not in booking
    assert room_stay_id not in [rs.room_stay_id for rs in booking_aggregate.room_stays]
    some_expense_with_valid_guest['room_stay_id'] = room_stay_id
    expense = ExpenseDto.from_dict(some_expense_with_valid_guest)
    with pytest.raises(ResourceNotFound):
        booking_aggregate.add_expense(expense, None)


def test_add_expense_to_booking_with_invalid_applicable_dates_should_throw_error(
    valid_booking_aggregate_with_one_room_stay, some_expense_with_valid_guest
):
    booking_aggregate = valid_booking_aggregate_with_one_room_stay

    date_after_booking = dateutils.add(booking_aggregate.booking.checkout_date, days=5)
    assert date_after_booking > booking_aggregate.booking.checkout_date
    some_expense_with_valid_guest['applicable_date'] = dateutils.to_date(
        date_after_booking
    )
    expense = ExpenseDto.from_dict(some_expense_with_valid_guest)
    with pytest.raises(InvalidExpenseDate):
        booking_aggregate.add_expense(expense, None)

    date_before_booking = dateutils.subtract(
        booking_aggregate.booking.checkin_date, days=1
    )
    assert date_before_booking < booking_aggregate.booking.checkin_date
    some_expense_with_valid_guest['applicable_date'] = dateutils.to_date(
        date_before_booking
    )
    expense = ExpenseDto.from_dict(some_expense_with_valid_guest)
    with pytest.raises(InvalidExpenseDate):
        booking_aggregate.add_expense(expense, None)


def test_add_cancellation_charge_to_booking_on_the_day_before_booking_should_succeed(
    some_expense,
):
    booking_aggregate = BookingAggregateFactory()
    cancellation_date = dateutils.subtract(
        booking_aggregate.booking.checkin_date, days=1
    )
    assert (booking_aggregate.booking.checkin_date - cancellation_date).days == 1
    booking_aggregate.cancel_booking(cancellation_reason="Test")
    some_expense['applicable_date'] = dateutils.to_date(cancellation_date)
    some_expense['expense_item_id'] = 'booking_cancellation'
    expense = ExpenseDto.from_dict(some_expense)
    added_expense = booking_aggregate.add_expense(expense, settlement_date=tomorrow())
    assert added_expense is not None


def test_add_cancellation_charge_to_booking_before_2_days_from_checkin_and_after_settlement_date_should_fail(
    some_expense,
):
    some_expense['expense_item_id'] = 'cancellation_charge'
    booking_aggregate = BookingAggregateFactory()
    cancellation_date = dateutils.subtract(
        booking_aggregate.booking.checkin_date, days=2
    )
    settlement_date = dateutils.add(booking_aggregate.booking.checkout_date, days=2)
    assert (booking_aggregate.booking.checkin_date - cancellation_date).days == 2

    applicable_dates = [cancellation_date, dateutils.add(settlement_date, 1)]
    for applicable_date in applicable_dates:
        some_expense['applicable_date'] = dateutils.to_date(applicable_date)
        expense = ExpenseDto.from_dict(some_expense)
        with pytest.raises(InvalidExpenseDate):
            booking_aggregate.add_expense(expense, settlement_date=settlement_date)


def test_add_expense_to_guest_outside_of_stay_should_throw_error(
    valid_booking_aggregate_with_one_room_stay, some_expense, settlement_date
):
    checkin_date = today()
    checkout_date = today_plus_days(8)
    guest_stays = [
        GuestStayData(
            status=BookingStatus.RESERVED,
            age_group=AgeGroup.ADULT,
            checkin_date=checkin_date,
            checkout_date=checkout_date,
        ),
        GuestStayData(
            status=BookingStatus.RESERVED,
            age_group=AgeGroup.ADULT,
            checkin_date=checkin_date,
            checkout_date=checkout_date,
        ),
    ]

    new_room_stay = RoomStayData(
        room_type_id=1,
        type=RoomStayType.NIGHT,
        checkin_date=checkin_date,
        checkout_date=checkout_date,
        guest_stays=guest_stays,
        charges=None,
    )
    room_stay = valid_booking_aggregate_with_one_room_stay.add_room_stay(new_room_stay)

    customer = Customer(
        customer_id="123",
        external_ref_id=None,
        profile_type=None,
        first_name=None,
        last_name=None,
        gender=Genders.MALE,
        age=None,
        address=None,
        phone=None,
        email=None,
    )
    valid_booking_aggregate_with_one_room_stay.append_customer(customer)

    customer = Customer(
        customer_id="test_guest_id_2",
        external_ref_id=None,
        profile_type=None,
        first_name=None,
        last_name=None,
        gender=Genders.MALE,
        age=None,
        address=None,
        phone=None,
        email=None,
    )
    valid_booking_aggregate_with_one_room_stay.append_customer(customer)

    guest_checkin_data = [
        GuestCheckinData(guest_stay_id=1, guest_id="123", checkin_date=today()),
        GuestCheckinData(
            guest_stay_id=2, guest_id="test_guest_id_2", checkin_date=today()
        ),
    ]
    room = Room(
        room_id=1,
        hotel_id='1',
        room_type_id=1,
        room_number="101",
        status=RoomStatus.ACTIVE,
        deleted=False,
    )
    room_allocation_checkin_date = min(gc.checkin_date for gc in guest_checkin_data)
    room_allocation_data = RoomAllocationData(
        room=room, room_id=1, checkin_date=room_allocation_checkin_date
    )
    if not valid_booking_aggregate_with_one_room_stay.is_future_booking():
        valid_booking_aggregate_with_one_room_stay.checkin_guests(
            room_stay.room_stay_id,
            guest_checkin_data,
            room_allocation_data=room_allocation_data,
        )

        room = Room(
            room_id=2,
            hotel_id='1',
            room_type_id=1,
            room_number="102",
            status=RoomStatus.ACTIVE,
            deleted=False,
        )
        valid_booking_aggregate_with_one_room_stay.allocate_room(
            2, room, room.room_id, today_plus_days(3)
        )

        some_expense["applicable_date"] = dateutils.to_date(today_plus_days(8))
        some_expense["room_stay_id"] = 2
        expense = ExpenseDto.from_dict(some_expense)
        with pytest.raises(InvalidExpenseDate):
            valid_booking_aggregate_with_one_room_stay.add_expense(
                expense, settlement_date
            )


def test_cancel_booking_on_cancelled_booking_should_throw_InvalidActionError(
    cancelled_booking_aggregate_with_one_room_stay,
):
    with pytest.raises(InvalidActionError):
        cancelled_booking_aggregate_with_one_room_stay.cancel_booking(
            "trying to cancel again"
        )


def test_marking_booking_noshow_on_confirmed_booking(
    valid_past_booking_aggregate_with_one_room_stay,
):
    valid_past_booking_aggregate_with_one_room_stay.mark_booking_noshow()
    assert (
        valid_past_booking_aggregate_with_one_room_stay.booking.status
        == BookingStatus.NOSHOW
    )


def test_marking_room_stay_noshow(valid_past_booking_aggregate_with_one_room_stay):
    room_stays = valid_past_booking_aggregate_with_one_room_stay.get_active_room_stays()
    for room_stay in room_stays:
        room_stay.mark_noshow()

    for room_stay in room_stays:
        assert room_stay.status == BookingStatus.NOSHOW


def test_checked_in_guest_ids_only_return_adult(
    valid_booking_aggregate_with_one_room_stay,
):
    checkin_date = today()
    checkout_date = today_plus_days(8)
    guest_stays = [
        GuestStayData(
            status=BookingStatus.RESERVED,
            age_group=AgeGroup.CHILD,
            checkin_date=checkin_date,
            checkout_date=checkout_date,
        ),
        GuestStayData(
            status=BookingStatus.RESERVED,
            age_group=AgeGroup.ADULT,
            checkin_date=checkin_date,
            checkout_date=checkout_date,
        ),
    ]

    # Add a new room stay
    new_room_stay = RoomStayData(
        room_type_id=1,
        type=RoomStayType.NIGHT,
        checkin_date=checkin_date,
        checkout_date=checkout_date,
        guest_stays=guest_stays,
        charges=None,
    )
    room_stay = valid_booking_aggregate_with_one_room_stay.add_room_stay(new_room_stay)

    customer = Customer(
        customer_id="test_guest_id",
        external_ref_id=None,
        profile_type=None,
        first_name=None,
        last_name=None,
        gender=Genders.MALE,
        age=None,
        address=None,
        phone=None,
        email=None,
    )
    valid_booking_aggregate_with_one_room_stay.append_customer(customer)
    customer = Customer(
        customer_id="test_guest_id_2",
        external_ref_id=None,
        profile_type=None,
        first_name=None,
        last_name=None,
        gender=Genders.MALE,
        age=None,
        address=None,
        phone=None,
        email=None,
    )
    valid_booking_aggregate_with_one_room_stay.append_customer(customer)
    guest_checkin_data = [
        GuestCheckinData(
            guest_stay_id=1, guest_id="test_guest_id", checkin_date=today()
        ),
        GuestCheckinData(
            guest_stay_id=2, guest_id="test_guest_id_2", checkin_date=today()
        ),
    ]
    room_allocation_checkin_date = min(gc.checkin_date for gc in guest_checkin_data)
    room = Room(
        room_id=1,
        hotel_id='1',
        room_type_id=1,
        room_number="101",
        status=RoomStatus.ACTIVE,
        deleted=False,
    )
    room_allocation_data = RoomAllocationData(
        room=room, room_id=1, checkin_date=room_allocation_checkin_date
    )

    if not valid_booking_aggregate_with_one_room_stay.is_future_booking():
        valid_booking_aggregate_with_one_room_stay.checkin_guests(
            room_stay.room_stay_id,
            guest_checkin_data,
            room_allocation_data=room_allocation_data,
        )
        assert room_stay.checked_in_guest_ids_on_date(
            dateutils.to_date(today()), age_group=AgeGroup.ADULT
        ) == ['test_guest_id_2']


def test_checked_in_guest_ids_return_both_adult_and_child(
    valid_booking_aggregate_with_one_room_stay,
):
    checkin_date = today()
    checkout_date = today_plus_days(8)
    guest_stays = [
        GuestStayData(
            status=BookingStatus.RESERVED,
            age_group=AgeGroup.CHILD,
            checkin_date=checkin_date,
            checkout_date=checkout_date,
        ),
        GuestStayData(
            status=BookingStatus.RESERVED,
            age_group=AgeGroup.ADULT,
            checkin_date=checkin_date,
            checkout_date=checkout_date,
        ),
    ]

    # Add a new room stay
    new_room_stay = RoomStayData(
        room_type_id=1,
        type=RoomStayType.NIGHT,
        checkin_date=checkin_date,
        checkout_date=checkout_date,
        guest_stays=guest_stays,
        charges=None,
    )
    room_stay = valid_booking_aggregate_with_one_room_stay.add_room_stay(new_room_stay)

    customer = Customer(
        customer_id="test_guest_id",
        external_ref_id=None,
        profile_type=None,
        first_name=None,
        last_name=None,
        gender=Genders.MALE,
        age=None,
        address=None,
        phone=None,
        email=None,
    )
    valid_booking_aggregate_with_one_room_stay.append_customer(customer)
    customer = Customer(
        customer_id="test_guest_id_2",
        external_ref_id=None,
        profile_type=None,
        first_name=None,
        last_name=None,
        gender=Genders.MALE,
        age=None,
        address=None,
        phone=None,
        email=None,
    )
    valid_booking_aggregate_with_one_room_stay.append_customer(customer)
    guest_checkin_data = [
        GuestCheckinData(
            guest_stay_id=1, guest_id="test_guest_id", checkin_date=today()
        ),
        GuestCheckinData(
            guest_stay_id=2, guest_id="test_guest_id_2", checkin_date=today()
        ),
    ]
    room_allocation_checkin_date = min(gc.checkin_date for gc in guest_checkin_data)
    room = Room(
        room_id=1,
        hotel_id='1',
        room_type_id=1,
        room_number="101",
        status=RoomStatus.ACTIVE,
        deleted=False,
    )
    room_allocation_data = RoomAllocationData(
        room=room, room_id=1, checkin_date=room_allocation_checkin_date
    )

    if not valid_booking_aggregate_with_one_room_stay.is_future_booking():
        valid_booking_aggregate_with_one_room_stay.checkin_guests(
            room_stay.room_stay_id,
            guest_checkin_data,
            room_allocation_data=room_allocation_data,
        )
        assert set(
            room_stay.checked_in_guest_ids_on_date(dateutils.to_date(today()))
        ) == {'test_guest_id', 'test_guest_id_2'}


class TestBookingActionReversal(object):
    def test_booking_part_checkin_reversal(
        self, booking_in_part_checked_in_state, confirmed_action_aggregate
    ):
        assert (
            booking_in_part_checked_in_state.booking.status
            == BookingStatus.PART_CHECKIN
        )
        gs_1_checkin_data = GuestStaySideEffect(
            1, GuestAllocationSideEffect(guest_allocation_id=1, is_new=True)
        )
        booking_in_part_checked_in_state.undo_checkin_guests(
            1,
            [gs_1_checkin_data],
            confirmed_action_aggregate.booking_action.previous_state,
        )
        assert (
            booking_in_part_checked_in_state.booking.status == BookingStatus.CONFIRMED
        )
        for room_stay in booking_in_part_checked_in_state.room_stays:
            room_stay.status == BookingStatus.RESERVED
        assert booking_in_part_checked_in_state.booking.actual_checkin_date is None

    def test_booking_checkin_reversal(
        self, booking_in_checked_in_state, confirmed_action_aggregate
    ):
        assert booking_in_checked_in_state.booking.status == BookingStatus.CHECKED_IN
        gs_1_checkin_data = GuestStaySideEffect(
            1, GuestAllocationSideEffect(guest_allocation_id=1, is_new=True)
        )
        gs_2_checkin_data = GuestStaySideEffect(
            2, GuestAllocationSideEffect(guest_allocation_id=1, is_new=True)
        )
        booking_in_checked_in_state.undo_checkin_guests(
            1,
            [gs_1_checkin_data, gs_2_checkin_data],
            confirmed_action_aggregate.booking_action.previous_state,
        )
        assert booking_in_checked_in_state.booking.status == BookingStatus.PART_CHECKIN
        for room_stay in booking_in_checked_in_state.room_stays:
            if room_stay.room_stay_id == 1:
                room_stay.status == BookingStatus.RESERVED
            elif room_stay.room_stay_id == 2:
                room_stay.status == BookingStatus.CHECKED_IN
        assert (
            booking_in_checked_in_state.booking.actual_checkin_date
            == booking_in_checked_in_state.get_room_stay(2).actual_checkin_date
        )
        gs_1_checkin_data = GuestStaySideEffect(
            1, GuestAllocationSideEffect(guest_allocation_id=1, is_new=True)
        )
        gs_2_checkin_data = GuestStaySideEffect(
            2, GuestAllocationSideEffect(guest_allocation_id=1, is_new=True)
        )
        booking_in_checked_in_state.undo_checkin_guests(
            2,
            [gs_1_checkin_data, gs_2_checkin_data],
            confirmed_action_aggregate.booking_action.previous_state,
        )
        assert booking_in_checked_in_state.booking.status == BookingStatus.CONFIRMED
        for room_stay in booking_in_checked_in_state.room_stays:
            room_stay.status == BookingStatus.RESERVED
        assert booking_in_checked_in_state.booking.actual_checkin_date is None

    def test_booking_checkout_reversal(self, booking_in_checked_out_state):
        assert booking_in_checked_out_state.booking.status == BookingStatus.CHECKED_OUT
        checkout_datetime = dateutils.today()
        room_checkout_1 = RoomCheckoutRequestData(
            1,
            checkout_datetime,
            [
                GuestCheckoutRequestData(1, '2', checkout_datetime),
                GuestCheckoutRequestData(2, '3', checkout_datetime),
            ],
            1,
            1,
        )
        room_checkout_2 = RoomCheckoutRequestData(
            2,
            checkout_datetime,
            [
                GuestCheckoutRequestData(1, '4', checkout_datetime),
                GuestCheckoutRequestData(2, '5', checkout_datetime),
            ],
            1,
            1,
        )
        booking_in_checked_out_state.undo_checkout_guests(
            [room_checkout_1, room_checkout_2]
        )
        assert booking_in_checked_out_state.booking.status == BookingStatus.CHECKED_IN
        for room_stay in booking_in_checked_out_state.room_stays:
            room_stay.status == BookingStatus.CHECKED_IN
        assert booking_in_checked_out_state.booking.actual_checkout_date is None

    def test_booking_part_checkout_reversal(
        self, booking_in_checked_out_state_in_two_steps
    ):
        assert (
            booking_in_checked_out_state_in_two_steps.booking.status
            == BookingStatus.CHECKED_OUT
        )
        checkout_datetime = dateutils.today()
        room_checkout_1 = RoomCheckoutRequestData(
            1,
            checkout_datetime,
            [GuestCheckoutRequestData(2, '3', checkout_datetime)],
            1,
            1,
        )
        room_checkout_2 = RoomCheckoutRequestData(
            2,
            checkout_datetime,
            [
                GuestCheckoutRequestData(1, '4', checkout_datetime),
                GuestCheckoutRequestData(2, '5', checkout_datetime),
            ],
            1,
            1,
        )
        booking_in_checked_out_state_in_two_steps.undo_checkout_guests(
            [room_checkout_1, room_checkout_2]
        )
        assert (
            booking_in_checked_out_state_in_two_steps.booking.status
            == BookingStatus.PART_CHECKOUT
        )
        for room_stay in booking_in_checked_out_state_in_two_steps.room_stays:
            if room_stay.room_stay_id == 1:
                room_stay.status == BookingStatus.PART_CHECKOUT
            elif room_stay.room_stay_id == 2:
                room_stay.status == BookingStatus.CHECKED_IN
        assert (
            booking_in_checked_out_state_in_two_steps.booking.actual_checkout_date
            is None
        )

    def test_booking_part_checkout_reversal_to_checkin(
        self, booking_in_part_checked_out_state
    ):
        assert (
            booking_in_part_checked_out_state.booking.status
            == BookingStatus.PART_CHECKOUT
        )
        checkout_datetime = dateutils.today()
        room_checkout_1 = RoomCheckoutRequestData(
            1,
            checkout_datetime,
            [GuestCheckoutRequestData(1, '2', checkout_datetime)],
            1,
            1,
        )
        booking_in_part_checked_out_state.undo_checkout_guests([room_checkout_1])
        assert (
            booking_in_part_checked_out_state.booking.status == BookingStatus.CHECKED_IN
        )
        for room_stay in booking_in_part_checked_out_state.room_stays:
            room_stay.status == BookingStatus.CHECKED_IN
        assert booking_in_part_checked_out_state.booking.actual_checkout_date is None

    def test_booking_no_show_reversal(
        self, booking_in_no_show_state, confirmed_action_aggregate
    ):
        assert booking_in_no_show_state.booking.status == BookingStatus.NOSHOW
        booking_in_no_show_state.booking.cancellation_reason is not None
        room_stay_side_effects = [
            RoomStaySideEffect(1, [GuestStaySideEffect(1), GuestStaySideEffect(2)]),
            RoomStaySideEffect(2, [GuestStaySideEffect(1), GuestStaySideEffect(2)]),
        ]
        booking_in_no_show_state.undo_booking_noshow(
            room_stay_side_effects,
            confirmed_action_aggregate.booking_action.previous_state,
        )
        booking_in_no_show_state.booking.status == BookingStatus.CONFIRMED
        for room_stay in booking_in_no_show_state.room_stays:
            room_stay.status == BookingStatus.RESERVED
        booking_in_no_show_state.booking.cancellation_reason is None

    def test_booking_room_no_show_reversal(self, booking_in_reserved_state_with_noshow):
        assert (
            booking_in_reserved_state_with_noshow.booking.status
            == BookingStatus.CONFIRMED
        )
        booking_in_reserved_state_with_noshow.booking.cancellation_reason is not None
        guest_stay_side_effect = [GuestStaySideEffect(1), GuestStaySideEffect(2)]
        booking_in_reserved_state_with_noshow.undo_mark_room_stay_noshow(
            1, guest_stay_side_effect
        )
        assert (
            booking_in_reserved_state_with_noshow.booking.status
            == BookingStatus.CONFIRMED
        )
        assert len(booking_in_reserved_state_with_noshow.room_stays) == 2
        for room_stay in booking_in_reserved_state_with_noshow.room_stays:
            room_stay.status == BookingStatus.RESERVED
        booking_in_reserved_state_with_noshow.booking.cancellation_reason is None

    def test_booking_guest_no_show_reversal(
        self, booking_in_reserved_state_with_noshow_guest
    ):
        assert (
            booking_in_reserved_state_with_noshow_guest.booking.status
            == BookingStatus.CONFIRMED
        )
        booking_in_reserved_state_with_noshow_guest.booking.cancellation_reason is not None
        booking_in_reserved_state_with_noshow_guest.undo_mark_guest_stays_noshow(1, [1])
        assert (
            booking_in_reserved_state_with_noshow_guest.booking.status
            == BookingStatus.CONFIRMED
        )
        assert len(booking_in_reserved_state_with_noshow_guest.room_stays) == 2
        for room_stay in booking_in_reserved_state_with_noshow_guest.room_stays:
            assert len(room_stay.guest_stays) == 2
            room_stay.status == BookingStatus.RESERVED
            for guest_stay in room_stay.guest_stays:
                guest_stay.status == BookingStatus.RESERVED
            booking_in_reserved_state_with_noshow_guest.booking.cancellation_reason is None

    def test_booking_cancel_reversal(
        self, booking_in_canceled_state, confirmed_action_aggregate
    ):
        assert booking_in_canceled_state.booking.status == BookingStatus.CANCELLED
        booking_in_canceled_state.booking.cancellation_reason is not None
        booking_side_effect = BookingSideEffect(
            [
                RoomStaySideEffect(1, [GuestStaySideEffect(1), GuestStaySideEffect(2)]),
                RoomStaySideEffect(2, [GuestStaySideEffect(1), GuestStaySideEffect(2)]),
            ]
        )
        booking_in_canceled_state.undo_cancel_booking(
            booking_side_effect,
            confirmed_action_aggregate.booking_action.previous_state,
        )
        booking_in_canceled_state.booking.status == BookingStatus.CONFIRMED
        for room_stay in booking_in_canceled_state.room_stays:
            room_stay.status == BookingStatus.RESERVED
        booking_in_canceled_state.booking.cancellation_reason is None
