import pytest

from prometheus import crs_context
from prometheus.domain.policy.facts import GuestStayFacts
from prometheus.domain.policy.facts.facts import Facts
from prometheus.domain.policy.rule.add_guest_rule import AddGuestRule
from prometheus.tests.response.role_manager_privileges_response import (
    get_privilege_details_by_role,
)
from ths_common.constants.booking_constants import BookingChannels
from ths_common.exceptions import PolicyAuthException


def test_add_guest_rule_fail_for_user_fdm_and_non_hotel_channel_booking(
    valid_booking_aggregate_with_one_room_stay,
):
    valid_booking_aggregate_with_one_room_stay.booking.source.channel_code = (
        BookingChannels.B2B.value
    )
    with pytest.raises(PolicyAuthException):
        privileges = get_privilege_details_by_role(role='fdm')
        AddGuestRule().allow(
            Facts(
                user_type="fdm",
                booking_aggregate=valid_booking_aggregate_with_one_room_stay,
                hotel_context=crs_context.get_hotel_context(),
            ),
            privileges=privileges,
        )


def test_add_guest_rule_pass_for_user_cr_team(
    valid_booking_aggregate_with_one_room_stay_one_night,
    bill_aggregate,
):
    privileges = get_privilege_details_by_role(role='cr-team')
    add_guest_allow = AddGuestRule().allow(
        GuestStayFacts(
            user_type="cr-team",
            booking_aggregate=valid_booking_aggregate_with_one_room_stay_one_night,
            hotel_context=crs_context.get_hotel_context(),
            bill_aggregate=bill_aggregate,
            room_stay=valid_booking_aggregate_with_one_room_stay_one_night.room_stays[
                0
            ],
        ),
        privileges=privileges,
    )
    assert add_guest_allow is True


def test_add_guest_rule_pass_for_user_fdm_and_hotel_channel_booking(
    valid_booking_aggregate_with_one_room_stay_one_night,
    bill_aggregate,
):
    valid_booking_aggregate_with_one_room_stay_one_night.booking.source.channel_code = (
        BookingChannels.HOTEL.value
    )
    privileges = get_privilege_details_by_role(role='fdm')
    add_guest_allow = AddGuestRule().allow(
        GuestStayFacts(
            user_type="fdm",
            booking_aggregate=valid_booking_aggregate_with_one_room_stay_one_night,
            hotel_context=crs_context.get_hotel_context(),
            bill_aggregate=bill_aggregate,
            room_stay=valid_booking_aggregate_with_one_room_stay_one_night.room_stays[
                0
            ],
        ),
        privileges=privileges,
    )
    assert add_guest_allow is True
