import pytest

from prometheus import crs_context
from prometheus.application import dto_mapper
from prometheus.domain.booking.dtos.new_booking_dto import NewBookingDomainDto
from prometheus.domain.policy.facts.new_booking_facts import NewBookingFacts
from prometheus.domain.policy.rule.create_booking_rule import CreateBookingRule
from prometheus.tests.response.role_manager_privileges_response import (
    get_privilege_details_by_role,
)
from ths_common.constants.billing_constants import ChargeTypes
from ths_common.constants.booking_constants import BookingChannels, BookingStatus
from ths_common.exceptions import PolicyAuthException


def test_create_booking_fail_for_b2b_booking_on_crs_web(
    create_booking_payload, active_hotel_aggregate
):
    create_booking_payload.source['channel_code'] = BookingChannels.B2B.value
    create_booking_payload.source['application_code'] = "treebo-pms"

    with pytest.raises(PolicyAuthException):
        CreateBookingRule().allow(
            facts=NewBookingFacts(
                user_type=None,
                action_payload=dto_mapper.map_dto(
                    create_booking_payload, NewBookingDomainDto
                ),
                hotel_aggregate=active_hotel_aggregate,
                hotel_context=crs_context.get_hotel_context(),
            ),
            privileges=dict(),
        )


def test_create_booking_fail_for_ta_booking_on_crs_web(
    create_booking_payload, active_hotel_aggregate
):
    create_booking_payload.source['channel_code'] = BookingChannels.TA.value
    create_booking_payload.source['application_code'] = "treebo-pms"

    with pytest.raises(PolicyAuthException):
        allow_booking = CreateBookingRule().allow(
            facts=NewBookingFacts(
                user_type=None,
                action_payload=dto_mapper.map_dto(
                    create_booking_payload, NewBookingDomainDto
                ),
                hotel_aggregate=active_hotel_aggregate,
                hotel_context=crs_context.get_hotel_context(),
            ),
            privileges=dict(),
        )


def test_create_booking_fail_for_non_b2b_booking_with_credit_charge(
    create_booking_payload, active_hotel_aggregate
):
    create_booking_payload.source['channel_code'] = BookingChannels.DIRECT.value
    create_booking_payload.source['application_code'] = "treebo-pms"
    create_booking_payload.room_stays[0]['prices'][0].type = ChargeTypes.CREDIT
    with pytest.raises(PolicyAuthException):
        allow_booking = CreateBookingRule().allow(
            facts=NewBookingFacts(
                user_type=None,
                action_payload=dto_mapper.map_dto(
                    create_booking_payload, NewBookingDomainDto
                ),
                hotel_aggregate=active_hotel_aggregate,
                hotel_context=crs_context.get_hotel_context(),
            ),
            privileges=dict(),
        )


def test_create_booking_fail_for_fdm_user_and_non_hotel_channel_booking(
    create_booking_payload, active_hotel_aggregate
):
    create_booking_payload.source['channel_code'] = BookingChannels.B2B.value
    create_booking_payload.status = BookingStatus.RESERVED
    privileges = get_privilege_details_by_role(role='fdm')
    with pytest.raises(PolicyAuthException):
        allow_booking = CreateBookingRule().allow(
            facts=NewBookingFacts(
                user_type="fdm",
                action_payload=dto_mapper.map_dto(
                    create_booking_payload, NewBookingDomainDto
                ),
                hotel_aggregate=active_hotel_aggregate,
                hotel_context=crs_context.get_hotel_context(),
            ),
            privileges=privileges,
        )
