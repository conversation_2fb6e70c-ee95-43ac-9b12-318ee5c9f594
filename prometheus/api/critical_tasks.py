import logging

from flask import Blueprint

from object_registry import inject
from prometheus.application.end_of_day.critical_task_service import CriticalTaskService
from prometheus.common.api_response import ApiResponse
from prometheus.common.request_parsers import read_user_data_from_request_header
from prometheus.common.serializers import CriticalTasksResponseSchema
from prometheus.common.serializers.request.critical_task import CriticalTasksSchema
from prometheus.core.api_docs import swag_route
from prometheus.domain.critical_task.dtos.critical_task_search_query import (
    CriticalTaskSearchQuery,
)
from shared_kernel.api_helpers.request_parsers import (
    RequestTypes,
    schema_wrapper_parser,
)
from ths_common.constants.user_constants import UserType

bp = Blueprint("CriticalTasks", __name__, url_prefix="/v1")
logger = logging.getLogger(__name__)


@swag_route
@bp.route("/critical-tasks", methods=["GET"])
@schema_wrapper_parser(CriticalTasksSchema, param_type=RequestTypes.ARGS)
@inject(critical_task_service=CriticalTaskService)
def search_critical_tasks(critical_task_service, parsed_request):
    """Search critical tasks
    ---
    operationId: search_critical_tasks
    consumes:
        - application/json
    produces:
        - application/json
    schemes: ['http', 'https']
    deprecated: false
    get:
        parameters:
            - name: search_criteria
              in: query
              required: true
              schema:
                $ref: "#/components/schemas/CriticalTasksSchema"
        description: Get list of critical task ids which match the search criteria.
        tags:
            - Critical Tasks
        responses:
            200:
                description: A list of critical task entity ids
                schema:
                    type: object
                    properties:
                        data:
                            $ref: "#/components/schemas/CriticalTasksResponseSchema"
                        meta:
                            type: object
                            additionalProperties: {}
                        errors:
                            type: array
                            items:
                                $ref: "#/components/schemas/ApiErrorSchema"
    """
    user_data = read_user_data_from_request_header(default=UserType.VIEW_ONLY.value)
    query = CriticalTaskSearchQuery(**parsed_request)

    critical_tasks = critical_task_service.search_critical_tasks(query, user_data)
    critical_tasks_response = CriticalTasksResponseSchema()
    response_dict = {"hotel_id": query.hotel_id, "critical_tasks": critical_tasks}
    response = critical_tasks_response.dump(response_dict)

    return ApiResponse.build(status_code=200, data=response.data)
