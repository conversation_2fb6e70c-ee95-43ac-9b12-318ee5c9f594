import logging

from flask import Blueprint

from object_registry import inject
from prometheus.application.booking.command_handlers.create_card import (
    CreateCardCommandHandler,
)
from prometheus.application.booking.query_handlers.get_card_by_id import (
    GetCardByIdQueryHandler,
)
from prometheus.application.booking.query_handlers.get_cards_for_bill import (
    GetCardsForBillQueryHandler,
)
from prometheus.common.decorators import authorize_write_op
from prometheus.common.serializers.request.card import AddCardSchema
from prometheus.common.serializers.response.card import CardSchema
from prometheus.core.api_docs import swag_route
from shared_kernel.api_helpers.request_parsers import schema_wrapper_parser
from shared_kernel.api_response import ApiResponse

bp = Blueprint('Card', __name__, url_prefix="/v1")
logger = logging.getLogger(__name__)


@swag_route
@bp.route('/bills/<string:bill_id>/cards/<string:card_id>', methods=['GET'])
@inject(handler=GetCardByIdQueryHandler)
def get_card(handler: GetCardByIdQueryHandler, bill_id, card_id):
    """Get Card
    ---
    parameters:
          - name: bill_id
            in: path
            type: string
            required: true
          - name: card_id
            in: path
            type: string
            required: true
    operationId: get_card
    consumes:
        - application/json
    produces:
        - application/json
    schemes: ['http', 'https']
    deprecated: false
    get:
        description: Get card data for a given bill.
        tags:
            - Card
        responses:
            200:
                description:  Card object.
                schema:
                    type: object
                    properties:
                        data:
                            $ref: "#/components/schemas/CardSchema"
                        meta:
                            type: object
                            additionalProperties: {}
                        errors:
                            type: array
                            items:
                                $ref: "#/components/schemas/ApiErrorSchema"
    """
    card_aggregate = handler.handle(bill_id, card_id)
    response = CardSchema().dump(card_aggregate.card)
    return ApiResponse.build(data=response.data, status_code=200)


@swag_route
@bp.route('/bills/<string:bill_id>/cards', methods=['GET'])
@inject(handler=GetCardsForBillQueryHandler)
def get_cards(handler: GetCardsForBillQueryHandler, bill_id):
    """Get all Cards for a bill
    ---
    parameters:
          - name: bill_id
            in: path
            type: string
            required: true
    operationId: get_cards
    consumes:
        - application/json
    produces:
        - application/json
    schemes: ['http', 'https']
    deprecated: false
    get:
        description: Get all cards for a given bill.
        tags:
            - Card
        responses:
            200:
                description:  Card object.
                schema:
                    type: object
                    properties:
                        data:
                            type: array
                            items:
                                $ref: "#/components/schemas/CardSchema"
                        meta:
                            type: object
                            additionalProperties: {}
                        errors:
                            type: array
                            items:
                                $ref: "#/components/schemas/ApiErrorSchema"
    """
    card_aggregates = handler.handle(bill_id)
    cards = [card_aggregate.card for card_aggregate in card_aggregates]
    response = CardSchema().dump(cards, many=True)
    return ApiResponse.build(data=response.data, status_code=200)


@swag_route
@bp.route('/bills/<string:bill_id>/cards', methods=['POST'])
@authorize_write_op
@schema_wrapper_parser(AddCardSchema)
@inject(handler=CreateCardCommandHandler)
def add_card_to_bill(handler: CreateCardCommandHandler, bill_id, parsed_request):
    """Add card entity to bill
    ---
    operationId: add_card_to_bill
    consumes:
        - application/json
    produces:
        - application/json
    schemes: ['http', 'https']
    deprecated: false
    post:
        description: adding card to a bill
        tags:
            - Card
        parameters:
            - in: body
              name: body
              description: Need to create card entity
              required: True
              schema:
                type: object
                properties:
                    data:
                        $ref: "#/definitions/AddCardSchema"
        responses:
            201:
                description: Card object.
                schema:
                    type: object
                    properties:
                        data:
                            $ref: "#/components/schemas/AddCardSchema"
            responses:
                201:
                    description: Card object.
                    schema:
                        type: object
                        properties:
                            data:
                                $ref: "#/components/schemas/CardSchema"
                            meta:
                                type: object
                                additionalProperties: {}
                            errors:
                                type: array
                                items:
                                    $ref: "#/components/schemas/ApiErrorSchema"
    """

    card_aggregate = handler.handle(bill_id, parsed_request)
    response = CardSchema().dump(card_aggregate.card)
    return ApiResponse.build(data=response.data, status_code=201)
