import logging

from flask import Blueprint

from object_registry import inject
from prometheus.application.funding.command_handlers.get_applicable_funding_details import (
    GetApplicableFundingDetailsQueryHandler,
)
from prometheus.application.funding.command_handlers.get_funding_requests import (
    GetFundingRequestsQueryHandler,
)
from prometheus.application.funding.command_handlers.update_funding_request import (
    UpdateFundingRequestCommandHandler,
)
from prometheus.common.api_response import ApiResponse
from prometheus.common.decorators import authorize_write_op
from prometheus.common.serializers import FundingSummaryResponseSchema
from prometheus.common.serializers.request.funding import (
    FundingSearchSchema,
    FundingSummaryRequestSchema,
    UpdateFundingRequestSchema,
)
from prometheus.common.serializers.response.funding import FundingDetailSchema
from prometheus.core.api_docs import swag_route
from shared_kernel.api_helpers.request_parsers import (
    RequestTypes,
    schema_wrapper_parser,
)

bp = Blueprint('Funding', __name__, url_prefix="/v1")
logger = logging.getLogger(__name__)


@swag_route
@bp.route('/booking-funding/<string:booking_id>', methods=['PUT'])
@authorize_write_op
@schema_wrapper_parser(UpdateFundingRequestSchema)
@inject(command_handler=UpdateFundingRequestCommandHandler)
def update_funding_details(command_handler, booking_id, parsed_request):
    """
    Update funding details for a booking.
    ---
    operationId: update_funding_details
    consumes:
        - application/json
    produces:
        - application/json
    tags:
        - Funding
    parameters:
        - in: path
          name: booking_id
          description: Booking ID for which funding details need to be updated.
          required: True
          type: string
        - in: body
          name: body
          description: Data for updating funding details.
          required: True
          schema:
            $ref: "#/definitions/UpdateFundingRequestSchema"
    responses:
        200:
            description: Funding details updated successfully.
            schema:
                type: object
                properties:
                    data:
                        $ref: "#/components/schemas/FundingDetailSchema"
        400:
            description: Invalid request data.
            schema:
                $ref: "#/components/schemas/ApiErrorSchema"
    """
    funding_aggregate = command_handler.handle(booking_id, parsed_request)
    response = FundingDetailSchema().dump(funding_aggregate)
    return ApiResponse.build(data=response.data, status_code=200)


@swag_route
@bp.route('/booking-funding', methods=['GET'])
@schema_wrapper_parser(FundingSearchSchema, param_type=RequestTypes.ARGS)
@inject(query_handler=GetFundingRequestsQueryHandler)
def get_funding_details(query_handler, parsed_request):
    """
    Retrieve funding details for a booking.
    ---
    operationId: get_funding_details
    consumes:
        - application/json
    produces:
        - application/json
    tags:
        - Funding
    parameters:
        - in: path
          name: booking_id
          description: Booking ID for which funding details need to be retrieved.
          required: True
          type: string
    responses:
        200:
            description: Funding details retrieved successfully.
            schema:
                type: object
                properties:
                    data:
                        $ref: "#/components/schemas/FundingDetailSchema"
        404:
            description: Funding details not found.
            schema:
                $ref: "#/components/schemas/ApiErrorSchema"
    """
    booking_id = parsed_request.get('booking_id')
    funding_type = parsed_request.get('funding_type') if parsed_request else None
    funding_aggregate = query_handler.handle(booking_id, funding_type)
    response = FundingDetailSchema(many=True).dump(funding_aggregate)
    return ApiResponse.build(data=response.data, status_code=200)


@swag_route
@bp.route('/booking-funding/summary', methods=['GET'])
@schema_wrapper_parser(FundingSummaryRequestSchema, param_type=RequestTypes.ARGS)
@inject(query_handler=GetApplicableFundingDetailsQueryHandler)
def get_applicable_funding_details(query_handler, parsed_request):
    """
    Retrieve applicable funding amounts for a booking.
    ---
    operationId: get_applicable_funding_details
    consumes:
        - application/json
    produces:
        - application/json
    tags:
        - Funding
    parameters:
        - in: path
          name: booking_id
          description: Booking ID for which funding details need to be retrieved.
          required: True
          type: string
    responses:
        200:
            description: Funding details retrieved successfully.
            schema:
                $ref: "#/components/schemas/FundingSummaryResponseSchema"
        404:
            description: Funding details not found.
            schema:
                $ref: "#/components/schemas/ApiErrorSchema"
    """
    booking_id = parsed_request.get('booking_id')
    funding_summary = query_handler.handle(booking_id)
    response_data = FundingSummaryResponseSchema().dump(funding_summary)
    return ApiResponse.build(data=response_data.data, status_code=200)
