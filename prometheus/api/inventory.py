# coding=utf-8
"""
Inventory API
"""
import logging

from flask import Blueprint

from object_registry import inject
from prometheus.application.inventory.command_handlers.bulk_resolve_dnrs import (
    BulkResolveDNRCommandHandler,
)
from prometheus.application.inventory.command_handlers.edit_dnr import (
    EditDNRCommandHandler,
)
from prometheus.application.inventory.command_handlers.mark_dnrs import (
    MarkDNRsCommandHandler,
)
from prometheus.application.inventory.command_handlers.resolve_dnr import (
    ResolveDNRCommandHandler,
)
from prometheus.application.inventory.command_handlers.schedule_inventory_sync import (
    ScheduleInventorySyncForAllHotelsCommandHandler,
)
from prometheus.application.inventory.command_handlers.sync_inventories import (
    SyncInventoriesCommandHandler,
)
from prometheus.application.inventory.command_handlers.update_inventory_block import (
    UpdateInventoryBlockCommandHandler,
)
from prometheus.application.inventory.query_handlers.get_availability import (
    GetAvailabilityQueryHandler,
)
from prometheus.application.inventory.query_handlers.get_available_room_slots import (
    GetAvailableRoomSlotsQueryHandlers,
)
from prometheus.application.inventory.query_handlers.get_dnr_audit_trail import (
    GetDNRAuditTrailQueryHandler,
)
from prometheus.application.inventory.query_handlers.get_dnr_by_id import (
    GetDNRByIdQueryHandler,
)
from prometheus.application.inventory.query_handlers.get_dnrs import GetDNRsQueryHandler
from prometheus.application.inventory.query_handlers.get_inventory_blocks import (
    SearchInventoryBlockQueryHandler,
)
from prometheus.application.inventory.query_handlers.get_occupied_rooms import (
    GetOccupiedRoomsQueryHandlers,
)
from prometheus.common.api_response import ApiResponse
from prometheus.common.decorators import authorize_write_op
from prometheus.common.request_parsers import read_user_data_from_request_header
from prometheus.common.serializers import (
    BulkMarkDNRResponse,
    BulkMarkDnrSchema,
    BulkResolveDnrSchema,
    InventoryBlockFilterRequest,
    UpdateInventoryBlocks,
)
from prometheus.common.serializers.request import (
    EditDNRSchema,
    GetAvailableRoomAllotmentsSchema,
    GetCheckedInRoomsSchema,
    GetDNRSchema,
    GetRoomAllotmentsSchema,
    GetRoomTypeInventorySchema,
    MarkDNRSchema,
)
from prometheus.common.serializers.request.crs_migration import InventorySetupSchema
from prometheus.common.serializers.response.inventory import (
    CheckedInRoomResponseSchema,
    DNRAuditTrailSchema,
    DNRResponseSchema,
    InventoryBlockResponse,
    RoomAllotmentAvailabilityResponseSchema,
    RoomTypeInventoryResponseSchema,
)
from prometheus.core.api_docs import swag_route
from prometheus.domain.inventory.dtos.inventory_block_dto import InventoryBlockFilterDTO
from shared_kernel.api_helpers.request_parsers import (
    RequestTypes,
    schema_wrapper_and_version_parser,
    schema_wrapper_parser,
)

bp = Blueprint('Inventory', __name__, url_prefix="/v1")
logger = logging.getLogger(__name__)

__all__ = [
    'mark_dnr',
    'remove_dnr',
    'resolve_dnr',
    'get_dnrs',
    'edit_dnr',
    'get_dnr_audit_trail',
    'sync_inventories',
    'get_vacant_room_allotments',
    'get_rooms_with_vacant_allotment_between_dates',
    'get_rooms',
    'bulk_resolve_dnr',
    'get_inventory_blocks',
]


@swag_route
@bp.route('/sync-inventories', methods=['POST'])
@authorize_write_op
@inject(command_handler=ScheduleInventorySyncForAllHotelsCommandHandler)
def sync_inventories_for_all_hotels(
    command_handler: ScheduleInventorySyncForAllHotelsCommandHandler,
):
    months = 13
    command_handler.handle(months=months)
    return ApiResponse.build(status_code=200)


@swag_route
@bp.route('/hotels/<hotel_id>/available-room-slots', methods=['GET'])
@schema_wrapper_parser(GetAvailableRoomAllotmentsSchema, param_type=RequestTypes.ARGS)
@inject(query_handler=GetAvailableRoomSlotsQueryHandlers)
def get_rooms_with_vacant_allotment_between_dates(
    query_handler: GetAvailableRoomSlotsQueryHandlers, hotel_id, parsed_request
):
    """Get all available room allotments grouped by room_id, for the given hotel_id, between a given start and end date
    ---
    operationId: get_rooms_with_vacant_allotment_between_dates
    consumes:
        - application/json
    produces:
        - application/json
    schemes: ['http', 'https']
    deprecated: false
    get:
        parameters:
            - name: hotel_id
              in: path
              type: string
              required: true
            - name: date_filter
              in: query
              description: Filter based on start and end date
              schema:
                $ref: "#/components/schemas/GetAvailableRoomAllotmentsSchema"
        description: Get all available room allotments grouped by room_id, for the given hotel_id, between a given
            start and end date
        tags:
            - Inventories
        responses:
            200:
                description: Available allotments for all available rooms
                schema:
                    type: object
                    properties:
                        data:
                            type: array
                            items:
                                $ref: "#/components/schemas/RoomAllotmentAvailabilityResponseSchema"
                        meta:
                            type: object
                            additionalProperties: {}
                        errors:
                            type: array
                            items:
                                $ref: "#/components/schemas/ApiErrorSchema"
    """
    room_allotments = query_handler.handle(
        hotel_id,
        parsed_request.get('from_date'),
        parsed_request.get('to_date'),
        parsed_request.get('room_type_id'),
        for_checkin=parsed_request.get('for_checkin'),
    )
    room_allotment_response_schema = RoomAllotmentAvailabilityResponseSchema(many=True)
    response = room_allotment_response_schema.dump(room_allotments)
    return ApiResponse.build(status_code=200, data=response.data)


@swag_route
@bp.route('/hotels/<hotel_id>/rooms/<room_id>/available-room-slots', methods=['GET'])
@schema_wrapper_parser(GetRoomAllotmentsSchema, param_type=RequestTypes.ARGS)
@inject(query_handler=GetAvailableRoomSlotsQueryHandlers)
def get_vacant_room_allotments(
    query_handler: GetAvailableRoomSlotsQueryHandlers, hotel_id, room_id, parsed_request
):
    """Get room allotments for the given hotel_id and room_id, between a given start and end date
    ---
    operationId: get_vacant_room_allotments
    consumes:
        - application/json
    produces:
        - application/json
    schemes: ['http', 'https']
    deprecated: false
    get:
        parameters:
            - name: hotel_id
              in: path
              type: string
              required: true
            - name: room_id
              in: path
              type: string
              required: true
            - name: date_filter
              in: query
              description: Filter based on start and end date
              schema:
                $ref: "#/components/schemas/GetRoomAllotmentsSchema"
        description: Get room allotments for given hotel_id and room_id between start and end date
        tags:
            - Inventories
        responses:
            200:
                description: A list of room allotments
                schema:
                    type: object
                    properties:
                        data:
                            type: array
                            items:
                                $ref: "#/components/schemas/RoomAllotmentAvailabilityResponseSchema"
                        meta:
                            type: object
                            additionalProperties: {}
                        errors:
                            type: array
                            items:
                                $ref: "#/components/schemas/ApiErrorSchema"
    """
    room_allotments = query_handler.handle(
        hotel_id,
        parsed_request.get('from_date'),
        parsed_request.get('to_date'),
        room_id=room_id,
        for_checkin=parsed_request.get('for_checkin'),
    )
    room_allotment_response_schema = RoomAllotmentAvailabilityResponseSchema(many=True)
    response = room_allotment_response_schema.dump(room_allotments)
    return ApiResponse.build(status_code=200, data=response.data)


@swag_route
@bp.route('/hotels/<hotel_id>/rooms', methods=['GET'])
@schema_wrapper_parser(GetCheckedInRoomsSchema, param_type=RequestTypes.ARGS)
@inject(query_handler=GetOccupiedRoomsQueryHandlers)
def get_rooms(query_handler: GetOccupiedRoomsQueryHandlers, hotel_id, parsed_request):
    """Get rooms for the given hotel_id for given status
    ---
    operationId: get_rooms
    consumes:
        - application/json
    produces:
        - application/json
    schemes: ['http', 'https']
    deprecated: false
    get:
        parameters:
            - name: hotel_id
              in: path
              type: string
              required: true
        description: Get rooms for given hotel_id
        tags:
            - Inventories
        responses:
            200:
                description: A list of rooms
                schema:
                    type: object
                    properties:
                        data:
                            type: array
                            items:
                                $ref: "#/components/schemas/CheckedInRoomResponseSchema"
                        meta:
                            type: object
                            additionalProperties: {}
                        errors:
                            type: array
                            items:
                                $ref: "#/components/schemas/ApiErrorSchema"
    """
    rooms = query_handler.handle(hotel_id)
    rooms_response_schema = CheckedInRoomResponseSchema(many=True)
    response = rooms_response_schema.dump(rooms)
    return ApiResponse.build(status_code=200, data=response.data)


@swag_route
@bp.route('/hotels/<hotel_id>/dnrs', methods=['POST'])
@authorize_write_op
@schema_wrapper_parser(MarkDNRSchema)
@inject(command_handler=MarkDNRsCommandHandler)
def mark_dnr(command_handler: MarkDNRsCommandHandler, hotel_id, parsed_request):
    """Marks a room as DNR for given dates
    ---
    operationId: mark_dnr
    consumes:
        - application/json
    produces:
        - application/json
    schemes: ['http', 'https']
    deprecated: false
    post:
        description: Mark a room as DNR for given dates
        tags:
            - Inventories
        parameters:
            - name: hotel_id
              in: path
              type: string
              required: true
            - in: body
              name: body
              description: The DNR object that will be created for corresponding room
              required: True
              schema:
                type: object
                properties:
                    data:
                        $ref: "#/components/schemas/MarkDNRSchema"
        responses:
            200:
                description: Detail of DNR created with id
                schema:
                    type: object
                    properties:
                        data:
                            $ref: "#/components/schemas/DNRResponseSchema"
                        meta:
                            type: object
                            additionalProperties: {}
                        errors:
                            type: array
                            items:
                                $ref: "#/components/schemas/ApiErrorSchema"
    """
    user_data = read_user_data_from_request_header()
    dnrs = command_handler.handle(hotel_id, [parsed_request], user_data)
    response = DNRResponseSchema().dump(dnrs[0])
    return ApiResponse.build(status_code=201, data=response.data)


@swag_route
@bp.route('/hotels/<hotel_id>/dnrs-list', methods=['POST'])
@authorize_write_op
@schema_wrapper_parser(BulkMarkDnrSchema)
@inject(command_handler=MarkDNRsCommandHandler)
def mark_bulk_dnrs(command_handler: MarkDNRsCommandHandler, hotel_id, parsed_request):
    """Marks multiple rooms as DNR for given dates
    ---
    operationId: mark_bulk_dnrs
    consumes:
        - application/json
    produces:
        - application/json
    schemes: ['http', 'https']
    deprecated: false
    post:
        description: Mark multiple rooms as DNR for given dates
        tags:
            - Inventories
        parameters:
            - name: hotel_id
              in: path
              type: string
              required: true
            - in: body
              name: body
              description: The DNR objects that will be created for corresponding room
              required: True
              schema:
                type: object
                properties:
                    data:
                        $ref: "#/components/schemas/BulkMarkDnrSchema"
        responses:
            200:
                description: Detail of DNR created with id
                schema:
                    type: object
                    properties:
                        data:
                            $ref: "#/components/schemas/BulkMarkDNRResponse"
                        meta:
                            type: object
                            additionalProperties: {}
                        errors:
                            type: array
                            items:
                                $ref: "#/components/schemas/ApiErrorSchema"
    """
    user_data = read_user_data_from_request_header()
    dnrs = command_handler.handle(hotel_id, parsed_request.get('dnrs'), user_data)
    response = BulkMarkDNRResponse().dump(dict(dnrs=dnrs))
    return ApiResponse.build(status_code=201, data=response.data)


@swag_route
@bp.route('/hotels/<hotel_id>/dnrs/<dnr_id>', methods=['PATCH'])
@authorize_write_op
@schema_wrapper_and_version_parser(EditDNRSchema)
@inject(command_handler=EditDNRCommandHandler)
def edit_dnr(
    command_handler: EditDNRCommandHandler,
    hotel_id,
    dnr_id,
    resource_version,
    parsed_request,
):
    """Edits the given DNR
    ---
    operationId: edit_dnr
    consumes:
        - application/json
    produces:
        - application/json
    schemes: ['http', 'https']
    deprecated: false
    patch:
        description: Edits the given DNR
        tags:
            - Inventories
        parameters:
            - name: hotel_id
              in: path
              type: string
              required: true
            - name: dnr_id
              in: path
              type: string
              required: true
            - in: body
              name: body
              description: The data for editing DNR
              required: True
              schema:
                type: object
                properties:
                    resource_version:
                        type: integer
                    data:
                        $ref: "#/components/schemas/EditDNRSchema"
        responses:
            200:
                description: Detail of DNR created with id
                schema:
                    type: object
                    properties:
                        data:
                            $ref: "#/components/schemas/DNRResponseSchema"
                        meta:
                            type: object
                            additionalProperties: {}
                        errors:
                            type: array
                            items:
                                $ref: "#/components/schemas/ApiErrorSchema"
    """
    user_data = read_user_data_from_request_header()
    dnr = command_handler.handle(
        hotel_id, dnr_id, resource_version, parsed_request, user_data
    )
    response = DNRResponseSchema().dump(dnr)
    return ApiResponse.build(status_code=200, data=response.data)


@swag_route
@bp.route('/hotels/<hotel_id>/dnrs/<dnr_id>', methods=['DELETE'])
@authorize_write_op
@inject(command_handler=ResolveDNRCommandHandler)
def remove_dnr(command_handler: ResolveDNRCommandHandler, hotel_id, dnr_id):
    """Removes the dnr with the given id
    ---
    operationId: remove_dnr
    consumes:
        - application/json
    produces:
        - application/json
    schemes: ['http', 'https']
    deprecated: false
    delete:
        parameters:
            - name: hotel_id
              in: path
              type: string
              required: true
            - name: dnr_id
              in: path
              type: string
              required: true
        description: Removes the dnr with the given id
        tags:
            - Inventories
        responses:
            200:
                description: Removed DNR
                schema:
                    type: object
                    properties:
                        data:
                            $ref: "#/components/schemas/DNRResponseSchema"
                        meta:
                            type: object
                            additionalProperties: {}
                        errors:
                            type: array
                            items:
                                $ref: "#/components/schemas/ApiErrorSchema"
    """
    user_data = read_user_data_from_request_header()
    dnr = command_handler.handle(hotel_id, dnr_id, user_data)
    response = DNRResponseSchema().dump(dnr)
    return ApiResponse.build(status_code=200, data=response.data)


@swag_route
@bp.route('/hotels/<hotel_id>/dnrs/<dnr_id>/resolve-dnr', methods=['POST'])
@authorize_write_op
@inject(command_handler=ResolveDNRCommandHandler)
def resolve_dnr(command_handler: ResolveDNRCommandHandler, hotel_id, dnr_id):
    """Marks the dnr with the given id as Inactive
    ---
    operationId: resolve_dnr
    consumes:
        - application/json
    produces:
        - application/json
    schemes: ['http', 'https']
    deprecated: false
    post:
        parameters:
            - name: hotel_id
              in: path
              type: string
              required: true
            - name: dnr_id
              in: path
              type: string
              required: true
        description: Marks the dnr with the given id as Inactive
        tags:
            - Inventories
        responses:
            200:
                description: Inactivated DNR
                schema:
                    type: object
                    properties:
                        data:
                            $ref: "#/components/schemas/DNRResponseSchema"
                        meta:
                            type: object
                            additionalProperties: {}
                        errors:
                            type: array
                            items:
                                $ref: "#/components/schemas/ApiErrorSchema"
    """
    user_data = read_user_data_from_request_header()
    dnr = command_handler.handle(hotel_id, dnr_id, user_data)
    response = DNRResponseSchema().dump(dnr)
    return ApiResponse.build(status_code=200, data=response.data)


@swag_route
@bp.route('/hotels/<hotel_id>/dnrs/<dnr_id>', methods=['GET'])
@inject(query_handler=GetDNRByIdQueryHandler)
def get_dnr(query_handler: GetDNRByIdQueryHandler, hotel_id, dnr_id):
    """Fetch the DNR by dnr_id, under the given hotel_id
    ---
    operationId: get_dnr
    consumes:
        - application/json
    produces:
        - application/json
    schemes: ['http', 'https']
    deprecated: false
    get:
        parameters:
            - name: hotel_id
              in: path
              type: string
              required: true
            - name: dnr_id
              in: path
              type: string
              required: true
        description: Fetch the DNR by dnr_id, under the given hotel_id
        tags:
            - Inventories
        responses:
            200:
                description: DNR entity
                schema:
                    type: object
                    properties:
                        data:
                            $ref: "#/components/schemas/DNRResponseSchema"
                        meta:
                            type: object
                            additionalProperties: {}
                        errors:
                            type: array
                            items:
                                $ref: "#/components/schemas/ApiErrorSchema"
    """
    dnr = query_handler.handle(hotel_id, dnr_id)
    response = DNRResponseSchema().dump(dnr)
    return ApiResponse.build(status_code=200, data=response.data)


@swag_route
@bp.route('/hotels/<hotel_id>/dnrs', methods=['GET'])
@schema_wrapper_parser(GetDNRSchema, param_type=RequestTypes.ARGS)
@inject(query_handler=GetDNRsQueryHandler)
def get_dnrs(query_handler: GetDNRsQueryHandler, hotel_id, parsed_request):
    """Get the list of DNRs created on given hotel_id between start and end date
    ---
    operationId: get_dnrs
    consumes:
        - application/json
    produces:
        - application/json
    schemes: ['http', 'https']
    deprecated: false
    get:
        parameters:
            - name: hotel_id
              in: path
              type: string
              required: true
            - name: date_filter
              in: query
              description: Filter based on start and end date
              schema:
                $ref: "#/components/schemas/GetDNRSchema"
        description: Get the list of DNRs created on given hotel_id between start and end date
        tags:
            - Inventories
        responses:
            200:
                description: A list of DNRs created on given hotel_id between start and end date
                schema:
                    type: object
                    properties:
                        data:
                            type: array
                            items:
                                $ref: "#/components/schemas/DNRResponseSchema"
                        meta:
                            type: object
                            additionalProperties: {}
                        errors:
                            type: array
                            items:
                                $ref: "#/components/schemas/ApiErrorSchema"
    """
    dnrs, _ = query_handler.handle(hotel_id, parsed_request)
    response = DNRResponseSchema(many=True).dump(dnrs)
    return ApiResponse.build(status_code=200, data=response.data)


@swag_route
@bp.route('/hotels/<hotel_id>/dnrs/<dnr_id>/audit-trail', methods=['GET'])
@inject(query_handler=GetDNRAuditTrailQueryHandler)
def get_dnr_audit_trail(query_handler: GetDNRAuditTrailQueryHandler, hotel_id, dnr_id):
    """Get DNR Audit Trail
    ---
    parameters:
          - name: hotel_id
            in: path
            type: string
            required: true
          - name: dnr_id
            in: path
            type: string
            required: true
    operationId: get_dnr_audit_trail
    consumes:
        - application/json
    produces:
        - application/json
    schemes: ['http', 'https']
    deprecated: false
    get:
        description: Get audit trail for the given dnr
        tags:
            - AuditTrail
        responses:
            200:
                description: A list of audit trail for this dnr
                schema:
                    type: object
                    properties:
                        data:
                            $ref: "#/components/schemas/DNRAuditTrailSchema"
                        meta:
                            type: object
                            additionalProperties: {}
                        errors:
                            type: array
                            items:
                                $ref: "#/components/schemas/ApiErrorSchema"
    """
    audit_trail_aggregates = query_handler.handle(dnr_id)
    audit_trails = [aggregate.dnr_audit_trail for aggregate in audit_trail_aggregates]
    response = DNRAuditTrailSchema(many=True).dump(audit_trails).data
    return ApiResponse.build(data=response, status_code=200)


@swag_route
@bp.route('/hotels/<hotel_id>/room-type-inventories', methods=['GET'])
@schema_wrapper_parser(GetRoomTypeInventorySchema, param_type=RequestTypes.ARGS)
@inject(query_handler=GetAvailabilityQueryHandler)
def get_room_type_inventories(
    query_handler: GetAvailabilityQueryHandler, hotel_id, parsed_request
):
    """Get room type inventories for the given hotel_id, between a given start and end date
    ---
    operationId: get_room_type_inventories
    consumes:
        - application/json
    produces:
        - application/json
    schemes: ['http', 'https']
    deprecated: false
    get:
        parameters:
            - name: hotel_id
              in: path
              type: string
              required: true
            - name: date_filter
              in: query
              description: Filter based on start and end date
              schema:
                $ref: "#/components/schemas/GetRoomTypeInventorySchema"
        description: Get room type inventories for the given hotel_id, between a given start and end date
        tags:
            - Inventories
        responses:
            200:
                description: A list of room type inventory details.
                schema:
                    type: object
                    properties:
                        data:
                            type: array
                            items:
                                $ref: "#/components/schemas/RoomTypeInventoryResponseSchema"
                        meta:
                            type: object
                            additionalProperties: {}
                        errors:
                            type: array
                            items:
                                $ref: "#/components/schemas/ApiErrorSchema"
    """
    room_type_inventories = query_handler.handle(
        hotel_id,
        parsed_request.get('from_date'),
        parsed_request.get('to_date'),
        parsed_request.get('room_type_id'),
    )
    response = RoomTypeInventoryResponseSchema(many=True).dump(room_type_inventories)
    return ApiResponse.build(status_code=200, data=response.data)


@swag_route
@bp.route('/hotels/<hotel_id>/sync-inventories', methods=['POST'])
@authorize_write_op
@schema_wrapper_parser(InventorySetupSchema, param_type=RequestTypes.ARGS)
@inject(command_handler=SyncInventoriesCommandHandler)
def sync_inventories(
    command_handler: SyncInventoriesCommandHandler, hotel_id, parsed_request
):
    """Sets up the RoomInventory, RoomTypeInventory and RoomCurrentStatus, for all rooms and room types
    configured for the given hotel

    ---
    operationId: sync_inventories
    consumes:
        - application/json
    produces:
        - application/json
    schemes: ['http', 'https']
    deprecated: false
    post:
        parameters:
            - name: inventory duration
              in: query
              required: true
              schema:
                $ref: "#/components/schemas/InventorySetupSchema"
            - name: hotel_id
              in: path
              type: string
              required: true
        tags:
            - Inventories
        responses:
            201: None
    """
    start_date = parsed_request.get('from_date')
    end_date = parsed_request.get('to_date')
    push_all = parsed_request.get('push_all')
    command_handler.handle(hotel_id, start_date, end_date, push_all=push_all)
    return ApiResponse.build(data="Inventory setup complete", status_code=201)


@swag_route
@bp.route('/hotels/<hotel_id>/dnrs/bulk-resolve-dnr', methods=['POST'])
@authorize_write_op
@schema_wrapper_parser(BulkResolveDnrSchema)
@inject(command_handler=BulkResolveDNRCommandHandler)
def bulk_resolve_dnr(
    command_handler: BulkResolveDNRCommandHandler, hotel_id, parsed_request
):
    """Marks the dnr with the given id as Inactive
    ---
    operationId: resolve_dnr
    consumes:
        - application/json
    produces:
        - application/json
    schemes: ['http', 'https']
    deprecated: false
    post:
        parameters:
            - name: hotel_id
              in: path
              type: string
              required: true
            - in: body
              name: body
              description: The list of dnr ids
              required: True
              schema:
                type: object
                properties:
                    data:
                        $ref: "#/components/schemas/BulkResolveDnrSchema"
        description: Bulk Marks the dnr with the given ids as Inactive
        tags:
            - Inventories
        responses:
            200:
                description: Inactivated DNR
                schema:
                    type: object
                    properties:
                        data:
                            type: array
                            items:
                                $ref: "#/components/schemas/DNRResponseSchema"
                        meta:
                            type: object
                            additionalProperties: {}
                        errors:
                            type: array
                            items:
                                $ref: "#/components/schemas/ApiErrorSchema"
    """
    user_data = read_user_data_from_request_header()
    dnrs = command_handler.handle(hotel_id, parsed_request.get('dnr_ids'), user_data)
    response = DNRResponseSchema().dump(dnrs, many=True)
    return ApiResponse.build(status_code=200, data=response.data)


@swag_route
@bp.route('/hotels/<hotel_id>/inventory-blocks', methods=['POST'])
@authorize_write_op
@schema_wrapper_parser(UpdateInventoryBlocks)
@inject(command_handler=UpdateInventoryBlockCommandHandler)
def update_inventory_blocks(
    command_handler: UpdateInventoryBlockCommandHandler,
    hotel_id: str,
    parsed_request: UpdateInventoryBlocks,
):
    """Update Inventory Blocks and handles inventory
    ---
    operationId: update_inventory_blocks
    consumes:
        - application/json
    produces:
        - application/json
    schemes: ['http', 'https']
    deprecated: false
    post:
        parameters:
            - name: hotel_id
              in: path
              type: string
              required: true
            - in: body
              name: body
              description: The list of inventory block
              required: True
              schema:
                type: object
                properties:
                    data:
                        $ref: "#/components/schemas/UpdateInventoryBlocks"
        description: Handles Inventory Blocks and blocks inventory
        tags:
            - Inventories
        responses:
            200:
                description: Updated Inventory Blocks
                schema:
                    type: object
                    properties:
                        data:
                            type: array
                            items:
                                $ref: "#/components/schemas/InventoryBlockResponse"
                        meta:
                            type: object
                            additionalProperties: {}
                        errors:
                            type: array
                            items:
                                $ref: "#/components/schemas/ApiErrorSchema"
    """
    user_data = read_user_data_from_request_header()
    inventory_blocks = command_handler.handle(
        parsed_request.action,
        hotel_id,
        parsed_request.booking_id,
        parsed_request.inventory_block_dtos,
        parsed_request.block_ids,
        user_data=user_data,
    )
    response = InventoryBlockResponse.dump(inventory_blocks, many=True)
    return ApiResponse.build(status_code=200, data=response)


@swag_route
@bp.route('/hotels/<hotel_id>/inventory-blocks', methods=['GET'])
@schema_wrapper_parser(InventoryBlockFilterRequest, param_type=RequestTypes.ARGS)
@inject(query_handler=SearchInventoryBlockQueryHandler)
def get_inventory_blocks(
    query_handler: SearchInventoryBlockQueryHandler,
    hotel_id,
    parsed_request: InventoryBlockFilterRequest,
):
    """Fetch Inventory Blocks
    ---
    operationId: get_inventory_blocks
    consumes:
        - application/json
    produces:
        - application/json
    schemes: ['http', 'https']
    deprecated: false
    get:
        parameters:
            - name: hotel_id
              in: path
              type: string
              required: true
            - name: parsed_request
              in: query
              description: Filter search inventory blocks
              schema:
                $ref: "#/components/schemas/InventoryBlockFilterRequest"
        description: Fetch Inventory Blocks
        tags:
            - Inventories
        responses:
            200:
                description: Fetched Inventory Blocks
                schema:
                    type: object
                    properties:
                        data:
                            type: array
                            items:
                                $ref: "#/components/schemas/InventoryBlockResponse"
                        meta:
                            type: object
                            additionalProperties: {}
                        errors:
                            type: array
                            items:
                                $ref: "#/components/schemas/ApiErrorSchema"
    """
    filter_dto: InventoryBlockFilterDTO = parsed_request.to_dto(hotel_id)
    inventory_blocks = query_handler.handle(hotel_id, filter_dto)

    response = InventoryBlockResponse.dump(inventory_blocks, many=True)
    return ApiResponse.build(status_code=200, data=response)
