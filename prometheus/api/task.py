from flask import Blueprint

from object_registry import inject
from prometheus.common.api_response import ApiResponse
from prometheus.common.serializers.request.tasks import ESDataSyncTaskSchedulerSchema
from prometheus.elastic_search.constants import ES_DATA_SYNC_ROUTING_KEY
from prometheus.elastic_search.es_data_sync_publisher import (
    ESDataSyncEvent,
    ESDataSyncJobPublisher,
)
from shared_kernel.api_helpers.request_parsers import schema_wrapper_parser

bp = Blueprint('AsyncTaskScheduler', __name__, url_prefix="/v1")


@bp.route('/task/es-data-sync', methods=['POST'])
@inject(es_data_sync_event_publisher=ESDataSyncJobPublisher)
@schema_wrapper_parser(ESDataSyncTaskSchedulerSchema)
def schedule_es_data_task(
    es_data_sync_event_publisher: ESDataSyncJobPublisher, parsed_request
):
    event = ESDataSyncEvent(body=parsed_request, routing_key=ES_DATA_SYNC_ROUTING_KEY)
    es_data_sync_event_publisher.publish(event)
    return ApiResponse.build(status_code=200)
