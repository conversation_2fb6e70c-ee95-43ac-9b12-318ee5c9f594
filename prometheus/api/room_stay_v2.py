import logging
from typing import List

from flask import Blueprint

from object_registry import inject
from prometheus.application.booking.command_handlers.update_room_stay_prices_v2 import (
    UpdateRoomStayPricesCommandHandler,
)
from prometheus.application.booking.query_handlers.get_room_stay_v2 import (
    GetRoomStayV2QueryHandler,
)
from prometheus.common.api_response import ApiResponse
from prometheus.common.decorators import authorize_write_op
from prometheus.common.request_parsers import read_user_data_from_request_header
from prometheus.common.serializers.request.room_stay_v2 import (
    UpdateRoomStayPriceSchemaV2,
)
from prometheus.common.serializers.response.room_stay import RoomStaySchema
from prometheus.common.serializers.response.room_stay_v2 import (
    ShallowRoomStayV2ResponseSchema,
)
from prometheus.core.api_docs import swag_route
from shared_kernel.api_helpers.request_parsers import schema_wrapper_and_version_parser
from ths_common.constants.user_constants import UserType
from ths_common.value_objects import EditRoomPriceDtoV2

bp = Blueprint('RoomStayV2', __name__, url_prefix="/v2")
logger = logging.getLogger(__name__)


@swag_route
@bp.route(
    '/bookings/<string:booking_id>/room-stays/<int:room_stay_id>/prices',
    methods=['PATCH'],
)
@authorize_write_op
@schema_wrapper_and_version_parser(UpdateRoomStayPriceSchemaV2, many=True)
@inject(command_handler=UpdateRoomStayPricesCommandHandler)
def update_room_stay_prices(
    command_handler: UpdateRoomStayPricesCommandHandler,
    booking_id,
    room_stay_id,
    resource_version,
    parsed_request: List[EditRoomPriceDtoV2],
):
    """API to update room night charges, along with inclusions (if associated with room night charge)
    ---
    operationId: update_room_stay_prices
    consumes:
        - application/json
    produces:
        - application/json
    schemes: ['http', 'https']
    deprecated: false
    patch:
        tags:
            - Bookings
        parameters:
            - name: booking_id
              in: path
              type: string
              required: true
            - name: room_stay_id
              in: path
              type: string
              required: true
            - in: body
              name: body
              description: Updated room stay prices
              required: True
              schema:
                type: object
                properties:
                    resource_version:
                        type: integer
                    data:
                        type: array
                        items:
                            $ref: "#/components/schemas/UpdateRoomStayPriceSchemaV2"
        responses:
            200:
                description: The updated room stay data
                schema:
                    type: object
                    properties:
                        resource_version:
                            type: integer
                        data:
                            $ref: "#/components/schemas/RoomStaySchema"
                        meta:
                            type: object
                            additionalProperties: {}
                        errors:
                            type: array
                            items:
                                $ref: "#/components/schemas/ApiErrorSchema"
    """
    user_data = read_user_data_from_request_header()
    edit_room_price_dtos = parsed_request
    room_stay, resource_version = command_handler.handle(
        booking_id, room_stay_id, resource_version, edit_room_price_dtos, user_data
    )
    response = RoomStaySchema().dump(room_stay)
    return ApiResponse.build(
        status_code=200, data=response.data, resource_version=resource_version
    )


@swag_route
@bp.route(
    '/bookings/<string:booking_id>/room-stays/<int:room_stay_id>', methods=['GET']
)
@inject(query_handler=GetRoomStayV2QueryHandler)
def get_room_stay(query_handler: GetRoomStayV2QueryHandler, booking_id, room_stay_id):
    """Get Room Stay Details V2
    ---
    parameters:
          - name: booking_id
            in: path
            type: string
            required: true
          - name: room_stay_id
            in: path
            type: integer
            required: true
    operationId: get_room_stay
    consumes:
        - application/json
    produces:
        - application/json
    schemes: ['http', 'https']
    deprecated: false
    get:
        description: Get room detail information for a given booking.
        tags:
            - Bookings
        responses:
            200:
                description: A deep detail of the Room stay object.
                schema:
                    type: object
                    properties:
                        resource_version:
                            type: integer
                        data:
                            $ref: "#/definitions/ShallowRoomStayV2ResponseSchema"
                        meta:
                            type: object
                            additionalProperties: {}
                        errors:
                            type: array
                            items:
                                $ref: "#/definitions/ApiErrorSchema"
    """
    user_data = read_user_data_from_request_header(default=UserType.VIEW_ONLY.value)
    room_stay, resource_version = query_handler.handle(
        booking_id, room_stay_id, user_data
    )
    response = ShallowRoomStayV2ResponseSchema().dump(room_stay)
    return ApiResponse.build(
        status_code=200, data=response.data, resource_version=resource_version
    )
