from decimal import Decimal

from treebo_commons.money import Money

from prometheus.reporting.base_report_aggregate import BaseReportAggregate
from prometheus.reporting.finance_erp_reporting.constants import Mop, finance_context
from prometheus.reporting.finance_erp_reporting.utils import sanitize_string, to_dmy_str
from ths_common.constants.billing_constants import (
    ChargeStatus,
    PaymentModes,
    PaymentStatus,
    PaymentTypes,
)


class OtaCommissionReportAggregate(BaseReportAggregate):
    TDS_PER_CODE = '194H'

    def __init__(self, booking_aggregate, bill_aggregate):
        self.booking_aggregate = booking_aggregate
        self.bill_aggregate = bill_aggregate

    @property
    def posting_date(self):
        return to_dmy_str(finance_context.posting_date)

    @property
    def paid_at_ota(self):
        payment_amount = Money(0, self.bill_aggregate.bill.base_currency)
        for payment in self.active_ota_prepaid_payments():
            if payment.payment_type == PaymentTypes.PAYMENT:
                payment_amount += payment.amount
            elif payment.payment_type == PaymentTypes.REFUND:
                payment_amount -= payment.amount
        return payment_amount.amount

    @property
    def commission_amount(self):
        return 0

    @property
    def tds_percentage(self):
        # TODO: Remove this later. AllE has to handle it
        return self.TDS_PER_CODE

    @property
    def reference_number(self):
        return self.booking_aggregate.booking.reference_number

    @property
    def hotel_code(self):
        return self.booking_aggregate.booking.hotel_id

    @property
    def booking_created_date(self):
        return to_dmy_str(self.booking_aggregate.booking.created_at)

    @property
    def check_in(self):
        return to_dmy_str(self.booking_aggregate.booking.checkin_date)

    @property
    def check_out(self):
        return to_dmy_str(self.booking_aggregate.booking.checkout_date)

    @property
    def pretax_room_rent(self):
        return sum(
            [
                charge.pretax_amount_post_allowance.amount
                for charge in self.get_room_stay_charges()
            ]
        )

    @property
    def guest_name(self):
        return sanitize_string(
            self.booking_aggregate.get_booking_owner().name.__str__(), 50
        )

    @property
    def ota_name(self):
        return self.booking_aggregate.booking.source.subchannel_code

    @property
    def commission_percent(self):
        return 0

    @property
    def mop(self):
        return Mop.PREPAID if self.paid_at_ota else Mop.PAH

    def active_ota_prepaid_payments(self):
        return [
            payment
            for payment in self.bill_aggregate.payments
            if payment.status != PaymentStatus.CANCELLED
            and not payment.deleted
            and payment.payment_mode == PaymentModes.PAID_AT_OTA
        ]

    def get_room_stay_charges(self):
        charge_ids = self.booking_aggregate.get_room_stay_charges(
            [room_stay.room_stay_id for room_stay in self.booking_aggregate.room_stays]
        )
        charges = [
            charge
            for charge in self.bill_aggregate.filter_and_get_charges(charge_ids)
            if charge.status != ChargeStatus.CANCELLED
        ]
        charges.extend(self.additional_room_related_charges)
        return charges

    @property
    def additional_room_related_charges(self):
        expense_charge_ids = [
            expense.charge_id
            for room_stay in self.booking_aggregate.room_stays
            for expense in self.booking_aggregate.get_active_expenses_for_room_stay(
                room_stay.room_stay_id
            )
        ]
        expense_charges = [
            charge
            for charge in self.bill_aggregate.filter_and_get_charges(expense_charge_ids)
            if charge.status != ChargeStatus.CANCELLED
            and charge.item.sku_category_id == 'stay'
        ]
        return expense_charges
