from decimal import Decimal

from prometheus.reporting.finance_erp_reporting.constants import (
    DocTypes,
    TaxEntryTypeKey,
    finance_context,
)
from prometheus.reporting.finance_erp_reporting.utils import to_dmy_str


class SettlementTaxAggregate(object):
    def __init__(self, hotel_id, entry_type_data: TaxEntryTypeKey, settlement):
        self.hotel_code = hotel_id
        self.entry_type = entry_type_data.tax_entry_type
        self._total_amount = (
            float(settlement.get(entry_type_data.tax_entry_type_field))
            if isinstance(settlement.get(entry_type_data.tax_entry_type_field), str)
            else settlement.get(entry_type_data.tax_entry_type_field)
        )

    @property
    def posting_date(self):
        return to_dmy_str(finance_context.settlement_date)

    @property
    def cgst_amount(self):
        return round(self.total_amount / 2, 2)

    @property
    def sgst_amount(self):
        return round(self.total_amount / 2, 2)

    @property
    def total_amount(self):
        return abs(self._total_amount)

    @property
    def is_non_zero_entry(self):
        return self._total_amount and self._total_amount != Decimal('0')

    @property
    def doc_type(self):
        return DocTypes.ORDER if self._total_amount < 0 else DocTypes.CREDIT
