from prometheus.reporting.base_report_aggregate import BaseReportAggregate
from prometheus.reporting.finance_erp_reporting.external_finance_reports.settlement_report.hotel_adjustment_aggregate import (
    HotelAdjustmentAggregate,
)
from prometheus.reporting.finance_erp_reporting.external_finance_reports.settlement_report.settlement_expense_aggregate import (
    SettlementExpenseAggregate,
)
from prometheus.reporting.finance_erp_reporting.external_finance_reports.settlement_report.settlement_loan_aggregate import (
    SettlementLoanAggregate,
)
from prometheus.reporting.finance_erp_reporting.external_finance_reports.settlement_report.settlement_tax_aggregate import (
    SettlementTaxAggregate,
)
from prometheus.reporting.finance_erp_reporting.external_finance_reports.settlement_report.settlement_treebo_fee_aggregate import (
    SettlementTreeboFeeAggregate,
)


class SettlementReportAggregate(BaseReportAggregate):
    def __init__(
        self,
        treebo_fee_aggregates: [SettlementTreeboFeeAggregate] = None,
        tax_aggregates: [SettlementTaxAggregate] = None,
        loan_aggregates: [SettlementLoanAggregate] = None,
        expense_aggregates: [SettlementExpenseAggregate] = None,
        hotel_adjustment_aggregates: [HotelAdjustmentAggregate] = None,
    ):
        self.treebo_fee_aggregates = treebo_fee_aggregates
        self.tax_aggregates = tax_aggregates
        self.loan_aggregates = loan_aggregates
        self.expense_aggregates = expense_aggregates
        self.hotel_adjustment_aggregates = hotel_adjustment_aggregates
