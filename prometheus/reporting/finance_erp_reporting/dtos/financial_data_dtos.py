from prometheus import crs_context
from prometheus.reporting.finance_erp_reporting.constants import RevenueCenters
from ths_common.constants.billing_constants import PaymentModes, PaymentTypes


class PseudoPaymentDetails:
    def __init__(self, folio_detail, credit_folio, is_refund=False, company_code=None):
        self.bill_id = folio_detail.bill_id
        self.hotel_id = folio_detail.hotel_id
        self.parent_reference_number = folio_detail.booking_id
        self.category = folio_detail.category
        self.owner_name = folio_detail.owner_name
        self.folio_number = folio_detail.folio_number
        self.billed_entity_id = folio_detail.billed_entity_id
        self.account_number = folio_detail.account_number
        self.booker_external_id = company_code
        self.payment_id = None
        self.payment_split_id = None
        self.payment_type = PaymentTypes.PAYMENT
        self.amount = self._calculate_amount(credit_folio.posttax_amount, is_refund)
        self.posting_date = self._get_report_date()
        self.date_of_payment = self.posting_date
        self.fin_erp_posting_date = self.posting_date
        self.payment_mode = PaymentModes.CITY_LEDGER
        self.payment_mode_sub_type = None
        self.crs_payment_mode = PaymentModes.TREEBO_BTC
        self.crs_payment_mode_sub_type = None
        self.payment_ref_id = credit_folio.invoice_id
        self.payment_channel = None
        self.paid_to = None
        self.checkin_date = None
        self.checkout_date = None
        self.guest_name = None
        self.room_no = None
        self.booking_id = None
        self.booking_reference_number = None
        self.revenue_center = RevenueCenters.FRONT_DESK.value

    @staticmethod
    def _calculate_amount(posttax_amount, is_refund):
        return -posttax_amount if is_refund else posttax_amount

    @staticmethod
    def _get_report_date():
        return crs_context.get_report_date()
