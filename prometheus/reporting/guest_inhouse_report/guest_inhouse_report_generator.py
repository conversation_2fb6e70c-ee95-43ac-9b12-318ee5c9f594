from treebo_commons.utils import dateutils

from prometheus import crs_context
from prometheus.domain.catalog.repositories import RoomTypeRepository
from prometheus.reporting.base_report_generator import BaseReportGenerator
from prometheus.reporting.guest_inhouse_report.guest_inhouse_report_aggregate import (
    GuestInHouseReportAggregate,
)
from ths_common.constants.booking_constants import BookingStatus


class GuestInhouseReportGenerator(BaseReportGenerator):
    """
    Responsible for generating report rows for a given booking
    """

    REPORT_COLUMNS = [
        "booking_id",
        "guest_name",
        "guest_count",
        "room_number",
        "room_type",
        "booking_status",
        "checkin_date",
        "checkout_date",
        "payment_amount",
        'pending_amount',
        'id_proof_type',
        'id_proof_number',
        "get_channel",
        "room_related_charges_posttax",
        'guest_phone',
        'guest_email',
        'guest_address',
        'guest_city',
        'guest_state',
        'guest_country',
        'guest_pincode',
        'guest_gender',
    ]

    GUEST_INHOUSE_REPORT_FOLDER_NAME = 'guest_inhouse_reports/'

    def __init__(
        self,
        booking_aggregate,
        bill_aggregate,
        hotel_aggregate,
        room_type_map,
        start_date,
        end_date,
        billed_entity_account_payment_map=None,
    ):
        self.hotel_aggregate = hotel_aggregate
        self.booking_aggregate = booking_aggregate
        self.bill_aggregate = bill_aggregate
        self.room_type_map = room_type_map
        self.start_date = start_date
        self.end_date = end_date
        self.billed_entity_account_payment_map = billed_entity_account_payment_map

    def generate(self):
        active_roomstays = []
        hotel_context = crs_context.set_hotel_context(self.hotel_aggregate)
        for rs in self.booking_aggregate.get_active_room_stays(as_generator=True):
            if (
                rs.room_type_id
                == RoomTypeRepository().get_house_accounts_room_type_id()
            ):
                continue
            checkin_date = dateutils.to_date(rs.checkin_date)
            checkout_date = (
                hotel_context.hotel_checkout_date(rs.actual_checkout_date)
                if rs.actual_checkout_date
                else dateutils.to_date(rs.checkout_date)
            )
            if rs.status in {
                BookingStatus.CHECKED_IN,
                BookingStatus.PART_CHECKIN,
                BookingStatus.RESERVED,
            }:
                if checkin_date <= self.end_date and checkout_date > self.start_date:
                    active_roomstays.append(rs)
            elif rs.status in {BookingStatus.CHECKED_OUT, BookingStatus.PART_CHECKOUT}:
                if checkout_date >= self.start_date and checkin_date <= self.end_date:
                    if checkout_date > self.start_date:
                        active_roomstays.append(rs)
                    elif (
                        checkout_date == self.start_date
                        and checkout_date == checkin_date
                    ):
                        active_roomstays.append(rs)
        return [
            GuestInHouseReportAggregate(
                self.booking_aggregate,
                self.bill_aggregate,
                self.hotel_aggregate,
                room_stay,
                self.billed_entity_account_payment_map,
            )
            for room_stay in active_roomstays
        ]

    @staticmethod
    def generate_guest_inhouse_report_file_name(extension='csv'):
        return GuestInhouseReportGenerator.generate_report_file_path(
            'Guest Inhouse Report', extension
        )

    @staticmethod
    def get_default_expiration_time():
        return 604800  # 7 days
