import os
from datetime import datetime

from treebo_commons.utils import dateutils

from prometheus.reporting.base_report_generator import BaseReportGenerator
from prometheus.reporting.in_touch_reports.code_file_report.code_report_aggregate import (
    InTouchCodeReportAggregate,
)


class InTouchCodeReportGenerator(BaseReportGenerator):
    """
    Responsible for generating report rows for a given booking
    """

    REPORT_COLUMNS = ["code", "code_description", "code_reference", "code_type"]

    def __init__(self, code_report_aggregate):
        self.code_report_aggregate = code_report_aggregate

    def generate(self):
        return InTouchCodeReportAggregate(self.code_report_aggregate)

    @staticmethod
    def generate_in_touch_code_report_file_name(report_date, extension='csv'):
        hotel_business_date_with_current_timestamp = datetime.combine(
            dateutils.ymd_str_to_date(report_date), dateutils.current_datetime().time()
        )
        hotel_business_datetime = datetime.strftime(
            hotel_business_date_with_current_timestamp, "%Y-%m-%d-%H:%M:%S"
        )
        file_name = f"/code_{hotel_business_datetime}.{extension}"

        return os.environ.get("TEMPORARY_REPORTS_DIRECTORY", "/tmp") + file_name

    @staticmethod
    def get_default_expiration_time():
        return 604800  # 7 days
