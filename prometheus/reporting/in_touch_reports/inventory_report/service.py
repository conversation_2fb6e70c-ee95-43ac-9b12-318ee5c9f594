from collections import defaultdict

from dateutil.relativedelta import relativedelta
from dateutil.rrule import DAILY, rrule
from treebo_commons.request_tracing.context import get_current_tenant_id
from treebo_commons.utils import dateutils

from object_registry import register_instance
from prometheus.application.inventory.service.inventory_application_service import (
    InventoryApplicationService,
)
from prometheus.domain.booking.repositories.booking_repository import BookingRepository
from prometheus.domain.catalog.repositories import RoomRepository, RoomTypeRepository
from prometheus.domain.catalog.repositories.hotel_repository import HotelRepository
from prometheus.domain.inventory.repositories import (
    RoomAllotmentRepository,
    RoomTypeInventoryRepository,
)
from prometheus.domain.inventory.repositories.dnr_repository import DNRRepository
from prometheus.infrastructure.external_clients.aws_service_client import (
    AwsServiceClient,
)
from prometheus.reporting.in_touch_reports.inventory_report.inventory_report_generator import (
    InTouchInventoryReportGenerator,
)
from prometheus.reporting.utils import CsvWriter
from shared_kernel.infrastructure.external_clients.catalog_service_client import (
    CatalogServiceClient,
)
from ths_common.constants.inventory_constants import DNRType, HouseKeepingStatus


@register_instance(
    dependencies=[
        BookingRepository,
        HotelRepository,
        RoomTypeRepository,
        CatalogServiceClient,
        RoomRepository,
        RoomAllotmentRepository,
        RoomTypeInventoryRepository,
        InventoryApplicationService,
        DNRRepository,
    ]
)
class InventoryReportingService(object):
    def __init__(
        self,
        booking_repository,
        hotel_repository,
        room_type_repository,
        catalog_service_client,
        room_repository,
        room_allotment_repository,
        room_type_inventory_repository,
        inventory_application_service,
        dnr_repository,
    ):
        self.booking_repository = booking_repository
        self.hotel_repository = hotel_repository
        self.room_type_repository = room_type_repository
        self.catalog_service_client = catalog_service_client
        self.room_repository = room_repository
        self.room_allotment_repository = room_allotment_repository
        self.room_type_inventory_repository = room_type_inventory_repository
        self.inventory_application_service = inventory_application_service
        self.dnr_repository = dnr_repository

    def _get_dnrs(self, hotel_id, from_date, to_date, room_ids):
        dnr_types = [
            dnr_type for dnr_type in DNRType.all() if dnr_type != DNRType.INACTIVE_ROOM
        ]
        dnr_aggregates = self.dnr_repository.load_dnrs(
            hotel_id=hotel_id,
            room_ids=room_ids,
            from_date=from_date,
            to_date=to_date,
            types=dnr_types,
        )
        return dnr_aggregates

    @staticmethod
    def create_room_type_to_room_ids_map(room_aggregates):
        room_type_to_room_ids_map = defaultdict(list)
        for room in room_aggregates:
            room_type_to_room_ids_map[room.room.room_type_id].append(room.room.room_id)
        return room_type_to_room_ids_map

    def _generate_csv_report(self, csv_writer, report_date, hotel_id):
        room_aggregates = self.room_repository.load_multiple(hotel_id)
        room_type_to_room_ids_map = self.create_room_type_to_room_ids_map(
            room_aggregates
        )
        report_aggregates = []
        all_room_types_inventory_data = (
            self.catalog_service_client.get_room_type_config(hotel_id)
        )
        room_type_ids = [
            room_type_inventory_data['room_type']['code']
            for room_type_inventory_data in all_room_types_inventory_data
        ]
        last_date_with_inventory_map = self.room_type_inventory_repository.fetch_last_date_with_inventory_availability_map(
            hotel_id, room_type_ids
        )

        date_wise_room_type_dnr = self.extract_room_type_dnr(
            hotel_id,
            last_date_with_inventory_map,
            report_date,
            room_type_to_room_ids_map,
        )

        start_date = dateutils.ymd_str_to_date(report_date)
        delta = relativedelta(days=1)
        rooms_count_map = self.room_repository.get_active_room_type_count_map(hotel_id)
        rooms_housekeeping_status_count = (
            self.room_allotment_repository.count_housekeeping_status_rooms(hotel_id)
        )
        room_type_inventories_map = (
            self.room_type_inventory_repository.get_room_type_inventories(
                hotel_id, room_type_ids, start_date
            )
        )

        for room_type_id in room_type_ids:
            end_date = last_date_with_inventory_map[room_type_id]
            total_rooms = rooms_count_map.get(
                room_type_id, 0
            )  # in-active rooms won't be present in rooms_count map.
            housekeeping_room_status_count = {
                "out_of_order_rooms": rooms_housekeeping_status_count.get(
                    room_type_id, {}
                ).get(HouseKeepingStatus.OUT_OF_ORDER.value, 0),
                "out_of_service_rooms": rooms_housekeeping_status_count.get(
                    room_type_id, {}
                ).get(HouseKeepingStatus.OUT_OF_SERVICE.value, 0),
            }
            current_date = start_date
            while current_date <= end_date:
                room_type_inventories_count = room_type_inventories_map.get(
                    room_type_id, {}
                ).get(current_date, 0)
                ooo_rooms = len(
                    date_wise_room_type_dnr[f"{current_date}_{room_type_id}"]
                )
                csv_report_aggregates = InTouchInventoryReportGenerator(
                    room_type_inventories_count,
                    room_type_id,
                    current_date,
                    housekeeping_room_status_count,
                    total_rooms,
                    ooo_rooms,
                ).generate()
                report_aggregates.extend(csv_report_aggregates)
                current_date += delta

        csv_writer.write_aggregates(
            report_aggregates, InTouchInventoryReportGenerator.REPORT_COLUMNS
        )
        return report_aggregates

    def extract_room_type_dnr(
        self,
        hotel_id,
        last_date_with_inventory_map,
        report_date,
        room_type_to_room_ids_map,
    ):
        date_wise_room_type_dnr = defaultdict(list)
        for room_type, last_date in last_date_with_inventory_map.items():
            dnr_aggregates = self._get_dnrs(
                hotel_id,
                report_date,
                str(last_date),
                room_type_to_room_ids_map[room_type],
            )
            for dnr_aggregate in dnr_aggregates:
                for date in rrule(
                    DAILY,
                    dtstart=dnr_aggregate.dnr.start_date,
                    until=dateutils.subtract(
                        dnr_aggregate.dnr.effective_end_date, days=1
                    ),
                ):
                    key = f"{str(date.date())}_{room_type}"
                    date_wise_room_type_dnr[key].append(dnr_aggregate.dnr.room_id)
        return date_wise_room_type_dnr

    def generate_csv_report(self, report_date, hotel_aggregate=None):
        file_path = InTouchInventoryReportGenerator.generate_in_touch_inventory_report_file_name(
            report_date
        )
        folder_path = f"{get_current_tenant_id()}/in-touch-data-files/{hotel_aggregate.hotel_id}/{report_date}/"
        report_aggregates = []
        with CsvWriter(file_path) as csv_writer:
            report_aggregates.append(
                self._generate_csv_report(
                    csv_writer, report_date, hotel_aggregate.hotel_id
                )
            )
            AwsServiceClient.upload_file_to_s3_and_get_presigned_url(
                folder_path,
                csv_writer.file_path,
                InTouchInventoryReportGenerator.get_default_expiration_time(),
            )

        return report_aggregates
