from collections import defaultdict
from typing import Sequence

from treebo_commons.money import Money
from treebo_commons.money.constants import CurrencyType

from prometheus import crs_context
from prometheus.reporting.base_report_aggregate import BaseReportAggregate
from ths_common.constants.billing_constants import (
    ChargeStatus,
    ChargeTypes,
    PaymentModes,
    PaymentReceiverTypes,
    PaymentStatus,
    PaymentTypes,
    TaxTypes,
)
from ths_common.constants.booking_constants import (
    BookingChannels,
    BookingStatus,
    ExpenseAddedBy,
    ExpenseTypes,
)


class MarvinReportAggregate(BaseReportAggregate):
    """
    Each aggregate object is a row in the marvin report
    """

    def __init__(
        self,
        booking_aggregate,
        bill_aggregate,
        hotel_aggregate,
        room_stay,
        invoice_aggregates,
        expense_item_ids,
        room_type_map,
    ):
        self.booking_aggregate = booking_aggregate
        self.bill_aggregate = bill_aggregate
        self.hotel_aggregate = hotel_aggregate
        self.invoice_aggregates = invoice_aggregates
        self.room_stay = room_stay
        self.hotel_context = crs_context.set_hotel_context(hotel_aggregate)
        self.expense_item_ids = expense_item_ids
        self.room_type_map = room_type_map
        self._proration_amount = None

    @property
    def currency(self):
        return (
            self.bill_aggregate.bill.base_currency
            if self.bill_aggregate.bill.base_currency
            else CurrencyType.INR
        )

    @property
    def hotel_id(self):
        return self.hotel_aggregate.hotel.hotel_id

    @property
    def hotel_name(self):
        return self.hotel_aggregate.hotel.name

    @property
    def booking_id(self):
        return self.booking_aggregate.booking.booking_id

    @property
    def bill_id(self):
        return self.bill_aggregate.bill.bill_id

    @property
    def reference_number(self):
        return self.booking_aggregate.booking.reference_number

    @property
    def room_stay_id(self):
        return self.room_stay.room_stay_id

    @property
    def room_stay_status(self):
        return self.room_stay.status.value

    @property
    def guest_names(self):
        customers_list = [
            self.booking_aggregate.get_customer(gs.guest_id)
            for gs in self.room_stay.all_guest_stays_except_cancelled()
            if gs.guest_id is not None
        ]
        return ", ".join([customer.first_name for customer in customers_list])

    @property
    def adult_count(self):
        return len(self.room_stay.adult_guest_stays())

    @property
    def child_count(self):
        return len(self.room_stay.all_guest_stays_except_cancelled()) - len(
            self.room_stay.adult_guest_stays()
        )

    @property
    def channel_code(self):
        return self.booking_aggregate.booking.source.channel_code

    @property
    def subchannel_code(self):
        return self.booking_aggregate.booking.source.subchannel_code

    @property
    def corporate_name(self):
        channel_code = self.booking_aggregate.booking.source.channel_code
        if channel_code in [BookingChannels.B2B.value, BookingChannels.TA.value]:
            customer = self.booking_aggregate.get_booking_owner()
            return customer.first_name
        else:
            return None

    @property
    def room_number(self):
        try:
            all_room_allocations = self.room_stay.all_room_allocations()
            return ", ".join(
                list(
                    {
                        room_allocation.room_no
                        for room_allocation in all_room_allocations
                    }
                )
            )
        except AttributeError:
            return None

    @property
    def room_type(self):
        try:
            room_type_ids = self.room_type_ids().split(', ')
            room_type_names = ", ".join(
                {
                    self.room_type_map[room_type_id].room_type.type
                    for room_type_id in room_type_ids
                }
            )
            return room_type_names
        except KeyError:
            return None
        except AttributeError:
            return None

    @property
    def guest_count(self):
        return self.room_stay.max_occupancy

    @property
    def invoice_numbers(self):
        if self.invoice_aggregates:
            return ", ".join(
                [
                    inv_agg.invoice_number
                    for inv_agg in self.invoice_aggregates
                    if inv_agg.invoice_number is not None
                ]
            )
        return None

    @property
    def invoicing_model(self):
        return self.booking_aggregate.booking.seller_model.value

    @property
    def checkin_date(self):
        return self.hotel_context.hotel_checkin_date(self.room_stay.checkin_date)

    @property
    def checkout_date(self):
        return self.hotel_context.hotel_checkout_date(self.room_stay.checkout_date)

    @property
    def total_room_nights(self):
        total_days = (self.checkout_date - self.checkin_date).days
        return 1 if total_days == 0 else total_days

    @property
    def pretax_room_rent(self):
        return sum(
            [
                charge.get_pretax_amount_post_allowance()
                for charge in self.get_room_stay_charges()
            ]
        )

    @property
    def cgst_room_rent(self):
        cgst_charge_details = [
            MarvinReportAggregate.get_tax_details_for_type(
                charge.tax_details, TaxTypes.CGST
            )
            for charge in self.get_room_stay_charges()
        ]
        return sum(
            [
                cgst_charge_detail.amount.amount
                for cgst_charge_detail in cgst_charge_details
            ]
        )

    @property
    def sgst_room_rent(self):
        sgst_charge_details = [
            MarvinReportAggregate.get_tax_details_for_type(
                charge.tax_details, TaxTypes.SGST
            )
            for charge in self.get_room_stay_charges()
        ]
        return sum(
            [
                sgst_charge_detail.amount.amount
                for sgst_charge_detail in sgst_charge_details
            ]
        )

    @property
    def igst_room_rent(self):
        igst_charge_details = [
            MarvinReportAggregate.get_tax_details_for_type(
                charge.tax_details, TaxTypes.IGST
            )
            for charge in self.get_room_stay_charges()
        ]
        return sum(
            [
                igst_charge_detail.amount.amount
                for igst_charge_detail in igst_charge_details
            ]
        )

    @property
    def flood_cess_room_rent(self):
        flood_cess_room_rent = Money(0, self.currency).amount
        for charge in self.get_room_stay_charges():
            flood_cess_details = MarvinReportAggregate.get_tax_details_for_type(
                charge.tax_details, TaxTypes.KERALA_FLOOD_CESS
            )
            try:
                flood_cess_room_rent += flood_cess_details.amount.amount
            except AttributeError:
                continue
        return flood_cess_room_rent

    @property
    def total_allowance_tax_for_room_stay(self):
        allowance_tax_amount = Money(0, self.currency).amount
        for charge in self.get_room_stay_charges():
            allowance_tax_amount += sum(
                [
                    allowance.tax_amount
                    for charge_split in charge.charge_splits
                    for allowance in charge_split.allowance
                    if allowance.is_active
                ]
            )
        return allowance_tax_amount

    @property
    def total_lodging_taxes(self):
        return (
            self.cgst_room_rent
            + self.sgst_room_rent
            + self.igst_room_rent
            + self.flood_cess_room_rent
        )

    @property
    def room_related_charges_pretax(self):
        return self.pretax_room_rent

    @property
    def room_related_charges_tax(self):
        return self.total_lodging_taxes - self.total_tax_allowance_for_room_stay

    @property
    def room_related_charges_posttax(self):
        total_room_charges = Money(0, self.currency).amount
        for charge in self.charges():
            if charge.item.item_id in [
                'extra_guest',
                'early_checkin',
                'late_checkin',
                'late_checkout',
                'early_checkout',
                'booking_cancellation',
                'no_show',
                'booking_modification',
            ]:
                total_room_charges += charge.get_posttax_amount_post_allowance()
        return total_room_charges

    @property
    def room_related_charges(self):
        total_room_charges = (
            self.room_related_charges_pretax
            + self.room_related_charges_tax
            + self.room_related_charges_posttax
        )
        return total_room_charges

    @property
    def per_night_room_charges(self):
        total_room_charges = self.room_related_charges
        return Money(total_room_charges / self.total_room_nights, self.currency).amount

    @property
    def total_booking_amount(self):
        return self.total_credit_charges + self.total_non_credit_charges

    @property
    def paid_to_hotel(self):
        valid_payment_receiver_types = [
            PaymentReceiverTypes.HOTEL,
            PaymentReceiverTypes.GUEST,
        ]
        total_paid_to_hotel = self.total_paid_to_from_payments(
            valid_payment_receiver_types
        )
        # prorate payment
        proration_amount = self.get_booking_payments_proration_amount()
        percentage = self.get_proration_percentage(proration_amount)
        return (percentage * total_paid_to_hotel).amount

    @property
    def paid_to_treebo(self):
        valid_payment_receiver_types = [
            PaymentReceiverTypes.TREEBO,
            PaymentReceiverTypes.OTA,
            PaymentReceiverTypes.CORPORATE,
        ]
        total_paid_to_treebo = self.total_paid_to_from_payments(
            valid_payment_receiver_types
        )
        # prorate payment
        proration_amount = self.get_booking_payments_proration_amount()
        percentage = self.get_proration_percentage(proration_amount)
        return (percentage * total_paid_to_treebo).amount

    @property
    def payment_mode_cc_treebo(self):
        valid_payment_receiver_types = [
            PaymentReceiverTypes.TREEBO,
            PaymentReceiverTypes.OTA,
            PaymentReceiverTypes.CORPORATE,
        ]
        total_paid_to_treebo = self.total_paid_to_from_payments(
            valid_payment_receiver_types, [PaymentModes.CREDIT_CARD]
        )
        # prorate payment
        proration_amount = self.get_booking_payments_proration_amount()
        percentage = self.get_proration_percentage(proration_amount)
        return (percentage * total_paid_to_treebo).amount

    @property
    def payment_mode_cc_hotel(self):
        valid_payment_receiver_types = [
            PaymentReceiverTypes.HOTEL,
            PaymentReceiverTypes.GUEST,
        ]
        total_paid_to_hotel = self.total_paid_to_from_payments(
            valid_payment_receiver_types, [PaymentModes.CREDIT_CARD]
        )
        # prorate payment
        proration_amount = self.get_booking_payments_proration_amount()
        percentage = self.get_proration_percentage(proration_amount)
        return (percentage * total_paid_to_hotel).amount

    @property
    def payment_mode_dc_treebo(self):
        valid_payment_receiver_types = [
            PaymentReceiverTypes.TREEBO,
            PaymentReceiverTypes.OTA,
            PaymentReceiverTypes.CORPORATE,
        ]
        total_paid_to_treebo = self.total_paid_to_from_payments(
            valid_payment_receiver_types, [PaymentModes.DEBIT_CARD]
        )
        # prorate payment
        proration_amount = self.get_booking_payments_proration_amount()
        percentage = self.get_proration_percentage(proration_amount)
        return (percentage * total_paid_to_treebo).amount

    @property
    def payment_mode_dc_hotel(self):
        valid_payment_receiver_types = [
            PaymentReceiverTypes.HOTEL,
            PaymentReceiverTypes.GUEST,
        ]
        total_paid_to_hotel = self.total_paid_to_from_payments(
            valid_payment_receiver_types, [PaymentModes.DEBIT_CARD]
        )

        # prorate payment
        proration_amount = self.get_booking_payments_proration_amount()
        percentage = self.get_proration_percentage(proration_amount)
        return (percentage * total_paid_to_hotel).amount

    @property
    def payment_mode_bank_transfer_treebo(self):
        valid_payment_receiver_types = [
            PaymentReceiverTypes.TREEBO,
            PaymentReceiverTypes.OTA,
            PaymentReceiverTypes.CORPORATE,
        ]
        total_paid_to_treebo = self.total_paid_to_from_payments(
            valid_payment_receiver_types, [PaymentModes.BANK_TRANSFER]
        )
        # prorate payment
        proration_amount = self.get_booking_payments_proration_amount()
        percentage = self.get_proration_percentage(proration_amount)
        return (percentage * total_paid_to_treebo).amount

    @property
    def payment_mode_bank_transfer_hotel(self):
        valid_payment_receiver_types = [
            PaymentReceiverTypes.HOTEL,
            PaymentReceiverTypes.GUEST,
        ]
        total_paid_to_hotel = self.total_paid_to_from_payments(
            valid_payment_receiver_types, [PaymentModes.BANK_TRANSFER]
        )
        # prorate payment
        proration_amount = self.get_booking_payments_proration_amount()
        percentage = self.get_proration_percentage(proration_amount)
        return (percentage * total_paid_to_hotel).amount

    @property
    def payments(self):
        payments_group = defaultdict(list)
        for mode in PaymentModes.all():
            payments_group[mode] = Money(
                0, self.currency
            )  # should populate new keys (report columns) automatically

        for payment in self.get_payments():
            if payment.payment_type == PaymentTypes.PAYMENT:
                payments_group[payment.payment_mode] += payment.amount
            elif payment.payment_type == PaymentTypes.REFUND:
                payments_group[payment.payment_mode] -= payment.amount

        proration_amount = self.get_booking_payments_proration_amount()
        percentage = self.get_proration_percentage(proration_amount)
        return self.prorate_partial_payments(payments_group, percentage)

    @property
    def non_food_expenses(self):
        # dictionary of charges grouped by expense item id
        charges_grouped = defaultdict(int)
        for item_id in self.expense_item_ids:
            key_name = MarvinReportAggregate.to_snake_case(item_id)
            charges_grouped[key_name + '_pretax'] = 0
            charges_grouped[key_name + '_tax_amount'] = 0
            charges_grouped[
                key_name + '_posttax'
            ] = 0  # should populate new keys (report columns) automatically
        for charge in self.charges():
            if (
                charge.item.item_id
                and charge.item.sku_category_id != ExpenseTypes.FOOD.value
                and charge.item.name != 'RoomStay'
            ):
                key_name = MarvinReportAggregate.to_snake_case(charge.item.item_id)
                charges_grouped[
                    key_name + '_pretax'
                ] += charge.get_pretax_amount_post_allowance()
                charges_grouped[
                    key_name + '_tax_amount'
                ] += charge.get_tax_amount_post_allowance()
                charges_grouped[
                    key_name + '_posttax'
                ] += charge.get_posttax_amount_post_allowance()

        return charges_grouped

    @property
    def food_charge_pretax(self):
        return sum(
            [
                charge.get_pretax_amount_post_allowance()
                for charge in self.get_food_charges()
            ]
        )

    @property
    def food_charge_tax_amount(self):
        return sum(
            [
                charge.get_tax_amount_post_allowance()
                for charge in self.get_food_charges()
            ]
        )

    @property
    def food_charge_posttax(self):
        return sum(
            [
                charge.get_posttax_amount_post_allowance()
                for charge in self.get_food_charges()
            ]
        )

    @property
    def food_charge_added_by_treebo_pretax(self):
        return sum(
            [
                charge.get_pretax_amount_post_allowance()
                for charge in self.food_charges_added_by_treebo()
            ]
        )

    @property
    def food_charge_added_by_treebo_tax_amount(self):
        return sum(
            [
                charge.get_tax_amount_post_allowance()
                for charge in self.food_charges_added_by_treebo()
            ]
        )

    @property
    def total_credit_charges(self):
        return sum(
            [
                charge.get_posttax_amount_post_allowance()
                for charge in self.charges()
                if charge.type == ChargeTypes.CREDIT
            ]
        )

    @property
    def total_non_credit_charges(self):
        return sum(
            [
                charge.get_posttax_amount_post_allowance()
                for charge in self.charges()
                if charge.type == ChargeTypes.NON_CREDIT
            ]
        )

    @property
    def proration_amount(self):
        try:
            return self.get_booking_payments_proration_amount().amount
        except AttributeError:
            return self.get_booking_payments_proration_amount()

    def food_charges_added_by_treebo(self):
        food_charges_added_by_treebo = list()
        room_stay_expenses = self.booking_aggregate.get_active_expenses_for_room_stay(
            room_stay_id=self.room_stay_id
        )
        for charge in self.get_food_charges():
            for expense in room_stay_expenses:
                if (
                    charge.charge_id == expense.charge_id
                    and expense.added_by == ExpenseAddedBy.TREEBO
                ):
                    food_charges_added_by_treebo.append(charge)
                    break
        return food_charges_added_by_treebo

    def get_food_charges(self):
        # TODO: change the 'food' value to enum
        return [
            charge
            for charge in self.charges()
            if charge.item.sku_category_id in ['food', 'beverages']
        ]

    def get_room_stay_expenses(self, room_stay_id=None):
        if room_stay_id is None:
            room_stay_id = self.room_stay_id

        charge_ids = [
            expense.charge_id
            for expense in self.booking_aggregate.get_active_expenses_for_room_stay(
                room_stay_id
            )
        ]
        charges = [
            charge
            for charge in self.bill_aggregate.filter_and_get_charges(charge_ids)
            if charge.status != ChargeStatus.CANCELLED
        ]
        return charges

    def get_room_stay_charges(self, room_stay_id=None):
        # TODO: check that all required expenses are fetched
        if room_stay_id is None:
            room_stay_id = self.room_stay_id

        charge_ids = self.booking_aggregate.get_room_stay_charges([room_stay_id])
        charges = [
            charge
            for charge in self.bill_aggregate.filter_and_get_charges(charge_ids)
            if charge.status != ChargeStatus.CANCELLED
        ]
        return charges

    def get_payments(self):
        return [
            payment
            for payment in self.bill_aggregate.payments
            if payment.status != PaymentStatus.CANCELLED
        ]

    def get_booking_payments_proration_amount(self):
        if self._proration_amount is not None:
            return self._proration_amount

        prorated_amount = Money(0, self.currency)
        room_stays = self.booking_aggregate.get_active_room_stays()

        total_payment = sum(
            payment.amount
            for payment in self.get_payments()
            if payment.payment_type == PaymentTypes.PAYMENT
        )
        total_refund = sum(
            payment.amount
            for payment in self.get_payments()
            if payment.payment_type == PaymentTypes.REFUND
        )
        total_paid = Money(total_payment, self.currency) - Money(
            total_refund, self.currency
        )
        checked_out_roomstays = [
            rs for rs in room_stays if rs.status == BookingStatus.CHECKED_OUT
        ]
        non_checked_out_roomstays = [
            rs for rs in room_stays if rs.status != BookingStatus.CHECKED_OUT
        ]
        current_room_stay_non_credit_charge = sum(
            rs_charge.get_posttax_amount_post_allowance()
            for rs_charge in self.charges()
            if rs_charge.type == ChargeTypes.NON_CREDIT
        )
        for checked_out_roomstay in checked_out_roomstays:
            total_roomstay_non_credit_charges = sum(
                rs_charge.get_posttax_amount_post_allowance()
                for rs_charge in self.charges(checked_out_roomstay.room_stay_id)
                if rs_charge.type == ChargeTypes.NON_CREDIT
            )
            if total_roomstay_non_credit_charges is None:
                continue  # no clue why this could happen
            if total_paid == 0:
                break
            if total_paid < total_roomstay_non_credit_charges:
                prorated_amount = total_paid
                total_paid = 0
            else:
                prorated_amount = total_roomstay_non_credit_charges
                total_paid -= total_roomstay_non_credit_charges

            if prorated_amount == current_room_stay_non_credit_charge:
                break

        total_non_checked_out_charges = sum(
            sum(
                rs_charge.get_posttax_amount_post_allowance()
                for rs_charge in self.get_room_stay_charges(rs.room_stay_id)
                if rs_charge.type == ChargeTypes.NON_CREDIT
            )
            for rs in non_checked_out_roomstays
        )

        for rs in non_checked_out_roomstays:
            total_roomstay_non_credit_charges = sum(
                rs_charge.get_posttax_amount_post_allowance()
                for rs_charge in self.get_room_stay_charges(rs.room_stay_id)
                if rs_charge.type == ChargeTypes.NON_CREDIT
            )
            if total_roomstay_non_credit_charges and total_non_checked_out_charges:
                prorated_amount = (
                    float(total_roomstay_non_credit_charges)
                    / float(total_non_checked_out_charges)
                ) * total_paid
            else:
                prorated_amount = Money(0, self.currency)

        self._proration_amount = prorated_amount
        return prorated_amount

    def get_proration_percentage(self, proration_amount):
        # TODO: figure out which charges and room stays to filter out
        booking_room_stay_ids = [
            rs.room_stay_id for rs in self.booking_aggregate.get_active_room_stays()
        ]
        booking_charges = list()
        for room_stay_id in booking_room_stay_ids:
            booking_charges.extend(
                [
                    charge.get_posttax_amount_post_allowance()
                    for charge in self.charges(room_stay_id)
                    if charge.type == ChargeTypes.NON_CREDIT
                ]
            )
        total_booking_amount_non_credit = sum(booking_charges)
        if (
            total_booking_amount_non_credit is None
            or not total_booking_amount_non_credit
        ):
            percentage = 1
        else:
            percentage = float(proration_amount) / float(
                total_booking_amount_non_credit
            )

        return percentage

    def prorate_partial_payments(self, payments_group, percentage):
        for payment_mode in payments_group.keys():
            payments_group[payment_mode] *= percentage
            payments_group[payment_mode] = payments_group[payment_mode].amount
        return payments_group

    def room_type_ids(self):
        try:
            all_room_allocations = self.room_stay.all_room_allocations()
            return ", ".join(
                list(
                    {
                        room_allocation.room_type_id
                        for room_allocation in all_room_allocations
                    }
                )
            )
        except AttributeError:
            return None

    def charges(self, room_stay_id=None):
        charges = self.get_room_stay_charges(room_stay_id)
        charges.extend(self.get_room_stay_expenses(room_stay_id))
        return charges

    @staticmethod
    def to_snake_case(key_name):
        return key_name.replace(" ", "_").lower()

    def total_paid_to_from_payments(
        self, payment_receiver_types: Sequence = None, payment_modes: Sequence = None
    ):
        def filter_check(p):
            matched_payment_type, matched_payment_mode = True, True
            if payment_receiver_types is not None:
                matched_payment_type = p.paid_to in payment_receiver_types
            if payment_modes is not None:
                matched_payment_mode = p.payment_mode in payment_modes
            return matched_payment_type and matched_payment_mode

        filtered_payments = [p for p in self.get_payments() if filter_check(p)]

        payments = [
            p.amount.amount
            for p in filtered_payments
            if p.payment_type == PaymentTypes.PAYMENT
        ]
        refunds = [
            p.amount.amount
            for p in filtered_payments
            if p.payment_type == PaymentTypes.REFUND
        ]
        return Money(sum(payments) - sum(refunds), self.currency)

    @staticmethod
    def get_tax_details_for_type(tax_details, tax_type):
        for tax_detail in tax_details:
            if tax_detail.tax_type == tax_type:
                return tax_detail
