import csv
import os
from collections import defaultdict
from decimal import Decimal

from treebo_commons.money import Money
from treebo_commons.utils import dateutils

from object_registry import register_instance
from prometheus import crs_context
from prometheus.application.decorators import session_manager, set_hotel_context
from prometheus.application.hotel_settings.tenant_settings import TenantSettings
from prometheus.common.decorators import bypass_access_entity_checks, timed
from prometheus.domain.billing.repositories import BillRepository
from prometheus.domain.booking.repositories import BookingRepository
from prometheus.domain.catalog.repositories import (
    HotelRepository,
    RoomRepository,
    RoomTypeRepository,
)
from prometheus.domain.catalog.repositories.seller_repository import SellerRepository
from prometheus.domain.inventory.repositories.dnr_repository import DNRRepository
from prometheus.domain.inventory.repositories.room_allotment_repository import (
    RoomAllotmentRepository,
)
from prometheus.domain.report.entities.flash_manager_report import FlashManagerReport
from prometheus.domain.report.repositories.flash_manager_report_repository import (
    FlashManagerReportRepository,
)
from prometheus.infrastructure.external_clients.aws_service_client import (
    AwsServiceClient,
)
from prometheus.reporting.flash_manager_report.flash_manager_report_generator import (
    FlashManagerReportGenerator,
)
from prometheus.reporting.reporting_service import ReportingApplicationService
from ths_common.constants.billing_constants import (
    ChargeStatus,
    PaymentStatus,
    PaymentTypes,
)
from ths_common.constants.booking_constants import BookingStatus
from ths_common.constants.inventory_constants import HouseKeepingStatus
from ths_common.constants.reporting_constants import (
    HOUSE_ACCOUNT_ROOM_TYPE,
    HOUSE_USE_RATE_PLAN_CODE,
    FlashManagerReportDatePeriods,
    FlashManagerSkuCategories,
)
from ths_common.constants.tenant_settings_constants import ReportName


@register_instance(
    dependencies=[
        BookingRepository,
        BillRepository,
        HotelRepository,
        RoomTypeRepository,
        ReportingApplicationService,
        DNRRepository,
        RoomRepository,
        RoomAllotmentRepository,
        FlashManagerReportRepository,
        SellerRepository,
        TenantSettings,
    ]
)
class FlashManagerReportingService(object):
    _ROOM_AND_RESERVATION_FIELDS = 'room_and_reservation_fields'
    _FINANCE_FIELDS = 'finance_fields'
    _FORECAST_FIELDS = 'forecast_fields'
    _POS_FIELDS = 'pos_fields'

    _TOTAL_ROOMS_IN_HOTEL = 'total_rooms_in_hotel'
    _DNR_ROOMS = 'dnr_rooms'
    _OOO_ROOMS = 'ooo_rooms'
    _TOTAL_ROOMS_IN_HOTEL_EXCLUDING_OOO_ROOMS = (
        'total_rooms_in_hotel_excluding_ooo_rooms'
    )
    _OCCUPIED_ROOMS = 'occupied_rooms'
    _MULTIPLE_OCCUPANCY = 'multiple_occupancy'
    _MAX_OCCUPANCY = 'max_occupancy'
    _AVAILABLE_ROOMS = 'available_rooms'
    _AVAILABLE_ROOMS_EXCLUDING_OOO_ROOMS = 'available_rooms_excluding_ooo_rooms'
    _AVAILABLE_ROOMS_EXCLUDING_DNR_ROOMS = 'available_rooms_excluding_dnr_rooms'
    _COMPLIMENTARY_ROOMS = 'complimentary_rooms'
    _HOUSE_USE_ROOMS = 'house_use_rooms'
    _DAY_USE_ROOMS = 'day_use_rooms'
    _OCCUPIED_ROOMS_EXCLUDING_COMP_AND_HOUSE_USE = (
        'occupied_rooms_excluding_comp_and_house_use'
    )
    _OCCUPIED_ROOMS_EXCLUDING_HOUSE_USE = 'occupied_rooms_excluding_house_use'
    _OCCUPIED_ROOMS_EXCLUDING_COMP_ROOMS = 'occupied_rooms_excluding_comp_rooms'
    _PERCENTAGE_OF_OCCUPIED_ROOMS = 'percentage_of_occupied_rooms'
    _PERCENTAGE_OF_OCCUPIED_ROOMS_EXCLUDING_COMP_AND_HOUSE_ROOMS = (
        'percentage_of_occupied_rooms_excluding_comp_and_house_rooms'
    )
    _PERCENTAGE_OF_OCCUPIED_ROOMS_EXCLUDING_COMP_AND_HOUSE_OOO_ROOMS = (
        'percentage_of_occupied_rooms_excluding_comp_house_ooo_rooms'
    )
    _PERCENTAGE_OF_OCCUPIED_ROOMS_EXCLUDING_COMP = (
        'percentage_of_occupied_rooms_excluding_comp'
    )
    _PERCENTAGE_OF_OCCUPIED_ROOMS_EXCLUDING_HOUSE = (
        'percentage_of_occupied_rooms_excluding_house'
    )
    _PERCENTAGE_OF_OCCUPIED_ROOMS_EXCLUDING_COMP_AND_OOO = (
        'percentage_of_occupied_rooms_excluding_comp_and_ooo'
    )
    _PERCENTAGE_OF_OCCUPIED_ROOMS_EXCLUDING_HOUSE_AND_OOO = (
        'percentage_of_occupied_rooms_excluding_house_and_ooo'
    )
    _PERCENTAGE_OF_OCCUPIED_ROOMS_EXCLUDING_OOO = (
        'percentage_of_occupied_rooms_excluding_ooo'
    )
    _IN_HOUSE_ADULTS = 'in_house_adults'
    _IN_HOUSE_CHILDREN = 'in_house_children'
    _TOTAL_IN_HOUSE_PERSONS = 'total_in_house_persons'
    _IN_HOUSE_NON_CORPORATE_PERSONS = 'in_house_non_corporate_persons'
    _IN_HOUSE_CORPORATE_PERSONS = 'in_house_corporate_persons'
    # _IN_HOUSE_INDIVIDUAL_ROOMS = 'in_house_individual_rooms'
    _IN_HOUSE_CORPORATE_ROOMS = 'in_house_corporate_rooms'
    _IN_HOUSE_TRAVEL_AGENT_ROOMS = 'in_house_travel_agent_rooms'
    _ARRIVAL_ROOMS = 'arrival_rooms'
    _ARRIVAL_PERSONS = 'arrival_persons'
    # _DEDUCTED_ARRIVALS = 'deducted_arrivals'
    _WALK_IN_ROOMS = 'walk_in_rooms'
    _WALK_IN_PERSONS = 'walk_in_persons'
    _EXTENDED_STAY_ROOMS = 'extended_stay_rooms'
    _EXTENDED_STAY_PERSONS = 'extended_stay_persons'
    _DEPARTURE_ROOMS = 'departure_rooms'
    _DEPARTURE_PERSONS = 'departure_persons'
    _EARLY_DEPARTURE_ROOMS = 'early_departure_rooms'
    _EARLY_DEPARTURE_PERSONS = 'early_departure_persons'
    _NO_SHOW_ROOMS = 'no_show_rooms'
    _NO_SHOW_PERSONS = 'no_show_persons'
    _TODAYS_CHECKIN_CANCELLED_RESERVATIONS = 'todays_checkin_cancelled_reservations'
    _LATE_RESERVATION_CANCELLATIONS_FOR_TODAY = (
        'late_reservation_cancellations_for_today'
    )
    _RESERVATION_MADE_TODAY = 'reservation_made_today'
    _RESERVATION_CANCELLATION_MADE_TODAY = 'reservation_cancellation_made_today'
    _ROOM_NIGHTS_RESERVED_TODAY = 'room_nights_reserved_today'
    _CLEAN_ROOMS = 'clean_rooms'
    _DIRTY_ROOMS = 'dirty_rooms'
    _OOS_ROOMS = 'oos_rooms'
    _DOUBLE_AS_SINGLES = 'double_as_singles'
    _PERCENTAGE_OF_OCCUPIED_BEDS = 'percentage_of_occupied_beds'
    _PERCENTAGE_OF_MULTIPLE_OCCUPANCY = 'percentage_of_multiple_occupancy'
    _ADR = 'adr'
    _ADR_MINUS_COMP = 'adr_minus_comp'
    _ADR_MINUS_HOUSE = 'adr_minus_house'
    _ADR_MINUS_HOUSE_AND_COMP = 'adr_minus_house_and_comp'
    _AVERAGE_PERSON_RATE = 'average_person_rate'
    _ROOM_REVENUE = 'room_revenue'
    _FNB_REVENUE = 'food_&_beverage_revenue'
    _OTHER_REVENUE = 'other_revenue'
    _TOTAL_REVENUE = 'total_revenue'
    _MEMBER_REVENUE = 'member_revenue'
    _MEMBER_ROOM_REVENUE = 'member_room_revenue'
    _TOTAL_REVENUE_PER_PERSON = 'total_revenue_per_person'
    _REV_PAR = 'rev_par'  # Revenue per available room
    _PAYMENT = 'payment'
    _REFUND = 'refund'
    _CREDIT_OFFERED = 'credit_offered'
    _TOMORROW_ARRIVAL_PERSONS = 'tomorrow_arrival_persons'
    _TOMORROW_ARRIVAL_ROOMS = 'tomorrow_arrival_rooms'
    _TOMORROW_DEPARTURE_ROOMS = 'tomorrow_departure_rooms'
    _TOMORROW_DEPARTURE_PERSONS = 'tomorrow_departure_persons'
    _PERCENTAGE_OF_ROOM_OCCUPIED_FOR_TOMORROW = (
        'percentage_of_room_occupied_for_tomorrow'
    )
    _PERCENTAGE_OF_OCCUPIED_ROOMS_FOR_NEXT_SEVEN_DAYS = (
        'percentage_of_occupied_rooms_for_next_seven_days'
    )
    _PERCENTAGE_OF_PROJECTED_OCCUPIED_ROOMS_FOR_CURRENT_MONTH = (
        'percentage_of_projected_occupied_rooms_for_current_month'
    )
    _PERCENTAGE_OF_PROJECTED_OCCUPIED_ROOMS_FOR_CURRENT_YEAR = (
        'percentage_of_projected_occupied_rooms_for_current_year'
    )
    _POS_TOTAL_REVENUE = 'pos_total_revenue'

    _CATEGORY_ROOMS_AND_RESERVATIONS = [
        ('Total Rooms in Hotel', _TOTAL_ROOMS_IN_HOTEL),
        ('OOO Rooms', _OOO_ROOMS),
        (
            'Total Rooms in Hotel minus OOO Rooms',
            _TOTAL_ROOMS_IN_HOTEL_EXCLUDING_OOO_ROOMS,
        ),
        ('Rooms Occupied', _OCCUPIED_ROOMS),
        ('Available Rooms', _AVAILABLE_ROOMS),
        ('Available Rooms minus OOO Rooms', _AVAILABLE_ROOMS_EXCLUDING_OOO_ROOMS),
        ('Complimentary Rooms', _COMPLIMENTARY_ROOMS),
        ('House Use Rooms', _HOUSE_USE_ROOMS),
        ('Day Use Rooms', _DAY_USE_ROOMS),
        (
            'Rooms Occupied minus Comp and House Use',
            _OCCUPIED_ROOMS_EXCLUDING_COMP_AND_HOUSE_USE,
        ),
        ('Rooms Occupied minus House Use', _OCCUPIED_ROOMS_EXCLUDING_HOUSE_USE),
        ('Rooms Occupied minus Comp', _OCCUPIED_ROOMS_EXCLUDING_COMP_ROOMS),
        ('% Rooms Occupied', _PERCENTAGE_OF_OCCUPIED_ROOMS),
        (
            '% Rooms Occupied minus Comp and House',
            _PERCENTAGE_OF_OCCUPIED_ROOMS_EXCLUDING_COMP_AND_HOUSE_ROOMS,
        ),
        ('% Rooms Occupied minus Comp', _PERCENTAGE_OF_OCCUPIED_ROOMS_EXCLUDING_COMP),
        ('% Rooms Occupied minus House', _PERCENTAGE_OF_OCCUPIED_ROOMS_EXCLUDING_HOUSE),
        (
            '% Rooms Occupied minus Comp and OOO',
            _PERCENTAGE_OF_OCCUPIED_ROOMS_EXCLUDING_COMP_AND_OOO,
        ),
        (
            '% Rooms Occupied minus Comp, House and OOO',
            _PERCENTAGE_OF_OCCUPIED_ROOMS_EXCLUDING_COMP_AND_HOUSE_OOO_ROOMS,
        ),
        (
            '% Rooms Occupied minus House and OOO',
            _PERCENTAGE_OF_OCCUPIED_ROOMS_EXCLUDING_HOUSE_AND_OOO,
        ),
        ('% Rooms Occupied minus OOO', _PERCENTAGE_OF_OCCUPIED_ROOMS_EXCLUDING_OOO),
        ('In-House Adults', _IN_HOUSE_ADULTS),
        ('In-House Children', _IN_HOUSE_CHILDREN),
        ('Total In-House Persons', _TOTAL_IN_HOUSE_PERSONS),
        ('Individual Persons In-House', _IN_HOUSE_NON_CORPORATE_PERSONS),
        ('Member Persons In-House', _IN_HOUSE_CORPORATE_PERSONS),
        # ('Individual Rooms In-House', _IN_HOUSE_INDIVIDUAL_ROOMS),
        ('Company Rooms In-House', _IN_HOUSE_CORPORATE_ROOMS),
        ('Travel Agent Rooms In-House', _IN_HOUSE_TRAVEL_AGENT_ROOMS),
        ('Arrival Rooms', _ARRIVAL_ROOMS),
        ('Arrival Persons', _ARRIVAL_PERSONS),
        # ('Deducted Arrivals', _DEDUCTED_ARRIVALS),
        ('Walk-in Rooms', _WALK_IN_ROOMS),
        ('Walk-in Persons', _WALK_IN_PERSONS),
        ('Extended Stay Rooms', _EXTENDED_STAY_ROOMS),
        ('Extended Stay Persons', _EXTENDED_STAY_PERSONS),
        ('Departure Rooms', _DEPARTURE_ROOMS),
        ('Departure Persons', _DEPARTURE_PERSONS),
        ('Early Departure Rooms', _EARLY_DEPARTURE_ROOMS),
        ('Early Departure Persons', _EARLY_DEPARTURE_PERSONS),
        ('No Show Rooms', _NO_SHOW_ROOMS),
        ('No Show Persons', _NO_SHOW_PERSONS),
        ('Cancelled Reservations for Today', _TODAYS_CHECKIN_CANCELLED_RESERVATIONS),
        ('Late Cancellations for Today', _LATE_RESERVATION_CANCELLATIONS_FOR_TODAY),
        ('Reservations Made Today', _RESERVATION_MADE_TODAY),
        ('Cancellations Made Today', _RESERVATION_CANCELLATION_MADE_TODAY),
        ('Room Nights Reserved Today', _ROOM_NIGHTS_RESERVED_TODAY),
        ('Clean Rooms', _CLEAN_ROOMS),
        ('Dirty Rooms', _DIRTY_ROOMS),
        ('OOS Rooms', _OOS_ROOMS),
        ('Doubles As Singles', _DOUBLE_AS_SINGLES),
        ('% Beds Occupied', _PERCENTAGE_OF_OCCUPIED_BEDS),
        ('% Multiple Occupancy', _PERCENTAGE_OF_MULTIPLE_OCCUPANCY),
    ]

    _CATEGORY_FINANCE = [
        ('ADR', _ADR),
        ('ADR minus Comp', _ADR_MINUS_COMP),
        ('ADR minus House', _ADR_MINUS_HOUSE),
        ('ADR minus Comp and House', _ADR_MINUS_HOUSE_AND_COMP),
        ('Average Person Rate', _AVERAGE_PERSON_RATE),
        ('Room Revenue', _ROOM_REVENUE),
        ('Food And Beverage Revenue', _FNB_REVENUE),
        ('Other Revenue', _OTHER_REVENUE),
        ('Total Revenue', _TOTAL_REVENUE),
        ('Member Revenue', _MEMBER_REVENUE),
        ('Member Room Revenue', _MEMBER_ROOM_REVENUE),
        ('Total Revenue per Person', _TOTAL_REVENUE_PER_PERSON),
        ('revPAR', _REV_PAR),
        ('Payments', _PAYMENT),
        ('Refunds', _REFUND),
        # ('Credit Offered', _CREDIT_OFFERED),
    ]

    _CATEGORY_FORECAST = [
        ('Arrival Rooms for Tomorrow', _TOMORROW_ARRIVAL_ROOMS),
        ('Arrival Persons for Tomorrow', _TOMORROW_ARRIVAL_PERSONS),
        ('Departure Rooms for Tomorrow', _TOMORROW_DEPARTURE_ROOMS),
        ('Departure Persons for Tomorrow', _TOMORROW_DEPARTURE_PERSONS),
        ('% Rooms Occupied for Tomorrow', _PERCENTAGE_OF_ROOM_OCCUPIED_FOR_TOMORROW),
        (
            '% Rooms Occupied for the next 7 days',
            _PERCENTAGE_OF_OCCUPIED_ROOMS_FOR_NEXT_SEVEN_DAYS,
        ),
        (
            'Projected % Rooms Occupied for current month',
            _PERCENTAGE_OF_PROJECTED_OCCUPIED_ROOMS_FOR_CURRENT_MONTH,
        ),
        (
            'Projected % Rooms Occupied for current year',
            _PERCENTAGE_OF_PROJECTED_OCCUPIED_ROOMS_FOR_CURRENT_YEAR,
        ),
    ]

    # this will have dynamic fields
    _CATEGORY_POS = [("POS total revenue", _POS_TOTAL_REVENUE)]

    def __init__(
        self,
        booking_repository,
        bill_repository,
        hotel_repository,
        room_type_repository,
        reporting_application_service,
        dnr_repository,
        room_repository,
        room_allotment_repository,
        flash_manager_report_repository,
        seller_repository,
        tenant_settings,
    ):
        self.booking_repository = booking_repository
        self.bill_repository = bill_repository
        self.hotel_repository = hotel_repository
        self.room_type_repository = room_type_repository
        self.reporting_application_service = reporting_application_service
        self.dnr_repository = dnr_repository
        self.room_repository = room_repository
        self.room_allotment_repository = room_allotment_repository
        self.flash_manager_report_repository = flash_manager_report_repository
        self.seller_repository = seller_repository
        self.tenant_settings = tenant_settings

    @bypass_access_entity_checks
    def _base_report_data(self, report_date, hotel_aggregate):
        # bypassing access entity check as we are accessing seller's bills from hotel_id
        booking_aggregates = self.booking_repository.manager_flash_report_query(
            report_date=report_date, hotel_id=hotel_aggregate.hotel_id
        )
        seller_map = {
            seller_aggregate.seller.seller_id: seller_aggregate
            for seller_aggregate in self.seller_repository.load_for_hotel_id(
                hotel_aggregate.hotel_id
            )
        }
        pos_bill_aggregates = []
        if seller_map:
            pos_bill_aggregates = (
                self.bill_repository.manager_flash_report_query_for_pos(
                    report_date=report_date, vendor_ids=seller_map.keys()
                )
            )
        room_type_map = self.room_type_repository.load_type_map()
        room_aggregates = self.room_repository.load_multiple(
            hotel_id=hotel_aggregate.hotel_id, skip_inactive=True
        )
        room_ids = [room_aggregate.room.room_id for room_aggregate in room_aggregates]
        dnr_aggregates = self.dnr_repository.load_dnrs(
            hotel_id=hotel_aggregate.hotel_id,
            from_date=report_date,
            to_date=report_date,
            room_ids=room_ids,
        )
        room_allotment_aggregates = self.room_allotment_repository.load_multiple(
            hotel_id=hotel_aggregate.hotel_id, room_ids=room_ids, start_time=report_date
        )

        return (
            booking_aggregates,
            room_aggregates,
            dnr_aggregates,
            room_allotment_aggregates,
            room_type_map,
            seller_map,
            pos_bill_aggregates,
        )

    @staticmethod
    def _get_house_account_room_type_id(room_type_map):
        for room_type_id, room in room_type_map.items():
            if room.room_type.type == HOUSE_ACCOUNT_ROOM_TYPE:
                return room_type_id
        return None

    @staticmethod
    def _get_clean_and_dirty_rooms(
        hotel_aggregate, room_allotment_aggregates, exclude_room_ids=None
    ):
        clean_rooms, dirty_rooms, oos_rooms = 0, 0, 0
        if not hotel_aggregate.hotel.housekeeping_enabled:
            return clean_rooms, dirty_rooms, oos_rooms
        for (
            room_allotment_id,
            room_allotment_aggregate,
        ) in room_allotment_aggregates.items():
            if (
                exclude_room_ids
                and room_allotment_aggregate.housekeeping_record.room_id
                in exclude_room_ids
            ):
                continue
            if (
                room_allotment_aggregate.housekeeping_record.housekeeping_status
                == HouseKeepingStatus.DIRTY
            ):
                dirty_rooms += 1
            elif (
                room_allotment_aggregate.housekeeping_record.housekeeping_status
                == HouseKeepingStatus.CLEAN
            ):
                clean_rooms += 1
            elif (
                room_allotment_aggregate.housekeeping_record.housekeeping_status
                == HouseKeepingStatus.OUT_OF_SERVICE
            ):
                oos_rooms += 1
        return clean_rooms, dirty_rooms, oos_rooms

    @staticmethod
    def _get_house_account_room_count_and_rooms_ids(
        room_aggregates, house_account_room_type_id
    ):
        house_account_room_count = 0
        house_account_room_ids = []
        if not house_account_room_type_id:
            return house_account_room_ids, house_account_room_count
        for room_aggregate in room_aggregates:
            if room_aggregate.room.room_type_id == house_account_room_type_id:
                house_account_room_ids.append(room_aggregate.room.room_id)
                house_account_room_count += 1
        return house_account_room_ids, house_account_room_count

    @staticmethod
    def _get_rate_plan_id(booking_aggregate, rate_plan_code):
        for rate_plan in booking_aggregate.rate_plans:
            if rate_plan.rate_plan_code == rate_plan_code:
                return rate_plan.rate_plan_id
        return None

    @staticmethod
    def _get_pos_data(seller_map, pos_bill_aggregates):
        pos_data_dict = defaultdict(dict)
        for bill_aggregate in pos_bill_aggregates:
            seller = seller_map.get(bill_aggregate.vendor_id)
            for charge in bill_aggregate.get_all_consumed_charges():
                pos_data_dict[bill_aggregate.vendor_id][
                    "seller_name"
                ] = seller.seller.name
                pos_data_dict[bill_aggregate.vendor_id][
                    "seller_id"
                ] = seller.seller.seller_id
                pos_data_dict[bill_aggregate.vendor_id]["pretax_revenue"] = (
                    pos_data_dict[bill_aggregate.vendor_id].get(
                        'pretax_revenue', Money(0, bill_aggregate.base_currency)
                    )
                    + charge.get_pretax_amount_post_allowance()
                )
                pos_data_dict[bill_aggregate.vendor_id]["posttax_revenue"] = (
                    pos_data_dict[bill_aggregate.vendor_id].get(
                        'posttax_revenue', Money(0, bill_aggregate.base_currency)
                    )
                    + charge.get_posttax_amount_post_allowance()
                )
        # [{"seller_name":"restaurant", "seller_id":"1002","pretax_revenue":"300 INR", "posttax_revenue":"500 INR"}]
        return [pos_data for _, pos_data in pos_data_dict.items()]

    @timed
    def _get_booking_sku_allowances_map(self, hotel_id, report_date):
        booking_sku_allowances_map = defaultdict(lambda: defaultdict(list))
        bill_aggregates = (
            self.bill_repository.manager_flash_report_query_for_allowances(
                hotel_id, report_date
            )
        )
        bill_ids = [bill_aggregate.bill_id for bill_aggregate in bill_aggregates]
        booking_aggregates = self.booking_repository.load_for_bill_ids_with_yield_per(
            bill_ids
        )
        booking_aggregate_map = {
            booking_aggregate.bill_id: booking_aggregate
            for booking_aggregate in booking_aggregates
        }

        for bill_aggregate in bill_aggregates:
            for charge in bill_aggregate.get_all_consumed_charges():
                for charge_split in charge.charge_splits:
                    for allowance in charge_split.allowances:
                        if allowance.posting_date == report_date:
                            booking_sku_allowances_map[
                                booking_aggregate_map[bill_aggregate.bill_id]
                            ][charge.item.sku_category_id].append(allowance)

        return booking_sku_allowances_map

    def _generate_report_details(
        self,
        report_date,
        booking_aggregates,
        room_aggregates,
        dnr_aggregates,
        room_allotment_aggregates,
        room_type_map,
        hotel_aggregate,
        seller_map,
        pos_bill_aggregates,
    ):
        complimentary_rooms = 0
        in_house_non_corporate_adults = 0
        in_house_non_corporate_children = 0
        in_house_corporate_adults = 0
        in_house_corporate_children = 0
        in_house_corporate_rooms = 0
        in_house_individual_rooms = 0
        in_house_travel_agent_rooms = 0
        arrival_rooms = 0
        arrival_adults = 0
        arrival_children = 0
        walk_in_rooms = 0
        walk_in_adults = 0
        walk_in_children = 0
        extended_stay_rooms = 0
        extended_stay_adults = 0
        extended_stay_children = 0
        departure_rooms = 0
        departure_adults = 0
        departure_children = 0
        early_departure_rooms = 0
        early_departure_adults = 0
        early_departure_children = 0
        no_show_rooms = 0
        no_show_adults = 0
        no_show_children = 0
        occupied_rooms = 0
        multiple_occupancy = 0
        house_account_in_use_rooms = 0
        in_house_adults = 0
        in_house_children = 0
        todays_checkin_cancelled_reservations = 0
        late_cancelled_reservations_today = 0
        reservation_made_today = 0
        reservation_cancellation_made_today = 0
        house_use_rooms = 0
        day_use_rooms = 0
        room_nights_reserved_today = 0
        doubles_as_singles = 0
        max_occupancy = 0
        pretax_booking_revenue_today = list()
        pretax_room_revenue_today = list()
        pretax_fnb_revenue_today = list()
        pretax_other_revenue_today = list()
        pretax_member_revenue_today = list()
        pretax_member_room_revenue_today = list()
        posttax_booking_revenue_today = list()
        posttax_room_revenue_today = list()
        posttax_fnb_revenue_today = list()
        posttax_other_revenue_today = list()
        posttax_member_revenue_today = list()
        posttax_member_room_revenue_today = list()
        total_payments_made_today = Money(
            0, currency=crs_context.hotel_context.base_currency
        )
        total_refunds_made_today = Money(
            0, currency=crs_context.hotel_context.base_currency
        )
        total_credit_offered_today = Money(
            0, currency=crs_context.hotel_context.base_currency
        )
        tomorrow_arrival_rooms = 0
        tomorrow_arrival_adults = 0
        tomorrow_arrival_children = 0
        tomorrow_departure_rooms = 0
        tomorrow_departure_adults = 0
        tomorrow_departure_children = 0
        tomorrow_occupied_rooms = 0
        occupied_rooms_for_next_seven_days = 0
        occupied_rooms_for_current_month = 0
        occupied_rooms_for_current_year = 0

        next_day_of_report_date = dateutils.add(report_date, days=1)
        # PROM-6854 seventh day date, month end date and year end date starts from tomorrow under forecast fields
        seventh_day_date = dateutils.add(next_day_of_report_date, days=7)
        last_date_of_month = dateutils.last_date_of_month(next_day_of_report_date)
        last_date_of_year = next_day_of_report_date.replace(month=12, day=31)

        house_account_room_type_id = self._get_house_account_room_type_id(room_type_map)
        (
            house_account_room_ids,
            house_account_room_count,
        ) = self._get_house_account_room_count_and_rooms_ids(
            room_aggregates, house_account_room_type_id
        )
        clean_rooms, dirty_rooms, oos_rooms = self._get_clean_and_dirty_rooms(
            hotel_aggregate,
            room_allotment_aggregates,
            exclude_room_ids=house_account_room_ids,
        )

        total_rooms_in_hotel = len(
            [
                room_aggregate.room.room_id
                for room_aggregate in room_aggregates
                if room_aggregate.room.room_id not in house_account_room_ids
            ]
        )
        dnr_rooms = len(
            [
                dnr_aggregate
                for dnr_aggregate in dnr_aggregates
                if dnr_aggregate.dnr.room_id not in house_account_room_ids
            ]
        )
        hotel_room_type_configs_map = {
            room_type_config.room_type_id: room_type_config.max_occupancy.total()
            for room_type_config in hotel_aggregate.room_type_configs
        }

        bill_ids = {
            booking_aggregate.bill_id for booking_aggregate in booking_aggregates
        }

        aggregate_maps = self.reporting_application_service.build_aggregate_maps(
            bill_ids=bill_ids,
            load_invoices_by_bill_id=False,
            room_type_map=room_type_map,
        )
        pos_field_data = self._get_pos_data(seller_map, pos_bill_aggregates)

        for item in pos_field_data:
            pretax_booking_revenue_today.append(item.get('pretax_revenue'))
            posttax_booking_revenue_today.append(item.get('posttax_revenue'))

        booking_sku_allowances_map = self._get_booking_sku_allowances_map(
            hotel_aggregate.hotel_id, report_date
        )
        room_stay_included_sku_categories = [
            FlashManagerSkuCategories.STAY.value,
            FlashManagerSkuCategories.STAY12.value,
            FlashManagerSkuCategories.STAY18.value,
            FlashManagerSkuCategories.ROOM.value,
        ]
        fnb_included_sku_categories = [
            FlashManagerSkuCategories.BEVERAGES.value,
            FlashManagerSkuCategories.FOOD.value,
            FlashManagerSkuCategories.ALCOHOL.value,
            FlashManagerSkuCategories.FOOD_AND_BEVERAGES.value,
        ]

        for booking_aggregate, sku_allowances_map in booking_sku_allowances_map.items():
            for sku_category_id, allowances in sku_allowances_map.items():
                for allowance in allowances:
                    pretax_booking_revenue_today.append(-allowance.pretax_amount)
                    posttax_booking_revenue_today.append(-allowance.posttax_amount)

                    if sku_category_id in room_stay_included_sku_categories:
                        pretax_room_revenue_today.append(-allowance.pretax_amount)
                        posttax_room_revenue_today.append(-allowance.posttax_amount)
                    if sku_category_id in fnb_included_sku_categories:
                        pretax_fnb_revenue_today.append(-allowance.pretax_amount)
                        posttax_fnb_revenue_today.append(-allowance.posttax_amount)
                    if (
                        sku_category_id
                        not in room_stay_included_sku_categories
                        + fnb_included_sku_categories
                    ):
                        pretax_other_revenue_today.append(-allowance.pretax_amount)
                        posttax_other_revenue_today.append(-allowance.posttax_amount)
                    if booking_aggregate.booking.membership_id:
                        pretax_member_revenue_today.append(-allowance.pretax_amount)
                        posttax_member_revenue_today.append(-allowance.posttax_amount)
                        if sku_category_id in room_stay_included_sku_categories:
                            pretax_member_room_revenue_today.append(
                                -allowance.pretax_amount
                            )
                            posttax_member_room_revenue_today.append(
                                -allowance.posttax_amount
                            )

        for booking_aggregate in booking_aggregates:
            bill_aggregate = aggregate_maps.bill_map.get(
                booking_aggregate.booking.bill_id
            )

            house_use_rate_plan_id = self._get_rate_plan_id(
                booking_aggregate, HOUSE_USE_RATE_PLAN_CODE
            )
            for payment in bill_aggregate.payments:
                if (
                    payment.status in (PaymentStatus.DONE, PaymentStatus.POSTED)
                    and dateutils.to_date(payment.date_of_payment) == report_date
                ):
                    if payment.payment_type == PaymentTypes.PAYMENT:
                        total_payments_made_today += payment.amount
                    elif payment.payment_type == PaymentTypes.REFUND:
                        total_refunds_made_today += payment.amount
                    elif payment.payment_type == PaymentTypes.CREDIT_OFFERED:
                        total_credit_offered_today += payment.amount
            if (
                booking_aggregate.booking.status == BookingStatus.CANCELLED
                and dateutils.to_date(booking_aggregate.booking.checkin_date)
                == report_date
            ):
                todays_checkin_cancelled_reservations += 1

            if (
                booking_aggregate.booking.status == BookingStatus.CANCELLED
                and dateutils.to_date(booking_aggregate.booking.cancellation_datetime)
                == dateutils.to_date(booking_aggregate.booking.checkin_date)
                == report_date
            ):
                late_cancelled_reservations_today += 1

            if dateutils.to_date(booking_aggregate.booking.created_at) == report_date:
                reservation_made_today += 1

            if (
                booking_aggregate.booking.status == BookingStatus.CANCELLED
                and dateutils.to_date(booking_aggregate.booking.cancellation_datetime)
                == report_date
            ):
                reservation_cancellation_made_today += 1

            for room_stay in booking_aggregate.room_stays:
                if (
                    room_stay.is_noshow()
                    and room_stay.stay_start == report_date
                    and room_stay.room_type_id != house_account_room_type_id
                ):
                    no_show_rooms += 1
                    no_show_adults += len(room_stay.adult_guest_stays())
                    no_show_children += len(room_stay.child_guest_stays())

                if booking_aggregate.booking.status not in {
                    BookingStatus.CANCELLED,
                    BookingStatus.NOSHOW,
                } and room_stay.status not in {
                    BookingStatus.CANCELLED,
                    BookingStatus.NOSHOW,
                }:
                    if (
                        dateutils.to_date(booking_aggregate.booking.created_at)
                        == report_date
                        and room_stay.actual_stay_start_date
                        != room_stay.actual_stay_end_date
                    ):
                        room_nights_reserved_today += (
                            room_stay.stay_end - room_stay.stay_start
                        ).days

                    if (
                        room_stay.stay_start == next_day_of_report_date
                        and room_stay.room_type_id != house_account_room_type_id
                    ):
                        tomorrow_arrival_rooms += 1
                        tomorrow_arrival_adults += len(room_stay.adult_guest_stays())
                        tomorrow_arrival_children += len(room_stay.child_guest_stays())
                    if (
                        room_stay.stay_end == next_day_of_report_date
                        and room_stay.room_type_id != house_account_room_type_id
                    ):
                        tomorrow_departure_rooms += 1
                        tomorrow_departure_adults += len(room_stay.adult_guest_stays())
                        tomorrow_departure_children += len(
                            room_stay.child_guest_stays()
                        )
                    if (
                        room_stay.stay_start
                        <= next_day_of_report_date
                        <= room_stay.stay_end
                        and room_stay.room_type_id != house_account_room_type_id
                        and room_stay.status
                        in (
                            BookingStatus.CONFIRMED,
                            BookingStatus.PART_CHECKIN,
                            BookingStatus.PART_CHECKOUT,
                            BookingStatus.CHECKED_IN,
                        )
                    ):
                        tomorrow_occupied_rooms += 1

                    if (
                        room_stay.stay_start <= seventh_day_date <= room_stay.stay_end
                        and room_stay.room_type_id != house_account_room_type_id
                        and room_stay.status
                        in (
                            BookingStatus.CONFIRMED,
                            BookingStatus.PART_CHECKIN,
                            BookingStatus.PART_CHECKOUT,
                            BookingStatus.CHECKED_IN,
                        )
                    ):
                        occupied_rooms_for_next_seven_days += 1

                    if (
                        room_stay.stay_start <= last_date_of_month <= room_stay.stay_end
                        and room_stay.room_type_id != house_account_room_type_id
                        and room_stay.status
                        in (
                            BookingStatus.CONFIRMED,
                            BookingStatus.PART_CHECKIN,
                            BookingStatus.PART_CHECKOUT,
                            BookingStatus.CHECKED_IN,
                        )
                    ):
                        occupied_rooms_for_current_month += 1

                    if (
                        room_stay.stay_start <= last_date_of_year <= room_stay.stay_end
                        and room_stay.room_type_id != house_account_room_type_id
                        and room_stay.status
                        in (
                            BookingStatus.CONFIRMED,
                            BookingStatus.PART_CHECKIN,
                            BookingStatus.PART_CHECKOUT,
                            BookingStatus.CHECKED_IN,
                        )
                    ):
                        occupied_rooms_for_current_year += 1

                    if (
                        room_stay.status
                        in {
                            BookingStatus.CHECKED_IN,
                            BookingStatus.PART_CHECKIN,
                            BookingStatus.PART_CHECKOUT,
                            BookingStatus.CHECKED_OUT,
                        }
                        and room_stay.stay_start <= report_date <= room_stay.stay_end
                    ):
                        all_charges_on_room_stay = (
                            booking_aggregate.get_all_applicable_charges_on_room_stay(
                                room_stay.room_stay_id
                            )
                        )
                        charges = bill_aggregate.filter_and_get_charges(
                            charge_ids=all_charges_on_room_stay,
                            allowed_charge_status={ChargeStatus.CONSUMED},
                        )

                        for charge in charges:
                            if charge.posting_date != report_date:
                                continue
                            pretax_booking_revenue_today.append(charge.pretax_amount)
                            posttax_booking_revenue_today.append(charge.posttax_amount)

                            if (
                                charge.item.sku_category_id
                                in room_stay_included_sku_categories
                            ):
                                if (
                                    room_stay.room_type_id != house_account_room_type_id
                                    and not charge.posttax_amount
                                ):
                                    if room_stay.room_rate_plans:
                                        for room_rate_plan in room_stay.room_rate_plans:
                                            if (
                                                room_rate_plan.rate_plan_id
                                                != house_use_rate_plan_id
                                            ):
                                                complimentary_rooms += 1
                                                break
                                    else:
                                        complimentary_rooms += 1

                                pretax_room_revenue_today.append(charge.pretax_amount)
                                posttax_room_revenue_today.append(charge.posttax_amount)
                            if (
                                charge.item.sku_category_id
                                in fnb_included_sku_categories
                            ):
                                pretax_fnb_revenue_today.append(charge.pretax_amount)
                                posttax_fnb_revenue_today.append(charge.posttax_amount)
                            elif (
                                charge.item.sku_category_id
                                not in room_stay_included_sku_categories
                                + fnb_included_sku_categories
                            ):
                                pretax_other_revenue_today.append(charge.pretax_amount)
                                posttax_other_revenue_today.append(
                                    charge.posttax_amount
                                )
                            if booking_aggregate.booking.membership_id:
                                pretax_member_revenue_today.append(charge.pretax_amount)
                                posttax_member_revenue_today.append(
                                    charge.posttax_amount
                                )
                                if (
                                    charge.item.sku_category_id
                                    in room_stay_included_sku_categories
                                ):
                                    pretax_member_room_revenue_today.append(
                                        charge.pretax_amount
                                    )
                                    posttax_member_room_revenue_today.append(
                                        charge.posttax_amount
                                    )

                        if room_stay.room_type_id != house_account_room_type_id:
                            if room_stay.stay_start == report_date:
                                arrival_rooms += 1
                                arrival_adults += len(room_stay.adult_guest_stays())
                                arrival_children += len(room_stay.child_guest_stays())

                            if room_stay.stay_end == report_date:
                                departure_rooms += 1
                                departure_adults += len(room_stay.adult_guest_stays())
                                departure_children += len(room_stay.child_guest_stays())

                            if (
                                room_stay.status
                                in (
                                    BookingStatus.CHECKED_OUT,
                                    BookingStatus.PART_CHECKOUT,
                                )
                                and dateutils.to_date(room_stay.actual_checkout_date)
                                == report_date
                                and dateutils.to_date(room_stay.checkout_date)
                                > report_date
                            ):
                                early_departure_rooms += 1
                                early_departure_adults += len(
                                    room_stay.adult_guest_stays()
                                )
                                early_departure_children += len(
                                    room_stay.child_guest_stays()
                                )

                        if room_stay.stay_start <= report_date < room_stay.stay_end:
                            if room_stay.room_type_id == house_account_room_type_id:
                                house_account_in_use_rooms += 1

                            if (
                                room_stay.room_type_id != house_account_room_type_id
                                and room_stay.actual_stay_start_date
                                == room_stay.actual_stay_end_date
                            ):
                                day_use_rooms += 1

                            if (
                                room_stay.room_type_id != house_account_room_type_id
                                and room_stay.actual_stay_start_date
                                != room_stay.actual_stay_end_date
                            ):
                                occupied_rooms += 1
                                in_house_adults += len(room_stay.adult_guest_stays())
                                in_house_children += len(room_stay.child_guest_stays())
                                max_occupancy += hotel_room_type_configs_map.get(
                                    room_stay.room_type_id, 0
                                )

                                if in_house_adults + in_house_children > 1:
                                    multiple_occupancy += 1

                                if (
                                    room_stay.max_occupancy > 1
                                    and len(room_stay.adult_guest_stays())
                                    + len(room_stay.child_guest_stays())
                                    == 1
                                ):
                                    doubles_as_singles += 1

                                if booking_aggregate.is_b2b_booking():
                                    in_house_corporate_rooms += 1
                                    in_house_corporate_adults += len(
                                        room_stay.adult_guest_stays()
                                    )
                                    in_house_corporate_children += len(
                                        room_stay.child_guest_stays()
                                    )
                                else:
                                    in_house_individual_rooms += 1
                                    in_house_non_corporate_adults += len(
                                        room_stay.adult_guest_stays()
                                    )
                                    in_house_non_corporate_children += len(
                                        room_stay.child_guest_stays()
                                    )

                                if booking_aggregate.is_ta_booking():
                                    in_house_travel_agent_rooms += 1

                                if room_stay.room_rate_plans:
                                    for room_rate_plan in room_stay.room_rate_plans:
                                        if (
                                            room_rate_plan.stay_date == report_date
                                            and room_rate_plan.rate_plan_id
                                            == house_use_rate_plan_id
                                        ):
                                            house_use_rooms += 1

                                if (
                                    booking_aggregate.booking.source.is_walk_in_channel()
                                ):
                                    walk_in_rooms += 1
                                    walk_in_adults += len(room_stay.adult_guest_stays())
                                    walk_in_children += len(
                                        room_stay.child_guest_stays()
                                    )

                                if (
                                    dateutils.to_date(room_stay.checkout_date)
                                    == report_date
                                    and room_stay.actual_checkout_date
                                    and room_stay.actual_checkout_date
                                    > room_stay.checkout_date
                                ):
                                    extended_stay_rooms += 1
                                    extended_stay_adults += len(
                                        room_stay.adult_guest_stays()
                                    )
                                    extended_stay_children += len(
                                        room_stay.child_guest_stays()
                                    )

        total_in_house_persons = in_house_children + in_house_adults
        arrival_persons = arrival_adults + arrival_children
        walk_in_persons = walk_in_adults + walk_in_children
        departure_persons = departure_adults + departure_children
        no_show_persons = no_show_adults + no_show_children

        percentage_of_occupied_rooms = str(
            round((occupied_rooms / total_rooms_in_hotel) * 100, 2)
        )
        percentage_of_occupied_rooms_excluding_comp_and_house_rooms = str(
            round(
                (
                    (occupied_rooms - complimentary_rooms - house_use_rooms)
                    / total_rooms_in_hotel
                )
                * 100,
                2,
            )
        )
        percentage_of_occupied_rooms_excluding_comp = str(
            round(
                ((occupied_rooms - complimentary_rooms) / total_rooms_in_hotel) * 100, 2
            )
        )
        percentage_of_occupied_rooms_excluding_house = str(
            round(((occupied_rooms - house_use_rooms) / total_rooms_in_hotel) * 100, 2)
        )
        percentage_of_occupied_rooms_excluding_comp_and_dnr = (
            str(
                round(
                    (occupied_rooms - complimentary_rooms)
                    / (total_rooms_in_hotel - dnr_rooms)
                    * 100,
                    2,
                )
            )
            if (total_rooms_in_hotel - dnr_rooms)
            else "0"
        )
        percentage_of_occupied_rooms_excluding_house_and_dnr = (
            str(
                round(
                    (occupied_rooms - house_use_rooms)
                    / (total_rooms_in_hotel - dnr_rooms)
                    * 100,
                    2,
                )
            )
            if (total_rooms_in_hotel - dnr_rooms)
            else "0"
        )
        percentage_of_occupied_rooms_excluding_dnr = (
            str(round(occupied_rooms / (total_rooms_in_hotel - dnr_rooms) * 100, 2))
            if (total_rooms_in_hotel - dnr_rooms)
            else "0"
        )
        percentage_of_occupied_beds = (
            str(round((total_in_house_persons / max_occupancy) * 100, 2))
            if max_occupancy
            else "0"
        )
        percentage_of_multiple_occupancy = (
            str(round((multiple_occupancy / occupied_rooms) * 100, 2))
            if occupied_rooms
            else "0"
        )

        pretax_room_revenue = sum(pretax_room_revenue_today)
        posttax_room_revenue = sum(posttax_room_revenue_today)
        pretax_fnb_revenue = sum(pretax_fnb_revenue_today)
        posttax_fnb_revenue = sum(posttax_fnb_revenue_today)
        pretax_other_revenue = sum(pretax_other_revenue_today)
        posttax_other_revenue = sum(posttax_other_revenue_today)
        pretax_total_revenue = sum(pretax_booking_revenue_today)
        posttax_total_revenue = sum(posttax_booking_revenue_today)
        pretax_member_revenue = sum(pretax_member_revenue_today)
        posttax_member_revenue = sum(posttax_member_revenue_today)
        pretax_member_room_revenue = sum(pretax_member_room_revenue_today)
        posttax_member_room_revenue = sum(posttax_member_room_revenue_today)
        pretax_adr = pretax_room_revenue / occupied_rooms if occupied_rooms else 0
        posttax_adr = posttax_room_revenue / occupied_rooms if occupied_rooms else 0
        pretax_adr_minus_comp = (
            pretax_room_revenue / (occupied_rooms - complimentary_rooms)
            if (occupied_rooms - complimentary_rooms)
            else 0
        )
        posttax_adr_minus_comp = (
            posttax_room_revenue / (occupied_rooms - complimentary_rooms)
            if (occupied_rooms - complimentary_rooms)
            else 0
        )
        pretax_adr_minus_house = (
            pretax_room_revenue / (occupied_rooms - house_use_rooms)
            if (occupied_rooms - house_use_rooms)
            else 0
        )
        posttax_adr_minus_house = (
            posttax_room_revenue / (occupied_rooms - house_use_rooms)
            if (occupied_rooms - house_use_rooms)
            else 0
        )
        pretax_adr_minus_house_and_comp = (
            pretax_room_revenue
            / (occupied_rooms - house_use_rooms - complimentary_rooms)
            if (occupied_rooms - house_use_rooms - complimentary_rooms)
            else 0
        )
        posttax_adr_minus_house_and_comp = (
            posttax_room_revenue
            / (occupied_rooms - house_use_rooms - complimentary_rooms)
            if (occupied_rooms - house_use_rooms - complimentary_rooms)
            else 0
        )
        pretax_average_person_rate = (
            pretax_room_revenue / total_in_house_persons
            if total_in_house_persons
            else 0
        )
        posttax_average_person_rate = (
            posttax_room_revenue / total_in_house_persons
            if total_in_house_persons
            else 0
        )
        pretax_total_revenue_per_person = (
            pretax_total_revenue / total_in_house_persons
            if total_in_house_persons
            else 0
        )
        posttax_total_revenue_per_person = (
            posttax_total_revenue / total_in_house_persons
            if total_in_house_persons
            else 0
        )
        posttax_rev_par = (
            posttax_room_revenue / (total_rooms_in_hotel - dnr_rooms)
            if (total_rooms_in_hotel - dnr_rooms)
            else 0
        )
        pretax_rev_par = (
            pretax_room_revenue / (total_rooms_in_hotel - dnr_rooms)
            if (total_rooms_in_hotel - dnr_rooms)
            else 0
        )

        # forecast fields
        tomorrow_arrival_persons = tomorrow_arrival_adults + tomorrow_arrival_children
        tomorrow_departure_persons = (
            tomorrow_departure_adults + tomorrow_departure_children
        )
        percentage_of_rooms_occupied_tomorrow = str(
            round((tomorrow_occupied_rooms / total_rooms_in_hotel) * 100, 2)
        )
        percentage_of_occupied_rooms_for_next_seven_days = str(
            round(
                (occupied_rooms_for_next_seven_days / (total_rooms_in_hotel * 7)) * 100,
                2,
            )
        )
        percentage_of_occupied_rooms_for_current_month = str(
            round(
                (
                    occupied_rooms_for_current_month
                    / (total_rooms_in_hotel * (last_date_of_month - report_date).days)
                )
                * 100,
                2,
            )
        )
        percentage_of_occupied_rooms_for_current_year = str(
            round(
                (
                    occupied_rooms_for_current_year
                    / (total_rooms_in_hotel * (last_date_of_year - report_date).days)
                )
                * 100,
                2,
            )
        )

        room_and_reservation_field_data = dict(
            total_rooms_in_hotel=total_rooms_in_hotel,
            dnr_rooms=dnr_rooms,
            total_rooms_in_hotel_excluding_dnr_rooms=total_rooms_in_hotel - dnr_rooms,
            occupied_rooms=occupied_rooms,
            multiple_occupancy=multiple_occupancy,
            available_rooms=total_rooms_in_hotel - occupied_rooms,
            available_rooms_excluding_dnr_rooms=total_rooms_in_hotel
            - dnr_rooms
            - occupied_rooms,
            complimentary_rooms=complimentary_rooms,
            house_use_rooms=house_use_rooms,
            day_use_rooms=day_use_rooms,
            occupied_rooms_excluding_comp_and_house_use=(
                occupied_rooms - complimentary_rooms - house_use_rooms
            ),
            occupied_rooms_excluding_house_use=occupied_rooms - house_use_rooms,
            occupied_rooms_excluding_comp_rooms=occupied_rooms - complimentary_rooms,
            percentage_of_occupied_rooms=percentage_of_occupied_rooms,
            percentage_of_occupied_rooms_excluding_comp_and_house_rooms=(
                percentage_of_occupied_rooms_excluding_comp_and_house_rooms
            ),
            percentage_of_occupied_rooms_excluding_comp=percentage_of_occupied_rooms_excluding_comp,
            percentage_of_occupied_rooms_excluding_house=percentage_of_occupied_rooms_excluding_house,
            percentage_of_occupied_rooms_excluding_comp_and_dnr=percentage_of_occupied_rooms_excluding_comp_and_dnr,
            percentage_of_occupied_rooms_excluding_house_and_dnr=percentage_of_occupied_rooms_excluding_house_and_dnr,
            percentage_of_occupied_rooms_excluding_dnrs=percentage_of_occupied_rooms_excluding_dnr,
            in_house_adults=in_house_adults,
            in_house_children=in_house_children,
            total_in_house_persons=total_in_house_persons,
            in_house_non_corporate_persons=in_house_non_corporate_adults
            + in_house_non_corporate_children,
            in_house_corporate_persons=in_house_corporate_adults
            + in_house_corporate_children,
            in_house_corporate_rooms=in_house_corporate_rooms,
            in_house_individual_rooms=in_house_individual_rooms,
            in_house_travel_agent_rooms=in_house_travel_agent_rooms,
            arrival_rooms=arrival_rooms,
            arrival_persons=arrival_persons,
            walk_in_rooms=walk_in_rooms,
            walk_in_persons=walk_in_persons,
            extended_stay_rooms=extended_stay_rooms,
            extended_stay_persons=extended_stay_adults + extended_stay_children,
            departure_rooms=departure_rooms,
            departure_persons=departure_persons,
            early_departure_rooms=early_departure_rooms,
            early_departure_persons=early_departure_adults + early_departure_children,
            no_show_rooms=no_show_rooms,
            no_show_persons=no_show_persons,
            todays_checkin_cancelled_reservations=todays_checkin_cancelled_reservations,
            late_reservation_cancellations_for_today=late_cancelled_reservations_today,
            reservation_made_today=reservation_made_today,
            reservation_cancellation_made_today=reservation_cancellation_made_today,
            room_nights_reserved_today=room_nights_reserved_today,
            clean_rooms=clean_rooms,
            dirty_rooms=dirty_rooms,
            oos_rooms=oos_rooms,
            double_as_singles=doubles_as_singles,
            max_occupancy=max_occupancy,
            percentage_of_occupied_beds=percentage_of_occupied_beds,
            percentage_of_multiple_occupancy=percentage_of_multiple_occupancy,
        )

        finance_field_data = dict(
            pretax_adr=pretax_adr,
            posttax_adr=posttax_adr,
            pretax_adr_minus_comp=pretax_adr_minus_comp,
            posttax_adr_minus_comp=posttax_adr_minus_comp,
            pretax_adr_minus_house=pretax_adr_minus_house,
            posttax_adr_minus_house=posttax_adr_minus_house,
            pretax_adr_minus_house_and_comp=pretax_adr_minus_house_and_comp,
            posttax_adr_minus_house_and_comp=posttax_adr_minus_house_and_comp,
            pretax_average_person_rate=pretax_average_person_rate,
            posttax_average_person_rate=posttax_average_person_rate,
            pretax_room_revenue=pretax_room_revenue,
            posttax_room_revenue=posttax_room_revenue,
            pretax_fnb_revenue=pretax_fnb_revenue,
            posttax_fnb_revenue=posttax_fnb_revenue,
            pretax_other_revenue=pretax_other_revenue,
            posttax_other_revenue=posttax_other_revenue,
            pretax_total_revenue=pretax_total_revenue,
            posttax_total_revenue=posttax_total_revenue,
            pretax_member_revenue=pretax_member_revenue,
            posttax_member_revenue=posttax_member_revenue,
            pretax_member_room_revenue=pretax_member_room_revenue,
            posttax_member_room_revenue=posttax_member_room_revenue,
            pretax_total_revenue_per_person=pretax_total_revenue_per_person,
            posttax_total_revenue_per_person=posttax_total_revenue_per_person,
            posttax_rev_par=posttax_rev_par,
            pretax_rev_par=pretax_rev_par,
            payment=total_payments_made_today,
            refund=total_refunds_made_today,
            # credit_offered=total_credit_offered_today,
        )

        forecast_field_data = dict(
            tomorrow_arrival_persons=tomorrow_arrival_persons,
            tomorrow_arrival_rooms=tomorrow_arrival_rooms,
            tomorrow_departure_rooms=tomorrow_departure_rooms,
            tomorrow_departure_persons=tomorrow_departure_persons,
            percentage_of_room_occupied_for_tomorrow=percentage_of_rooms_occupied_tomorrow,
            percentage_of_occupied_rooms_for_next_seven_days=percentage_of_occupied_rooms_for_next_seven_days,
            percentage_of_projected_occupied_rooms_for_current_month=percentage_of_occupied_rooms_for_current_month,
            percentage_of_projected_occupied_rooms_for_current_year=percentage_of_occupied_rooms_for_current_year,
        )

        from prometheus.common.serializers.response.reporting import (
            FlashReportFinanceFieldSchema,
            FlashReportForecastFieldSchema,
            FlashReportPOSFieldSchema,
            FlashReportRoomAndReservationFieldSchema,
        )

        room_and_reservation_field_data = (
            FlashReportRoomAndReservationFieldSchema()
            .dump(room_and_reservation_field_data)
            .data
        )
        finance_field_data = (
            FlashReportFinanceFieldSchema().dump(finance_field_data).data
        )
        forecast_field_data = (
            FlashReportForecastFieldSchema().dump(forecast_field_data).data
        )
        # pos data
        pos_field_data = FlashReportPOSFieldSchema(many=True).dump(pos_field_data).data

        self.flash_manager_report_repository.save(
            FlashManagerReport(
                report_date=report_date,
                data=dict(
                    room_and_reservation_fields=room_and_reservation_field_data,
                    finance_fields=finance_field_data,
                    forecast_fields=forecast_field_data,
                    pos_fields=pos_field_data,
                ),
                hotel_id=hotel_aggregate.hotel_id,
            )
        )

    @session_manager(commit=True)
    def generate_report_summary_after_night_audit(self, report_date, hotel_id):
        if not self.tenant_settings.is_report_enabled(
            report_name=ReportName.MANAGER_FLASH_REPORT.value, hotel_id=hotel_id
        ):
            return

        report_date = dateutils.ymd_str_to_date(report_date)
        hotel_aggregate = self.hotel_repository.load(hotel_id)
        crs_context.set_hotel_context(hotel_aggregate)

        (
            booking_aggregates,
            room_aggregates,
            dnr_aggregates,
            room_allotment_aggregates,
            room_type_map,
            seller_map,
            pos_bill_aggregates,
        ) = self._base_report_data(report_date, hotel_aggregate)

        self._generate_report_details(
            report_date,
            booking_aggregates,
            room_aggregates,
            dnr_aggregates,
            room_allotment_aggregates,
            room_type_map,
            hotel_aggregate,
            seller_map,
            pos_bill_aggregates,
        )

    @set_hotel_context()
    def generate_report_details(
        self, report_date, date_periods, tax_inclusive=False, hotel_aggregate=None
    ):
        end_date = report_date
        result = dict()

        if FlashManagerReportDatePeriods.DAY.value in date_periods:
            start_date = report_date
            data = dict()
            dataset = self.flash_manager_report_repository.load(
                start_date, end_date, hotel_aggregate.hotel_id
            )
            if dataset:
                result['DAY'] = self._sum_up_date_range_flash_manager_data(
                    data,
                    dataset[0].data,
                    tax_inclusive,
                    show_forecast=dateutils.subtract(
                        hotel_aggregate.hotel.current_business_date, days=1
                    )
                    == report_date,
                )
                self._calculate_percentage_related_fields(data)
                self._extract_amount_from_money_fields(data)

        if FlashManagerReportDatePeriods.MONTH.value in date_periods:
            start_date = report_date.replace(day=1)
            data = dict()
            dataset = self.flash_manager_report_repository.load(
                start_date, end_date, hotel_aggregate.hotel_id
            )
            if dataset:
                [
                    self._sum_up_date_range_flash_manager_data(
                        data, d.data, tax_inclusive
                    )
                    for d in dataset
                ]
                self._calculate_percentage_related_fields(data)
                result['MONTH'] = data
                self._extract_amount_from_money_fields(data)
                no_of_report_days = len(dataset)
                self._calculate_average_fields(data, no_of_report_days)

        if FlashManagerReportDatePeriods.YEAR.value in date_periods:
            start_date = report_date.replace(day=1, month=1)
            data = dict()
            dataset = self.flash_manager_report_repository.load(
                start_date, end_date, hotel_aggregate.hotel_id
            )
            if dataset:
                [
                    self._sum_up_date_range_flash_manager_data(
                        data, d.data, tax_inclusive
                    )
                    for d in dataset
                ]
                self._calculate_percentage_related_fields(data)
                result['YEAR'] = data
                self._extract_amount_from_money_fields(data)
                no_of_report_days = len(dataset)
                self._calculate_average_fields(data, no_of_report_days)

        return result

    @set_hotel_context()
    def generate_report_url(
        self,
        report_date,
        date_periods,
        flash_manager_report_generator: FlashManagerReportGenerator,
        tax_inclusive=False,
        hotel_aggregate=None,
    ):
        data = self.generate_report_details(report_date, date_periods, tax_inclusive)

        file_path = (
            flash_manager_report_generator.generate_flash_manager_report_file_name()
        )
        folder_path = flash_manager_report_generator.FLASH_MANAGER_REPORT_FOLDER_NAME
        url_expiry_time = flash_manager_report_generator.get_default_expiration_time()
        result = []
        headers = ['']
        [headers.append(key.upper()) for key in data.keys()]
        result.append(headers)
        fields = []
        fields.extend(
            self._CATEGORY_ROOMS_AND_RESERVATIONS
            + self._CATEGORY_FINANCE
            + list(set(self._CATEGORY_POS))
        )

        if (
            dateutils.subtract(hotel_aggregate.hotel.current_business_date, days=1)
            == report_date
        ):
            fields.extend(self._CATEGORY_FORECAST)

        for field in fields:
            temp = [field[0]]
            [temp.append(data[key].get(field[1], '')) for key in data.keys()]
            result.append(temp)

        if not data:
            return
        with open(file_path, 'w') as file:
            writer = csv.writer(file, quoting=csv.QUOTE_ALL)
            writer.writerows(result)
        pre_signed_url = AwsServiceClient.upload_file_to_s3_and_get_presigned_url(
            folder_path, file_path, url_expiry_time
        )
        os.remove(file_path)

        return pre_signed_url

    def _sum_up_date_range_flash_manager_data(
        self,
        data: dict,
        flash_manager_report: dict,
        tax_inclusive=False,
        show_forecast=False,
    ):
        # region rooms and reservations
        data[self._TOTAL_ROOMS_IN_HOTEL] = data.get(
            self._TOTAL_ROOMS_IN_HOTEL, 0
        ) + flash_manager_report[self._ROOM_AND_RESERVATION_FIELDS].get(
            self._TOTAL_ROOMS_IN_HOTEL, 0
        )
        data[self._OOO_ROOMS] = data.get(self._OOO_ROOMS, 0) + flash_manager_report[
            self._ROOM_AND_RESERVATION_FIELDS
        ].get(self._DNR_ROOMS, 0)
        data[self._TOTAL_ROOMS_IN_HOTEL_EXCLUDING_OOO_ROOMS] = data.get(
            self._TOTAL_ROOMS_IN_HOTEL_EXCLUDING_OOO_ROOMS, 0
        ) + flash_manager_report[self._ROOM_AND_RESERVATION_FIELDS].get(
            'total_rooms_in_hotel_excluding_dnr_rooms', 0
        )
        data[self._OCCUPIED_ROOMS] = data.get(
            self._OCCUPIED_ROOMS, 0
        ) + flash_manager_report[self._ROOM_AND_RESERVATION_FIELDS].get(
            self._OCCUPIED_ROOMS, 0
        )
        data[self._MAX_OCCUPANCY] = data.get(
            self._MAX_OCCUPANCY, 0
        ) + flash_manager_report[self._ROOM_AND_RESERVATION_FIELDS].get(
            self._MAX_OCCUPANCY, 0
        )
        data[self._MULTIPLE_OCCUPANCY] = data.get(
            self._MULTIPLE_OCCUPANCY, 0
        ) + flash_manager_report[self._ROOM_AND_RESERVATION_FIELDS].get(
            self._MULTIPLE_OCCUPANCY, 0
        )
        data[self._AVAILABLE_ROOMS] = data.get(
            self._AVAILABLE_ROOMS, 0
        ) + flash_manager_report[self._ROOM_AND_RESERVATION_FIELDS].get(
            self._AVAILABLE_ROOMS, 0
        )
        data[self._AVAILABLE_ROOMS_EXCLUDING_OOO_ROOMS] = data.get(
            self._AVAILABLE_ROOMS_EXCLUDING_OOO_ROOMS, 0
        ) + flash_manager_report[self._ROOM_AND_RESERVATION_FIELDS].get(
            self._AVAILABLE_ROOMS_EXCLUDING_DNR_ROOMS, 0
        )
        data[self._COMPLIMENTARY_ROOMS] = data.get(
            self._COMPLIMENTARY_ROOMS, 0
        ) + flash_manager_report[self._ROOM_AND_RESERVATION_FIELDS].get(
            self._COMPLIMENTARY_ROOMS, 0
        )
        data[self._HOUSE_USE_ROOMS] = data.get(
            self._HOUSE_USE_ROOMS, 0
        ) + flash_manager_report[self._ROOM_AND_RESERVATION_FIELDS].get(
            self._HOUSE_USE_ROOMS, 0
        )
        data[self._DAY_USE_ROOMS] = data.get(
            self._DAY_USE_ROOMS, 0
        ) + flash_manager_report[self._ROOM_AND_RESERVATION_FIELDS].get(
            self._DAY_USE_ROOMS, 0
        )
        data[self._OCCUPIED_ROOMS_EXCLUDING_COMP_AND_HOUSE_USE] = data.get(
            self._OCCUPIED_ROOMS_EXCLUDING_COMP_AND_HOUSE_USE, 0
        ) + flash_manager_report[self._ROOM_AND_RESERVATION_FIELDS].get(
            self._OCCUPIED_ROOMS_EXCLUDING_COMP_AND_HOUSE_USE, 0
        )
        data[self._OCCUPIED_ROOMS_EXCLUDING_HOUSE_USE] = data.get(
            self._OCCUPIED_ROOMS_EXCLUDING_HOUSE_USE, 0
        ) + flash_manager_report[self._ROOM_AND_RESERVATION_FIELDS].get(
            self._OCCUPIED_ROOMS_EXCLUDING_HOUSE_USE, 0
        )
        data[self._OCCUPIED_ROOMS_EXCLUDING_COMP_ROOMS] = data.get(
            self._OCCUPIED_ROOMS_EXCLUDING_COMP_ROOMS, 0
        ) + flash_manager_report[self._ROOM_AND_RESERVATION_FIELDS].get(
            self._OCCUPIED_ROOMS_EXCLUDING_COMP_ROOMS, 0
        )
        data[self._IN_HOUSE_ADULTS] = data.get(
            self._IN_HOUSE_ADULTS, 0
        ) + flash_manager_report[self._ROOM_AND_RESERVATION_FIELDS].get(
            self._IN_HOUSE_ADULTS, 0
        )
        data[self._IN_HOUSE_CHILDREN] = data.get(
            self._IN_HOUSE_CHILDREN, 0
        ) + flash_manager_report[self._ROOM_AND_RESERVATION_FIELDS].get(
            self._IN_HOUSE_CHILDREN, 0
        )
        data[self._TOTAL_IN_HOUSE_PERSONS] = data.get(
            self._TOTAL_IN_HOUSE_PERSONS, 0
        ) + flash_manager_report[self._ROOM_AND_RESERVATION_FIELDS].get(
            self._TOTAL_IN_HOUSE_PERSONS, 0
        )
        data[self._IN_HOUSE_NON_CORPORATE_PERSONS] = data.get(
            self._IN_HOUSE_NON_CORPORATE_PERSONS, 0
        ) + flash_manager_report[self._ROOM_AND_RESERVATION_FIELDS].get(
            self._IN_HOUSE_NON_CORPORATE_PERSONS, 0
        )
        data[self._IN_HOUSE_CORPORATE_PERSONS] = data.get(
            self._IN_HOUSE_CORPORATE_PERSONS, 0
        ) + flash_manager_report[self._ROOM_AND_RESERVATION_FIELDS].get(
            self._IN_HOUSE_CORPORATE_PERSONS, 0
        )
        data[self._IN_HOUSE_CORPORATE_ROOMS] = data.get(
            self._IN_HOUSE_CORPORATE_ROOMS, 0
        ) + flash_manager_report[self._ROOM_AND_RESERVATION_FIELDS].get(
            self._IN_HOUSE_CORPORATE_ROOMS, 0
        )
        data[self._IN_HOUSE_TRAVEL_AGENT_ROOMS] = data.get(
            self._IN_HOUSE_TRAVEL_AGENT_ROOMS, 0
        ) + flash_manager_report[self._ROOM_AND_RESERVATION_FIELDS].get(
            self._IN_HOUSE_TRAVEL_AGENT_ROOMS, 0
        )
        data[self._ARRIVAL_ROOMS] = data.get(
            self._ARRIVAL_ROOMS, 0
        ) + flash_manager_report[self._ROOM_AND_RESERVATION_FIELDS].get(
            self._ARRIVAL_ROOMS, 0
        )
        data[self._ARRIVAL_PERSONS] = data.get(
            self._ARRIVAL_PERSONS, 0
        ) + flash_manager_report[self._ROOM_AND_RESERVATION_FIELDS].get(
            self._ARRIVAL_PERSONS, 0
        )
        data[self._WALK_IN_ROOMS] = data.get(
            self._WALK_IN_ROOMS, 0
        ) + flash_manager_report[self._ROOM_AND_RESERVATION_FIELDS].get(
            self._WALK_IN_ROOMS, 0
        )
        data[self._WALK_IN_PERSONS] = data.get(
            self._WALK_IN_PERSONS, 0
        ) + flash_manager_report[self._ROOM_AND_RESERVATION_FIELDS].get(
            self._WALK_IN_PERSONS, 0
        )
        data[self._EXTENDED_STAY_ROOMS] = data.get(
            self._EXTENDED_STAY_ROOMS, 0
        ) + flash_manager_report[self._ROOM_AND_RESERVATION_FIELDS].get(
            self._EXTENDED_STAY_ROOMS, 0
        )
        data[self._EXTENDED_STAY_PERSONS] = data.get(
            self._EXTENDED_STAY_PERSONS, 0
        ) + flash_manager_report[self._ROOM_AND_RESERVATION_FIELDS].get(
            self._EXTENDED_STAY_PERSONS, 0
        )
        data[self._DEPARTURE_ROOMS] = data.get(
            self._DEPARTURE_ROOMS, 0
        ) + flash_manager_report[self._ROOM_AND_RESERVATION_FIELDS].get(
            self._DEPARTURE_ROOMS, 0
        )
        data[self._DEPARTURE_PERSONS] = data.get(
            self._DEPARTURE_PERSONS, 0
        ) + flash_manager_report[self._ROOM_AND_RESERVATION_FIELDS].get(
            self._DEPARTURE_PERSONS, 0
        )
        data[self._EARLY_DEPARTURE_ROOMS] = data.get(
            self._EARLY_DEPARTURE_ROOMS, 0
        ) + flash_manager_report[self._ROOM_AND_RESERVATION_FIELDS].get(
            self._EARLY_DEPARTURE_ROOMS, 0
        )
        data[self._EARLY_DEPARTURE_PERSONS] = data.get(
            self._EARLY_DEPARTURE_PERSONS, 0
        ) + flash_manager_report[self._ROOM_AND_RESERVATION_FIELDS].get(
            self._EARLY_DEPARTURE_PERSONS, 0
        )
        data[self._NO_SHOW_ROOMS] = data.get(
            self._NO_SHOW_ROOMS, 0
        ) + flash_manager_report[self._ROOM_AND_RESERVATION_FIELDS].get(
            self._NO_SHOW_ROOMS, 0
        )
        data[self._NO_SHOW_PERSONS] = data.get(
            self._NO_SHOW_PERSONS, 0
        ) + flash_manager_report[self._ROOM_AND_RESERVATION_FIELDS].get(
            self._NO_SHOW_PERSONS, 0
        )
        data[self._TODAYS_CHECKIN_CANCELLED_RESERVATIONS] = data.get(
            self._TODAYS_CHECKIN_CANCELLED_RESERVATIONS, 0
        ) + flash_manager_report[self._ROOM_AND_RESERVATION_FIELDS].get(
            self._TODAYS_CHECKIN_CANCELLED_RESERVATIONS, 0
        )
        data[self._LATE_RESERVATION_CANCELLATIONS_FOR_TODAY] = data.get(
            self._LATE_RESERVATION_CANCELLATIONS_FOR_TODAY, 0
        ) + flash_manager_report[self._ROOM_AND_RESERVATION_FIELDS].get(
            self._LATE_RESERVATION_CANCELLATIONS_FOR_TODAY, 0
        )
        data[self._RESERVATION_MADE_TODAY] = data.get(
            self._RESERVATION_MADE_TODAY, 0
        ) + flash_manager_report[self._ROOM_AND_RESERVATION_FIELDS].get(
            self._RESERVATION_MADE_TODAY, 0
        )
        data[self._RESERVATION_CANCELLATION_MADE_TODAY] = data.get(
            self._RESERVATION_CANCELLATION_MADE_TODAY, 0
        ) + flash_manager_report[self._ROOM_AND_RESERVATION_FIELDS].get(
            self._RESERVATION_CANCELLATION_MADE_TODAY, 0
        )
        data[self._ROOM_NIGHTS_RESERVED_TODAY] = data.get(
            self._ROOM_NIGHTS_RESERVED_TODAY, 0
        ) + flash_manager_report[self._ROOM_AND_RESERVATION_FIELDS].get(
            self._ROOM_NIGHTS_RESERVED_TODAY, 0
        )
        data[self._CLEAN_ROOMS] = data.get(self._CLEAN_ROOMS, 0) + flash_manager_report[
            self._ROOM_AND_RESERVATION_FIELDS
        ].get(self._CLEAN_ROOMS, 0)
        data[self._DIRTY_ROOMS] = data.get(self._DIRTY_ROOMS, 0) + flash_manager_report[
            self._ROOM_AND_RESERVATION_FIELDS
        ].get(self._DIRTY_ROOMS, 0)
        data[self._OOS_ROOMS] = data.get(self._OOS_ROOMS, 0) + flash_manager_report[
            self._ROOM_AND_RESERVATION_FIELDS
        ].get(self._OOS_ROOMS, 0)
        data[self._DOUBLE_AS_SINGLES] = data.get(
            self._DOUBLE_AS_SINGLES, 0
        ) + flash_manager_report[self._ROOM_AND_RESERVATION_FIELDS].get(
            self._DOUBLE_AS_SINGLES, 0
        )

        # endregion rooms and reservations

        zero_money = Money(0, crs_context.hotel_context.base_currency)
        # region finance
        data[self._ADR] = data.get(self._ADR, zero_money) + Money(
            flash_manager_report[self._FINANCE_FIELDS].get('pretax_adr', zero_money)
            if not tax_inclusive
            else flash_manager_report[self._FINANCE_FIELDS].get(
                'posttax_adr', zero_money
            ),
            crs_context.hotel_context.base_currency,
        )
        data[self._ADR_MINUS_COMP] = data.get(self._ADR_MINUS_COMP, zero_money) + Money(
            flash_manager_report[self._FINANCE_FIELDS].get(
                'pretax_adr_minus_comp', zero_money
            )
            if not tax_inclusive
            else flash_manager_report[self._FINANCE_FIELDS].get(
                'posttax_adr_minus_comp', zero_money
            ),
            crs_context.hotel_context.base_currency,
        )
        data[self._ADR_MINUS_HOUSE] = data.get(
            self._ADR_MINUS_HOUSE, zero_money
        ) + Money(
            flash_manager_report[self._FINANCE_FIELDS].get(
                'pretax_adr_minus_house', zero_money
            )
            if not tax_inclusive
            else flash_manager_report[self._FINANCE_FIELDS].get(
                'posttax_adr_minus_house', zero_money
            ),
            crs_context.hotel_context.base_currency,
        )
        data[self._ADR_MINUS_HOUSE_AND_COMP] = data.get(
            self._ADR_MINUS_HOUSE_AND_COMP, zero_money
        ) + Money(
            flash_manager_report[self._FINANCE_FIELDS].get(
                'pretax_adr_minus_house_and_comp', zero_money
            )
            if not tax_inclusive
            else flash_manager_report[self._FINANCE_FIELDS].get(
                'posttax_adr_minus_house_and_comp', zero_money
            ),
            crs_context.hotel_context.base_currency,
        )
        data[self._AVERAGE_PERSON_RATE] = data.get(
            self._AVERAGE_PERSON_RATE, zero_money
        ) + Money(
            flash_manager_report[self._FINANCE_FIELDS].get(
                'pretax_average_person_rate', zero_money
            )
            if not tax_inclusive
            else flash_manager_report[self._FINANCE_FIELDS].get(
                'posttax_average_person_rate', zero_money
            ),
            crs_context.hotel_context.base_currency,
        )
        data[self._ROOM_REVENUE] = data.get(self._ROOM_REVENUE, zero_money) + Money(
            flash_manager_report[self._FINANCE_FIELDS].get(
                'pretax_room_revenue', zero_money
            )
            if not tax_inclusive
            else flash_manager_report[self._FINANCE_FIELDS].get(
                'posttax_room_revenue', zero_money
            ),
            crs_context.hotel_context.base_currency,
        )
        data[self._FNB_REVENUE] = data.get(self._FNB_REVENUE, zero_money) + Money(
            flash_manager_report[self._FINANCE_FIELDS].get(
                'pretax_fnb_revenue', zero_money
            )
            if not tax_inclusive
            else flash_manager_report[self._FINANCE_FIELDS].get(
                'posttax_fnb_revenue', zero_money
            ),
            crs_context.hotel_context.base_currency,
        )
        data[self._OTHER_REVENUE] = data.get(self._OTHER_REVENUE, zero_money) + Money(
            flash_manager_report[self._FINANCE_FIELDS].get(
                'pretax_other_revenue', zero_money
            )
            if not tax_inclusive
            else flash_manager_report[self._FINANCE_FIELDS].get(
                'posttax_other_revenue', zero_money
            ),
            crs_context.hotel_context.base_currency,
        )
        data[self._TOTAL_REVENUE] = data.get(self._TOTAL_REVENUE, zero_money) + Money(
            flash_manager_report[self._FINANCE_FIELDS].get(
                'pretax_total_revenue', zero_money
            )
            if not tax_inclusive
            else flash_manager_report[self._FINANCE_FIELDS].get(
                'posttax_total_revenue', zero_money
            ),
            crs_context.hotel_context.base_currency,
        )
        data[self._MEMBER_REVENUE] = data.get(self._MEMBER_REVENUE, zero_money) + Money(
            flash_manager_report[self._FINANCE_FIELDS].get(
                'pretax_member_revenue', zero_money
            )
            if not tax_inclusive
            else flash_manager_report[self._FINANCE_FIELDS].get(
                'posttax_member_revenue', zero_money
            ),
            crs_context.hotel_context.base_currency,
        )
        data[self._MEMBER_ROOM_REVENUE] = data.get(
            self._MEMBER_ROOM_REVENUE, zero_money
        ) + Money(
            flash_manager_report[self._FINANCE_FIELDS].get(
                'pretax_member_room_revenue', zero_money
            )
            if not tax_inclusive
            else flash_manager_report[self._FINANCE_FIELDS].get(
                'posttax_member_room_revenue', zero_money
            ),
            crs_context.hotel_context.base_currency,
        )
        data[self._TOTAL_REVENUE_PER_PERSON] = data.get(
            self._TOTAL_REVENUE_PER_PERSON, zero_money
        ) + Money(
            flash_manager_report[self._FINANCE_FIELDS].get(
                'pretax_total_revenue_per_person', zero_money
            )
            if not tax_inclusive
            else flash_manager_report[self._FINANCE_FIELDS].get(
                'posttax_total_revenue_per_person', zero_money
            ),
            crs_context.hotel_context.base_currency,
        )
        data[self._REV_PAR] = data.get(self._REV_PAR, zero_money) + Money(
            flash_manager_report[self._FINANCE_FIELDS].get('pretax_rev_par', zero_money)
            if not tax_inclusive
            else flash_manager_report[self._FINANCE_FIELDS].get(
                'posttax_rev_par', zero_money
            ),
            crs_context.hotel_context.base_currency,
        )
        data[self._PAYMENT] = data.get(self._PAYMENT, zero_money) + Money(
            flash_manager_report[self._FINANCE_FIELDS].get(self._PAYMENT, zero_money),
            crs_context.hotel_context.base_currency,
        )
        data[self._REFUND] = data.get(self._REFUND, zero_money) + Money(
            flash_manager_report[self._FINANCE_FIELDS].get(self._REFUND, zero_money),
            crs_context.hotel_context.base_currency,
        )

        # endregion finance

        # pos fields
        if flash_manager_report.get(self._POS_FIELDS):
            for pos_data in flash_manager_report.get(self._POS_FIELDS):
                pos_field = f"pos_{pos_data.get('seller_name')}_revenue"
                # appending the csv field and its variable
                self._CATEGORY_POS.insert(
                    0, (f"POS {pos_data.get('seller_name')} revenue", pos_field)
                )
                data[pos_field] = data.get(pos_field, zero_money) + (
                    pos_data.get("posttax_revenue")
                    if tax_inclusive
                    else pos_data.get("pretax_revenue")
                )

                data[self._POS_TOTAL_REVENUE] = data.get(
                    self._POS_TOTAL_REVENUE, zero_money
                ) + (
                    pos_data.get("posttax_revenue")
                    if tax_inclusive
                    else pos_data.get("pretax_revenue")
                )

        # region forecast
        if show_forecast:
            data[self._TOMORROW_ARRIVAL_PERSONS] = flash_manager_report[
                self._FORECAST_FIELDS
            ].get(self._TOMORROW_ARRIVAL_PERSONS, 0)
            data[self._TOMORROW_ARRIVAL_ROOMS] = flash_manager_report[
                self._FORECAST_FIELDS
            ].get(self._TOMORROW_ARRIVAL_ROOMS, 0)
            data[self._TOMORROW_DEPARTURE_ROOMS] = flash_manager_report[
                self._FORECAST_FIELDS
            ].get(self._TOMORROW_DEPARTURE_ROOMS, 0)
            data[self._TOMORROW_DEPARTURE_PERSONS] = flash_manager_report[
                self._FORECAST_FIELDS
            ].get(self._TOMORROW_DEPARTURE_PERSONS, 0)
            data[self._PERCENTAGE_OF_ROOM_OCCUPIED_FOR_TOMORROW] = flash_manager_report[
                self._FORECAST_FIELDS
            ].get(self._PERCENTAGE_OF_ROOM_OCCUPIED_FOR_TOMORROW, 0)
            data[
                self._PERCENTAGE_OF_OCCUPIED_ROOMS_FOR_NEXT_SEVEN_DAYS
            ] = flash_manager_report[self._FORECAST_FIELDS].get(
                self._PERCENTAGE_OF_OCCUPIED_ROOMS_FOR_NEXT_SEVEN_DAYS, 0
            )
            data[
                self._PERCENTAGE_OF_PROJECTED_OCCUPIED_ROOMS_FOR_CURRENT_MONTH
            ] = flash_manager_report[self._FORECAST_FIELDS].get(
                self._PERCENTAGE_OF_PROJECTED_OCCUPIED_ROOMS_FOR_CURRENT_MONTH, 0
            )
            data[
                self._PERCENTAGE_OF_PROJECTED_OCCUPIED_ROOMS_FOR_CURRENT_YEAR
            ] = flash_manager_report[self._FORECAST_FIELDS].get(
                self._PERCENTAGE_OF_PROJECTED_OCCUPIED_ROOMS_FOR_CURRENT_YEAR, 0
            )

        # endregion forecast
        return data

    def _calculate_percentage_related_fields(self, data: dict):
        # region rooms and reservations
        data[self._PERCENTAGE_OF_OCCUPIED_ROOMS] = (
            str(
                round(
                    (data[self._OCCUPIED_ROOMS] / data[self._TOTAL_ROOMS_IN_HOTEL])
                    * 100,
                    2,
                )
            )
            if data[self._TOTAL_ROOMS_IN_HOTEL]
            else 0
        )
        data[self._PERCENTAGE_OF_OCCUPIED_ROOMS_EXCLUDING_COMP_AND_HOUSE_ROOMS] = (
            str(
                round(
                    (
                        (
                            data[self._OCCUPIED_ROOMS]
                            - data[self._COMPLIMENTARY_ROOMS]
                            - data[self._HOUSE_USE_ROOMS]
                        )
                        / data[self._TOTAL_ROOMS_IN_HOTEL]
                    )
                    * 100,
                    2,
                )
            )
            if data[self._TOTAL_ROOMS_IN_HOTEL]
            else 0
        )
        data[self._PERCENTAGE_OF_OCCUPIED_ROOMS_EXCLUDING_COMP_AND_HOUSE_OOO_ROOMS] = (
            str(
                round(
                    (
                        (
                            (
                                data[self._OCCUPIED_ROOMS]
                                - data[self._COMPLIMENTARY_ROOMS]
                                - data[self._HOUSE_USE_ROOMS]
                                - data[self._OOO_ROOMS]
                            )
                            / data[self._TOTAL_ROOMS_IN_HOTEL]
                        )
                    )
                    * 100,
                    2,
                )
            )
            if data[self._TOTAL_ROOMS_IN_HOTEL]
            else 0
        )
        data[self._PERCENTAGE_OF_OCCUPIED_ROOMS_EXCLUDING_COMP] = (
            str(
                round(
                    (
                        (data[self._OCCUPIED_ROOMS] - data[self._COMPLIMENTARY_ROOMS])
                        / data[self._TOTAL_ROOMS_IN_HOTEL]
                    )
                    * 100,
                    2,
                )
            )
            if data[self._TOTAL_ROOMS_IN_HOTEL]
            else 0
        )
        data[self._PERCENTAGE_OF_OCCUPIED_ROOMS_EXCLUDING_HOUSE] = (
            str(
                round(
                    (
                        (data[self._OCCUPIED_ROOMS] - data[self._HOUSE_USE_ROOMS])
                        / data[self._TOTAL_ROOMS_IN_HOTEL]
                    )
                    * 100,
                    2,
                )
            )
            if data[self._TOTAL_ROOMS_IN_HOTEL]
            else 0
        )
        data[self._PERCENTAGE_OF_OCCUPIED_ROOMS_EXCLUDING_COMP_AND_OOO] = (
            str(
                round(
                    (
                        (
                            data[self._OCCUPIED_ROOMS]
                            - data[self._COMPLIMENTARY_ROOMS]
                            - data[self._OOO_ROOMS]
                        )
                        / data[self._TOTAL_ROOMS_IN_HOTEL]
                    )
                    * 100,
                    2,
                )
            )
            if data[self._TOTAL_ROOMS_IN_HOTEL]
            else 0
        )
        data[self._PERCENTAGE_OF_OCCUPIED_ROOMS_EXCLUDING_HOUSE_AND_OOO] = (
            str(
                round(
                    (
                        (
                            data[self._OCCUPIED_ROOMS]
                            - data[self._HOUSE_USE_ROOMS]
                            - data[self._OOO_ROOMS]
                        )
                        / data[self._TOTAL_ROOMS_IN_HOTEL]
                    )
                    * 100,
                    2,
                )
            )
            if data[self._TOTAL_ROOMS_IN_HOTEL]
            else 0
        )
        data[self._PERCENTAGE_OF_OCCUPIED_ROOMS_EXCLUDING_OOO] = (
            str(
                round(
                    data[self._OCCUPIED_ROOMS]
                    / (data[self._TOTAL_ROOMS_IN_HOTEL] - data[self._OOO_ROOMS])
                    * 100,
                    2,
                )
            )
            if (data[self._TOTAL_ROOMS_IN_HOTEL] - data[self._OOO_ROOMS])
            else 0
        )
        data[self._PERCENTAGE_OF_OCCUPIED_BEDS] = (
            round(
                (
                    (data[self._IN_HOUSE_ADULTS] + data[self._IN_HOUSE_CHILDREN])
                    / data[self._MAX_OCCUPANCY]
                )
                * 100,
                2,
            )
            if data[self._MAX_OCCUPANCY]
            else 0
        )
        data[self._PERCENTAGE_OF_MULTIPLE_OCCUPANCY] = (
            str(
                round(
                    (data[self._MULTIPLE_OCCUPANCY] / data[self._OCCUPIED_ROOMS]) * 100,
                    2,
                )
            )
            if data[self._OCCUPIED_ROOMS]
            else 0
        )

        # region remove not required
        data.pop(self._MAX_OCCUPANCY)

        # endregion remove

        # endregion rooms and reservations

    @staticmethod
    def _extract_amount_from_money_fields(data):
        # In report Money field would be displayed without currency info
        for data_key, data_value in data.items():
            if isinstance(data_value, Money):
                data[data_key] = data_value.amount

    def _calculate_average_fields(self, data, no_of_report_days):
        data[self._ADR] = (
            round(data[self._ROOM_REVENUE] / data[self._OCCUPIED_ROOMS], 2)
            if data[self._OCCUPIED_ROOMS] > 0
            else Decimal('0.00')
        )
        data[self._REV_PAR] = round(
            data[self._ROOM_REVENUE] / data[self._TOTAL_ROOMS_IN_HOTEL], 2
        )
        data[self._ADR_MINUS_COMP] = (
            round(
                data[self._ROOM_REVENUE]
                / data[self._OCCUPIED_ROOMS_EXCLUDING_COMP_ROOMS],
                2,
            )
            if data[self._OCCUPIED_ROOMS_EXCLUDING_COMP_ROOMS] > 0
            else Decimal('0.00')
        )
        data[self._ADR_MINUS_HOUSE] = (
            round(
                data[self._ROOM_REVENUE]
                / data[self._OCCUPIED_ROOMS_EXCLUDING_HOUSE_USE],
                2,
            )
            if data[self._OCCUPIED_ROOMS_EXCLUDING_HOUSE_USE] > 0
            else Decimal('0.00')
        )
        data[self._ADR_MINUS_HOUSE_AND_COMP] = (
            round(
                data[self._ROOM_REVENUE]
                / data[self._OCCUPIED_ROOMS_EXCLUDING_COMP_AND_HOUSE_USE],
                2,
            )
            if data[self._OCCUPIED_ROOMS_EXCLUDING_COMP_AND_HOUSE_USE] > 0
            else Decimal('0.00')
        )
        data[self._AVERAGE_PERSON_RATE] = round(
            data[self._AVERAGE_PERSON_RATE] / no_of_report_days, 2
        )
