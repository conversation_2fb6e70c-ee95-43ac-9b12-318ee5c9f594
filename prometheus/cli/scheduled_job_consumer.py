"""
Flask command to start Easy Job Lite Workers
"""

import logging
import os

import click
import newrelic
from flask import current_app
from flask.cli import with_appcontext
from treebo_commons.credentials.aws_secret_manager import AwsSecretManager
from treebo_commons.multitenancy.tenant_client import TenantClient

from prometheus.core.globals import consumer_context
from prometheus.easyjoblite import orchestrator

logger = logging.getLogger(__name__)


@click.command()
@click.option(
    '--tenant_id',
    help="Tenant ID for which this command should be run.",
    default=TenantClient.get_default_tenant(),
)
@with_appcontext
@newrelic.agent.background_task()
def start_scheduled_job_consumer(tenant_id=TenantClient.get_default_tenant()):
    """Start the event job lite workers"""
    click.echo("Tenant ID: %s" % tenant_id)
    consumer_context.set_tenant_id(tenant_id)
    rabbitmq_url = AwsSecretManager.get_rmq_url(tenant_id)
    worker = orchestrator.Orchestrator(
        rabbitmq_url=rabbitmq_url,
        config_file=os.path.join(
            current_app.root_path, current_app.config['EASYJOBLITE_CONFIG_PATH']
        ),
    )
    worker.start_service()
