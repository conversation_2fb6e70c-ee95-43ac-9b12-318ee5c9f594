import csv
import json
import logging

import click
from flask.cli import with_appcontext
from sqlalchemy import text
from treebo_commons.multitenancy.tenant_client import TenantClient

from prometheus.application.decorators import session_manager
from prometheus.common.decorators import consumer_middleware, timed
from prometheus.core.globals import consumer_context
from prometheus.infrastructure.database import db_engine

logger = logging.getLogger(__name__)


BATCH_SIZE = 3000


@click.option(
    '--tenant_id',
    help="Tenant ID for which this command should be run.",
    default=TenantClient.get_default_tenant(),
)
@click.option(
    '--file_path',
    help="File path of csv file",
)
@click.command("populate_platform_fee")
@with_appcontext
@timed
def populate_platform_fee(tenant_id, file_path):
    consumer_context.set_tenant_id(tenant_id)
    process(file_path)


@consumer_middleware
def process(file_path):
    batch_count = 0
    for batch in process_csv_in_batches(file_path, BATCH_SIZE):
        batch_count += 1
        try:
            update_amount_in_db(batch)
            print(f"Processed batch: {batch_count}")
        except Exception as e:
            logger.exception(f"Error while processing batch: {e}, batch: {batch}")


@session_manager(commit=True)
def update_amount_in_db(batch):
    session = db_engine.get_scoped_session()
    booking_select = text(
        """
        SELECT bill_id, reference_number
        FROM booking
        WHERE reference_number IN :reference_number
    """
    )
    reference_numbers = [row[0] for row in batch]
    result = session.execute(
        booking_select, {"reference_number": tuple(reference_numbers)}
    )
    booking_ref_id_to_bill_id_map = {row[1]: row[0] for row in result}
    update_query = """
        UPDATE bill
        SET fees = data.fees::jsonb
        FROM (VALUES :values) AS data(bill_id, fees)
        WHERE bill.bill_id = data.bill_id
    """
    values_clause = ", ".join(
        f"('{booking_ref_id_to_bill_id_map.get(row[0])}', '{row[1]}')"
        for row in batch
        if booking_ref_id_to_bill_id_map.get(row[0])
    )
    # Execute the batch update
    session.execute(text(update_query.replace(":values", values_clause)))


def process_csv_in_batches(csv_file, batch_size):
    with open(csv_file, mode="r") as file:
        reader = csv.reader(file)
        next(reader)
        batch = []

        for row in reader:
            fees = json.dumps({"platform_fee": float(row[1])})
            batch.append((row[0], fees))
            if len(batch) == batch_size:
                yield batch
                batch = []

        if batch:
            yield batch
