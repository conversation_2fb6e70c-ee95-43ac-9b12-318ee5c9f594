"""
Flask command to poll <PERSON> for unpublished Integration Events
"""

import logging
import time

import click
from treebo_commons.multitenancy.tenant_client import TenantClient

from object_registry import inject
from prometheus.application.services.integration_event_application_service import (
    IntegrationEventApplicationService,
)
from prometheus.core.globals import consumer_context
from ths_common.constants.integration_event_constants import IntegrationEventType

logger = logging.getLogger(__name__)


@click.command()
@click.option(
    '--tenant_id',
    help="Tenant ID for which this command should be run.",
    default=TenantClient.get_default_tenant(),
)
@inject(integration_event_application_service=IntegrationEventApplicationService)
def start_integration_event_worker(
    integration_event_application_service, tenant_id=TenantClient.get_default_tenant()
):
    """Poll DB for unpublished Integration Events and publish them"""
    # TODO: Set Tenant ID Context

    click.echo("Tenant ID: %s" % tenant_id)
    consumer_context.set_tenant_id(tenant_id)

    failures = 0

    sleep_time = 2

    while True:
        try:
            (
                unpublished_event_exists,
                event_aggregate,
            ) = (
                integration_event_application_service.publish_oldest_unpublished_event_to_queue()
            )
            success = True
            if not unpublished_event_exists:
                time.sleep(1)
                continue
            integration_event_application_service.publish_to_sns(event_aggregate)
        except Exception as ex:
            logger.exception('Error while publishing event to queue')
            logger.exception(ex)
            success = False

        if not success:
            failures += 1
        else:
            failures = 0
            sleep_time = 2

        if failures > 3:
            logger.critical(
                'Integration event message publishing failed for the %s th time',
                failures,
            )
            if sleep_time < 64:
                sleep_time = sleep_time * 2
            time.sleep(sleep_time)


@click.command()
@click.option('--hotel_id')
@inject(integration_event_application_service=IntegrationEventApplicationService)
def replay_integration_events(integration_event_application_service, hotel_id):
    # TODO: customize this script such that we can replay any type of event not just the integration event
    try:
        logger.debug("Starting the Replay of events")
        events_replayed = (
            integration_event_application_service.publish_replay_events_to_queue(
                [hotel_id],
                [
                    IntegrationEventType.INVOICE_PREVIEW_CREATED.value,
                    IntegrationEventType.INVOICE_PREVIEW_UPDATED.value,
                    IntegrationEventType.INVOICE_GENERATED.value,
                    IntegrationEventType.INVOICE_UPDATED.value,
                ],
            )
        )
        if not events_replayed:
            logger.debug("No events replayed for hotel_id:{}".format(hotel_id))
    except Exception as ex:
        logger.exception('Error while publishing event to queue')
