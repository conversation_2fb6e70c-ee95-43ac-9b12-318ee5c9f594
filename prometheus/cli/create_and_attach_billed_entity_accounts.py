import logging

import click
from flask.cli import with_appcontext
from treebo_commons.money import Money
from treebo_commons.multitenancy.tenant_client import TenantClient
from treebo_commons.request_tracing.context import request_context

from object_registry import inject
from prometheus import crs_context
from prometheus.application.booking.helpers.billed_entity_service import (
    BilledEntityService,
)
from prometheus.application.decorators import session_manager
from prometheus.application.helpers.billed_entity_helper import (
    get_room_stay_default_billed_entity,
)
from prometheus.domain.billing.dto.payment_split_data import PaymentSplitData
from prometheus.domain.billing.repositories.bill_repository import BillRepository
from prometheus.domain.billing.repositories.credit_note_repository import (
    CreditNoteRepository,
)
from prometheus.domain.billing.repositories.invoice_repository import InvoiceRepository
from prometheus.domain.booking.dtos.booking_search_query import BookingSearchQuery
from prometheus.domain.booking.entities.customer import Customer
from prometheus.domain.booking.repositories.booking_repository import BookingRepository
from ths_common.constants.billing_constants import (
    ChargeBillToTypes,
    ChargeTypes,
    PaymentStatus,
    PaymentTypes,
)
from ths_common.utils.collectionutils import chunks
from ths_common.value_objects import GSTDetails

logger = logging.getLogger(__name__)


@click.command()
@click.option(
    '--booking_ids',
    help="comma separated booking_ids to which billed entity accounts are to be attached",
    default=None,
)
@click.option(
    '--checkout_start_date',
    help="checkout start date of bookings that we need to attach billed entity "
    "accounts to charges in ymd format",
    default=None,
)
@click.option(
    '--checkout_end_date',
    help="checkout end date of bookings that we need to attach billed entity "
    "accounts to charges in ymd format",
    default=None,
)
@click.option(
    '--tenant_id',
    help="Tenant ID for which this command should be run.",
    default=TenantClient.get_default_tenant(),
)
@with_appcontext
@inject(
    booking_repo=BookingRepository,
    bill_repo=BillRepository,
    invoice_repo=InvoiceRepository,
    credit_note_repo=CreditNoteRepository,
    billed_entity_service=BilledEntityService,
)
def create_and_attach_billed_entity_accounts(
    booking_repo,
    bill_repo,
    invoice_repo,
    credit_note_repo,
    billed_entity_service,
    booking_ids,
    checkout_start_date,
    checkout_end_date,
    tenant_id,
):
    request_context.tenant_id = tenant_id
    booking_ids = booking_repo.load_all_bookings(
        booking_ids=booking_ids.split(',') if booking_ids else None,
        search_query=BookingSearchQuery(sort_by='checkin'),
        checkout_start_date=checkout_start_date,
        checkout_end_date=checkout_end_date,
    )
    count = 0
    for bookings in chunks(booking_ids, 500):
        try:
            _create_and_attach_billed_entity_accounts(
                booking_repo,
                bill_repo,
                invoice_repo,
                credit_note_repo,
                billed_entity_service,
                bookings,
            )
            count += 500
            logger.info(
                f"Billed entity migration completed for {count} out of {len(booking_ids)}"
            )
        except Exception as e:
            logger.exception(
                "Billed entity migration failed for bookings: %s", bookings
            )


@session_manager(commit=True)
def _create_and_attach_billed_entity_accounts(
    booking_repo,
    bill_repo,
    invoice_repo,
    credit_note_repo,
    billed_entity_service,
    bookings,
):
    booking_aggregates = booking_repo.load_all_for_update(bookings)
    bill_ids = [booking_aggregate.bill_id for booking_aggregate in booking_aggregates]
    bill_map = {
        bill_aggregate.bill_id: bill_aggregate
        for bill_aggregate in bill_repo.load_all_for_update(bill_ids)
    }
    invoice_map = {
        invoice_aggregate.invoice_id: invoice_aggregate
        for invoice_aggregate in invoice_repo.load_for_bill_ids_with_yield_per(bill_ids)
    }
    credit_note_map = {
        credit_note_aggregate.credit_note_id: credit_note_aggregate
        for credit_note_aggregate in credit_note_repo.load_for_bill_ids_with_yield_per(
            bill_ids
        )
    }
    (
        failure_report_rows,
        updated_bookings,
        updated_bills,
        updated_invoices,
        updated_credit_notes,
    ) = ([], [], [], [], [])
    for booking_aggregate in booking_aggregates:
        try:
            booking_owner = booking_aggregate.get_booking_owner()
            if (
                not booking_owner.has_company_detail()
                and not crs_context.is_treebo_tenant()
            ):
                booking_owner.update_gst_details(
                    GSTDetails(
                        legal_name=booking_owner.name.full_name,
                        address=booking_owner.address,
                    )
                )
            bill_aggregate = bill_map.get(booking_aggregate.bill_id)
            billed_entity_service.add_billed_entities_for_booking(
                booking_aggregate, bill_aggregate
            )
            _attach_billed_entity_account_to_charges(
                booking_aggregate, bill_aggregate, invoice_map, credit_note_map
            )
            _attach_billed_entity_account_to_payments(
                booking_aggregate, bill_aggregate, invoice_map
            )
            updated_invoices.extend(
                _attach_billed_entity_account_to_invoices(
                    booking_aggregate, bill_aggregate, invoice_map
                )
            )
            updated_credit_notes.extend(
                _attach_billed_entity_account_to_credit_notes(
                    booking_aggregate, bill_aggregate, invoice_map, credit_note_map
                )
            )
            updated_bookings.append(booking_aggregate)
            updated_bills.append(bill_aggregate)
        except Exception as e:
            logger.exception(
                f'Creating and attaching billed entity failed due to {e} for bill {booking_aggregate.booking_id}'
            )
            failure_report_rows.append(booking_aggregate.booking_id)

    booking_repo.update_all(updated_bookings)
    bill_repo.update_all(updated_bills)
    invoice_repo.update_all(updated_invoices)
    credit_note_repo.update_all(updated_credit_notes)


def _attach_billed_entity_account_to_charges(
    booking_aggregate, bill_aggregate, invoice_map, credit_note_map
):
    for charge in bill_aggregate._charges:
        default_billed_entity_account = (
            bill_aggregate.get_default_billed_entity_account(
                booking_aggregate.get_default_billed_entity_category(),
                charge_type=charge.type,
            )
        )
        if not charge.charge_splits:
            charge.add_new_charge_splits(default_billed_entity_account)
        default_billed_entity_attrib = (
            'billed_entity_id'
            if charge.bill_to_type == ChargeBillToTypes.GUEST
            else 'company_billed_entity_id'
        )
        for cs in charge.charge_splits:
            cs.charge_type = charge.type
            cs.bill_to_type = charge.bill_to_type
            if cs.is_invoiced:
                invoice_aggregate = invoice_map.get(cs.invoice_id)
                customer: Customer = booking_aggregate.get_customer(
                    invoice_aggregate.bill_to.customer_id
                )
                cs.attach_default_billed_entity(
                    bill_aggregate.get_billed_entity(
                        getattr(customer, default_billed_entity_attrib)
                    ).get_account_for_new_assignment(cs.charge_type)
                )
            if cs.is_credit_note_charge_split:
                credit_note_aggregate = credit_note_map.get(cs.credit_note_id)
                customer: Customer = booking_aggregate.get_customer(
                    credit_note_aggregate.credit_note.issued_to.customer_id
                )
                cs.attach_default_billed_entity(
                    bill_aggregate.get_billed_entity(
                        getattr(customer, default_billed_entity_attrib)
                    ).get_account_for_new_assignment(cs.charge_type)
                )
            bill_aggregate.add_folio_if_not_exists(
                cs.billed_entity_account.billed_entity_id,
                cs.billed_entity_account.account_number,
            )

            cs.attach_default_billed_entity(default_billed_entity_account)
        charge.charge_to = [cs.charge_to for cs in charge.charge_splits if cs.charge_to]


def _attach_billed_entity_account_to_payments(
    booking_aggregate, bill_aggregate, invoice_map
):
    invoice_aggregates = {
        invoice_map.get(charge_split.invoice_id)
        for charge in bill_aggregate._charges
        for charge_split in charge.charge_splits
        if charge_split.invoice_id
    }
    remaining_payment_amount = {
        payment.payment_id: payment.effective_amount
        for payment in bill_aggregate.payments
    }
    for invoice_aggregate in invoice_aggregates:
        invoice_amount = (
            invoice_aggregate.invoice.posttax_amount
            - invoice_aggregate.credit_note_amount
        )
        default_billed_entity_attrib = (
            'billed_entity_id'
            if invoice_aggregate.invoice.bill_to_type == ChargeBillToTypes.GUEST
            else 'company_billed_entity_id'
        )
        customer: Customer = booking_aggregate.get_customer(
            invoice_aggregate.bill_to.customer_id
        )
        for payment in bill_aggregate._payments:
            if payment.deleted or payment.status == PaymentStatus.CANCELLED:
                continue
            if remaining_payment_amount[payment.payment_id] == 0:
                continue
            if not invoice_amount:
                continue
            should_break = False
            if remaining_payment_amount[payment.payment_id] > invoice_amount:
                split = payment.add_payment_splits(
                    [
                        PaymentSplitData(
                            amount=invoice_amount,
                            billed_entity_account=bill_aggregate.get_billed_entity(
                                getattr(customer, default_billed_entity_attrib)
                            ).get_account_for_new_assignment(ChargeTypes.NON_CREDIT),
                        )
                    ]
                )[
                    0
                ]  # single payment split
                should_break = True
            else:
                split = payment.add_payment_splits(
                    [
                        PaymentSplitData(
                            amount=Money(
                                abs(remaining_payment_amount[payment.payment_id]),
                                bill_aggregate.bill.base_currency,
                            ),
                            billed_entity_account=bill_aggregate.get_billed_entity(
                                getattr(customer, default_billed_entity_attrib)
                            ).get_account_for_new_assignment(ChargeTypes.NON_CREDIT),
                        )
                    ]
                )[
                    0
                ]  # single payment split
            bill_aggregate.add_folio_if_not_exists(
                split.billed_entity_account.billed_entity_id,
                split.billed_entity_account.account_number,
            )
            if payment.payment_type == PaymentTypes.PAYMENT:
                invoice_amount -= split.amount
                remaining_payment_amount[payment.payment_id] -= split.amount
            elif payment.payment_type == PaymentTypes.REFUND:
                invoice_amount += split.amount
                remaining_payment_amount[payment.payment_id] += split.amount
            if should_break:
                break
    for payment_id, remaining_amount in remaining_payment_amount.items():
        if remaining_amount == 0:
            continue
        default_billed_entity = get_room_stay_default_billed_entity(
            booking_aggregate.get_active_room_stays()[0],
            booking_aggregate,
            bill_aggregate,
        )
        payment = bill_aggregate.get_payment(payment_id)

        if payment.payment_type in {PaymentTypes.PAYMENT, PaymentTypes.REFUND}:
            billed_entity_account_type = ChargeTypes.NON_CREDIT
        else:
            billed_entity_account_type = ChargeTypes.CREDIT
        payment.add_payment_splits(
            [
                PaymentSplitData(
                    amount=Money(
                        abs(remaining_amount), bill_aggregate.bill.base_currency
                    ),
                    billed_entity_account=bill_aggregate.get_billed_entity_account_for_new_assignment(
                        default_billed_entity, billed_entity_account_type
                    ),
                )
            ]
        )
    import datetime

    bill_aggregate.post_eligible_payments(datetime.date(2021, 7, 20))


def _attach_billed_entity_account_to_invoices(
    booking_aggregate, bill_aggregate, invoice_map
):
    invoice_aggregates = {
        invoice_map.get(charge_split.invoice_id)
        for charge in bill_aggregate._charges
        for charge_split in charge.charge_splits
        if charge_split.invoice_id and invoice_map.get(charge_split.invoice_id)
    }
    for invoice_aggregate in list(invoice_aggregates):
        default_billed_entity_attrib = (
            'billed_entity_id'
            if invoice_aggregate.invoice.bill_to_type == ChargeBillToTypes.GUEST
            else 'company_billed_entity_id'
        )
        customer: Customer = booking_aggregate.get_customer(
            invoice_aggregate.bill_to.customer_id
        )
        charge_type = invoice_aggregate.allowed_charge_types[0]
        invoice_aggregate.invoice.billed_entity_account = (
            bill_aggregate.get_billed_entity(
                getattr(customer, default_billed_entity_attrib)
            ).get_account_for_new_assignment(charge_type=charge_type)
        )
        bill_aggregate.add_folio_if_not_exists(
            invoice_aggregate.invoice.billed_entity_account.billed_entity_id,
            invoice_aggregate.invoice.billed_entity_account.account_number,
        )
    return invoice_aggregates


def _attach_billed_entity_account_to_credit_notes(
    booking_aggregate, bill_aggregate, invoice_map, credit_note_map
):
    credit_note_aggregates = {
        credit_note_map.get(charge_split.credit_note_id)
        for charge in bill_aggregate._charges
        for charge_split in charge.charge_splits
        if charge_split.credit_note_id
        and credit_note_map.get(charge_split.credit_note_id)
    }
    for credit_note_aggregate in list(credit_note_aggregates):
        invoice_id = list(
            {
                line_item.invoice_id
                for line_item in credit_note_aggregate.credit_note_line_items
            }
        )[0]
        invoice_aggregate = invoice_map.get(invoice_id)
        default_billed_entity_attrib = (
            'billed_entity_id'
            if invoice_aggregate.invoice.bill_to_type == ChargeBillToTypes.GUEST
            else 'company_billed_entity_id'
        )
        customer: Customer = booking_aggregate.get_customer(
            credit_note_aggregate.credit_note.issued_to.customer_id
        )
        charge_type = invoice_aggregate.allowed_charge_types[0]
        credit_note_aggregate.credit_note.billed_entity_account = (
            bill_aggregate.get_billed_entity(
                getattr(customer, default_billed_entity_attrib)
            ).get_account_for_new_assignment(charge_type)
        )
        bill_aggregate.add_folio_if_not_exists(
            credit_note_aggregate.credit_note.billed_entity_account.billed_entity_id,
            credit_note_aggregate.credit_note.billed_entity_account.account_number,
        )
    return credit_note_aggregates
