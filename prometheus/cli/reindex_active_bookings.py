import logging

import click
from flask.cli import with_appcontext
from treebo_commons.multitenancy.tenant_client import TenantClient
from treebo_commons.request_tracing.context import request_context

from object_registry import inject
from prometheus.domain.booking.repositories.booking_repository import BookingRepository
from prometheus.elastic_search.synchronizer.booking_data_synchronizer import (
    BookingDataSynchronizer,
)
from ths_common.constants.booking_constants import BookingStatus
from ths_common.utils.collectionutils import chunks

logger = logging.getLogger(__name__)


@click.command("reindex_active_bookings")
@click.option(
    '--booking_ids',
    help="Comma separated booking IDs to which is to be updated.",
    default=None,
)
@click.option(
    '--start_date',
    help="Checkout start date of bookings to process.",
    default=None,
)
@click.option(
    '--end_date',
    help="Checkout start date of bookings to process.",
    default=None,
)
@click.option(
    '--tenant_id',
    help="Tenant ID for which this command should be run.",
    default=TenantClient.get_default_tenant(),
)
@with_appcontext
@inject(
    booking_repo=BookingRepository,
    booking_data_synchronizer=BookingDataSynchronizer,
)
def reindex_active_bookings(
    booking_repo,
    booking_data_synchronizer,
    start_date,
    end_date,
    booking_ids,
    tenant_id,
):
    request_context.tenant_id = tenant_id
    booking_ids = _get_booking_ids(booking_repo, booking_ids, start_date, end_date)
    click.echo(f"Total bookings loaded: {len(booking_ids)}")

    _process_bookings(booking_data_synchronizer, booking_ids)


def _get_booking_ids(booking_repo, booking_ids, start_date, end_date):
    if booking_ids:
        return booking_repo.load_all_bookings_ids(booking_ids=booking_ids.split(','))
    return booking_repo.load_all_bookings_ids(
        start_date=start_date,
        end_date=end_date,
        status_to_exclude=[
            BookingStatus.CHECKED_OUT.value,
            BookingStatus.CANCELLED.value,
            BookingStatus.NOSHOW.value,
        ],
    )


def _process_bookings(booking_data_synchronizer: BookingDataSynchronizer, booking_ids):
    count = 0
    total = len(booking_ids)
    for batch_booking_ids in chunks(booking_ids, 1000):
        try:
            booking_data_synchronizer.handle(batch_booking_ids)
            count += len(batch_booking_ids)
            click.echo(f"Completed {count} / {total}")
        except Exception:
            logger.exception("Failed for bookings: %s", batch_booking_ids)
            click.echo(f"Failed for bookings: {batch_booking_ids}")
