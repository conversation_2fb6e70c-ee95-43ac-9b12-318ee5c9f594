import logging

import click
from flask.cli import with_appcontext
from treebo_commons.multitenancy.tenant_client import TenantClient
from treebo_commons.request_tracing.context import request_context
from treebo_commons.utils import dateutils

from object_registry import inject
from prometheus.application.decorators import session_manager
from prometheus.domain.billing.repositories import BillRepository, InvoiceRepository
from prometheus.domain.booking.dtos import BookingSearchQuery
from prometheus.domain.booking.repositories import BookingRepository
from ths_common.constants.billing_constants import (
    BilledEntityCategory,
    BilledEntityStatus,
    InvoiceStatus,
)
from ths_common.constants.booking_constants import BookingStatus
from ths_common.utils.collectionutils import chunks

logger = logging.getLogger(__name__)


@click.command()
@click.option(
    '--booking_ids',
    help="comma separated booking_ids to which folios are to be attached",
    default=None,
)
@click.option(
    '--start_date',
    help="start date of bookings that we need to attach folios ",
    default=None,
)
@click.option(
    '--end_date',
    help="end date of bookings that we need to attach folios",
    default=None,
)
@click.option(
    '--tenant_id',
    help="Tenant ID for which this command should be run.",
    default=TenantClient.get_default_tenant(),
)
@click.option(
    '--batch_size',
    help="Batch Size - Number of records That should be processed & persisted at a given point of time. Default - 500",
    default=500,
)
@with_appcontext
@inject(
    booking_repo=BookingRepository,
    bill_repo=BillRepository,
    invoice_repo=InvoiceRepository,
)
@with_appcontext
def create_and_attach_folios_and_update_billed_entity_status(
    booking_repo,
    bill_repo,
    invoice_repo,
    booking_ids,
    start_date,
    end_date,
    tenant_id,
    batch_size,
):
    request_context.tenant_id = tenant_id
    logger.info('Started create_and_attach_folio.. ')
    if booking_ids and (start_date or end_date):
        raise Exception(
            'Please provide either the booking_ids or start_date & end_date.'
        )

    if not (start_date and end_date) and not booking_ids:
        raise Exception('Please provide both start_date & end_date.')
    if booking_ids:
        logger.info('Booking IDs were provided. Proceeding with provided bookings.')
        booking_aggregates = booking_repo.load_all(booking_ids.split(','))
    else:
        logger.info('Start & End Date were provided. Proceeding with Start & End Date.')
        booking_aggregates = booking_repo.search(
            BookingSearchQuery(
                checkin_start=dateutils.ymd_str_to_date(start_date),
                checkin_end=dateutils.ymd_str_to_date(end_date),
                limit=10000000,
            )
        )

    count = 0
    for booking_aggregates_batch in chunks(booking_aggregates, batch_size):
        booking_by_status_map = dict()
        for booking_aggregate in booking_aggregates_batch:
            booking_by_status_map.setdefault(
                booking_aggregate.booking.status, []
            ).append(booking_aggregate)

        bill_wise_booking_aggregate_map = dict()
        for booking_aggregate in booking_aggregates_batch:
            bill_wise_booking_aggregate_map[
                booking_aggregate.bill_id
            ] = booking_aggregate

        if booking_by_status_map.get(
            BookingStatus.CANCELLED
        ) or booking_by_status_map.get(BookingStatus.NOSHOW):
            logger.info('Handling Cancelled & NoShow bookings.')
            _create_folio_and_update_billed_entity_status_for_cancelled_noshow_bookings(
                booking_by_status_map.get(BookingStatus.CANCELLED)
                or [] + booking_by_status_map.get(BookingStatus.NOSHOW)
                or [],
                bill_repo,
                bill_wise_booking_aggregate_map,
            )

        invoice_aggregates = []
        if booking_by_status_map.get(BookingStatus.CHECKED_OUT):
            invoice_aggregates = invoice_repo.load_for_bill_ids_with_yield_per(
                [
                    b.bill_id
                    for b in booking_by_status_map.get(BookingStatus.CHECKED_OUT)
                ],
                [InvoiceStatus.CANCELLED.value, InvoiceStatus.PREVIEW.value],
                load_charges=False,
            )

        invoice_bill_billed_entity_account_map = dict()
        for ia in invoice_aggregates or []:
            invoice_bill_billed_entity_account_map.setdefault(
                ia.invoice.bill_id, []
            ).append(ia.invoice.billed_entity_account)

        if invoice_bill_billed_entity_account_map:
            logger.info('Handling Checked Out bookings.')
            _create_folio_and_update_billed_entity_status_for_checked_out_bookings(
                bill_wise_booking_aggregate_map,
                bill_repo,
                invoice_bill_billed_entity_account_map,
            )

        for status in [
            k
            for k in booking_by_status_map.keys()
            if k
            not in (
                BookingStatus.CANCELLED,
                BookingStatus.NOSHOW,
                BookingStatus.CHECKED_OUT,
            )
        ]:
            logger.info(
                'Handling bookings which are not in Checked-Out, Cancelled & NoShow.'
            )
            _create_folio_and_update_billed_entity_status_for_non_checked_out_active_bookings(
                booking_by_status_map.get(status),
                bill_repo,
                bill_wise_booking_aggregate_map,
            )
        count += batch_size

    logger.info('Completed create_and_attach_folio')


@session_manager(commit=True)
def _create_folio_and_update_billed_entity_status_for_checked_out_bookings(
    bill_wise_booking_aggregate_map, bill_repo, invoice_bill_billed_entity_account_map
):
    bill_aggregates = bill_repo.load_all_for_update(
        bill_wise_booking_aggregate_map.keys()
    )
    for bill_aggregate in bill_aggregates:
        logger.info(
            f"Processing for Booking ID: {bill_aggregate.parent_reference_number}"
        )
        billed_entity_accounts = set()
        if (
            invoice_bill_billed_entity_account_map
            and invoice_bill_billed_entity_account_map.get(bill_aggregate.bill_id)
        ):
            billed_entity_accounts.update(
                invoice_bill_billed_entity_account_map.get(bill_aggregate.bill_id)
            )
        billed_entity_accounts.update(
            [
                ps.billed_entity_account
                for p in bill_aggregate.payments
                for ps in p.payment_splits
            ]
        )
        billed_entity_accounts.update(
            set(
                [
                    cs.billed_entity_account
                    for c in bill_aggregate.charges
                    for cs in c.charge_splits
                ]
            )
        )

        if billed_entity_accounts:
            logger.info(
                f"Booking ID: {bill_aggregate.parent_reference_number}, "
                f"Adding folio for "
                f"{[bea.__str__() for bea in billed_entity_accounts]}"
            )
            [
                bill_aggregate.add_folio_if_not_exists(
                    bea.billed_entity_id, bea.account_number
                )
                for bea in billed_entity_accounts
            ]

        _update_billed_entity_status_for_inactive_guests(
            bill_wise_booking_aggregate_map, bill_aggregate
        )

    bill_repo.update_all(bill_aggregates)


@session_manager(commit=True)
def _create_folio_and_update_billed_entity_status_for_cancelled_noshow_bookings(
    booking_aggregates, bill_repo, bill_wise_booking_aggregate_map
):
    bill_aggregates = bill_repo.load_all_for_update(
        [b.bill_id for b in booking_aggregates]
    )
    for bill_aggregate in bill_aggregates:
        logger.info(
            f"Processing for Booking ID: {bill_aggregate.parent_reference_number}"
        )
        billed_entity_accounts_with_charges = set(
            [
                cs.billed_entity_account
                for c in bill_aggregate.charges
                for cs in c.charge_splits
            ]
        )

        if billed_entity_accounts_with_charges:
            logger.info(
                f"Booking ID: {bill_aggregate.parent_reference_number}, "
                f"Adding folio for {[bea.__str__() for bea in billed_entity_accounts_with_charges]}"
            )
            [
                bill_aggregate.add_folio_if_not_exists(
                    bea.billed_entity_id, bea.account_number
                )
                for bea in billed_entity_accounts_with_charges
            ]
            billed_entity_accounts_with_charges.update(
                [
                    ps.billed_entity_account
                    for p in bill_aggregate.payments
                    for ps in p.payment_splits
                ]
            )

        billed_entity_status_map = dict()
        for billed_entity in bill_aggregate.billed_entities:
            billed_entity_status_map.setdefault(billed_entity.deleted, []).append(
                billed_entity
            )

        if billed_entity_status_map.get(True):
            logger.info(
                f"Booking ID: {bill_aggregate.parent_reference_number}, Billed Entities "
                f"{[be.billed_entity_id for be in billed_entity_status_map.get(True)]} "
                f"marked as INACTIVE"
            )
            bill_aggregate.safe_inactivate_billed_entities(
                [be.billed_entity_id for be in billed_entity_status_map.get(True)],
                BilledEntityStatus.INACTIVE,
            )
        else:
            if (
                bill_wise_booking_aggregate_map.get(
                    bill_aggregate.bill_id
                ).booking.status
                == BookingStatus.CANCELLED
            ):
                if billed_entity_status_map.get(False) and [
                    be.billed_entity_id
                    for be in billed_entity_status_map.get(False)
                    if be.category
                    in (
                        BilledEntityCategory.PRIMARY_GUEST,
                        BilledEntityCategory.CONSUMING_GUESTS,
                    )
                ]:
                    logger.info(
                        f"Booking ID: {bill_aggregate.parent_reference_number}, Billed Entities "
                        f"{[be.billed_entity_id for be in billed_entity_status_map.get(False) if be.category in (BilledEntityCategory.PRIMARY_GUEST, BilledEntityCategory.CONSUMING_GUESTS)]} "
                        f"marked as CANCEL"
                    )
                    bill_aggregate.safe_inactivate_billed_entities(
                        [
                            be.billed_entity_id
                            for be in billed_entity_status_map.get(False)
                            if be.category
                            in (
                                BilledEntityCategory.PRIMARY_GUEST,
                                BilledEntityCategory.CONSUMING_GUESTS,
                            )
                        ],
                        BilledEntityStatus.CANCEL,
                    )
            if (
                bill_wise_booking_aggregate_map.get(
                    bill_aggregate.bill_id
                ).booking.status
                == BookingStatus.NOSHOW
            ):
                if billed_entity_status_map.get(False) and [
                    be.billed_entity_id
                    for be in billed_entity_status_map.get(False)
                    if be.category
                    in (
                        BilledEntityCategory.PRIMARY_GUEST,
                        BilledEntityCategory.CONSUMING_GUESTS,
                    )
                ]:
                    logger.info(
                        f"Booking ID: {bill_aggregate.parent_reference_number}, Billed Entities "
                        f"{[be.billed_entity_id for be in billed_entity_status_map.get(False) if be.category in (BilledEntityCategory.PRIMARY_GUEST, BilledEntityCategory.CONSUMING_GUESTS)]} "
                        f"marked as NOSHOW"
                    )
                    bill_aggregate.safe_inactivate_billed_entities(
                        [
                            be.billed_entity_id
                            for be in billed_entity_status_map.get(False)
                            if be.category
                            in (
                                BilledEntityCategory.PRIMARY_GUEST,
                                BilledEntityCategory.CONSUMING_GUESTS,
                            )
                        ],
                        BilledEntityStatus.NOSHOW,
                    )
            if [
                be.billed_entity_id
                for be in bill_aggregate.billed_entities
                if be.status == BilledEntityStatus.ACTIVE
            ]:
                logger.info(
                    f"Booking ID: {bill_aggregate.parent_reference_number}, Billed Entities "
                    f"{[be.billed_entity_id for be in bill_aggregate.billed_entities if be.status == BilledEntityStatus.ACTIVE]} "
                    f"marked as ACTIVE"
                )
                bill_aggregate.activate_inactive_billed_entitties(
                    [
                        be.billed_entity_id
                        for be in bill_aggregate.billed_entities
                        if be.status == BilledEntityStatus.ACTIVE
                    ]
                )
    bill_repo.update_all(bill_aggregates)


@session_manager(commit=True)
def _create_folio_and_update_billed_entity_status_for_non_checked_out_active_bookings(
    booking_aggregates, bill_repo, bill_wise_booking_aggregate_map
):
    bill_aggregates = bill_repo.load_all_for_update(
        [b.bill_id for b in booking_aggregates]
    )
    for bill_aggregate in bill_aggregates:
        logger.info(
            f"Processing for Booking ID: {bill_aggregate.parent_reference_number}"
        )
        billed_entity_accounts_with_charges = set(
            [
                cs.billed_entity_account
                for c in bill_aggregate.charges
                for cs in c.charge_splits
            ]
        )
        billed_entity_accounts_with_charges.update(
            [
                ps.billed_entity_account
                for p in bill_aggregate.payments
                for ps in p.payment_splits
            ]
        )

        [
            bill_aggregate.add_folio_if_not_exists(
                bea.billed_entity_id, bea.account_number
            )
            for bea in billed_entity_accounts_with_charges
        ]

        billed_entity_status_map = dict()
        for billed_entity in bill_aggregate.billed_entities:
            billed_entity_status_map.setdefault(billed_entity.deleted, []).append(
                billed_entity
            )

        if billed_entity_status_map.get(True):
            logger.info(
                f"Booking ID: {bill_aggregate.parent_reference_number}, Billed Entities "
                f"{[be.billed_entity_id for be in billed_entity_status_map.get(True)]} marked as "
                f"INACTIVE"
            )
            bill_aggregate.safe_inactivate_billed_entities(
                [be.billed_entity_id for be in billed_entity_status_map.get(True)],
                BilledEntityStatus.INACTIVE,
            )
        if billed_entity_status_map.get(False):
            _update_billed_entity_status_for_inactive_guests(
                bill_wise_booking_aggregate_map, bill_aggregate
            )

            pending_billed_entity_ids = [
                be.billed_entity_id
                for be in billed_entity_status_map.get(False)
                if bill_aggregate.get_billed_entity(be.billed_entity_id).status
                == BilledEntityStatus.ACTIVE
            ]
            if pending_billed_entity_ids:
                logger.info(
                    f"Booking ID: {bill_aggregate.parent_reference_number}, Billed Entities {pending_billed_entity_ids}"
                    f" marked as ACTIVE"
                )
                bill_aggregate.activate_inactive_billed_entitties(
                    pending_billed_entity_ids
                )

    bill_repo.update_all(bill_aggregates)


def _update_billed_entity_status_for_inactive_guests(
    bill_wise_booking_aggregate_map, bill_aggregate
):
    logger.info(
        f"Booking ID: {bill_aggregate.parent_reference_number}, _update_billed_entity_status_for_inactive_guests."
    )
    booking_aggregate = bill_wise_booking_aggregate_map.get(bill_aggregate.bill_id)

    guest_status_map = dict()
    for gs in booking_aggregate.get_all_guest_stays():
        guest_status_map.setdefault(gs.status, []).append(gs.guest_id)

    deleted_billed_entity_ids = [
        be.billed_entity_id
        for be in bill_aggregate.billed_entities
        if be.deleted is True
    ]
    if deleted_billed_entity_ids:
        logger.info(
            f"Booking ID: {booking_aggregate.booking_id}, Billed Entities {deleted_billed_entity_ids} marked as "
            f"INACTIVE"
        )
        bill_aggregate.safe_inactivate_billed_entities(
            deleted_billed_entity_ids, BilledEntityStatus.INACTIVE
        )

    if guest_status_map.get(BookingStatus.NOSHOW):
        no_show_billed_entity_ids = set(
            [
                c.billed_entity_id
                for c in booking_aggregate.get_customers(
                    guest_status_map.get(BookingStatus.NOSHOW)
                )
            ]
        )
        if None in no_show_billed_entity_ids:
            no_show_billed_entity_ids.remove(None)
        if no_show_billed_entity_ids:
            logger.info(
                f"Booking ID: {booking_aggregate.booking_id}, Billed Entities {no_show_billed_entity_ids} marked as "
                f"NOSHOW"
            )
            bill_aggregate.safe_inactivate_billed_entities(
                no_show_billed_entity_ids, BilledEntityStatus.NOSHOW
            )

    if guest_status_map.get(BookingStatus.CANCELLED):
        cancelled_billed_entity_ids = set(
            [
                c.billed_entity_id
                for c in booking_aggregate.get_customers(
                    guest_status_map.get(BookingStatus.CANCELLED)
                )
            ]
        )

        if None in cancelled_billed_entity_ids:
            cancelled_billed_entity_ids.remove(None)
        if cancelled_billed_entity_ids:
            logger.info(
                f"Booking ID: {booking_aggregate.booking_id}, Billed Entities {cancelled_billed_entity_ids} marked as "
                f"CANCEL"
            )
            bill_aggregate.safe_inactivate_billed_entities(
                cancelled_billed_entity_ids, BilledEntityStatus.CANCEL
            )

    if guest_status_map.get(BookingStatus.CHECKED_OUT):
        checked_out_billed_entity_ids = set(
            [
                c.billed_entity_id
                for c in booking_aggregate.get_customers(
                    guest_status_map.get(BookingStatus.CHECKED_OUT)
                )
            ]
        )
        checked_out_billed_entity_ids.update(
            set(
                [
                    be.billed_entity_id
                    for be in bill_aggregate.billed_entities
                    if be.status == BilledEntityStatus.ACTIVE
                ]
            )
        )
        if None in checked_out_billed_entity_ids:
            checked_out_billed_entity_ids.remove(None)

        logger.info(
            f"Booking ID: {booking_aggregate.booking_id}, Billed Entities {checked_out_billed_entity_ids} marked as "
            f"ACTIVE"
        )
        if checked_out_billed_entity_ids:
            bill_aggregate.activate_inactive_billed_entitties(
                set(checked_out_billed_entity_ids)
            )
