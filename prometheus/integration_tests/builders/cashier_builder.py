import json
from prometheus.integration_tests.config import common_config
from prometheus.integration_tests.utilities.common_utils import increment_date, sanitize_blank, sanitize_test_data
from prometheus.integration_tests.config import sheet_names
from prometheus.integration_tests.utilities import excel_utils


class CreateRegisterRequest:
    def __init__(self, sheet_name, test_case_id, hotel_id):
        test_data = excel_utils.get_test_case_data(sheet_name, test_case_id)[0]
        self.data = CreateRegisterRequest.CreateRegisterData(test_data, hotel_id).__dict__

    class CreateRegisterData:
        def __init__(self, test_data, hotel_id):
            self.default_opening_balance = sanitize_test_data(test_data['default_opening_balance'])
            self.vendor_id = hotel_id
            self.cash_register_name = sanitize_test_data(test_data['cash_register_name'])
            self.carry_balance_to_next_shift = sanitize_test_data(test_data['carry_balance_to_next_shift'])


class CreateCashRegisterSessionRequest:
    def __init__(self, sheet_name, test_case_id, hotel_id):
        test_data = excel_utils.get_test_case_data(sheet_name, test_case_id)[0]
        self.data = CreateCashRegisterSessionRequest.CashRegisterSessionData(test_data, hotel_id).__dict__

    class CashRegisterSessionData:
        def __init__(self, test_data, hotel_id):
            self.opening_balance = sanitize_test_data(test_data['default_opening_balance'])
            self.vendor_id = hotel_id
            self.opening_balance_in_base_currency = sanitize_test_data(test_data['opening_balance_in_base_currency'])


class CreateSessionPaymentsRequest:
    def __init__(self, sheet_name, test_case_id):
        test_data = excel_utils.get_test_case_data(sheet_name, test_case_id)[0]
        self.data = CreateSessionPaymentsRequest.SessionPaymentData(test_data).__dict__

    class SessionPaymentData:
        def __init__(self, test_data):
            self.amount = sanitize_test_data(test_data['amount'])
            self.amount_in_payment_currency = sanitize_test_data(test_data['amount_in_payment_currency'])
            self.paid_to = sanitize_test_data(test_data['paid_to'])
            self.date_of_payment = increment_date(int(test_data['date_of_payment']))
            self.payment_mode = sanitize_test_data(test_data['payment_mode'])
            self.payment_mode_sub_type = sanitize_test_data(test_data['payment_mode_sub_type'])
            self.payment_type = sanitize_test_data(test_data['payment_type'])
            self.payment_details = json.loads(test_data['payment_details'])
            self.comment = sanitize_test_data(test_data['comment'])
            self.status = sanitize_test_data(test_data['status'])


class EditRegisterSession:
    def __init__(self, sheet_name, test_case_id, vendor_id):
        test_data = excel_utils.get_test_case_data(sheet_name, test_case_id)[0]
        self.data = EditRegisterSession.EditRegisterSessionData(test_data, vendor_id).__dict__

    class EditRegisterSessionData:
        def __init__(self, test_data, vendor_id):
            self.status = sanitize_test_data(test_data['status'])
            self.vendor_id = vendor_id


class EditCashRegister:
    def __init__(self, sheet_name, test_case_id):
        test_data = excel_utils.get_test_case_data(sheet_name, test_case_id)[0]
        self.data = EditCashRegister.EditCashRegisterData(test_data).__dict__

    class EditCashRegisterData:
        def __init__(self, test_data):
            self.carry_balance_to_next_session = sanitize_test_data(test_data['carry_balance_to_next_shift'])
            self.default_opening_balance = sanitize_test_data(test_data['default_opening_balance'])
