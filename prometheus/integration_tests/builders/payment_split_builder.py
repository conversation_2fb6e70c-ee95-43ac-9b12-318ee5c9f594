import json
from prometheus.integration_tests.utilities.common_utils import increment_date, sanitize_test_data, sanitize_test_data
from prometheus.integration_tests.config import sheet_names
from prometheus.integration_tests.utilities import excel_utils
from prometheus.domain.booking.services.booking_id_generator import BookingIdGenerator
from prometheus.integration_tests.builders import common_request_builder
from prometheus.integration_tests.builders.common_request_builder import booking_repo
from prometheus.integration_tests.config import common_config, sheet_names


class PaymentSplit(object):
    def __init__(self, sheet_name, test_case_id):
        test_data = excel_utils.get_test_case_data(sheet_name, test_case_id)
        self.data = PaymentSplit.PaymentSplitData(test_data[0]).__dict__
        self.resource_version = test_data[0]['version']

    class PaymentSplitData(object):
        def __init__(self, payment_split_data):
            self.amount = sanitize_test_data(payment_split_data['amount'])
            self.amount_in_payment_currency = sanitize_test_data(payment_split_data['amount_in_payment_currency'])
            self.comment = sanitize_test_data(payment_split_data['comment'])
            self.paid_by = sanitize_test_data(payment_split_data['paid_by'])
            self.paid_to = sanitize_test_data(payment_split_data['paid_to'])
            if sanitize_test_data(payment_split_data['payment_details']):
                self.payment_details = json.loads(sanitize_test_data(payment_split_data['payment_details']))
            self.payment_channel = sanitize_test_data(payment_split_data['payment_channel'])
            self.payment_mode = sanitize_test_data(payment_split_data['payment_mode'])
            self.payment_ref_id = sanitize_test_data(str(payment_split_data['payment_ref_id']))
            self.payment_type = sanitize_test_data(payment_split_data['payment_type'])
            self.status = sanitize_test_data(payment_split_data['status'])
            if sanitize_test_data(payment_split_data['payment_splits']):
                self.payment_splits = json.loads(sanitize_test_data(payment_split_data['payment_splits']))

        class PaymentSplits(object):
            def __init__(self, payment_split_data, amount):
                self.amount = amount
                self.billed_entity_account = sanitize_test_data(payment_split_data['BilledEntityAccount'])
