import json

from prometheus.integration_tests.builders.common_request_builder import booking_repo, invoice_repo
from prometheus.integration_tests.config import common_config
from prometheus.integration_tests.utilities import excel_utils
from prometheus.integration_tests.utilities.common_utils import sanitize_test_data, increment_date


class PreviewInvoiceBuilder(object):
    def __init__(self, sheet_name, test_case_id, booking_id):
        test_data = excel_utils.get_test_case_data(sheet_name, test_case_id)[0]
        self.resource_version = booking_repo().load(booking_id).booking.version
        self.data = PreviewInvoiceBuilder.Data(test_data).__dict__
    
    class Data(object):
        def __init__(self, preview_data):
            self.include_cancellation_no_show_charges = sanitize_test_data(
                preview_data['include_cancel_no_show_charges'])
            if sanitize_test_data(preview_data['is_advance']):
                self.is_advanced = preview_data['is_advance']
            else:
                self.is_advanced = False
            if sanitize_test_data(preview_data['preview_date']):
                self.preview_date = increment_date(int(preview_data['preview_date']),
                                                   common_config.PREVIEW_INVOICE_TIME_ZONE)
            room_stay_list = []
            guest_stay_list = []
            if sanitize_test_data(preview_data['room_stay_id']):
                room_stay_list = preview_data['room_stay_id'].split(',')
            if sanitize_test_data(preview_data['guest_ids']):
                guest_stay_list = list(preview_data['guest_ids'].split('#'))
            if len(room_stay_list) == len(guest_stay_list):
                self.room_wise_invoice_request = []
                for room_stay_list_data, guest_stay_list_data in zip(room_stay_list, guest_stay_list):
                    room_wise_invoice_request = PreviewInvoiceBuilder.Data.RoomWiseInvoiceRequest(room_stay_list_data,
                                                                                                  guest_stay_list_data,
                                                                                                  True).__dict__
                    self.room_wise_invoice_request.append(room_wise_invoice_request)
            else:
                self.room_wise_invoice_request = []
                room_wise_invoice_request = PreviewInvoiceBuilder.Data.RoomWiseInvoiceRequest(room_stay_list,
                                                                                              guest_stay_list,
                                                                                              False).__dict__
                self.room_wise_invoice_request.append(room_wise_invoice_request)

            if sanitize_test_data(preview_data['booked_charges_to_post']) and \
                    preview_data['booked_charges_to_post'] != 'NULL':
                self.booked_charges_to_post = json.loads(preview_data['booked_charges_to_post'])
            else:
                self.booked_charges_to_post = sanitize_test_data(preview_data['booked_charges_to_post'])

            if sanitize_test_data(preview_data['booked_allowances_to_post']) and \
                    preview_data['booked_allowances_to_post'] != 'NULL':
                self.booked_allowances_to_post = json.loads(preview_data['booked_allowances_to_post'])
            elif preview_data['booked_allowances_to_post'] == '[]':
                self.booked_allowances_to_post = []
            else:
                self.booked_allowances_to_post = sanitize_test_data(preview_data['booked_allowances_to_post'])

            if sanitize_test_data(preview_data['booked_charges_to_cancel']) and \
                    preview_data['booked_charges_to_cancel'] != 'NULL':
                self.booked_charges_to_cancel = json.loads(preview_data['booked_charges_to_cancel'])
            else:
                self.booked_charges_to_cancel = sanitize_test_data(preview_data['booked_charges_to_cancel'])

            if sanitize_test_data(preview_data['booked_allowances_to_cancel']) and \
                    preview_data['booked_allowances_to_cancel'] != 'NULL':
                self.booked_allowances_to_cancel = json.loads(preview_data['booked_allowances_to_cancel'])
            elif preview_data['booked_allowances_to_cancel'] == '[]':
                self.booked_allowances_to_cancel = []
            else:
                self.booked_allowances_to_cancel = sanitize_test_data(preview_data['booked_allowances_to_cancel'])

            self.cancellation_policy = sanitize_test_data(preview_data['cancellation_policy'])
            self.cancellation_amount = sanitize_test_data(preview_data['cancellation_amount'])

        class RoomWiseInvoiceRequest(object):
            def __init__(self, room_stay_list, guest_stay_list, flag):
                if flag:
                    self.room_stay_id = room_stay_list
                    self.guest_ids = guest_stay_list.split(',')
                else:
                    if len(room_stay_list) == 0:
                        self.room_stay_id = ""
                    else:
                        self.room_stay_id = room_stay_list[0]
                    if len(guest_stay_list) == 0:
                        self.guest_ids = guest_stay_list
                    else:
                        self.guest_ids = guest_stay_list[0].split(',')


class PatchInvoiceBuilder(object):
    def __init__(self, sheet_name, test_case_id, invoice_id):
        test_data = excel_utils.get_test_case_data(sheet_name, test_case_id)[0]
        self.resource_version = invoice_repo().load(invoice_id).invoice.version
        self.data = Data(test_data).__dict__


class Data(object):
    def __init__(self, test_data):
        self.is_downloaded = sanitize_test_data(test_data['is_downloaded'])
