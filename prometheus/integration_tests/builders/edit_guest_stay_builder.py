import json

from prometheus.integration_tests.builders.common_request_builder import Customers
from prometheus.integration_tests.builders.common_request_builder import booking_repo, get_resource_version
from prometheus.integration_tests.utilities import excel_utils
from prometheus.integration_tests.utilities.common_utils import sanitize_test_data, del_none


class EditGuestStay:
    def __init__(self, sheet_name, test_case_id, booking_id):
        self.test_case_id = test_case_id
        self.sheet_name = sheet_name
        self.booking_id = booking_id
        test_data = excel_utils.get_test_case_data(sheet_name, test_case_id)
        if 'PatchGuestStay_13' in test_case_id:
            self.resource_version = 1000
        else:
            self.resource_version = booking_repo().load(booking_id).booking.version

    def get_edit_guest_stay_request(self):
        test_data = excel_utils.get_test_case_data(self.sheet_name, self.test_case_id)[0]
        guest_id = None
        if sanitize_test_data(test_data['guest_id']):
            guest_id = str(sanitize_test_data(test_data['guest_id']))
        guest_data = Customers().get_customers_data(test_data)
        resource_version = get_resource_version(self.booking_id)
        return json.dumps(del_none({
            "data": {
                "guest_id": guest_id,
                "guest": guest_data
            },
            "resource_version": resource_version
        }))
