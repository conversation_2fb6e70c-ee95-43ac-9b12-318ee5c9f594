import json

from prometheus.integration_tests.builders.common_request_builder import booking_repo
from prometheus.integration_tests.config.sheet_names import billing_instruction_sheet_name, inclusion_charge_sheet_name
from prometheus.integration_tests.utilities import excel_utils
from prometheus.integration_tests.utilities.common_utils import sanitize_test_data


class EditChargePrice(object):
    def __init__(self, sheet_name, test_case_id, booking_id):
        test_data = excel_utils.get_test_case_data(sheet_name, test_case_id)
        self.data = []
        for data in test_data:
            self.data.append(self.Data(data).__dict__)
        self.resource_version = booking_repo().load(booking_id).booking.version

    class Data(object):
        def __init__(self, test_data):
            if test_data['billed_entity_account']:
                self.billing_instructions = []
                for billing_instruction_data in test_data['billed_entity_account'].split(','):
                    charge_data = excel_utils.get_test_case_data(billing_instruction_sheet_name,
                                                                 billing_instruction_data)
                    for data in charge_data:
                        bill_instruction = self.BillingInstructionData(data).__dict__
                        self.billing_instructions.append(bill_instruction)
            self.charge_id = test_data['charge_id']
            if test_data['posttax_amount']:
                self.posttax_amount = str(sanitize_test_data(test_data['posttax_amount']))
            if test_data['pretax_amount']:
                self.pretax_amount = str(sanitize_test_data(test_data['pretax_amount']))
            if test_data['inclusion_charges']:
                self.inclusion_charges = []
                for data in test_data['inclusion_charges'].split(','):
                    inclusion_charge_data = excel_utils.get_test_case_data(inclusion_charge_sheet_name,
                                                                           data)[0]
                    self.inclusion_charges.append(InclusionChargeData(inclusion_charge_data).__dict__)

            if test_data['charge_to']:
                self.inclusion_charges = int(sanitize_test_data(test_data['charge_to']))

        class BillingInstructionData(object):
            def __init__(self, charge_data):
                if sanitize_test_data(charge_data['billed_entity_account']):
                    self.billed_entity_account = json.loads(
                        sanitize_test_data(charge_data['billed_entity_account']))
                self.payment_instruction = sanitize_test_data(charge_data['payment_instruction'])
                if sanitize_test_data(charge_data['split_percentage']):
                    self.split_percentage = int(sanitize_test_data(charge_data['split_percentage']))


class InclusionChargeData(object):
    def __init__(self, inclusion_charge_data):
        self.charge_id = int(inclusion_charge_data['charge_id'])
        if inclusion_charge_data['posttax_amount']:
            self.posttax_amount = str(inclusion_charge_data['posttax_amount'])
        else:
            self.pretax_amount = str(inclusion_charge_data['pretax_amount'])
