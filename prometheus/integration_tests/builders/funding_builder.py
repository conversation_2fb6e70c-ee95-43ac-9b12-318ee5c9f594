import json

from prometheus.integration_tests.utilities import excel_utils
from prometheus.integration_tests.config import sheet_names
from prometheus.integration_tests.utilities.common_utils import sanitize_test_data


class AddManualFundingBuilder:
    def __init__(self, sheet_name, test_case_id, booking_id):
        test_data = excel_utils.get_test_case_data(sheet_name, test_case_id)[0]
        self.data = {}

        if sanitize_test_data(test_data.get('manual_funding_request')):
            funding_data = excel_utils.get_test_case_data(
                sheet_names.funding_summary_sheet_name,
                test_case_id
            )[0]

            manual_funding_request = json.loads(funding_data['manual_funding_request'])

            self.data = {
                "amount": str(manual_funding_request.get("amount", "")),
                "funding_type": manual_funding_request.get("funding_type", "manual_funding"),
                "reason": manual_funding_request.get("reason", "")
            }
