import json
from prometheus.integration_tests.config import sheet_names
from prometheus.integration_tests.utilities.common_utils import assert_, sanitize_blank
from prometheus.integration_tests.utilities.excel_utils import get_test_case_data


class ValidationArrivalDeparture:
    def __init__(self, test_case_id, response):
        self.test_data = get_test_case_data(sheet_names.arrival_departure_sheet_name, test_case_id)[0]
        self.response = response

    def validate_response(self):
        expected_response = json.loads(self.test_data['expected_response'])
        actual_arrived = self.response['data']['arrivals']['arrived']
        actual_due_in_room_assigned = self.response['data']['arrivals']['due_in_room_assigned']
        actual_due_in_room_unassigned = self.response['data']['arrivals']['due_in_room_unassigned']
        actual_departed = self.response['data']['departures']['departed']
        actual_due_out = self.response['data']['departures']['due_out']
        expected_arrived = expected_response['arrivals']['arrived']
        expected_due_in_room_assigned = expected_response['arrivals']['due_in_room_assigned']
        expected_due_in_room_unassigned = expected_response['arrivals']['due_in_room_unassigned']
        expected_departed = expected_response['departures']['departed']
        expected_due_out = expected_response['departures']['due_out']

        self.validate_room_count_for_each_case(actual_arrived.values(), expected_arrived.values())
        self.validate_room_count_for_each_case(actual_due_in_room_assigned.values(),
                                               expected_due_in_room_assigned.values())
        self.validate_room_count_for_each_case(actual_due_in_room_unassigned.values(),
                                               expected_due_in_room_unassigned.values())
        self.validate_room_count_for_each_case(actual_departed.values(), expected_departed.values())
        self.validate_room_count_for_each_case(actual_due_out.values(), expected_due_out.values())

    @staticmethod
    def validate_room_count_for_each_case(actual_response, expected_response):
        for actual_response_today, expected_response_today in zip(actual_response, expected_response):
            assert_(actual_response_today, expected_response_today)
