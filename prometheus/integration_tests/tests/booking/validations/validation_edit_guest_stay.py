from flask import json

from prometheus.integration_tests.tests.base_validations import *


class ValidationEditGuestStay(BaseValidations):
    def __init__(self, client_, test_case_id, response, booking_id, booking_request):
        self.test_case_id = test_case_id
        self.response = response
        self.client = client_
        self.booking_id = booking_id
        self.booking_request = booking_request

    def validate_response(self, customer_request):
        test_data = get_test_case_data(sheet_names.guest_stays_sheet_name, self.test_case_id)[0]
        response_data = self.response['data']
        if sanitize_test_data(test_data['guest_stay_id']):
            assert_(sanitize_test_data(test_data['guest_stay_id']), str(response_data['guest_stay_id']))

        if sanitize_test_data(test_data['expected_guest_allocation']):
            expected_guest_allocation = json.loads(test_data['expected_guest_allocation'])
            assert_(response_data['guest_allocation']['assigned_by'],
                    sanitize_test_data(expected_guest_allocation['assigned_by']))
            assert_(response_data['guest_allocation']['checkin_date'], expected_guest_allocation['checkin_date'])
            assert_(response_data['guest_allocation']['checkout_date'], expected_guest_allocation['checkout_date'])
            assert_(response_data['guest_allocation']['guest_allocation_id'],
                    expected_guest_allocation['guest_allocation_id'])
            assert_(response_data['guest_allocation']['guest_id'], expected_guest_allocation['guest_id'])

        if sanitize_test_data(test_data['expected_guest_allocation_history']):
            expected_guest_allocation_history = json.loads(test_data['expected_guest_allocation_history'])
            expected_guest_allocation_history = sorted(expected_guest_allocation_history,
                                                       key=lambda i: i['guest_allocation_id'])
            actual_guest_allocation_history = sorted(response_data['guest_allocation_history'],
                                                     key=lambda i: i['guest_allocation_id'])
            for expected_value, actual_value in zip(expected_guest_allocation_history, actual_guest_allocation_history):
                assert_(expected_value['guest_allocation_id'], actual_value['guest_allocation_id'])
                assert_(expected_value['guest_id'], actual_value['guest_id'])

        if sanitize_test_data(test_data['expected_guest_id_for_edit_stay']):
            expected_guest_id_and_guest_data = test_data['expected_guest_id_for_edit_stay'].split('#')
            expected_customer_data = customer_request.get_customer_request(self.client, 200, self.booking_id,
                                                                           expected_guest_id_and_guest_data[0])

            actual_customer_data = get_test_case_data(sheet_names.guest_stays_sheet_name,
                                                      expected_guest_id_and_guest_data[1])[0]
            if sanitize_test_data(actual_customer_data['age']):
                assert_(sanitize_test_data(actual_customer_data['age']), str(expected_customer_data['data']['age']))
            if sanitize_test_data(actual_customer_data['email']):
                assert_(sanitize_test_data(actual_customer_data['email']), expected_customer_data['data']['email'])
            if sanitize_test_data(actual_customer_data['first_name']):
                assert_(sanitize_test_data(actual_customer_data['first_name']),
                        expected_customer_data['data']['first_name'])
            if sanitize_test_data(actual_customer_data['gender']):
                assert_(sanitize_test_data(actual_customer_data['gender']), expected_customer_data['data']['gender'])
            if sanitize_test_data(actual_customer_data['image_url']):
                assert_(sanitize_test_data(actual_customer_data['image_url']),
                        expected_customer_data['data']['image_url'])
            if sanitize_test_data(actual_customer_data['last_name']):
                assert_(sanitize_test_data(actual_customer_data['last_name']),
                        expected_customer_data['data']['last_name'])
            if sanitize_test_data(actual_customer_data['nationality']):
                assert_(sanitize_test_data(actual_customer_data['nationality']),
                        expected_customer_data['data']['nationality'])
            if sanitize_test_data(actual_customer_data['profile_type']):
                assert_(sanitize_test_data(actual_customer_data['profile_type']),
                        expected_customer_data['data']['profile_type'])
            if sanitize_test_data(actual_customer_data['reference_id']):
                assert_(sanitize_test_data(actual_customer_data['reference_id']),
                        expected_customer_data['data']['reference_id'])
