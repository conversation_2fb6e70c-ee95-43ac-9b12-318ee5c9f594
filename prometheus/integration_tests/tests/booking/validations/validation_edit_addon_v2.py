from flask import json

from prometheus.integration_tests.config import sheet_names
from prometheus.integration_tests.utilities.common_utils import assert_, sanitize_blank, return_date
from prometheus.integration_tests.utilities.excel_utils import get_test_case_data
from prometheus.integration_tests.config.common_config import *


class EditAddOnValidations(object):
    def __init__(self, test_case_id, hotel_id):
        self.test_data = get_test_case_data(sheet_names.add_ons_sheet_name, test_case_id)[0]
        self.hotel_id = hotel_id

    def validate_response(self, response, client, booking_request, booking_id, billing_request, bill_id):
        expected_test_data = self.test_data
        create_add_on_data = \
            get_test_case_data(sheet_names.add_ons_sheet_name, expected_test_data['Booking_AddOnTcId'])[0]
        edit_add_on_response = response['data']

        if sanitize_blank(expected_test_data['added_by']) is None and sanitize_blank(
                create_add_on_data['added_by']) is None:
            assert_(edit_add_on_response['added_by'], 'treebo')
        elif sanitize_blank(expected_test_data['added_by']):
            assert_(edit_add_on_response['added_by'], expected_test_data['added_by'])
        else:
            assert_(edit_add_on_response['added_by'], create_add_on_data['added_by'])
        assert edit_add_on_response['addon_id'] is not None

        if expected_test_data['linked_addon']:  # In case of linked add-on below fields will be None
            assert_(edit_add_on_response['bill_to_type'], None)
            assert_(edit_add_on_response['charge_type'], None)
        else:  # Validating the data against create add-on for cases where patch data is not provided
            if sanitize_blank(expected_test_data['bill_to_type']) is None:
                assert_(edit_add_on_response['bill_to_type'], create_add_on_data['bill_to_type'])
            else:
                assert_(edit_add_on_response['bill_to_type'], expected_test_data['bill_to_type'])

            if sanitize_blank(expected_test_data['charge_type']) is None:
                assert_(edit_add_on_response['charge_type'], create_add_on_data['charge_type'])
            else:
                assert_(edit_add_on_response['charge_type'], expected_test_data['charge_type'])

        assert_(edit_add_on_response['end_date'], str(return_date(expected_test_data['expected_end_date'])))
        if sanitize_blank(expected_test_data['end_date']):
            assert_(sanitize_blank(edit_add_on_response['end_relative']), None)
        elif sanitize_blank(expected_test_data['end_relative']) is None:
            assert_(edit_add_on_response['end_relative'], sanitize_blank(create_add_on_data['end_relative']))
        else:
            assert_(edit_add_on_response['end_relative'], sanitize_blank(expected_test_data['end_relative']))

        assert_(edit_add_on_response['start_date'], str(return_date(expected_test_data['expected_start_date'])))
        if sanitize_blank(expected_test_data['start_date']):
            assert_(sanitize_blank(edit_add_on_response['start_relative']), None)
        elif sanitize_blank(expected_test_data['start_relative']) is None:
            assert_(edit_add_on_response['start_relative'], sanitize_blank(create_add_on_data['start_relative']))
        else:
            assert_(edit_add_on_response['start_relative'], sanitize_blank(expected_test_data['start_relative']))

        assert_(edit_add_on_response['expense_item_id'], create_add_on_data['expense_item_id'])
        assert_(edit_add_on_response['name'], create_add_on_data['name'])

        if sanitize_blank(expected_test_data['posttax_price']) is None and sanitize_blank(
                create_add_on_data['posttax_price']) is None:
            assert_(edit_add_on_response['posttax_price'], "")
        else:
            if self.hotel_id != HOTEL_ID[0]:
                assert_(edit_add_on_response['posttax_price'].split(' ')[0], HOTEL_CURRENCY_MAP[self.hotel_id])
                assert_(edit_add_on_response['posttax_price'].split(' ')[1],
                        expected_test_data['expected_posttax_price'])
            else:
                assert_(edit_add_on_response['posttax_price'], expected_test_data['expected_posttax_price'])

        if sanitize_blank(expected_test_data['pretax_price']) is None and sanitize_blank(
                create_add_on_data['pretax_price']) is None:
            assert_(edit_add_on_response['pretax_price'], "")
        else:
            if self.hotel_id != HOTEL_ID[0]:
                assert_(edit_add_on_response['pretax_price'].split(' ')[0], HOTEL_CURRENCY_MAP[self.hotel_id])
                assert_(edit_add_on_response['pretax_price'].split(' ')[1], expected_test_data['expected_pretax_price'])
            else:
                assert_(edit_add_on_response['pretax_price'], expected_test_data['expected_pretax_price'])

        if sanitize_blank(expected_test_data['quantity']) is None:
            assert_(edit_add_on_response['quantity'], int(create_add_on_data['quantity']))
        else:
            assert_(edit_add_on_response['quantity'], int(expected_test_data['quantity']))
        assert_(edit_add_on_response['room_stay_id'], int(create_add_on_data['room_stay_id']))

        if expected_test_data['linked_addon']:
            assert_(edit_add_on_response['expense_ids'], [])
            expected_charge_data = json.loads(expected_test_data['expected_charge_data'])
            for charge in expected_charge_data:
                self.validate_charge(client, billing_request, bill_id, expected_test_data, charge)
        else:
            expected_expense_data = json.loads(expected_test_data['expected_expense_data'])
            assert_(edit_add_on_response['expense_ids'], [str(id['expense_id']) for id in expected_expense_data])
            for expense_data in expected_expense_data:
                self.validate_expenses(expense_data['expense_id'], create_add_on_data, client, booking_request,
                                       booking_id, expense_data['charge_id'])
                self.validate_charge(client, billing_request, bill_id, expected_test_data, expense_data)
        assert_(edit_add_on_response['version'], 2)

    def validate_expenses(self, expense_id, expected_data, client, booking_request, booking_id, charge_id):
        expense_response = booking_request.get_expense(client, booking_id, expense_id, 200)['data']
        assert_(expense_response['charge_id'], int(charge_id))
        assert_(expense_response['expense_item_id'], expected_data['expense_item_id'])
        assert_(expense_response['room_stay_id'], int(expected_data['room_stay_id']))
        assert_(expense_response['status'], 'created')
        assert_(expense_response['via_addon'], True)
        assert_(expense_response['comments'], expected_data['name'])

    def validate_charge(self, client, billing_request, bill_id, expected_data_excel, expected_charge_data):
        charge_response = billing_request.get_charge_request(client, bill_id, 200, expected_charge_data['charge_id'])[
            'data']
        assert_(charge_response['status'], expected_charge_data['status'])
        if charge_response['status'] == 'consumed':
            assert_(charge_response['charge_split_type'], 'equal_split')
        if sanitize_blank(expected_charge_data['assign_to']) is not None:
            assert_([x['charge_to'] for x in charge_response['charge_splits']].sort(),
                    expected_charge_data['assign_to'].sort())
        else:
            assert_(charge_response['charge_splits'][0]['bill_to_type'], expected_data_excel['bill_to_type'])
        # Charge component
        if expected_data_excel['linked_addon']:
            if self.hotel_id !=HOTEL_ID[0]:
                charges = []
                for charge in charge_response['charge_components']:
                    assert_(charge['posttax_amount'].split(' ')[0],HOTEL_CURRENCY_MAP[self.hotel_id])
                    charge['posttax_amount'] = charge['posttax_amount'].split(' ')[1]
                    charges.append(charge)
                assert_(charges,expected_charge_data['charge_components'])
            else:
                assert_(charge_response['charge_components'], expected_charge_data['charge_components'])
        else:
            assert_(charge_response['charge_components'], None)
