import json

from prometheus.integration_tests.config import sheet_names
from prometheus.integration_tests.config.common_config import *
from prometheus.integration_tests.utilities.common_utils import assert_
from prometheus.integration_tests.utilities.excel_utils import get_test_case_data


class ValidationCharges:

    def __init__(self, client_, test_case_id, response):
        self.test_data = get_test_case_data(sheet_names.edit_charge_price_sheet_name, test_case_id)[0]
        self.response = response
        self.client = client_

    def validate_response(self, client_, billing_request, status_code, bill_id, user_type):
        get_bill_charges_response = billing_request.get_bill_request(client_, bill_id, status_code, user_type)['data']
        assert_(float(self.test_data['expected_total_posttax_amount']),
                float(get_bill_charges_response['total_posttax_amount']))
        assert_(float(self.test_data['expected_total_pretax_amount']),
                float(get_bill_charges_response['total_pretax_amount']))
        assert_(float(self.test_data['expected_total_tax_amount']),
                float(get_bill_charges_response['total_tax_amount']))
        assert_(float(self.test_data['expected_net_payable']), float(get_bill_charges_response['net_payable']))
        assert_(float(self.test_data['expected_net_paid_amount']), float(get_bill_charges_response['net_paid_amount']))
        assert_(float(self.test_data['expected_paid_amount']), float(get_bill_charges_response['paid_amount']))
