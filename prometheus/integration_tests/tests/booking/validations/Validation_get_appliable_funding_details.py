import json
from prometheus.integration_tests.utilities.common_utils import assert_, convert_to_decimal
from prometheus.integration_tests.utilities.excel_utils import get_test_case_data
from prometheus.integration_tests.config import sheet_names


class ValidationGetApplicableFundingDetails:

    def __init__(self, test_case_id, response):
        self.test_case_data = get_test_case_data(sheet_names.funding_summary_sheet_name, test_case_id)[0]
        self.response = response["data"]

    def validate_response(self):
        assert_(
            convert_to_decimal(str(self.response["applicable_funding_amount"])),
            convert_to_decimal(str(self.test_case_data["expected_applicable_funding_amount"])),
            "Applicable funding amount mismatch"
        )

        assert_(
            convert_to_decimal(str(self.response["actual_funded_amount"])),
            convert_to_decimal(str(self.test_case_data["expected_actual_funded_amount"])),
            "Actual funded amount mismatch"
        )

        expected_funding_amount_breakup = json.loads(self.test_case_data["expected_funding_amount_breakup"])
        for funding_type, actual_amount in self.response["funding_amount_breakup"].items():
            assert_(
                convert_to_decimal(str(actual_amount)),
                convert_to_decimal(str(expected_funding_amount_breakup[funding_type])),
                f"Mismatch in funding_amount_breakup for '{funding_type}'"
            )
