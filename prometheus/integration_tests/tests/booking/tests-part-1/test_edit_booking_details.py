import pytest

from prometheus.integration_tests.tests.base_test import BaseTest
from prometheus.integration_tests.tests.before_test_actions import *
from prometheus.integration_tests.tests.booking.validations.validation_edit_booking import ValidationEditBooking
from prometheus.integration_tests.config.common_config import *


class TestEditBookingDetails(BaseTest):

    @pytest.mark.parametrize(
        "test_case_id, existing_test_case_id,tc_description, previous_actions, extra_data, status_code, user_type, "
        "error_code, error_message, dev_message, error_payload, skip_case, skip_message", [
            ("EditBooking_01", None, "Send only one new field in payload",
             SINGLE_BOOKING_01, {"include_kerala_cess": False}, 200, None, "", "", "", "", False, ""),
            ("EditBooking_03", None, "Edit some of the Existing nonmandatory fields to null",
             SINGLE_BOOKING_01, {"include_kerala_cess": False}, 200, None, "", "", "", "", False, ""),
            ("EditBooking_04", None, 'Remove the "source" payload verify',
             SINGLE_BOOKING_01, {"include_kerala_cess": False}, 200, None, "", "", "", "", False, ""),
            ("EditBooking_05", None, 'Remove mandatory fields from "source" payload and verify',
             SINGLE_BOOKING_01, {"include_kerala_cess": False}, 200, None, "", "", "", "", False, ""),
            ("EditBooking_06", None, 'BLANK_PAYLOAD', SINGLE_BOOKING_01, {"include_kerala_cess": False}, 200,
             None, "", "", "", "", False, ""),
            ("EditBooking_07", None, 'INVALID_RESOURCE_VERSION', SINGLE_BOOKING_01, {"include_kerala_cess": False},
             409, None, "", "", "", "", True, "Working on Invalid_Resource_Version"),
            ("EditBooking_08", None, 'INVALID_RESOURCE_NULL', SINGLE_BOOKING_01, {"include_kerala_cess": False},
             400, None, "", "", "", "", False, ""),
            ("EditBooking_09", None, 'WRONG_BOOKING_ID', SINGLE_BOOKING_01, {"include_kerala_cess": False},
             404, None, "", "", "", "", False, ""),
            ("EditBooking_11", None, 'Send only one new field in payload', SINGLE_BOOKING_01,
             {"include_kerala_cess": False}, 200, None, "", "", "", "", False, ""),
        ])

    @pytest.mark.regression
    def test_edit_booking_details(self, client_, test_case_id, existing_test_case_id, tc_description, previous_actions,
                                  extra_data, status_code, user_type,
                                  error_code, error_message, dev_message, error_payload, skip_case, skip_message):
        if skip_case:
            pytest.skip(skip_message)
        if existing_test_case_id:
            test_case_id = existing_test_case_id

        hotel_id = HOTEL_ID[1] if "MultiCurrency" in tc_description else HOTEL_ID[0]

        if previous_actions:
            self.common_request_caller(client_, previous_actions, hotel_id)

        if "EditBooking_09" in test_case_id:
            response = self.booking_request.edit_booking_details(client_, test_case_id,
                                                                 status_code, "1000-1000-0000",
                                                                 user_type,
                                                                 include_kerala_cess=extra_data['include_kerala_cess'],
                                                                 hotel_id=hotel_id)
        else:
            response = self.booking_request.edit_booking_details(client_, test_case_id,
                                                              status_code, self.booking_request.booking_id,
                                                              user_type,
                                                              include_kerala_cess=extra_data['include_kerala_cess'],
                                                              hotel_id=hotel_id)
        if status_code in ERROR_CODES:
            self.response_validation_negative_cases(response, error_code, error_message, dev_message, error_payload)
        elif status_code in SUCCESS_CODES:
            self.validation(response, test_case_id, client_, extra_data, hotel_id)
        else:
            assert False, "Response status code is not matching"

    def validation(self, response, test_case_id, client_, extra_data, hotel_id):
        validation = ValidationEditBooking(client_, test_case_id, response, self.booking_request.bill_id, extra_data,
                                            hotel_id)
        validation.validate_response(self.billing_request)
