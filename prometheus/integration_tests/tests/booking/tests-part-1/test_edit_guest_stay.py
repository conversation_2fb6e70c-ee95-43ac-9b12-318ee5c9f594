from datetime import date, timedelta

import pytest

from prometheus.integration_tests.config.common_config import *
from prometheus.integration_tests.resources import db_queries
from prometheus.integration_tests.tests.base_test import BaseTest
from prometheus.integration_tests.tests.booking.validations.validation_edit_guest_stay import ValidationEditGuestStay
from prometheus.integration_tests.utilities.common_utils import query_execute


class TestEditGuestStay(BaseTest):

    @pytest.mark.parametrize(
        "test_case_id, tc_description, previous_actions, status_code, user_type, error_code, "
        "dev_message, error_payload, skip_message, extras", [
            ("patch_guest_stay_01", 'Add booking owner guest to the guest stay',
             [{'id': "booking_01", 'type': 'booking_v2'}], 200, 'super-admin', "", "", "", "", None),
            ("patch_guest_stay_02", 'Add new guest to the guest stay which is in Reserved State',
             [{'id': "booking_01", 'type': 'booking_v2'}], 200, 'super-admin', "", "", "", "", None),
            ("patch_guest_stay_03", 'Add already allocated Reserved Guest Id to the Guest Stay',
             [{'id': "booking_01", 'type': 'booking_v2'}], 400, 'super-admin', "04010183", "", "", "", None),
            ("patch_guest_stay_04", 'Add a New Guest to a Checked In Guest Stay',
             [{'id': "booking_01", 'type': 'booking_v2'}, {'id': "checkin_01", 'type': 'checkin_v2'}],
             400, 'super-admin', "04015011", "", "", "", None),
            ("patch_guest_stay_10", 'Add already existing Guest to a Checked in Guest Stay',
             [{'id': "booking_01", 'type': 'booking_v2'}, {'id': "checkin_01", 'type': 'checkin_v2'}],
             400, 'super-admin', "04015011", "", "", "", None),
            ("patch_guest_stay_05", 'Add a Guest to a cancelled Room Stay',
             [{'id': "booking_08", 'type': 'booking_v2'}, {'id': "cancel_room_03", 'type': 'mark_cancel'}],
             400, 'super-admin', "04015011", "", "", "", None),
            ("patch_guest_stay_06", 'Add New Guest to a Guest Stay having 2 adults',
             [{'id': "booking_19_01_checkin_04", 'type': 'booking_v2'}],
             200, 'super-admin', "", "", "", "", None),
            ("patch_guest_stay_07", 'Add released guest to the guest stay',
             [{'id': "booking_01", 'type': 'booking_v2'},
              {'id': 'patch_guest_stay_01', 'type': 'update_guest_stay_not_in_bulk'}],
             200, 'super-admin', "", "", "", "", None),
            ("patch_guest_stay_08", 'Add checked in Guest to the Guest Stay',
             [{'id': "booking_135_checkin_06", 'type': 'booking_v2'},
              {'id': 'checkin_06', 'type': 'checkin_v2'}], 400, 'super-admin', "04010183", "", "", "", None),
            ("patch_guest_stay_09", 'Add a Guest to the Reserved Room in a Part Checked In Room',
             [{'id': "booking_135_checkin_06", 'type': 'booking_v2'},
              {'id': 'checkin_06', 'type': 'checkin_v2'}], 200, 'super-admin', "", "", "", "", None),
            ("patch_guest_stay_11", 'Add a guest to No Show Room Stay',
             [{'id': "booking_03_no_show", 'type': 'booking_v2'},
              {'id': 'MarkNoShow_25', 'type': 'mark_no_show'}], 400, 'super-admin', "04015011", "", "", "",
             {'perform_night_audit': True}),
            ("patch_guest_stay_12", 'Add Guest to Booking id which wont exist',
             [{'id': "booking_01", 'type': 'booking_v2'}], 404, 'super-admin', "04010007", "", "", "", None),
            ("patch_guest_stay_13", 'Pass booking_id as null in the Request uri ',
             [{'id': "booking_01", 'type': 'booking_v2'}], 404, 'super-admin', "04010007", "", "", "", None),
            ("patch_guest_stay_14", 'Add Guest to a Room Stay which wont exist',
             [{'id': "booking_01", 'type': 'booking_v2'}], 404, 'super-admin', "04010004", "", "", "", None),
            ("patch_guest_stay_15", 'Pass room_stay_id as null',
             [{'id': "booking_01", 'type': 'booking_v2'}], 404, 'super-admin', "", "", "", "", None),
            ("patch_guest_stay_16", 'Add Guest to a Guest Stay which wont exist',
             [{'id': "booking_01", 'type': 'booking_v2'}], 404, 'super-admin', "04010004", "", "", "", None),
            ("patch_guest_stay_17", 'Pass guest_stay_id as null',
             [{'id': "booking_01", 'type': 'booking_v2'}], 404, 'super-admin', "", "", "", "", None),

        ])
    @pytest.mark.regression
    def test_edit_guest_stay(self, client_, test_case_id, previous_actions, tc_description, status_code, user_type,
                             error_code, dev_message, error_payload, skip_message, extras):
        if skip_message:
            pytest.skip(skip_message)

        hotel_id = HOTEL_ID[1] if isinstance(extras, dict) and extras.get('multicurrency') is True else HOTEL_ID[0]
        perform_night_audit = True if isinstance(extras, dict) and extras.get('perform_night_audit') else False
        if perform_night_audit:
            query_execute(db_queries.UPDATE_BUSINESS_DATE.format(date.today() - timedelta(days=1), hotel_id))

        if previous_actions:
            self.common_request_caller(client_, previous_actions, hotel_id)
        response = self.booking_request.edit_guest_stay(client_, self.booking_request.booking_id, test_case_id,
                                                        status_code, user_type=None, extras=extras)
        query_execute(db_queries.UPDATE_BUSINESS_DATE.format(date.today(), hotel_id))
        if status_code in ERROR_CODES:
            self.response_validation_negative_cases_other_approach(response, error_code, dev_message, error_payload,
                                                                   test_case_id)
        elif status_code in SUCCESS_CODES:
            self.validation(response, test_case_id, client_, self.customer_request, self.booking_request.booking_id,
                            self.booking_request)
        else:
            assert False, "Response status code is not matching"

    @staticmethod
    def validation(response, test_case_id, client_, customer_request, booking_id, booking_request):
        validation = ValidationEditGuestStay(client_, test_case_id, response, booking_id, booking_request)
        validation.validate_response(customer_request)
