from prometheus.integration_tests.tests.base_test import BaseTest
from prometheus.integration_tests.requests.card_requests import CardRequests
from prometheus.integration_tests.config import sheet_names
from prometheus.integration_tests.utilities.common_utils import assert_, increment_date, get_room_type_id, \
    sanitize_blank, return_date, sanitize_test_data, exp_date
from prometheus.integration_tests.utilities.excel_utils import get_test_case_data
import json


class ValidationSaveCard:

    def __init__(self, client_, response,test_case_id, bill_id, card_request, card_id,status_code, extra_data=None, hotel_id=None):
        self.test_data = get_test_case_data(sheet_names.card_sheet_name, test_case_id)[0]
        self.response = response
        self.client = client_
        self.bill_id = bill_id
        self.extra_data = extra_data
        self.hotel_id = hotel_id
        self.get_card_response = card_request.get_card(client_, test_case_id, status_code,bill_id,card_id,user_type=None)
        self.card_additions= 0
        if test_case_id == "SaveCard_16":
            self.card_additions = 3
            for card_added in range(self.card_additions):
                self.save_card_response = card_request.save_card(client_, test_case_id, 201, bill_id)
                self.get_cards_response = card_request.get_cards(client_, test_case_id, 200, bill_id)
                self.validate_response_with_get_cards_response(card_added)
        else:
            self.get_cards_response = card_request.get_cards(client_, test_case_id, 200, bill_id)

    def validate_response(self):
        if sanitize_test_data(self.response['data']['billed_entity_id']):
            assert_(self.response['data']['billed_entity_id'], int(self.test_data['billed_entity_id']))
        if sanitize_test_data(self.response['data']['bin']):
            assert_(self.response['data']['bin'], str(int(self.test_data['bin'])))
        if sanitize_test_data(self.response['data']['brand']):
            assert_(self.response['data']['brand'], self.test_data['brand'])
        if sanitize_test_data(self.response['data']['card_type']):
            assert_(self.response['data']['card_type'], self.test_data['card_type'])
        if sanitize_test_data(self.response['data']['expiry']):
            assert_(self.response['data']['expiry'], exp_date(self.test_data['expiry_date']))
        if sanitize_test_data(self.response['data']['holder_name']):
            assert_(self.response['data']['holder_name'], self.test_data['holder_name'])
        if sanitize_test_data(self.response['data']['last_digits']):
            assert_(self.response['data']['last_digits'], str(int(self.test_data['last_digits'])))
        if sanitize_test_data(self.response['data']['pre_auth_amount']):
            assert_(self.response['data']['pre_auth_amount'], self.test_data['pre_auth_amount'])
        if sanitize_test_data(self.response['data']['pre_auth_code']):
            assert_(self.response['data']['pre_auth_code'], self.test_data['pre_auth_code'])
        if sanitize_test_data(self.response['data']['token']):
            assert_(self.response['data']['token'], self.test_data['token'])

    def validate_response_with_get_card_response(self):
        if sanitize_test_data(self.response['data']['billed_entity_id']):
            assert_(self.response['data']['billed_entity_id'], self.get_card_response['data']['billed_entity_id'])
        if sanitize_test_data(self.response['data']['bin']):
            assert_(self.response['data']['bin'], self.get_card_response['data']['bin'])
        if sanitize_test_data(self.response['data']['brand']):
            assert_(self.response['data']['brand'], self.get_card_response['data']['brand'])
        if sanitize_test_data(self.response['data']['card_type']):
            assert_(self.response['data']['card_type'], self.get_card_response['data']['card_type'])
        if sanitize_test_data(self.response['data']['expiry']):
            assert_(self.response['data']['expiry'], self.get_card_response['data']['expiry'])
        if sanitize_test_data(self.response['data']['holder_name']):
            assert_(self.response['data']['holder_name'], self.get_card_response['data']['holder_name'])
        if sanitize_test_data(self.response['data']['last_digits']):
            assert_(self.response['data']['last_digits'], self.get_card_response['data']['last_digits'])
        if sanitize_test_data(self.response['data']['pre_auth_amount']):
            assert_(self.response['data']['pre_auth_amount'], self.get_card_response['data']['pre_auth_amount'])
        if sanitize_test_data(self.response['data']['pre_auth_code']):
            assert_(self.response['data']['pre_auth_code'], self.get_card_response['data']['pre_auth_code'])
        if sanitize_test_data(self.response['data']['token']):
            assert_(self.response['data']['token'], self.get_card_response['data']['token'])

    def validate_response_with_get_cards_response(self,card_added):
        if sanitize_test_data(self.response['data']['billed_entity_id']):
            assert_(self.response['data']['billed_entity_id'], self.get_cards_response['data'][card_added]['billed_entity_id'])
        if sanitize_test_data(self.response['data']['bin']):
            assert_(self.response['data']['bin'], self.get_cards_response['data'][card_added]['bin'])
        if sanitize_test_data(self.response['data']['brand']):
            assert_(self.response['data']['brand'], self.get_cards_response['data'][card_added]['brand'])
        if sanitize_test_data(self.response['data']['card_type']):
            assert_(self.response['data']['card_type'], self.get_cards_response['data'][card_added]['card_type'])
        if sanitize_test_data(self.response['data']['expiry']):
            assert_(self.response['data']['expiry'], self.get_cards_response['data'][card_added]['expiry'])
        if sanitize_test_data(self.response['data']['holder_name']):
            assert_(self.response['data']['holder_name'], self.get_cards_response['data'][card_added]['holder_name'])
        if sanitize_test_data(self.response['data']['last_digits']):
            assert_(self.response['data']['last_digits'], self.get_cards_response['data'][card_added]['last_digits'])
        if sanitize_test_data(self.response['data']['pre_auth_amount']):
            assert_(self.response['data']['pre_auth_amount'], self.get_cards_response['data'][card_added]['pre_auth_amount'])
        if sanitize_test_data(self.response['data']['pre_auth_code']):
            assert_(self.response['data']['pre_auth_code'], self.get_cards_response['data'][card_added]['pre_auth_code'])
        if sanitize_test_data(self.response['data']['token']):
            assert_(self.response['data']['token'], self.get_cards_response['data'][card_added]['token'])
