from prometheus.integration_tests.config import sheet_names
from prometheus.integration_tests.utilities.common_utils import assert_
from prometheus.integration_tests.utilities.excel_utils import get_test_case_data


class ValidationPatchWebCheckIn:

    def __init__(self, client_, response, test_case_id):
        self.expected_data = get_test_case_data(sheet_names.web_checkin_sheet_name, test_case_id)[0]
        self.response = response
        self.client = client_

    def validate_response(self, booking_request):
        assert_(self.response['data']['status'], self.expected_data['status'])
        booking_response = booking_request.get_booking_request(client=self.client,
                                                               booking_id=booking_request.booking_id, status_code=200)
        assert_(booking_response['data']['status'], self.expected_data['expected_booking_status'])