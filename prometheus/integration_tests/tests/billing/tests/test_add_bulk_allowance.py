import pytest

from prometheus.integration_tests.config.common_config import *
from prometheus.integration_tests.tests.base_test import BaseTest
from prometheus.integration_tests.tests.before_test_actions import *
from prometheus.integration_tests.tests.billing.validations.validation_add_bulk_allowance import \
    ValidationAddBulkAllowance


class TestAddBulkAllowance(BaseTest):

    @pytest.mark.parametrize(
        "test_case_id, tc_description, previous_actions, status_code, user_type, "
        "error_code, error_message, dev_message, error_payload, skip_case, skip_message", [
            ("AddBulkAllowance_01", 'Add allowance to non-credit booked charge', SINGLE_WALK_BOOKING_V2_01, 400, None,
             "04010398", "Charge should be in consumed state for adding allowance", "", "", False, ""),
            ("AddBulkAllowance_02", 'Add allowance to non-credit cancelled charge', BOOKING_WITH_CANCELLED_CHARGE, 400,
             None, "04010398", "Charge should be in consumed state for adding allowance", "", "", False, ""),
            ("AddBulkAllowance_03", 'Add allowance on non-credit charge after checkout', FULL_CHECKOUT_01_V2, 400, None,
             "", "", "", "", True, "Nees to check"),
            ("AddBulkAllowance_04", 'Add allowance to booked and posted charge',
             [{'id': 'booking_01', 'type': 'booking_v2'}, {'id': "checkin_01", 'type': 'checkin_v2'},
              {'id': 'Create_Expense_01', 'type': 'expense'},
              {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 6}], 400,
             None, "04010398", "Charge should be in consumed state for adding allowance", "", "", False, ""),
            ("AddBulkAllowance_05", 'Add allowance to cancelled and posted charge',
             [{'id': 'booking_01', 'type': 'booking_v2'}, {'id': "checkin_01", 'type': 'checkin_v2'},
              {'id': 'Create_Expense_01', 'type': 'expense'},
              {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 3},
              {'id': "CancelCharge_01", 'type': 'update_expense', 'charge_id': '6'}], 400, None, "04010398",
             "Charge should be in consumed state for adding allowance", "", "", False, ""),
            ("AddBulkAllowance_06", 'Add allowance to booked, cancelled and posted charge',
             BOOKING_WITH_CANCEL_POSTED_BOOKED_CHARGE, 400, None, "04010398",
             "Charge should be in consumed state for adding allowance", "", "", False, ""),
            ("AddBulkAllowance_07", 'Add allowance to future booking', FUTURE_WALK_BOOKING_V2_01, 400, None, "04010398",
             "Charge should be in consumed state for adding allowance", "", "", False, ""),
            ("AddBulkAllowance_08", 'Add allowance to credit booked charge',
             [{'id': 'booking_171', 'type': 'booking_v2'}], 400, None, "04010398",
             "Charge should be in consumed state for adding allowance", "", "", False, ""),
            ("AddBulkAllowance_09", 'Add allowance to credit cancelled charge',
             [{'id': 'booking_171', 'type': 'booking_v2'}, {'id': 'Create_Expense_08', 'type': 'expense'},
              {'id': "CancelCharge_01", 'type': 'update_expense', 'charge_id': '6'}], 400, None, "04010398",
             "Charge should be in consumed state for adding allowance", "", "", False, ""),
            ("AddBulkAllowance_10", 'Add allowance on credit charge after checkout',
             [{'id': 'booking_171', 'type': 'booking_v2'}, {'id': "checkin_01", 'type': 'checkin_v2'},
              {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': 1},
              {'id': "invoicePreviewCheckoutV2_01", 'type': 'preview_invoice'},
              {'id': "CheckoutV2_01", 'type': 'checkout_v2'}], 400, None, "04010398",
             "Charge should be in consumed state for adding allowance", "", "", True, "Need to check"),
            ("AddBulkAllowance_11", 'Provide charge_id as NULL', POST_RATE_PLAN_CHARGE, 400, None, "04010006",
             "[Charge Id] -> Field may not be null.", "", {"field": "0.charge_id"}, False, ""),
            ("AddBulkAllowance_12", 'Provide charge_id as empty', POST_RATE_PLAN_CHARGE, 400, None, "04010006",
             "[Charge Id] -> Not a valid integer.", "", {"field": "0.charge_id"}, False, ""),
            ("AddBulkAllowance_13", 'Delete charge_id key', POST_RATE_PLAN_CHARGE, 400, None, "04010006",
             "[Charge Id] -> Missing data for required field.", "", {"field": "0.charge_id"}, False, ""),
            ("AddBulkAllowance_14", 'Provide charge_id as Invalid', POST_RATE_PLAN_CHARGE, 404, None, "04010004",
             "Charge not found. Please contact escalations.", "", "", False, ""),
            ("AddBulkAllowance_15", 'Provide charge_split_id as NULL', POST_RATE_PLAN_CHARGE, 400, None, "04010006",
             "[Charge Split Id] -> Field may not be null.", "", {"field": "0.charge_split_id"}, False, ""),
            ("AddBulkAllowance_16", 'Provide charge_split_id as empty', POST_RATE_PLAN_CHARGE, 400, None, "04010006",
             "[Charge Split Id] -> Not a valid integer.", "", {"field": "0.charge_split_id"}, False, ""),
            ("AddBulkAllowance_17", 'Delete charge_split_id key', POST_RATE_PLAN_CHARGE, 400, None, "04010006",
             "[Charge Split Id] -> Missing data for required field.", "", {"field": "0.charge_split_id"}, False, ""),
            ("AddBulkAllowance_18", 'Provide charge_split_id as Invalid', POST_RATE_PLAN_CHARGE, 404, None, "04010004",
             "ChargeSplit not found. Please contact escalations.", "Charge_split_id: 15 is not present in charge: 1",
             "", False, ""),
            ("AddBulkAllowance_19", 'Provide both pretax and posttax amount', POST_RATE_PLAN_CHARGE, 400, None,
             "04010006", "[ Schema] -> Please provide either pretax_amount or posttax_amount", "",
             {"field": "0._schema"}, False, ""),
            ("AddBulkAllowance_20", 'Does not provide both of them pretax or posttax amount', POST_RATE_PLAN_CHARGE,
             400, None, "04010006", "[ Schema] -> Please provide either pretax_amount or posttax_amount", "",
             {"field": "0._schema"}, False, ""),
            ("AddBulkAllowance_21", 'Provide negative amount', POST_RATE_PLAN_CHARGE, 400, None, "04010006", "", "",
             "", True, "Need to fix"),
            ("AddBulkAllowance_22", 'Provide invalid amount data type', POST_RATE_PLAN_CHARGE, 400, None, "04010006",
             "", "", "", False, ""),
            ("AddBulkAllowance_23", 'Provide charge_id and charge_split_id invalid', POST_RATE_PLAN_CHARGE, 404, None,
             "04010004", "Charge not found. Please contact escalations.", "", "", False, ""),
            ("AddBulkAllowance_24", 'Provide some valid charge_id, some invalid charge_id', POST_RATE_PLAN_CHARGE, 404,
             None, "04010004", "Charge not found. Please contact escalations.", "", "", False, ""),
            ("AddBulkAllowance_25", 'Provide some valid charge_split_id, some invalid charge_split_id',
             POST_RATE_PLAN_CHARGE, 404, None, "04010004", "ChargeSplit not found. Please contact escalations.",
             "Charge_split_id: 3 is not present in charge: 2", "", False, ""),
            ("AddBulkAllowance_26",
             'Provide some valid charge_id and charge_split_id, some invalid charge_id and charge_split_id',
             POST_RATE_PLAN_CHARGE, 404, None, "04010004", "Charge not found. Please contact escalations.", "", "",
             False, ""),
            ("AddBulkAllowance_27", 'Provide somewhere pretax amount and somewhere posttax amount',
             POST_RATE_PLAN_CHARGE, 400, None, "", "", "", "", True, "Need to check"),
            ("AddBulkAllowance_28", 'Add allowance to non-credit posted charge', POST_RATE_PLAN_CHARGE, 200, None, "",
             "", "", "", False, ""),
            ("AddBulkAllowance_29", 'Add allowance to credit posted charge',
             [{'id': 'booking_171', 'type': 'booking_v2'}, {'id': "checkin_01", 'type': 'checkin_v2'},
              {'id': "PostCharge_06", 'type': 'update_expense', 'charge_id': '1'}], 200, None, "", "", "", "",
             False, ""),
            ("AddBulkAllowance_30", 'Create allowance for one posted charge',
             [{'id': 'booking_01', 'type': 'booking_v2'}, {'id': "checkin_01", 'type': 'checkin_v2'},
              {'id': 'Create_Expense_01', 'type': 'expense'},
              {'id': "PostCharge_06", 'type': 'update_expense', 'charge_id': '6'}], 200, None, "", "", "", "",
             False, ""),
            ("AddBulkAllowance_31", 'Create allowance for multiple consumed charge',
             [{'id': 'booking_01', 'type': 'booking_v2'}, {'id': "checkin_01", 'type': 'checkin_v2'},
              {'id': 'Create_Expense_01', 'type': 'expense'}, {'id': 'Create_Expense_01', 'type': 'expense'},
              {'id': 'Create_Expense_01', 'type': 'expense'},
              {'id': "PostCharge_06", 'type': 'update_expense', 'charge_id': '6'},
              {'id': "PostCharge_06", 'type': 'update_expense', 'charge_id': '7'},
              {'id': "PostCharge_06", 'type': 'update_expense', 'charge_id': '8'}], 200, None, "", "", "", "",
             False, ""),
            ("AddBulkAllowance_32", 'Create allowance on rate plan charge',
             [{'id': 'booking_01', 'type': 'booking_v2'}, {'id': "checkin_01", 'type': 'checkin_v2'},
              {'id': "PostCharge_06", 'type': 'update_expense', 'charge_id': '1'}], 200, None, "", "", "", "",
             False, ""),
            ("AddBulkAllowance_33", 'Create allowance without remarks',
             [{'id': 'booking_01', 'type': 'booking_v2'}, {'id': "checkin_01", 'type': 'checkin_v2'},
              {'id': "PostCharge_06", 'type': 'update_expense', 'charge_id': '1'}], 200, None, "", "", "", "",
             True, "Need to fix"),
            ("AddBulkAllowance_34", 'Create allowance on both credit and non-credit charge',
             [{'id': 'credit_non_credit_booking_01', 'type': 'booking_v2'},
              {'id': "checkin_24_credit_non_credit", 'type': 'checkin_v2'},
              {'id': "PostCharge_06", 'type': 'update_expense', 'charge_id': '1'},
              {'id': "PostCharge_06", 'type': 'update_expense', 'charge_id': '2'}], 200, None, "", "", "", "",
             False, ""),
            ("AddBulkAllowance_35", 'Create allowance for same charge_id, different charge_split_id',
             [{'id': 'booking_01', 'type': 'booking_v2'}, {'id': "checkin_01", 'type': 'checkin_v2'},
              {'id': 'Create_Expense_split', 'type': 'expense'},
              {'id': "PostCharge_06", 'type': 'update_expense', 'charge_id': '6'}], 200, None, "", "", "", "",
             False, ""),
            ("AddBulkAllowance_36", 'Provide allowance amount more than charge amount',
             [{'id': 'booking_01', 'type': 'booking_v2'}, {'id': "checkin_01", 'type': 'checkin_v2'},
              {'id': "PostCharge_06", 'type': 'update_expense', 'charge_id': '1'}], 400, None, "04010400",
             "Cannot add allowance amount greater than charge split amount", "", "", False, ""),
            ("AddBulkAllowance_37",
             'Provide Multiple Allowance on same charge such that amount become less than charge',
             [{'id': 'booking_01', 'type': 'booking_v2'}, {'id': "checkin_01", 'type': 'checkin_v2'},
              {'id': 'Create_Expense_01', 'type': 'expense'},
              {'id': "PostCharge_06", 'type': 'update_expense', 'charge_id': '6'}], 200, None, "", "", "", "",
             False, ""),
            ("AddBulkAllowance_38",
             'Provide Multiple Allowance on same charge such that amount become more than charge',
             [{'id': 'booking_171', 'type': 'booking_v2'}, {'id': "checkin_01", 'type': 'checkin_v2'},
              {'id': 'Create_Expense_01', 'type': 'expense'},
              {'id': "PostCharge_06", 'type': 'update_expense', 'charge_id': '6'}], 400, None, "04010400",
             "Cannot add allowance amount greater than charge split amount", "", "", False, ""),
            ("AddBulkAllowance_39",
             'Provide Multiple Allowance on same charge such that amount become equal to charge',
             [{'id': 'booking_171', 'type': 'booking_v2'}, {'id': "checkin_01", 'type': 'checkin_v2'},
              {'id': 'Create_Expense_01', 'type': 'expense'},
              {'id': "PostCharge_06", 'type': 'update_expense', 'charge_id': '6'}], 200, None, "", "", "", "",
             False, ""),
            ("AddBulkAllowance_40", 'Add allowance to room stay posted charge of OTA booking',
             POST_ROOMSTAY_NON_CREDIT_CHARGE_OTA_BOOKING, 200, None, "", "", "", "", False, ""),
            ("AddBulkAllowance_41", 'Add allowance to inclusion posted charge of OTA booking',
             POST_ROOMSTAY_NON_CREDIT_CHARGE_OTA_BOOKING, 200, None, "", "", "", "", False, ""),
            ("AddBulkAllowance_42", 'Add allowance to both room stay and inclusion posted charge of OTA booking',
             POST_ROOMSTAY_NON_CREDIT_CHARGE_OTA_BOOKING, 200, None, "", "", "", "", False, ""),
            ("AddBulkAllowance_43", 'Add allowance to both room stay and inclusion posted charge of OTA booking(Pretax)',
             POST_ROOMSTAY_NON_CREDIT_CHARGE_OTA_BOOKING, 200, None, "", "", "", "", False, ""),
            ("AddBulkAllowance_44", 'Provide Multiple Allowance on room stay charge of OTA booking',
             POST_ROOMSTAY_NON_CREDIT_CHARGE_OTA_BOOKING, 200, None, "", "", "", "", False, ""),
            ("AddBulkAllowance_45", 'Add bulk allowance after add guest',
             [{'id': "booking_212", 'type': 'booking_v2'},
              {'id': 'AddGuest_01', 'type': 'add_multiple_guest_stay', 'enable_rate_plan': False,
               'is_inclusion_added': False}, {'id': "checkin_04", 'type': 'checkin_v2'},
              {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': '1'}], 200, None, "", "", "", "", False,
             ""),
            ("AddBulkAllowance_46", 'Add bulk allowance after remove guest',
             [{'id': "ota_booking_multiple_guest", 'type': 'booking_v2'},
              {'id': 'MarkCancelled_02', 'type': 'mark_cancelled', 'enable_rate_manager': False,
               'is_inclusion_added': False}, {'id': "checkin_01", 'type': 'checkin_v2'},
              {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': '1'}], 200, None, "", "", "", "", False,
             ""),
            ("AddBulkAllowance_47", 'Add bulk allowance after updating stay duration',
             [{'id': "booking_212", 'type': 'booking_v2'},
              {'id': 'UpdateStayDuration_129', 'type': 'update_stay_duration', 'enable_rate_manager': True,
               'is_inclusion_added': False}, {'id': "checkin_01", 'type': 'checkin_v2'},
              {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': '1'}], 200, None, "", "", "", "", False,
             ""),
            ("AddBulkAllowance_48", 'Add bulk allowance after updating rate plan',
             [{'id': "booking_212", 'type': 'booking_v2'},
              {'id': 'UpdateRatePlan_01', 'type': 'update_rate_plan', 'enable_rate_manager': True,
               'is_inclusion_added': True}, {'id': "checkin_01", 'type': 'checkin_v2'},
              {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': '1'}], 200, None, "", "", "", "", False,
             ""),
            ("AddBulkAllowance_49", 'Add bulk allowance after updating room stay price',
             [{'id': "booking_212", 'type': 'booking_v2'},
              {'id': 'EditCharge_66', 'type': 'update_expense', 'charge_id': 1},
              {'id': "checkin_01", 'type': 'checkin_v2'},
              {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': '1'}], 200, None, "", "", "", "", False,
             ""),
            ("AddBulkAllowance_50", 'Add bulk allowance after put booking',
             [{'id': "booking_212", 'type': 'booking_v2'}, {'id': "put_booking_109", 'type': 'edit_booking_v2'},
              {'id': "checkin_01_01", 'type': 'checkin_v2'},
              {'id': "PostCharge_01", 'type': 'update_expense', 'charge_id': '6'}], 200, None, "", "", "", "", False,
             ""),
        ])
    @pytest.mark.regression
    def test_add_bulk_allowance(self, client_, test_case_id, tc_description, previous_actions, status_code, user_type,
                                error_code, error_message, dev_message, error_payload, skip_case, skip_message):
        if skip_case:
            pytest.skip(skip_message)

        hotel_id = HOTEL_ID[1] if "MultiCurrency" in tc_description else HOTEL_ID[0]

        if previous_actions:
            self.common_request_caller(client_, previous_actions, hotel_id)

        response = self.billing_request.add_buk_allowance(client_, test_case_id, status_code,
                                                          self.booking_request.bill_id, user_type)

        if status_code in ERROR_CODES:
            self.response_validation_negative_cases(response, error_code, error_message, dev_message, error_payload)
        elif status_code in SUCCESS_CODES:
            self.validation(self.billing_request, test_case_id, client_, self.booking_request.bill_id, hotel_id,
                            self.booking_request.booking_id)
        else:
            assert False, "Response status code is not matching"

    @staticmethod
    def validation(billing_request, test_case_id, client_, bill_id, hotel_id, booking_id):
        validation = ValidationAddBulkAllowance(client_, test_case_id, billing_request, bill_id, hotel_id)
        validation.validate_response()
        validation.validate_bill_summary_response()
        validation.validate_billed_entity_response()
        validation.validate_integration_event(booking_id, 'add_allowance')
        validation.validate_commissions()
