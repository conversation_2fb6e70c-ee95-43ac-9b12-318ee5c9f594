import pytest
from pos.integration_tests.config.common_config import ERROR_CODES, SUCCESS_CODES
from prometheus.integration_tests.requests.billing_requests import BillingRequests
from prometheus.integration_tests.tests.base_test import BaseTest
from prometheus.integration_tests.tests.before_test_actions import REVERSE_CHECKOUT_01, FULL_CHECKOUT_03, \
    REISSUE_INVOICE_01, REISSUE_INVOICE_03
from prometheus.integration_tests.config.common_config import *
from prometheus.integration_tests.tests.invoice.validations.validation_reverse_checkout import ValidationReverseCheckout


class TestReverseCheckout(BaseTest):
    @pytest.mark.parametrize(
        "test_case_id, previous_actions, tc_description, status_code, user_type, error_code, "
        "error_message, dev_message, error_payload, skip_message",
        [
            ("ReverseCheckout_01", FULL_CHECKOUT_03,
             "Reversal checkout a b2c booking with 1 guests", 200, 'super-admin', "", "", "", "", ""),
            ("ReverseCheckout_01", REVERSE_CHECKOUT_01,
             "Reversal checkout a b2c booking with 3 guests", 200, 'super-admin', "", "", "", "", ""),
            ("ReverseCheckout_01", REISSUE_INVOICE_01,
             "Reversal checkout a b2b booking with 1 guests", 200, 'super-admin', "", "", "", "", ""),
            ("ReverseCheckout_01", REISSUE_INVOICE_03,
             "Reversal checkout a b2b booking with 2 guests and part checkout", 200, 'super-admin', "", "", "", "", ""),
        ])
    @pytest.mark.regression
    def test_reverse_checkout(self, client_, test_case_id, previous_actions, tc_description, status_code, user_type,
                              error_code, error_message, dev_message, error_payload, skip_message):
        if skip_message:
            pytest.skip(skip_message)

        hotel_id = HOTEL_ID[1] if "MultiCurrency" in tc_description else HOTEL_ID[0]

        if previous_actions:
            self.common_request_caller(client_, previous_actions, hotel_id)

        locked_accounts = BillingRequests.account_locked_after_checkout(self.booking_request.bill_id)

        response = self.booking_request.delete_booking_action(client_, status_code, hotel_id)

        if status_code in ERROR_CODES:
            self.response_validation_negative_cases(response, error_code, message=error_message,
                                                    dev_message=dev_message, extra_payload=error_payload)
        elif status_code in SUCCESS_CODES:
            before_reverse_checkout_booking_response = self.booking_request.booking_response
            before_reverse_checkout_billing_response = self.billing_request.billing_response
            before_reverse_checkout_charges = self.billing_request.charges
            before_reverse_checkout_booking_invoices = self.booking_request.booking_invoices
            before_reverse_checkout_inventory = self.inventory_request.get_room_wise_inventory(client_, 200,
                                                                                               before_reverse_checkout_booking_response)
            self.validation(client_, response, test_case_id, user_type, self.booking_request.booking_id,
                            self.booking_request.bill_id, before_reverse_checkout_charges,
                            before_reverse_checkout_booking_response,
                            before_reverse_checkout_billing_response, self.booking_request, self.billing_request,
                            before_reverse_checkout_booking_invoices, before_reverse_checkout_inventory,
                            self.inventory_request, locked_accounts, hotel_id)
        else:
            assert False, "Response status code is not matching"

    @staticmethod
    def validation(client_, response, test_case_id, user_type, booking_id, bill_id, before_reverse_checkout_charges,
                   before_checkout_booking_response, before_reverse_checkout_billing_response,
                   booking_request, billing_request, before_reverse_checkout_booking_invoices,
                   before_reverse_checkout_inventory, inventory_request, locked_accounts, hotel_id,
                   action_type='checkout'):
        validation = ValidationReverseCheckout(client_, response=response, test_case_id=test_case_id,
                                               user_type=user_type, booking_id=booking_id,
                                               booking_request=booking_request,
                                               before_checkout_booking_response=before_checkout_booking_response)
        validation.validate_response(booking_request=booking_request, action_type=action_type)
        validation.validate_bill(billing_request, before_reverse_checkout_billing_response,
                                 before_reverse_checkout_charges, before_reverse_checkout_booking_invoices)
        validation.validate_inventory(before_reverse_checkout_inventory, inventory_request)
        validation.validate_booking_response()
        validation.validate_account_and_invoice(bill_id, hotel_id, locked_accounts)
