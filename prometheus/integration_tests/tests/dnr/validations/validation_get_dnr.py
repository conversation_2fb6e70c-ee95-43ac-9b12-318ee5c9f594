from prometheus.integration_tests.tests.base_test import BaseTest
from prometheus.integration_tests.utilities.common_utils import assert_


class ValidationGetDnr(BaseTest):

    def __init__(self, client_, response, test_case_id):
        self.client_ = client_
        self.test_case_id = test_case_id
        self.response = response

    # Validations for Get DNR:
    def validate_get_dnr_response(self, status):
        assert_(self.response['data']['dnr_id'], self.dnr_request.dnr_id)
        assert_(self.response['data']['room_allotment_id'], self.dnr_request.allotment_id)
        assert_(self.response['data']['room_id'], self.dnr_request.room_id)
        assert_(self.response['data']['status'], status)
        assert_(self.response['data']['type'], self.dnr_request.type)
        assert_(self.response['data']['subtype'], self.dnr_request.subtype)

    # Validations for Get Multiple DNRs:
    def validate_multiple_dnr_response(self):
        sorted_response_by_dnr_id = sorted(self.response['data'], key=lambda i: (i['dnr_id']))
        for index, value in enumerate(sorted(self.dnr_request.dnr_ids)):
            assert_(sorted_response_by_dnr_id[index]['dnr_id'], value)
            if self.test_case_id == 'GetMultipleDNR_05':
                assert_(sorted_response_by_dnr_id[index]['status'], 'inactive')
            else:
                assert_(sorted_response_by_dnr_id[index]['status'], 'active')

    def assert_dnrs_in_getdnrs(self):
        get_dnrs_response = self.response['data']
        create_dnrs_response = self.dnr_request.inactive_dnr_list if self.test_case_id in 'GetMultipleDNR_05' else \
            self.dnr_request.dnr_list
        assert_(sorted(get_dnrs_response, key=lambda i: int(i['room_id'])),
                sorted(create_dnrs_response, key=lambda i: int(i['room_id'])))
