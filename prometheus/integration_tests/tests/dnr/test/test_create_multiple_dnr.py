import pytest

from prometheus.integration_tests.config.common_config import SUCCESS_CODES, ERROR_CODES
from prometheus.integration_tests.tests.base_test import BaseTest
from prometheus.integration_tests.tests.before_test_actions import *
from prometheus.integration_tests.tests.dnr.validations.validation_create_multiple_dnr import \
    ValidationCreateMultipleDnr
from prometheus.integration_tests.config.common_config import *


class TestCreateMultipleDnr(BaseTest):

    @pytest.mark.parametrize("test_case_id, previous_actions, tc_description, status_code, user_type, error_code, "
                             "error_message, dev_message, error_payload, skip_message",
                             [
                                 ("CreateMultipleDnr_01", "", "Create Multiple Dnr for a particular hotel", 201,
                                  'super-admin', "", "", "", "", ""),
                                 ("CreateMultipleDnr_02", "", "Create Multiple Dnr with user_type as aom", 201,
                                  'aom', "", "", "", "", ""),
                                 ("CreateMultipleDnr_03", "", "Create Multiple Dnr with user_type as fdm", 201,
                                  'fdm', "", "", "", "", ""),
                                 ("CreateMultipleDnr_04", "", "Create Multiple Dnr with two room types", 201,
                                  'super-admin', "", "", "", "", ""),
                                 ("CreateMultipleDnr_05", "", "Create Multiple Dnr with three room types", 201,
                                  'super-admin', "", "", "", "", ""),
                                 ("CreateMultipleDnr_06", "", "Create Multiple past dated DNR with user_type as fdm",
                                  400,
                                  'fdm', "", "", "", "", ""),
                                 ("CreateMultipleDnr_07", "", "Create Multiple past dated DNR with user_type as aom",
                                  400, 'aom', "", "", "", "", ""),
                                 ("CreateMultipleDnr_08", "",
                                  "Create Multiple past dated DNR with user_type as super-admin", 400,
                                  'super-admin', "", "", "", "", ""),
                                 ("CreateMultipleDnr_09", "", "Create Multiple past dated DNR with user_type as fdm",
                                  400, 'fdm', "", "", "", "", ""),
                                 ("CreateMultipleDnr_10", "",
                                  "Create Multiple past dated DNR with user_type as super-admin", 400,
                                  'super-admin', "", "", "", "", ""),
                                 ("CreateMultipleDnr_11", SINGLE_BOOKING_CHECK_IN_01,
                                  "Create Multiple DNR with user_type as super-admin of checked in room", 400,
                                  'super-admin', "04010603", "Room is not available for selected dates", "", "", ""),
                             ])
    @pytest.mark.regression
    def test_create_multiple_dnr(self, client_, test_case_id, previous_actions, tc_description, status_code, user_type,
                                 error_code, error_message, dev_message, error_payload, skip_message):

        if skip_message:
            pytest.skip(skip_message)

        if previous_actions:
            self.common_request_caller(client_, previous_actions, HOTEL_ID[0])

        total_inventory_count = self.dnr_request.get_inventory_count(client_, 200, test_case_id)

        if test_case_id == 'CreateMultipleDnr_11':
            check_in_room_id = 1
        else:
            check_in_room_id = None

        response = self.dnr_request.create_multiple_dnr_request(client_, test_case_id, status_code, user_type,
                                                                check_in_room_id)

        if status_code in ERROR_CODES:
            self.response_validation_negative_cases(response, error_code, error_message, dev_message, error_payload)
        elif status_code in SUCCESS_CODES:
            self.validation(client_, response, test_case_id, total_inventory_count)
        else:
            assert False, "Response status code is not matching"

    @staticmethod
    def validation(client_, response, test_case_id, total_inventory_count):
        validation = ValidationCreateMultipleDnr(client_, response, test_case_id, total_inventory_count)
        validation.validate_response()
