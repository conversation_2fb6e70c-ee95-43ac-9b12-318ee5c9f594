import pytest

from prometheus.integration_tests.config.common_config import *
from prometheus.integration_tests.tests.e_reg_card.validations.validation_checkin_status import ValidationCheckinStatus
from prometheus.integration_tests.tests.base_test import BaseTest


class TestDeleteERegCard(BaseTest):
    @pytest.mark.parametrize(
        "test_case_id, existing_test_case_id, tc_description, previous_actions, e_reg_card_config_level, "
        "status_code, user_type, error_code, error_message, dev_message, error_payload, skip_case",
        [("Delete_ERegCard_01", "checkinPost_66",
          "Checkin two guests in a Single booking when one e-reg-card is deleted",
          [{'id': "Booking_710", 'type': 'booking'}, {'id': "ERegCard_01", 'type': "create_e_reg_card"},
           {'type': "delete_e_reg_card", "is_primary": True}], "guest", 400, None, "04010193",
          "ERegCard is not completed", "", "", True),
         ("Delete_ERegCard_02", "checkinPost_66", "Checkin two guests in a Single booking when both e_reg_cards "
                                                  "are deleted",
          [{'id': "Booking_710", 'type': 'booking'}, {'id': "ERegCard_01", 'type': "create_e_reg_card"},
           {'type': "delete_e_reg_card", "is_primary": True}, {'type': "delete_e_reg_card", "is_primary": False}],
          "guest", 400,
          None, "04010193", "ERegCard is not completed", "", "", True),
         ("Delete_ERegCard_03", "checkinPost_66", "Checkin two guests in a Single booking when primary guest "
                                                  "e-reg-card is deleted", [{'id': "Booking_710", 'type': 'booking'},
                                                                            {'id': "ERegCard_01",
                                                                             'type': "create_e_reg_card"},
                                                                            {'type': "delete_e_reg_card",
                                                                             "is_primary": True}],
          "room", 400, None, "04010193", "ERegCard is not completed", "", "", True),
         ("Delete_ERegCard_04", "checkinPost_66", "Checkin two guests in a Single booking when non-primary guest"
                                                  "e-reg-card is deleted", [{'id': "Booking_710", 'type': 'booking'},
                                                                            {'id': "ERegCard_01",
                                                                             'type': "create_e_reg_card"},
                                                                            {'type': "delete_e_reg_card",
                                                                             "is_primary": False}], "room",
          200, None, "", "", "", "", True),
         ])
    @pytest.mark.regression
    def test_delete_e_reg_card(self, client_, test_case_id, existing_test_case_id, tc_description, previous_actions,
                               e_reg_card_config_level, status_code, user_type, error_code, error_message,
                               dev_message, error_payload, skip_case):

        if skip_case:
            pytest.skip()

        if previous_actions:
            self.common_request_caller(client_, previous_actions)

        if existing_test_case_id:
            test_case_id = existing_test_case_id
        response = self.booking_request.check_in_request(client_, self.booking_request.booking_id,
                                                         test_case_id, status_code, user_type,
                                                         e_reg_card_config_level=e_reg_card_config_level if e_reg_card_config_level else None)

        if status_code in ERROR_CODES:
            self.response_validation_negative_cases(response, error_code, error_message, dev_message, error_payload)
        elif status_code in SUCCESS_CODES:
            self.validation(client_, response, test_case_id, self.booking_request)
        else:
            assert False, "Response status code is not matching"

    @staticmethod
    def validation(client, response, test_case_id, booking_request):
        validation = ValidationCheckinStatus(client, response, test_case_id, booking_request)
        validation.validate_checkin_status()
