UPDATE_INVENTORY_COUNT = "update room_type_inventory_availability set count = {0} where hotel_id='{1}'"

INSERT_ROOM_ALLOTMENT = "insert into room_allotment (created_at, modified_at,deleted,hotel_id, room_id, start_time, " \
                        "expected_end_time, actual_end_time, allotted_for,allotment_id) values (now(),now(),'f'," \
                        "'0016932','{room_id}','{start_time}','{expected_end_time}',null,'stay','{allotment_id}') "

ADD_ATTACHMENT = "INSERT into booking_attachment (created_at, modified_at, deleted, attachment_id, booking_id, url, " \
                 "display_name, file_type, attachment_group, source, uploaded_by, status) VALUES (now(), now(), FALSE, " \
                 "'{attachment_id}', '{booking_id}', '{url}', '{display_name}', '{file_type}', '{attachment_group}', " \
                 "'{source}', '{uploaded_by}', '{status}')"

EDIT_ATTACHMENT = "UPDATE booking_attachment set status = '{status}' where attachment_id = '{attachment_id}'"

DELETE_ATTACHMENT = "DELETE from booking_attachment"

GET_ALL_ACCOUNT = "SELECT deleted,billed_entity_id,account_number,invoiced,locked,is_allowance_account," \
                  "invoice_numbers_available_for_use FROM account WHERE bill_id='{bill_id}'"

GET_SPECIFIC_ACCOUNT = "SELECT deleted,invoiced,locked,is_allowance_account," \
                       "invoice_numbers_available_for_use FROM account WHERE bill_id='{bill_id}' " \
                       "and billed_entity_id ={billed_entity_id} and account_number={account_number}"

GET_INVOICE = "SELECT deleted,invoice_id,invoice_date,status FROM invoice WHERE invoice_number='{invoice_number}' " \
              "AND billed_entity_id={billed_entity_id} AND billed_entity_account_number={billed_entity_account_number}" \
              " AND bill_id='{bill_id}'"

GET_INVOICE_CHARGES = "SELECT deleted FROM invoice_charges WHERE invoice_id='{invoice_id}'"

UPDATE_BUSINESS_DATE = "Update hotel set current_business_date='{0}' where hotel_id='{1}'"

INSERT_ROOM_INVENTORY_AVAILABLITY = "insert into room_inventory_availability (hotel_id, room_id, date, status," \
                                    " created_at, modified_at) values ('{hotel_id}', '{room_id}', '{date}', 'available'," \
                                    " now(), now())"

DROP_ROOM_INVENTORY_AVAILABILITY_TABLE = "DELETE from room_inventory_availability"

DELETE_DNR_TABLE = "DELETE from dnr"

UPDATE_ROOM_INVENTORY_AVAILABLITY = "UPDATE room_inventory_availability set date='{date}' WHERE status='available'"

UPDATE_ACCOUNT_TO_LOCKED = "UPDATE account SET locked=TRUE WHERE bill_id='{bill_id}'"

UPDATE_SPLIT_ALLOWED = "UPDATE charge SET split_allowed=FALSE WHERE charge_id='{charge_id}'"

ADD_CHARGE_IN_BOOKING_INVOICE_GROUP = "UPDATE booking_invoice_group SET newly_added_charge_ids='{checkout_charge_id}'"

UPDATE_INVOICE_STATUS_TO_LOCKED = "UPDATE invoice SET status='locked'"

GET_CHARGE_ID_MAP = "select charge_id_map from room_stay"

BOOKING_SIDE_EFFECTS = "select side_effects from booking_action"

UPDATE_PACKAGE_DETAILS = "UPDATE booking_rate_plan SET package_details = '{package_details}';"

UPDATE_CASHIER_SESSION = "UPDATE cashier_session set start_datetime = '{start_time}' , " \
                         "end_datetime = '{end_time}' where session_id='{session_id}'"

UPDATE_EXPENSE_ITEM_SEQ = "SELECT setval('expense_item_id_seq', max(id)) FROM expense_item;"

UPDATE_PAYMENT_TO_POSTED = "UPDATE payment set status='posted' where bill_id='{bill_id}'"

UPDATE_SOURCE_ID_IN_PAYMENT = "UPDATE payment set source_id='{source_id}' where payment_id='{payment_id}'"

DELETE_EXPENSE_ITEM = "DELETE from expense_item where id>22"

UPDATE_ROOM_STAY_SKU_CATEGORY_SLAB_BASED_TAXATION = "UPDATE sku_category SET has_slab_based_taxation={has_slab_based_taxation} " \
                                                    "WHERE sku_category_id='stay'"

GET_POST_ALLOWANCE_TAX_DETAILS_OF_CHARGE_SPLITS = "SELECT post_allowance_tax_details from charge_split where bill_id='{bill_id}'" \
                                                  " and charge_id='{charge_id}' and charge_split_id='{charge_split_id}';"

POS_CHARGE = "insert into charge (deleted,charge_id,bill_id,pretax_amount,tax_details,tax_amount,posttax_amount," \
             "item_id,item_name,sku_category_id,charge_item_detail,status,applicable_date,comment,charge_split_type," \
             "charge_components,charge_to,posting_date,split_allowed,applicable_business_date) values " \
             "('f','6','{bill_id}','150.0000','{tax_details}','18.0000'," \
             "'168.0000','3655','Cold Coffee','food','{charge_item_detail}','consumed','{applicable_date}',''," \
             "'percentage_split',null,'{charge_to}','{posting_date}','t','{applicable_business_date}');"

POS_CHARGE_SPLIT = "insert into charge_split (deleted,charge_split_id,charge_id,bill_id,tax,pre_tax,post_tax," \
                   "tax_details,percentage,billed_entity_id,billed_entity_account_number,charge_type,bill_to_type) " \
                   "values ('f','1','6','{bill_id}','18.0000','150.0000','168.0000','{tax_details}','100.0000','1','1'," \
                   "'non-credit', 'guest');"
POS_EXPENSE = "insert into expense (deleted,booking_id,expense_id,room_stay_id,expense_item_id,status,charge_id," \
              "applicable_date,added_by,sku_id,applicable_business_date) values " \
              "('f','{booking_id}','5','1','3655','consumed','6','{applicable_date}','pos','3655'," \
              "'{applicable_business_date}');"

UPDATE_FREE_LATE_CHECKOUT_TIME = "UPDATE hotel SET free_late_checkout_time = to_char(now() - interval '1 hour', 'HH24:MI:SS');"

UPDATE_CHECKOUT_TIME = "UPDATE hotel SET checkout_time='11:00:00'"

UPDATE_CHECKOUT_TIME_WITH_INTERVAL = "UPDATE hotel SET checkout_time = to_char(now() - interval '{interval_minutes} min', 'HH24:MI:SS');"

UPDATE_SWITCH_OVER_TIME = "UPDATE hotel SET switch_over_time = '{switch_over_time}';"

GET_INTEGRATION_EVENT_FOR_BOOKING = "select * from integration_event where booking_id='{booking_id}' and " \
                                    "user_action='{user_action}'"

UPDATE_CHECKIN_DATE = "UPDATE booking set checkin_date='{0}'"

INVOICE_CHARGES_IN_INVOICE = "select * from invoice_charges where invoice_id='{invoice_id}'"

INVOICE_CHARGES_NOT_CONTAINS_ROOM_STAY_ID = "select * from invoice_charges where charge_item_detail->>'room_stay_id' is null"

CHARGE_WITH_CREATED_STATUS = "select * from charge where status='created'"

GET_TA_COMMISSION = "select * from ta_commission"

GET_TA_COMMISSION_PERCENTAGE = "select travel_agent_details from booking where booking_id='{booking_id}'"

GET_TAX_FROM_INVOICE = "SELECT tax_details_breakup FROM invoice WHERE billed_entity_id={billed_entity_id} AND " \
                       "billed_entity_account_number={billed_entity_account_number}"

GET_ACCOUNT_DETAILS = "select * from booking where reference_number='{reference_number}';"

GET_CREDIT_NOTE_COMMENT = "select comment from credit_note where credit_note_id='{credit_note_id}';"

GET_COMMENT_FROM_BOOKING = "select comments from booking where booking_id!='{booking_id}';"

GET_BOOKING_RATE_PLANS = "select restrictions from booking_rate_plan where booking_id='{booking_id}' and deleted=false;"

INSERT_FUNDING_CONFIG = "INSERT INTO booking_funding_config (booking_id, guardrails, created_at, modified_at, " \
                        "extra_information, deleted) VALUES ('{booking_id}', '{guardrails}', NOW(), NOW(), " \
                        "'{extra_info}', 'FALSE');"

INSERT_FUNDING_EXPENSE_ITEMS = "INSERT INTO expense_item (deleted, id, name, description, short_name, sku_category_id, " \
                               "expense_item_id, created_at, modified_at, linked, addon_code, external_item_code, sku_id) " \
                               "VALUES ('f', 7520, 'Tariff Support A', 'Treebo Funding Auto', 'Treebo Funding Auto', " \
                               "'stay', 'TariffSupport::A', 'NOW()' , 'NOW()', 'f', 'NULL', 'NULL', '37421')," \
                               "('f', 7521, 'Tariff Support B', 'Treebo Funding Manual', 'Treebo Funding Manual', " \
                               "'stay', 'TariffSupport::B', 'NOW()' , 'NOW()', 'f', 'NULL', 'NULL', '37422');"

GET_FUNDING_STATUS = "select status from booking_funding_request where booking_id='{booking_id}';"

GET_INVENTORY_BLOCK_ID = "select block_id from inventory_block where booking_id='{booking_id}' and " \
                          "block_type='{block_type}';"
