from prometheus.integration_tests.resources.db_queries import ADD_ATTACHMENT, EDIT_ATTACHMENT
from prometheus.integration_tests.config.sheet_names import add_attachment_sheet_name
from prometheus.integration_tests.utilities import excel_utils
from prometheus.integration_tests.utilities.common_utils import query_execute


class AttachmentRequests:

    def add_attachments(self, test_case_id, booking_id):
        attachment_data = excel_utils.get_test_case_data(add_attachment_sheet_name, test_case_id)[0]
        query_execute(ADD_ATTACHMENT.format(attachment_id=attachment_data['attachment_id'], booking_id=booking_id,
                                            url=attachment_data['url'], display_name=attachment_data['display_name'],
                                            file_type=attachment_data['file_type'], attachment_group=
                                            attachment_data['attachment_group'], source=attachment_data['source'],
                                            uploaded_by=
                                            attachment_data['uploaded_by'], status=attachment_data['status']))
        print('\n', ADD_ATTACHMENT, '\n', "Add attachment")

    def edit_attachments(self, test_case_id):
        attachment_data = excel_utils.get_test_case_data(add_attachment_sheet_name, test_case_id)[0]
        query_execute(EDIT_ATTACHMENT.format(status=attachment_data['status'],
                                             attachment_id=attachment_data['attachment_id']))
        print('\n', EDIT_ATTACHMENT, '\n', "Edit attachment ")
