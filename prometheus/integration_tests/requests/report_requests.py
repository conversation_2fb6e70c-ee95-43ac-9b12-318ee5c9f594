from prometheus.integration_tests.requests.base_request import BaseRequest
from prometheus.integration_tests.config.request_uris import invoice_report_uri
from prometheus.tests.mockers import mock_upload_file_to_s3_and_get_presigned_url, mock_trigger_invoice_report_email
from prometheus.integration_tests.utilities.common_utils import return_date
from prometheus.integration_tests.config.common_config import START_DATE_MIN, END_DATE_MAX, EMAIL


class ReportRequests(BaseRequest):

    def crs_report(self, client, status_code, hotel_id, user_type=None):
        uri = invoice_report_uri.format(return_date(START_DATE_MIN), return_date(END_DATE_MAX), EMAIL, hotel_id)
        with mock_upload_file_to_s3_and_get_presigned_url(), mock_trigger_invoice_report_email():
            response = self.request_processor(client, 'POST', uri, status_code, None, user_type)
        return response.json
