import logging

from treebo_commons.utils import dateutils

from object_registry import register_instance
from prometheus.application.decorators import session_manager
from prometheus.common.error_codes.errors import ApplicationErrors
from prometheus.domain.billing.repositories.bill_repository import BillRepository
from prometheus.domain.booking.repositories.booking_repository import BookingRepository
from prometheus.domain.hotel_config.factory import HotelConfigFactory
from prometheus.domain.hotel_config.repository import HotelConfigRepository
from prometheus.domain.inventory.repositories.dnr_repository import DNRRepository
from prometheus.domain.inventory.repositories.room_allotment_repository import (
    RoomAllotmentRepository,
)
from prometheus.domain.inventory.repositories.room_type_inventory_repository import (
    RoomTypeInventoryRepository,
)
from ths_common.constants.hotel_constants import ManagedBy
from ths_common.exceptions import ValidationException

logger = logging.getLogger(__name__)


@register_instance(
    dependencies=[
        HotelConfigRepository,
        BookingRepository,
        BillRepository,
        RoomTypeInventoryRepository,
        DNRRepository,
        RoomAllotmentRepository,
    ]
)
class HotelConfigApplicationService(object):
    def __init__(
        self,
        hotel_config_repository,
        booking_repository: BookingRepository,
        bill_repository: BillRepository,
        room_type_inventory_repository: RoomTypeInventoryRepository,
        dnr_repository: DNRRepository,
        room_allotment_repository: RoomAllotmentRepository,
    ):
        self.hotel_config_repository = hotel_config_repository
        self.booking_repository = booking_repository
        self.bill_repository = bill_repository
        self.room_type_inventory_repository = room_type_inventory_repository
        self.dnr_repository = dnr_repository
        self.room_allotment_repository = room_allotment_repository

    def is_hotel_managed_by_crs(self, hotel_id):
        hotel_config_aggregate = self.get_hotel_config(hotel_id)
        if not hotel_config_aggregate:
            return False
        return hotel_config_aggregate.hotel_config.managed_by == ManagedBy.CRS

    def get_hotel_config(self, hotel_id):
        hotel_config_aggregate = self.hotel_config_repository.load(hotel_id)
        if not hotel_config_aggregate:
            logger.debug(
                "Hotel id: {} not present in out config so defaulting to hx".format(
                    hotel_id
                )
            )
            hotel_config_aggregate = HotelConfigFactory.create_hotel_config(
                hotel_id=hotel_id, managed_by=ManagedBy.HX
            )
        return hotel_config_aggregate

    def get_hotel_configs(self):
        hotel_config_aggregates = self.hotel_config_repository.load_all()
        return hotel_config_aggregates

    @session_manager(commit=True)
    def initiate_hotel_migration_to_crs(self, hotel_id, user_data=None, mock=False):
        hotel_config_aggregate = self.hotel_config_repository.load(hotel_id)

        if not hotel_config_aggregate:
            if mock:
                hotel_config_aggregate = HotelConfigFactory.create_hotel_config(
                    hotel_id=hotel_id,
                    migration_start_date=dateutils.current_datetime(),
                    managed_by=ManagedBy.MOCK_MIGRATION_IN_PROGRESS,
                )
            else:
                hotel_config_aggregate = HotelConfigFactory.create_hotel_config(
                    hotel_id=hotel_id,
                    migration_start_date=dateutils.current_datetime(),
                    managed_by=ManagedBy.MIGRATION_IN_PROGRESS,
                )
            self.hotel_config_repository.save(hotel_config_aggregate)
        else:
            if hotel_config_aggregate.is_managed_by_crs():
                raise ValidationException(ApplicationErrors.MIGRATION_ALREADY_COMPLETED)

            if hotel_config_aggregate.is_migration_in_progress() and mock:
                raise ValidationException(
                    ApplicationErrors.MIGRATION_ALREADY_IN_PROGRESS
                )

            if (
                hotel_config_aggregate.is_managed_by_hx()
                or hotel_config_aggregate.is_mock_migration_in_progress()
            ):
                if mock:
                    hotel_config_aggregate.hotel_config.migration_start_date = (
                        dateutils.current_datetime()
                    )
                    hotel_config_aggregate.hotel_config.managed_by = (
                        ManagedBy.MOCK_MIGRATION_IN_PROGRESS
                    )
                    self.hotel_config_repository.update(hotel_config_aggregate)
                else:
                    hotel_config_aggregate.hotel_config.migration_start_date = (
                        dateutils.current_datetime()
                    )
                    hotel_config_aggregate.hotel_config.managed_by = (
                        ManagedBy.MIGRATION_IN_PROGRESS
                    )
                    self.hotel_config_repository.update(hotel_config_aggregate)

        return hotel_config_aggregate

    @session_manager(commit=True)
    def complete_hotel_migration(self, hotel_id, user_data=None):
        hotel_config_aggregate = self.hotel_config_repository.load(hotel_id)
        if not hotel_config_aggregate:
            raise ValidationException(ApplicationErrors.HOTEL_NOT_MANAGED_BY_CRS)
        if hotel_config_aggregate.is_managed_by_crs():
            raise ValidationException(ApplicationErrors.MIGRATION_ALREADY_COMPLETED)
        hotel_config_aggregate.complete_migration(dateutils.current_datetime())
        self.hotel_config_repository.update(hotel_config_aggregate)
        return hotel_config_aggregate

    @session_manager(commit=True)
    def rollback_hotel_migration(self, hotel_id, user_data=None):
        hotel_config_aggregate = self.hotel_config_repository.load(hotel_id)
        if not hotel_config_aggregate:
            raise ValidationException(ApplicationErrors.HOTEL_NOT_MANAGED_BY_CRS)

        if (
            hotel_config_aggregate.is_managed_by_crs()
            and hotel_config_aggregate.hotel_config.live_date < dateutils.yesterday()
        ):
            raise ValidationException(ApplicationErrors.ROLLBACK_NOT_ALLOWED)

        hotel_config_aggregate.rollback_migration()

        booking_ids = self.booking_repository.fetch_booking_ids(hotel_id)
        bill_ids = self.booking_repository.fetch_bill_ids(hotel_id)

        deleted_booking_count = self.booking_repository.delete_bookings(
            booking_ids, user_data=user_data
        )
        logger.info("Total booking deleted: %s", deleted_booking_count)
        deleted_bill_count = self.bill_repository.delete_all(
            bill_ids, user_data=user_data
        )
        logger.info("Total bill deleted: %s", deleted_bill_count)

        self.room_type_inventory_repository.delete_inventories_for_hotel_id(
            hotel_id, user_data=user_data
        )

        deleted_dnr_count = self.dnr_repository.delete_dnrs_for_hotel_id(
            hotel_id, user_data=user_data
        )
        logger.info("Total DNR deleted: %s", deleted_dnr_count)

        deleted_room_allotment_count = (
            self.room_allotment_repository.delete_allotments_for_hotel_id(
                hotel_id, user_data=user_data
            )
        )
        logger.info("Total room allotment deleted: %s", deleted_room_allotment_count)

        self.hotel_config_repository.update(hotel_config_aggregate)
        return hotel_config_aggregate
