from object_registry import register_instance
from prometheus.application.booking.dtos.room_stay_cancellation_charge_dto import (
    CancellationChargeResponseV2Dto,
)
from prometheus.application.booking.helpers.calculate_cancellation_charge_v2_service import (
    CalculateCancellationChargeV2Service,
)
from prometheus.application.decorators import set_hotel_context
from ths_common.constants.cancellation_charges_constants import CancellationChargeTypes


@register_instance(
    dependencies=[
        CalculateCancellationChargeV2Service,
    ]
)
class CalculateCancellationChargeV2QueryHandler:
    def __init__(
        self,
        calculate_cancellation_charge_v2_service: CalculateCancellationChargeV2Service,
    ):
        self.calculate_cancellation_charge_v2_service = (
            calculate_cancellation_charge_v2_service
        )

    @set_hotel_context()
    def handle(
        self,
        booking_id,
        cancellation_request_payload,
        hotel_aggregate=None,
    ):
        cancellation_datetime = cancellation_request_payload['cancellation_datetime']
        if (
            cancellation_request_payload.get('action')
            == CancellationChargeTypes.EARLY_CHECKOUT.value
        ):
            invoice_group_id = cancellation_request_payload['action_payload'].get(
                'invoice_group_id'
            )
            cancellation_policy_details = self.calculate_cancellation_charge_v2_service.calculate_cancellation_charge_for_early_checkout(
                booking_id, cancellation_datetime, invoice_group_id
            )
        else:
            cancellation_policy_details = self.calculate_cancellation_charge_v2_service.calculate_cancellation_charges_for_cancel_room_stays(
                booking_id, cancellation_datetime
            )
        return CancellationChargeResponseV2Dto(cancellation_policy_details)
