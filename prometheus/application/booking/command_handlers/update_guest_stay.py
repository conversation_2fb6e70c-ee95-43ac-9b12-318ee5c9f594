import logging

from object_registry import register_instance
from prometheus import crs_context
from prometheus.application import crs_context_middleware
from prometheus.application.audit_trail.decorator import audit
from prometheus.application.booking.helpers.billed_entity_service import (
    BilledEntityService,
)
from prometheus.application.decorators import session_manager, set_hotel_context
from prometheus.common.error_codes.errors import ApplicationErrors
from prometheus.common.helpers import occupancy_change_handler
from prometheus.domain.billing.repositories.bill_repository import BillRepository
from prometheus.domain.booking.repositories.booking_repository import BookingRepository
from ths_common.constants.audit_trail_constants import AuditType
from ths_common.constants.booking_constants import BookingStatus
from ths_common.exceptions import ValidationException

logger = logging.getLogger(__name__)


@register_instance(
    dependencies=[BookingRepository, BillRepository, BilledEntityService]
)
class UpdateGuestStayCommandHandler:
    def __init__(
        self,
        booking_repository: BookingRepository,
        bill_repository: BillRepository,
        billed_entity_service: BilledEntityService,
    ):
        self.booking_repository = booking_repository
        self.bill_repository = bill_repository
        self.billed_entity_service = billed_entity_service

    @session_manager(commit=True)
    @audit(audit_type=AuditType.GUEST_ASSIGNED_TO_GUEST_STAY)
    @set_hotel_context()
    def handle(
        self,
        booking_id,
        room_stay_id,
        guest_stay_id,
        booking_version,
        guest_stay_data: dict,
        user_data,
        hotel_aggregate=None,
    ):
        """
        :param booking_id:
        :param room_stay_id:
        :param guest_stay_id:
        :param booking_version:
        :param guest_stay_data: a dict created from EditGuestStaySchema
        :param user_data:
        :return:
        """
        booking_aggregate = self.booking_repository.load_for_update(
            booking_id, version=booking_version, user_data=user_data
        )
        bill_aggregate = self.bill_repository.load_for_update(
            booking_aggregate.booking.bill_id
        )
        if not hotel_aggregate:
            crs_context_middleware.set_hotel_context(booking_aggregate.booking.hotel_id)

        crs_context.set_current_booking(booking_aggregate)
        crs_context.set_current_bill(bill_aggregate)
        guest_stay_data['guest_stay_id'] = guest_stay_id
        updated_guest_stays = self.update_guest_stay_allocation(
            booking_aggregate=booking_aggregate,
            bill_aggregate=bill_aggregate,
            room_stay_id=room_stay_id,
            guest_stays_data=[guest_stay_data],
        )
        self.booking_repository.update(booking_aggregate)
        self.bill_repository.update(bill_aggregate)
        guest_stay = updated_guest_stays.pop()
        return guest_stay, booking_aggregate.current_version()

    def update_guest_stay_allocation(
        self, booking_aggregate, bill_aggregate, room_stay_id, guest_stays_data: list
    ):
        updated_guest_stays = []
        for guest_stay_data in guest_stays_data:
            guest_id = guest_stay_data.get('guest_id')
            new_guest = guest_stay_data.get('guest')
            guest_stay_id = guest_stay_data.get('guest_stay_id')

            if not (bool(guest_id) ^ bool(new_guest)):
                raise ValidationException(
                    "One of the two fields guest or guest_id must be provided, but not both"
                )

            guest_stay = booking_aggregate.get_guest_stay(room_stay_id, guest_stay_id)
            if guest_stay.status != BookingStatus.RESERVED:
                raise ValidationException(
                    ApplicationErrors.GUEST_ALLOCATION_UPDATE_ERROR
                )

            customer = (
                booking_aggregate.get_customer(guest_id)
                if guest_id
                else booking_aggregate.add_customer(new_guest)
            )
            booking_aggregate.assign_guest_to_guest_stay(
                room_stay_id, guest_stay, customer
            )
            updated_guest_stays.append(guest_stay)
        self.billed_entity_service.create_or_update_billed_entity_data_for_allocated_guest_stays(
            booking_aggregate, bill_aggregate, updated_guest_stays
        )

        room_stay = booking_aggregate.get_room_stay(room_stay_id)
        occupancy_change_handler.update_occupancy_details_in_booked_charges(
            booking_aggregate, bill_aggregate, room_stay=room_stay
        )

        return updated_guest_stays
