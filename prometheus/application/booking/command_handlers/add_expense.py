from typing import Union

from treebo_commons.utils import dateutils

from object_registry import register_instance
from prometheus import crs_context
from prometheus.application import crs_context_middleware
from prometheus.application.audit_trail.decorator import audit
from prometheus.application.booking.helpers.billed_entity_service import (
    BilledEntityService,
)
from prometheus.application.decorators import session_manager, set_hotel_context
from prometheus.application.helpers.billed_entity_helper import (
    get_billed_entity_for_given_category,
    get_room_stay_default_billed_entity,
)
from prometheus.application.helpers.gst_details_helper import (
    get_gst_details_from_billed_entity,
)
from prometheus.application.helpers.ta_commission_helper import TACommissionHelper
from prometheus.application.hotel_settings.tenant_settings import TenantSettings
from prometheus.application.services.integration_event_application_service import (
    IntegrationEventApplicationService,
)
from prometheus.common.error_codes.errors import ApplicationErrors
from prometheus.domain.billing.dto import ChargeData, ChargeSplitData
from prometheus.domain.billing.entities.billed_entity import BilledEntity
from prometheus.domain.billing.repositories import BillRepository
from prometheus.domain.billing.services import TaxService
from prometheus.domain.booking.dtos import ExpenseDto
from prometheus.domain.booking.repositories import (
    BookingRepository,
    ExpenseItemRepository,
)
from prometheus.domain.catalog.repositories import (
    RoomTypeRepository,
    SkuCategoryRepository,
)
from prometheus.domain.policy.context import TenantFactsContext
from prometheus.domain.policy.engine import RuleEngine
from prometheus.domain.policy.facts import ExpenseFacts
from prometheus.infrastructure.external_clients.core.constants import CORPORATE_CHANNELS
from ths_common.constants.audit_trail_constants import AuditType
from ths_common.constants.billing_constants import (
    BilledEntityCategory,
    ChargeBillToTypes,
    ChargeSplitType,
    ChargeStatus,
    ChargeSubTypes,
    ChargeTypes,
)
from ths_common.constants.booking_constants import (
    BookingChannels,
    ExpenseAddedBy,
    ExpenseStatus,
)
from ths_common.constants.user_constants import UserType
from ths_common.exceptions import ValidationException
from ths_common.utils.dateutils import get_settlement_date
from ths_common.value_objects import (
    ChargeItem,
    ExpenseChargeItemDetails,
    GSTDetails,
    Occupancy,
    PriceData,
)


@register_instance(
    dependencies=[
        BookingRepository,
        BillRepository,
        ExpenseItemRepository,
        RoomTypeRepository,
        SkuCategoryRepository,
        TaxService,
        TACommissionHelper,
        BilledEntityService,
        TenantSettings,
    ]
)
class AddExpenseCommandHandler:
    def __init__(
        self,
        booking_repository: BookingRepository,
        bill_repository: BillRepository,
        expense_item_repository: ExpenseItemRepository,
        room_type_repository: RoomTypeRepository,
        sku_category_repository: SkuCategoryRepository,
        tax_service: TaxService,
        ta_commission_helper: TACommissionHelper,
        billed_entity_service: BilledEntityService,
        tenant_settings: TenantSettings,
    ):
        self.booking_repository = booking_repository
        self.bill_repository = bill_repository
        self.expense_item_repository = expense_item_repository
        self.room_type_repository = room_type_repository
        self.sku_category_repository = sku_category_repository
        self.tax_service = tax_service
        self.ta_commission_helper = ta_commission_helper
        self.billed_entity_service = billed_entity_service
        self.tenant_settings = tenant_settings

    @staticmethod
    def _fail_if_expense_not_assigned(expense):
        if not expense["guests"]:
            raise ValidationException(
                ApplicationErrors.CANT_ADD_EXPENSE_IF_NOT_ASSIGNED
            )

    @staticmethod
    def _fail_if_future_expense(expense):
        if dateutils.to_date(expense["applicable_date"]) > dateutils.current_date():
            raise ValidationException(ApplicationErrors.CANT_ADD_FUTURE_EXPENSE)

    @staticmethod
    def _fail_if_duplicate_guests(expense):
        if expense.get("assigned_to") and len(expense["assigned_to"]) != len(
            set(expense["assigned_to"])
        ):  # has duplicates
            raise ValidationException(ApplicationErrors.DUPLICATE_GUESTS)

    @session_manager(commit=True)
    @audit(audit_type=AuditType.CHARGE_ADDED)
    @set_hotel_context()
    def handle(
        self,
        booking_id,
        booking_version_id,
        expense_data: dict,
        price_data: PriceData,
        user_data,
        hotel_aggregate=None,
    ):
        booking_aggregate = self.booking_repository.load_for_update(
            booking_id, version=booking_version_id
        )
        bill_aggregate = self.bill_repository.load_for_update(
            booking_aggregate.booking.bill_id
        )

        if not hotel_aggregate:
            crs_context_middleware.set_hotel_context(booking_aggregate.booking.hotel_id)

        hotel_context = crs_context.get_hotel_context()
        crs_context.set_current_booking(booking_aggregate)
        crs_context.set_current_bill(bill_aggregate)

        expense_item = self.expense_item_repository.load(
            expense_data['expense_item_id'], sku_id=expense_data.get('sku_id')
        )
        settlement_date = get_settlement_date(
            dateutils.current_datetime(), next_month=True
        )

        self._fail_if_expense_not_assigned(expense_data)
        self._fail_if_future_expense(expense_data)
        self._fail_if_duplicate_guests(expense_data)

        # fallback: fdm to select expense added_by: 'hotel'
        if expense_data.get('added_by') is None:
            if not crs_context.is_treebo_tenant():
                expense_data['added_by'] = ExpenseAddedBy.HOTEL
            elif user_data and user_data.user_type == UserType.FDM:
                expense_data['added_by'] = ExpenseAddedBy.HOTEL
            else:
                expense_data['added_by'] = ExpenseAddedBy.TREEBO

        expense_dto = ExpenseDto.from_dict(expense_data)
        corporate_channels = self.tenant_settings.get_setting_value(CORPORATE_CHANNELS)
        RuleEngine.action_allowed(
            action="create_expense",
            facts=ExpenseFacts(
                expense=expense_data,
                price=price_data,
                user_type=user_data.user_type,
                booking_aggregate=booking_aggregate,
                hotel_context=crs_context.get_hotel_context(),
                tenant_facts_context=TenantFactsContext(
                    corporate_channels=corporate_channels
                ),
            ),
            fail_on_error=True,
        )
        expense = booking_aggregate.add_expense(expense_dto, settlement_date)
        if expense_dto.room_stay_id:
            room_stay = booking_aggregate.get_room_stay(expense_dto.room_stay_id)
            if expense_dto.added_by == ExpenseAddedBy.POS:
                default_billed_entity = get_billed_entity_for_given_category(
                    bill_aggregate,
                    booking_aggregate.get_default_billed_entity_category_for_extras(),
                    booking_aggregate,
                    room_stay,
                )
            else:
                default_billed_entity = get_room_stay_default_billed_entity(
                    room_stay, booking_aggregate, bill_aggregate
                )
        else:
            room_stay = None
            default_billed_entity = bill_aggregate.get_billed_entity_for_category(
                booking_aggregate.get_default_billed_entity_category()
            )

        charge = self._create_charge_for_expense(
            expense_dto,
            price_data,
            expense_item,
            room_stay,
            default_billed_entity,
            sku_category=self.sku_category_repository.load(
                expense_item.sku_category_id
            ).sku_category,
            bill_aggregate=bill_aggregate,
            booking_aggregate=booking_aggregate,
        )
        tax_updated_charge_dtos = self.tax_service.update_taxes(
            [charge],
            buyer_gst_details=get_gst_details_from_billed_entity(
                booking_aggregate, default_billed_entity
            ),
            seller_has_lut=self.tax_service.seller_has_lut(
                booking_aggregate.booking.seller_model, crs_context.get_hotel_context()
            ),
            hotel_id=hotel_context.hotel_id,
        )

        added_charges = bill_aggregate.add_charges(
            tax_updated_charge_dtos
        )  # 1 charge only
        added_charge = added_charges[0]
        expense.charge_id = added_charge.charge_id  # Link charge
        bill_aggregate.consume_charge(added_charge, hotel_context.current_date())

        self.booking_repository.update(booking_aggregate)
        self.bill_repository.update(bill_aggregate)

        IntegrationEventApplicationService.create_booking_updated_event(
            booking_aggregate=booking_aggregate,
            bill_aggregate=bill_aggregate,
            user_action="add_expense",
        )

        return expense, booking_aggregate.current_version()

    def _create_charge_for_expense(
        self,
        expense: ExpenseDto,
        price,
        expense_item,
        room_stay,
        default_billed_entity: BilledEntity,
        sku_category,
        bill_aggregate,
        booking_aggregate,
    ):
        if room_stay:
            expense_charge_item_details = (
                self._get_expense_charge_item_details_from_room_stay(expense, room_stay)
            )
        else:
            expense_charge_item_details = ExpenseChargeItemDetails()
        hotel_context = crs_context.get_hotel_context()
        charge_applicable_date = self._get_default_applicable_date_for_expense(
            expense.applicable_date, room_stay, hotel_context
        )
        charge_type = price.type
        if expense.added_by == ExpenseAddedBy.POS:
            default_payment_instruction = (
                booking_aggregate.get_default_payment_instruction_for_extras()
            )
            charge_type = ChargeTypes.from_payment_instruction(
                default_payment_instruction
            )
        billed_entity_account = (
            bill_aggregate.get_billed_entity_account_for_new_assignment(
                default_billed_entity, charge_type
            )
        )
        bill_aggregate.add_folio_if_not_exists(
            billed_entity_account.billed_entity_id, billed_entity_account.account_number
        )
        return ChargeData(
            pretax_amount=price.pretax_amount,
            tax_details=None,
            tax_amount=None,
            posttax_amount=price.posttax_amount,
            status=ChargeStatus.CREATED,
            applicable_date=charge_applicable_date,
            comment=expense.comments,
            charge_split_type=ChargeSplitType.EQUAL_SPLIT,
            charge_splits=[
                ChargeSplitData(
                    billed_entity_account=billed_entity_account,
                    charge_type=charge_type,
                    bill_to_type=price.bill_to_type,
                )
            ],
            created_by=None,
            item=ChargeItem(
                expense_item.name,
                sku_category.sku_category_id,
                expense_charge_item_details,
                item_id=expense_item.expense_item_id,
            ),
            charge_to=expense.guests,
        )

    @staticmethod
    def _get_default_applicable_date_for_expense(
        applicable_date, room_stay, hotel_context
    ):
        if (
            dateutils.to_date(room_stay.checkin_date)
            < dateutils.to_date(applicable_date)
            < dateutils.to_date(room_stay.checkout_date)
        ):
            return dateutils.datetime_at_given_time(
                applicable_date, hotel_context.checkin_time
            )
        elif dateutils.to_date(room_stay.checkin_date) == dateutils.to_date(
            applicable_date
        ):
            return room_stay.checkin_date
        elif dateutils.to_date(room_stay.checkout_date) == dateutils.to_date(
            applicable_date
        ):
            return room_stay.checkout_date
        assert False, "Should never reach here"

    def _get_expense_charge_item_details_from_room_stay(
        self, expense: ExpenseDto, room_stay
    ):
        is_pos_charge = expense.added_by.value == ExpenseAddedBy.POS.value
        pos_order_id = (
            pos_order_number
        ) = (
            pos_bill_id
        ) = (
            pos_bill_number
        ) = (
            seller_name
        ) = (
            pos_item_quantity
        ) = (
            is_touche_pos_charge
        ) = (
            revenue_center
        ) = waiter_id = workstation_id = interface_id = serving_time = None
        if is_pos_charge:
            pos_order_id = expense.extra_information.get('order_id')
            pos_order_number = expense.extra_information.get('order_number')
            pos_bill_id = expense.extra_information.get('bill_id')
            pos_bill_number = expense.extra_information.get('bill_number')
            seller_name = expense.extra_information.get('seller_name')
            pos_item_quantity = expense.extra_information.get('quantity')
        if expense.extra_information and expense.extra_information.get(
            'is_touche_pos_charge'
        ):
            is_pos_charge = True
            is_touche_pos_charge = expense.extra_information.get('is_touche_pos_charge')
            interface_id = expense.extra_information.get('interface_id')
            pos_order_number = expense.extra_information.get('check_number')
            pos_bill_id = expense.extra_information.get('check_number')
            pos_bill_number = expense.extra_information.get('check_number')
            revenue_center = expense.extra_information.get('revenue_center')
            waiter_id = expense.extra_information.get('waiter_id')
            seller_name = expense.extra_information.get('seller_name')
            workstation_id = expense.extra_information.get('workstation_id')
            serving_time = expense.extra_information.get('serving_time')
        room_no = (
            room_stay.room_allocation.room_no if room_stay.room_allocation else None
        )
        room_type = self.room_type_repository.load_type_map()[
            room_stay.room_type_id
        ].room_type.type
        occupancy = room_stay.date_wise_occupancies.get(
            dateutils.to_date(expense.applicable_date), Occupancy(1, 0)
        )
        expense_charge_item_details = ExpenseChargeItemDetails(
            room_stay.room_stay_id,
            room_no,
            room_type,
            room_stay.room_type_id,
            occupancy.adult,
            is_pos_charge=is_pos_charge,
            pos_order_id=pos_order_id,
            pos_order_number=pos_order_number,
            pos_bill_id=pos_bill_id,
            pos_bill_number=pos_bill_number,
            seller_name=seller_name,
            pos_item_quantity=pos_item_quantity,
            is_touche_pos_charge=is_touche_pos_charge,
            interface_id=interface_id,
            revenue_center=revenue_center,
            serving_time=serving_time,
            waiter_id=waiter_id,
            workstation_id=workstation_id,
        )
        return expense_charge_item_details

    @staticmethod
    def _get_gst_details_from_billing_instructions(
        billing_instructions, booking_aggregate, bill_aggregate
    ):
        current_gst_details: Union[GSTDetails, None] = None
        for billing_instruction in billing_instructions:
            billed_entity = bill_aggregate.get_billed_entity(
                billing_instruction.billed_entity_account.billed_entity_id
            )
            gst_details = get_gst_details_from_billed_entity(
                booking_aggregate, billed_entity
            )
            if (
                current_gst_details
                and not current_gst_details.has_same_tax_determiners(gst_details)
            ):
                raise ValidationException(
                    ApplicationErrors.CHARGE_CANNOT_BE_SPLIT_TO_DIFFERENT_GST_CONFIG
                )
            current_gst_details = gst_details
        return current_gst_details

    @staticmethod
    def _get_gst_details_from_billed_entity(booking_aggregate, billed_entity):
        gst_details = get_gst_details_from_billed_entity(
            booking_aggregate, billed_entity
        )
        return gst_details

    def _create_charge_dto(
        self,
        bill_aggregate,
        booking_aggregate,
        expense_dto: ExpenseDto,
        parsed_request,
        room_stay,
        expense_item,
        sku_category,
        pretax_price=None,
        posttax_price=None,
    ):
        expense_charge_item_details = (
            self._get_expense_charge_item_details_from_room_stay(expense_dto, room_stay)
        )
        if parsed_request.get("bill_to") and not parsed_request.get(
            "billing_instructions"
        ):
            (
                charge_type,
                charge_sub_type,
                bill_to_type,
                billed_entity_account,
                billed_entity,
            ) = self._get_charge_details(
                booking_aggregate,
                bill_aggregate,
                parsed_request["bill_to"],
            )
            charge_splits = [
                ChargeSplitData(
                    billed_entity_account=billed_entity_account,
                    charge_type=charge_type,
                    bill_to_type=bill_to_type,
                    charge_sub_type=charge_sub_type,
                    percentage=100,
                )
            ]
            buyer_gst_details = self._get_gst_details_from_billed_entity(
                booking_aggregate, billed_entity
            )
        else:
            charge_splits = [
                ChargeSplitData.from_billing_instruction(
                    billing_instruction,
                    self.billed_entity_service.derive_bill_to_type(
                        bill_aggregate, billing_instruction.billed_entity_account
                    ),
                )
                for billing_instruction in parsed_request['billing_instructions']
            ]
            buyer_gst_details = self._get_gst_details_from_billing_instructions(
                parsed_request['billing_instructions'],
                booking_aggregate,
                bill_aggregate,
            )

        return ChargeData.create_for_expense(
            expense_dto,
            expense_item,
            sku_category,
            expense_charge_item_details,
            charge_splits,
            ChargeSplitType.PERCENTAGE_SPLIT,
            pretax_price=pretax_price,
            posttax_price=posttax_price,
            buyer_gst_details=buyer_gst_details,
        )

    @staticmethod
    def _create_expense_dto(
        expense_data,
        date_wise_expense_price,
        parsed_request,
        expense_item,
        hotel_context,
    ):
        if (
            dateutils.to_date(date_wise_expense_price['applicable_date'])
            > hotel_context.current_date()
        ):
            date_wise_expense_price['status'] = ExpenseStatus.CREATED
        return ExpenseDto.from_dict(
            dict(
                room_stay_id=parsed_request['room_stay_id'],
                status=date_wise_expense_price['status'],
                comments=date_wise_expense_price.get('comments', ''),
                applicable_date=date_wise_expense_price['applicable_date'],
                added_by=parsed_request['added_by'],
                guests=parsed_request['guests'],
                sku_id=expense_item.sku_id,
                sku_name=expense_item.name,
                expense_item_id=expense_item.expense_item_id,
                via_rate_plan=expense_data.get('via_rate_plan', False),
                linked=expense_item.linked,
                extra_information=parsed_request.get('extra_information'),
                service_context=parsed_request.get('service_context'),
            )
        )

    @staticmethod
    def _get_charge_details(booking_aggregate, bill_aggregate, guest_id):
        customer = booking_aggregate.get_customer(str(guest_id))
        billed_entity_id = (
            customer.billed_entity_id or customer.company_billed_entity_id
        )
        billed_entity = bill_aggregate.get_billed_entity(billed_entity_id)
        bill_to_type = ChargeBillToTypes.from_billed_entity_category(
            billed_entity.category
        )
        charge_type = ChargeTypes.NON_CREDIT
        charge_sub_type = None
        if (
            booking_aggregate.booking.default_billed_entity_category_for_extras
            == billed_entity.category
        ):
            charge_type = ChargeTypes.from_payment_instruction(
                booking_aggregate.booking.default_payment_instruction_for_extras
            )

        billed_entity_account = (
            bill_aggregate.get_billed_entity_account_for_new_assignment(
                billed_entity, charge_type
            )
        )

        return (
            charge_type,
            charge_sub_type,
            bill_to_type,
            billed_entity_account,
            billed_entity,
        )
