import logging

from object_registry import register_instance
from prometheus import crs_context
from prometheus.application.audit_trail.decorator import audit
from prometheus.application.booking.command_handlers.invoice.helpers import (
    preview_invoice_helpers,
)
from prometheus.application.commands.consume_charge import ConsumeChargeCommand
from prometheus.application.decorators import session_manager
from prometheus.application.helpers.invoice_bill_to_service import InvoiceBillToService
from prometheus.application.helpers.invoice_issuer_service import InvoiceIssuerService
from prometheus.application.hotel_settings.tenant_settings import TenantSettings
from prometheus.application.services.integration_event_application_service import (
    IntegrationEventApplicationService,
)
from prometheus.async_job.job_scheduler_service import JobSchedulerService
from prometheus.common.error_codes.errors import ApplicationErrors
from prometheus.common.helpers.invoice_generation_helper import InvoiceGenerationHelper
from prometheus.domain.billing.repositories.bill_repository import BillRepository
from prometheus.domain.billing.repositories.invoice_repository import InvoiceRepository
from prometheus.domain.billing.services.invoice_service import InvoiceService
from prometheus.domain.booking.repositories.booking_repository import BookingRepository
from prometheus.domain.catalog.repositories.hotel_repository import HotelRepository
from prometheus.domain.catalog.repositories.reseller_gst_repository import (
    ResellerGstRepository,
)
from prometheus.domain.catalog.repositories.room_type_repository import (
    RoomTypeRepository,
)
from prometheus.domain.catalog.repositories.sku_category_repository import (
    SkuCategoryRepository,
)
from prometheus.domain.hotel_config.repository import HotelConfigRepository
from prometheus.domain.policy.engine import RuleEngine
from prometheus.domain.policy.facts.facts import Facts
from prometheus.infrastructure.external_clients.catalog_service_client import (
    CatalogServiceClient,
)
from ths_common.constants.audit_trail_constants import AuditType
from ths_common.constants.billing_constants import (
    ChargeStatus,
    IssuedByType,
    IssuedToType,
)
from ths_common.constants.booking_constants import BookingStatus
from ths_common.constants.integration_event_constants import IntegrationEventType
from ths_common.exceptions import ValidationException
from ths_common.value_objects import InvoiceBillToInfo

logger = logging.getLogger(__name__)


@register_instance(
    dependencies=[
        InvoiceRepository,
        BookingRepository,
        BillRepository,
        JobSchedulerService,
        RoomTypeRepository,
        SkuCategoryRepository,
        ResellerGstRepository,
        CatalogServiceClient,
        InvoiceService,
        HotelRepository,
        HotelConfigRepository,
        TenantSettings,
    ]
)
class InvoiceAccountsCommandHandler:
    DefaultSignedUrlExpirationHours = 2
    DefaultSignedUrlExpirationSeconds = DefaultSignedUrlExpirationHours * 3600

    def __init__(
        self,
        invoice_repository: InvoiceRepository,
        booking_repository: BookingRepository,
        bill_repository: BillRepository,
        job_scheduler_service: JobSchedulerService,
        room_type_repository: RoomTypeRepository,
        sku_category_repository: SkuCategoryRepository,
        reseller_gst_repository: ResellerGstRepository,
        catalog_service_client: CatalogServiceClient,
        invoice_service: InvoiceService,
        hotel_repository: HotelRepository,
        hotel_config_repository: HotelConfigRepository,
        tenant_settings: TenantSettings,
    ):
        self.invoice_repository = invoice_repository
        self.booking_repository = booking_repository
        self.bill_repository = bill_repository
        self.room_type_repository = room_type_repository
        self.job_scheduler_service = job_scheduler_service
        self.sku_category_repository = sku_category_repository
        self.reseller_gst_detail_repository = reseller_gst_repository
        self.catalog_service_client = catalog_service_client
        self.invoice_service = invoice_service
        self.hotel_repository = hotel_repository
        self.hotel_config_repository = hotel_config_repository
        self.tenant_settings = tenant_settings

    def _set_hotel_context(self, hotel_id):
        hotel_aggregate = self.hotel_repository.load(hotel_id)
        hotel_config_aggregate = self.hotel_config_repository.load(hotel_id)
        hotel_context = crs_context.set_hotel_context(
            hotel_aggregate, hotel_config_aggregate
        )
        return hotel_aggregate, hotel_config_aggregate, hotel_context

    def generate_confirmed_invoices_for_billed_entity_accounts(
        self,
        bill_aggregate,
        billed_entity_account_wise_charge_map,
        booking_aggregate,
        grouped_sku_categories,
        hotel_context,
        issued_by,
        issued_by_type,
        generate_hotel_side,
    ):
        hotel_invoice_aggregates, new_invoice_aggregates = [], []
        for group_key, charge_map in billed_entity_account_wise_charge_map.items():
            # TODO: Shouldn't generate invoice, if there is another charges for this billed entity account
            if not charge_map:
                continue
            user_info_map = InvoiceBillToService.compute_charge_to_info(
                charge_map, bill_aggregate, booking_aggregate
            )

            bill_to_info = InvoiceBillToInfo.create_from_billed_entity(
                bill_aggregate.get_billed_entity(
                    group_key.billed_entity_account.billed_entity_id
                ),
                booking_aggregate,
            )

            invoice, hotel_invoice = self.invoice_service.create_confirmed_invoice(
                bill_aggregate,
                group_key,
                charge_map,
                hotel_context.current_date(),
                user_info_map,
                bill_to_info,
                issued_by_type,
                IssuedToType.CUSTOMER,
                issued_by,
                grouped_sku_categories,
                group_key.billed_entity_account,
                hotel_context=hotel_context,
                generate_hotel_side=generate_hotel_side,
            )
            invoice_aggregate = invoice[0]
            InvoiceGenerationHelper.populate_actual_check_in_and_checkout_dates_in_invoice(
                invoice_aggregate,
                booking_aggregate,
            )
            if hotel_invoice:
                hotel_invoice_aggregates.append(hotel_invoice[0])
            new_invoice_aggregates.append(invoice_aggregate)
            bill_aggregate.mark_billed_entity_account_as_invoiced(
                group_key.billed_entity_account
            )
            return new_invoice_aggregates, hotel_invoice_aggregates

    @session_manager(commit=True)
    @audit(audit_type=AuditType.INVOICE_GENERATED_VIA_INVOICE_ACCOUNT)
    def handle(
        self, bill_id, invoice_accounts_request, user_data, hotel_aggregate=None
    ):
        booking_aggregate = self.booking_repository.load_booking_by_bill_id(bill_id)
        crs_context.set_current_booking(booking_aggregate)

        RuleEngine.action_allowed(
            action='generate_invoice_for_billed_entity_accounts',
            facts=Facts(
                user_type=user_data.user_type, booking_aggregate=booking_aggregate
            ),
            fail_on_error=True,
        )

        bill_aggregate = self.bill_repository.load_for_update(bill_id)
        hotel_context = crs_context.get_hotel_context() if hotel_aggregate else None
        if not hotel_aggregate:
            (
                hotel_aggregate,
                _,
                hotel_context,
            ) = self._set_hotel_context(booking_aggregate.booking.hotel_id)
        billed_entity_accounts = invoice_accounts_request['billed_entity_accounts']
        (
            hotel_invoice_aggregates,
            invoice_aggregates,
        ) = self.generate_invoices_for_billed_entity_accounts(
            bill_aggregate, billed_entity_accounts, booking_aggregate, hotel_context
        )
        self.bill_repository.update(bill_aggregate)
        self.invoice_repository.save_all(invoice_aggregates)
        self.invoice_repository.save_all(hotel_invoice_aggregates)

        self.job_scheduler_service.schedule_invoice_upload(
            bill_aggregate, invoice_aggregates, send_invoices_to_guest=False
        )

        IntegrationEventApplicationService.create_invoice_event(
            event_type=IntegrationEventType.INVOICE_GENERATED,
            invoice_aggregates=invoice_aggregates,
            user_action="generate_invoice_accounts",
        )

        return invoice_aggregates

    def generate_invoices_for_billed_entity_accounts(
        self,
        bill_aggregate,
        billed_entity_accounts,
        booking_aggregate,
        hotel_context,
        generate_hotel_side=True,
        generate_invoice_for_auto_funding=False,
    ):
        for bea in billed_entity_accounts:
            bill_summary_dto = bill_aggregate.get_account_summary(bea)
            if bill_summary_dto.balance > 0:
                raise ValidationException(
                    error=ApplicationErrors.PAYMENT_PENDING_FOR_CHECKOUT
                )
        if generate_invoice_for_auto_funding:
            issued_by_type = IssuedByType.HOTEL
        else:
            issued_by_type, _ = InvoiceIssuerService.get_issuer_type_for_new_invoice(
                bill_aggregate.bill.bill_id,
                hotel_context.hotel_id,
                self.invoice_repository,
                self.catalog_service_client,
                booking_aggregate,
                self.tenant_settings,
            )
        issued_by = InvoiceIssuerService.get_issuer(
            issued_by_type,
            hotel_context.build_vendor_details(),
            self.reseller_gst_detail_repository.load(hotel_context.legal_state_id),
            catalog_service_client=self.catalog_service_client,
        )
        sku_category_aggregates = self.sku_category_repository.load_all()
        grouped_sku_categories = {
            aggregate.sku_category.sku_category_id: aggregate.sku_category
            for aggregate in sku_category_aggregates
        }
        billed_entity_account_wise_charge_map = (
            bill_aggregate.get_charge_and_split_ids_for_billed_entity_accounts(
                billed_entity_accounts
            )
        )
        charge_ids = []
        for charges_map in billed_entity_account_wise_charge_map.values():
            for charge_id, charge_split_id in charges_map.items():
                charge_ids.append(charge_id)
        ConsumeChargeCommand(
            booking_aggregate,
            bill_aggregate,
            hotel_context.current_date(),
            crs_context.get_hotel_context(),
            self.room_type_repository.load_type_map(),
            is_checkout=True,
        ).execute(charge_ids)
        billed_entity_ids = [bea.billed_entity_id for bea in billed_entity_accounts]
        bill_aggregate.post_eligible_payments(
            current_business_date=hotel_context.current_date(),
            billed_entity_ids=billed_entity_ids,
        )
        for charge in bill_aggregate.get_charges(charge_ids):
            if charge.status == ChargeStatus.CREATED:
                raise ValidationException(
                    error=ApplicationErrors.CANCEL_OR_MOVE_BOOKED_CHARGES_TO_OTHER_ACCOUNT
                )
        billed_entity_account_wise_charge_map = (
            preview_invoice_helpers.build_charge_map_to_be_invoiced(
                booking_aggregate,
                bill_aggregate,
                billed_entity_accounts=billed_entity_accounts,
            )
        )
        # As per product, we should fail the process, if any account can't be invoiced, due to non-checked out room
        # charge assigned to it
        if booking_aggregate.booking.status != BookingStatus.CHECKED_OUT:
            if len(billed_entity_accounts) != len(
                [bea for bea in billed_entity_account_wise_charge_map.keys()]
                if billed_entity_account_wise_charge_map
                else []
            ):
                raise ValidationException(
                    error=ApplicationErrors.INVOICE_ACCOUNT_AVAILABLE_ON_CHECKOUT
                )
        (
            invoice_aggregates,
            hotel_invoice_aggregates,
        ) = self.generate_confirmed_invoices_for_billed_entity_accounts(
            bill_aggregate,
            billed_entity_account_wise_charge_map,
            booking_aggregate,
            grouped_sku_categories,
            hotel_context,
            issued_by,
            issued_by_type,
            generate_hotel_side=generate_hotel_side,
        )
        return hotel_invoice_aggregates, invoice_aggregates
