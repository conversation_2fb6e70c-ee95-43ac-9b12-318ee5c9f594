import logging

from object_registry import register_instance
from prometheus import crs_context
from prometheus.application.audit_trail.decorator import audit
from prometheus.application.booking.command_handlers.create_addon import (
    CreateAddonCommandHandler,
)
from prometheus.application.booking.helpers.expense_service import (
    ExpenseApplicationService,
)
from prometheus.application.booking.helpers.inclusion_charge_service import (
    InclusionChargeService,
)
from prometheus.application.decorators import session_manager, set_hotel_context
from prometheus.application.hotel_settings.tenant_settings import TenantSettings
from prometheus.application.services.integration_event_application_service import (
    IntegrationEventApplicationService,
)
from prometheus.application.services.room_linked_addon_charge_service import (
    RoomLinkedAddonChargeService,
)
from prometheus.domain.billing.repositories.bill_repository import BillRepository
from prometheus.domain.booking.repositories import AddonRepository
from prometheus.domain.booking.repositories.booking_repository import BookingRepository
from prometheus.domain.booking.repositories.expense_item_repository import (
    ExpenseItemRepository,
)
from prometheus.domain.catalog.repositories import SkuCategoryRepository
from prometheus.domain.catalog.repositories.hotel_repository import HotelRepository
from prometheus.domain.catalog.repositories.room_type_repository import (
    RoomTypeRepository,
)
from prometheus.domain.hotel_config.repository import HotelConfigRepository
from ths_common.constants.audit_trail_constants import AuditType

logger = logging.getLogger(__name__)


@register_instance(
    dependencies=[
        BookingRepository,
        BillRepository,
        HotelRepository,
        RoomTypeRepository,
        ExpenseItemRepository,
        HotelConfigRepository,
        SkuCategoryRepository,
        RoomLinkedAddonChargeService,
        InclusionChargeService,
        ExpenseApplicationService,
        AddonRepository,
        TenantSettings,
    ]
)
class BulkCreateAddonCommandHandler(CreateAddonCommandHandler):
    @session_manager(commit=True)
    @audit(audit_type=AuditType.ADDON_CREATED)
    @set_hotel_context()
    def handle(self, booking_id, addons_data, user_data=None, hotel_aggregate=None):
        hotel_context = crs_context.get_hotel_context() if hotel_aggregate else None
        booking_aggregate = self.booking_repository.load_for_update(booking_id)

        if not hotel_aggregate:
            (
                hotel_aggregate,
                _,
                hotel_context,
            ) = self._set_hotel_context(booking_aggregate.booking.hotel_id)

        crs_context.set_current_booking(booking_aggregate)

        bill_aggregate = self.bill_repository.load_for_update(
            booking_aggregate.booking.bill_id
        )
        all_expense_items = self.expense_item_repository.load_all(include_linked=True)

        sku_category_aggregates = self.sku_category_repository.load_all()
        grouped_sku_categories = {
            aggregate.sku_category.sku_category_id: aggregate.sku_category
            for aggregate in sku_category_aggregates
        }
        addon_aggregates = []
        for addon_data in addons_data['addons']:
            if addon_data.get('sku_id'):
                expense_item = next(
                    ex
                    for ex in all_expense_items
                    if ex.sku_id == addon_data.get('sku_id')
                )
            else:
                expense_item = next(
                    ex
                    for ex in all_expense_items
                    if ex.expense_item_id == addon_data.get('expense_item_id')
                )

            addon_aggregate = self._add_addon(
                addon_data,
                bill_aggregate,
                booking_aggregate,
                hotel_aggregate,
                hotel_context,
                user_data,
                expense_item,
                grouped_sku_categories.get(expense_item.sku_category_id),
            )
            addon_aggregates.append(addon_aggregate)

        self.addon_repository.save_all(addon_aggregates)
        self.booking_repository.update(booking_aggregate)
        self.bill_repository.update(bill_aggregate)
        IntegrationEventApplicationService.create_booking_updated_event(
            booking_aggregate=booking_aggregate,
            bill_aggregate=bill_aggregate,
            user_action="add_new_addon",
        )
        return addon_aggregates
