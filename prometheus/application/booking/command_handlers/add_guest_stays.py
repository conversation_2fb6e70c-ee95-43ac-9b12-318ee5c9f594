import logging

from object_registry import register_instance
from prometheus import crs_context
from prometheus.application import crs_context_middleware
from prometheus.application.audit_trail.decorator import audit
from prometheus.application.booking.command_handlers.add_guest_stay import (
    AddGuestStayCommandHandler,
)
from prometheus.application.booking.helpers.add_guest_stay import AddGuestStayCommand
from prometheus.application.booking.helpers.billed_entity_service import (
    BilledEntityService,
)
from prometheus.application.booking.helpers.e_reg_card_template_service import (
    ERegCardTemplateService,
)
from prometheus.application.booking.helpers.room_stay_price_change_handler import (
    RoomStayPriceChangeHandler,
)
from prometheus.application.booking.helpers.web_checkin_service import WebCheckinService
from prometheus.application.decorators import session_manager, set_hotel_context
from prometheus.application.helpers.ta_commission_helper import TACommissionHelper
from prometheus.application.services.integration_event_application_service import (
    IntegrationEventApplicationService,
)
from prometheus.domain.billing.repositories.bill_repository import BillRepository
from prometheus.domain.billing.services import ChargeEditService
from prometheus.domain.billing.services.tax_service import TaxService
from prometheus.domain.booking.repositories.booking_repository import BookingRepository
from ths_common.constants.audit_trail_constants import AuditType

logger = logging.getLogger(__name__)


@register_instance(
    dependencies=[
        BookingRepository,
        BillRepository,
        RoomStayPriceChangeHandler,
        TaxService,
        BilledEntityService,
        ChargeEditService,
        WebCheckinService,
        ERegCardTemplateService,
        TACommissionHelper,
    ]
)
class AddGuestStaysCommandHandler(AddGuestStayCommandHandler):
    @session_manager(commit=True)
    @audit(audit_type=AuditType.GUEST_STAY_ADDED)
    @set_hotel_context()
    def handle(
        self,
        booking_id,
        room_stay_id,
        booking_version,
        new_guest_stays: dict,
        user_data,
        hotel_aggregate=None,
    ):
        booking_aggregate = self.booking_repository.load_for_update(
            booking_id, user_data=user_data
        )
        booking_aggregate.fail_if_outdated_version(booking_version)
        room_stay = booking_aggregate.get_room_stay(room_stay_id)

        if not hotel_aggregate:
            hotel_aggregate = crs_context_middleware.set_hotel_context(
                booking_aggregate.booking.hotel_id
            )

        hotel_context = crs_context.get_hotel_context()

        bill_aggregate = self.bill_repository.load_for_update(
            booking_aggregate.booking.bill_id
        )

        crs_context.set_current_booking(booking_aggregate)
        crs_context.set_current_bill(bill_aggregate)
        rate_plan_inclusions = (
            new_guest_stays.get('rate_plan_inclusions')
            if new_guest_stays.get('rate_plan_inclusions')
            else None
        )

        guest_stays = AddGuestStayCommand(
            booking_aggregate,
            room_stay,
            new_guest_stays["guest_stays"],
            new_guest_stays['new_room_stay_prices'],
            bill_aggregate,
            hotel_aggregate,
            user_data,
            hotel_context,
            self.tax_service,
            self.billed_entity_service,
            self.charge_edit_service,
            self.room_stay_price_change_handler,
            rate_plan_inclusions=rate_plan_inclusions,
        ).execute()

        if (
            crs_context.is_treebo_tenant()
            and booking_aggregate.should_calculate_ta_commission()
        ):
            self.ta_commission_helper.recalculate_commission_for_room_stays(
                booking_aggregate, [room_stay], bill_aggregate
            )

        self.booking_repository.update(booking_aggregate)
        self.bill_repository.update(bill_aggregate)

        IntegrationEventApplicationService.create_booking_updated_event(
            booking_aggregate=booking_aggregate,
            bill_aggregate=bill_aggregate,
            user_action="add_guest_stay",
        )
        self.web_checkin_application_service.reject_completed_web_checkins(
            booking_id=booking_id
        )
        self.eregcard_template_service.schedule_eregcard_url_generation(
            booking_aggregate.booking_id,
            hotel_aggregate.hotel_id,
            customer_ids=booking_aggregate.get_customer_ids_for_room_stays(
                [room_stay_id]
            ),
        )
        return guest_stays, booking_aggregate.current_version()
