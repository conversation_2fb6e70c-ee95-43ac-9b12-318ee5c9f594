from decimal import Decimal
from typing import List


class RoomStayCancellationChargeDto:
    def __init__(self, room_stay_id: int, cancellation_charge: Decimal):
        self.room_stay_id = room_stay_id
        self.cancellation_charge = cancellation_charge


class CancellationChargeResponseDto:
    def __init__(
        self,
        cancellation_charge: Decimal,
        room_stay_cancellation_charges: List[RoomStayCancellationChargeDto],
    ):
        self.cancellation_charge = cancellation_charge
        self.room_stay_cancellation_charges = room_stay_cancellation_charges


class CancellationAndRefundAmountDetailsDto:
    def __init__(self, cancellation_charge, refund_amount):
        self.cancellation_charge = cancellation_charge
        self.refund_amount = refund_amount


class CancellationPolicyDetailsDto:
    def __init__(
        self,
        policy: str,
        cancellation_and_refund_amount_details: CancellationAndRefundAmountDetailsDto,
    ):
        self.policy = policy
        self.cancellation_and_refund_amount_details = (
            cancellation_and_refund_amount_details
        )


class CancellationChargeResponseV2Dto:
    def __init__(self, cancellation_policy_details: List[CancellationPolicyDetailsDto]):
        self.cancellation_policy_details = cancellation_policy_details
