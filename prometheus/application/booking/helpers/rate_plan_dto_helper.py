from object_registry import register_instance
from prometheus.application.hotel_settings.tenant_settings import TenantSettings
from prometheus.domain.booking.aggregates.booking_aggregate import BookingAggregate
from prometheus.domain.booking.dtos.rate_plan_dtos import RatePlanDTO, RatePlanPolicies
from shared_kernel.infrastructure.external_clients.rate_manager_client import (
    RateManagerClient,
)
from ths_common.constants.booking_constants import BookingChannels, RatePlanCodes
from ths_common.constants.tenant_settings_constants import TenantSettingName


@register_instance(dependencies=[RateManagerClient, TenantSettings])
class RatePlanDTOHelperService:
    def __init__(
        self,
        rate_manager_client,
        tenant_settings,
    ):
        self.rate_manager_client = rate_manager_client
        self.tenant_settings = tenant_settings

    def _is_su_bulk_booking(
        self, booking_aggregate: BookingAggregate, no_of_rooms
    ) -> bool:
        acq_configs = self.tenant_settings.get_acq_config_for_hotel(
            booking_aggregate.booking.hotel_id
        )
        is_acq_enabled_for_su_booking = (
            acq_configs.get(TenantSettingName.IS_ACQ_ENABLED_FOR_SU.value, False)
            if acq_configs
            else False
        )
        default_threshold = 3
        bulk_rate_plan_room_threshold = (
            acq_configs.get(
                TenantSettingName.ROOMS_COUNT_FOR_BULK_RATE_PLAN_APPLICABILITY.value, {}
            ).get(BookingChannels.OTA.value, default_threshold)
            if acq_configs
            else default_threshold
        )
        return (
            booking_aggregate.is_su_booking()
            and not booking_aggregate.is_b2b_booking()
            and is_acq_enabled_for_su_booking
            and no_of_rooms >= bulk_rate_plan_room_threshold
        )

    def _get_policies_for_bulk_rate_plan(self, booking_aggregate):
        rate_plans = self.rate_manager_client.get_rate_plans(
            booking_aggregate.booking.hotel_id,
            short_codes=RatePlanCodes.TRB_DEFAULT_BULK.value,
        )
        return rate_plans[0].policies if rate_plans else None

    def apply_rate_plans_dto_corrections_if_needed(
        self,
        booking_aggregate: BookingAggregate,
        rate_plan_dtos: [RatePlanDTO],
        no_of_rooms=None,
    ):
        if not no_of_rooms:
            no_of_rooms = len(booking_aggregate.get_active_room_stays())
        if self._is_su_bulk_booking(booking_aggregate, no_of_rooms):
            policies = self._get_policies_for_bulk_rate_plan(booking_aggregate)
            if policies:
                for rate_plan_dto in rate_plan_dtos:
                    rate_plan_dto.override_policies(
                        RatePlanPolicies.from_json(policies)
                    )
        return
