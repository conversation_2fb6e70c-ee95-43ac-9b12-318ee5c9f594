from treebo_commons.utils import dateutils

from prometheus.common.error_codes.errors import ApplicationErrors
from prometheus.domain.booking.exceptions import PriceError
from ths_common.exceptions import ValidationException


def validate_rate_plan_references(
    prices, room_level_rate_plan_reference_id=None, is_rate_plan_ref_mandatory=False
):
    rate_plan_reference_ids_in_price = [
        price.rate_plan_reference_id
        for price in prices
        if price.rate_plan_reference_id is not None
    ]
    rate_plan_reference_ids_in_price = set(rate_plan_reference_ids_in_price)
    if len(rate_plan_reference_ids_in_price) == 1:
        if (
            room_level_rate_plan_reference_id is not None
            and room_level_rate_plan_reference_id
            not in rate_plan_reference_ids_in_price
        ):
            raise PriceError(
                description='Mismatch in rate_plan_reference_id in price and room level'
            )
    elif len(rate_plan_reference_ids_in_price) > 1:
        raise PriceError(
            description='Multiple rate_plan_reference_id provided in price'
        )

    if (
        is_rate_plan_ref_mandatory
        and room_level_rate_plan_reference_id is None
        and len(rate_plan_reference_ids_in_price) == 0
    ):
        raise ValidationException(ApplicationErrors.RATE_PLAN_REFERENCE_ID_MISSING)


def validate_rate_plan_and_inclusions(
    prices, rate_plan_inclusions, room_level_rate_plan_reference_id=None
):
    rate_plan_reference_ids_in_price = [
        price.rate_plan_reference_id
        for price in prices
        if price.rate_plan_reference_id is not None
    ]
    rate_plan_reference_ids_in_price = set(rate_plan_reference_ids_in_price)
    if (
        room_level_rate_plan_reference_id is None
        and len(rate_plan_reference_ids_in_price) == 0
    ):
        if rate_plan_inclusions is not None and len(rate_plan_inclusions) > 0:
            raise ValidationException(
                ApplicationErrors.RATE_PLAN_INCLUSION_PROVIDED_WITHOUT_RATE_PLAN_REF
            )


def validate_inclusion_with_duration(rate_plan_inclusions, checkin_date, checkout_date):
    if not rate_plan_inclusions:
        return
    for rate_plan_inclusion in rate_plan_inclusions:
        if (
            dateutils.to_date(checkin_date) > rate_plan_inclusion.start_date
            or dateutils.to_date(checkout_date) <= rate_plan_inclusion.end_date
        ):
            raise ValidationException(
                ApplicationErrors.RATE_PLAN_INCLUSION_OUT_OF_STAY_DURATION
            )
