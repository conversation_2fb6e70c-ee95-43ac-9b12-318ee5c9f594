from prometheus.domain import PaymentEventData, PaymentSplitEventData
from prometheus.domain.billing.entities.payment import Payment


def create_payment_event(payment: Payment):
    return PaymentEventData(
        payment.payment_id,
        payment.paid_by,
        payment.paid_to,
        payment.payment_mode,
        payment.payment_mode_sub_type,
        payment.amount,
        payment.date_of_payment,
        payment.payment_type,
        payment.amount_in_payment_currency,
        payment.payer,
        payment.payment_ref_id,
        payment.comment,
        payment.confirmed,
        payment_splits=[
            PaymentSplitEventData(
                payment_split_id=ps.payment_split_id,
                billed_entity_account=ps.billed_entity_account,
                amount=ps.amount,
            )
            for ps in payment.payment_splits
        ],
        payor_billed_entity_id=payment.payor_billed_entity_id,
    )
