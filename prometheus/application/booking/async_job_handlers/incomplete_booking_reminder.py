import logging

import sentry_sdk
from treebo_commons.utils import dateutils

from object_registry import register_instance
from prometheus.application.decorators import session_manager
from prometheus.application.hotel_settings.tenant_settings import TenantSettings
from prometheus.application.services.alerts.incomplete_booking_alert_service import (
    IncompleteBookingAlertService,
)
from prometheus.async_job.job_result_dto import JobResultDto
from prometheus.async_job.job_scheduler_service import JobSchedulerService
from prometheus.domain.booking.repositories import BookingRepository
from prometheus.domain.booking.services.b2b_booking_issues_service import (
    B2BBookingIssues,
)
from prometheus.domain.catalog.repositories import HotelRepository
from ths_common.constants.booking_constants import (
    BookingApplications,
    BookingChannels,
    BookingStatus,
)
from ths_common.constants.tenant_settings_constants import TenantSettingName

logger = logging.getLogger(__name__)


@register_instance(
    dependencies=[
        BookingRepository,
        HotelRepository,
        IncompleteBookingAlertService,
        JobSchedulerService,
        TenantSettings,
    ]
)
class IncompleteBookingHandler(object):
    def __init__(
        self,
        booking_repository: BookingRepository,
        hotel_repository: HotelRepository,
        incomplete_booking_alert_service: IncompleteBookingAlertService,
        job_scheduler_service: JobSchedulerService,
        tenant_settings: TenantSettings,
    ):
        self.booking_repository = booking_repository
        self.hotel_repository = hotel_repository
        self.incomplete_booking_alert_service = incomplete_booking_alert_service
        self.job_scheduler_service = job_scheduler_service
        self.tenant_settings = tenant_settings

    @session_manager(commit=True)
    def send_and_schedule_incomplete_booking_communication(
        self, booking_id, schedule_slack_reminder
    ) -> JobResultDto:
        """
        Send incomplete booking details communication to POC via email
        Args:
            booking_id (str): The booking ID.
            schedule_slack_reminder (bool): schedule slack reminder or not
        Returns:
            JobResultDto
        """
        try:
            booking_aggregate = self.booking_repository.load(booking_id)
            hotel_aggregate = self.hotel_repository.load(
                booking_aggregate.booking.hotel_id
            )
            if not self.is_eligible_for_b2b_alerts(booking_aggregate.booking):
                return JobResultDto.success()
            (
                issues,
                billing_entity,
            ) = self.incomplete_booking_alert_service.check_for_potential_issues_with_b2b_booking(
                booking_aggregate, hotel_aggregate
            )
            if issues:
                booker_entity = self.incomplete_booking_alert_service.get_booker_entity_company_profile(
                    booking_aggregate.booking, billing_entity
                )
                self.incomplete_booking_alert_service.construct_and_send_email_for_incomplete_booking(
                    booking_aggregate.booking,
                    booker_entity,
                    issues,
                    hotel_aggregate.hotel,
                )
                if schedule_slack_reminder:
                    self.job_scheduler_service.schedule_incomplete_booking_reminder(
                        hotel_aggregate, booking_aggregate
                    )
            return JobResultDto.success()
        except Exception as e:
            logger.info(
                "Error: %s while sending incomplete booking communication for booking_id: %s",
                str(e),
                booking_id,
            )
            sentry_sdk.capture_exception(e)

    @session_manager(commit=True)
    def send_incomplete_booking_reminder(self, booking_id) -> JobResultDto:
        """
        Send incomplete booking details reminder communication to POC via email
        Args:
            booking_id (str): The booking ID.
        Returns:
            JobResultDto
        """
        try:
            booking_aggregate = self.booking_repository.load(booking_id)
            if not self.is_eligible_for_b2b_alerts(booking_aggregate.booking) or (
                booking_aggregate.booking.extra_information or dict()
            ).get('incomplete_booking_reminder_stopped'):
                return JobResultDto.success()
            hotel_aggregate = self.hotel_repository.load(
                booking_aggregate.booking.hotel_id
            )
            (
                issues,
                billing_entity,
            ) = self.incomplete_booking_alert_service.check_for_potential_issues_with_b2b_booking(
                booking_aggregate, hotel_aggregate
            )
            if issues:
                booker_entity = self.incomplete_booking_alert_service.get_booker_entity_company_profile(
                    booking_aggregate.booking, billing_entity
                )
                if B2BBookingIssues.MULTIPLE_GSTIN_ADDRESSES in issues:
                    del issues[B2BBookingIssues.MULTIPLE_GSTIN_ADDRESSES]
                poc_alerted = (
                    self.incomplete_booking_alert_service.send_incomplete_booking_alert(
                        booking_aggregate, issues, booker_entity
                    )
                )
                if poc_alerted:
                    self.job_scheduler_service.schedule_incomplete_booking_reminder(
                        hotel_aggregate, booking_aggregate
                    )

            return JobResultDto.success()
        except Exception as e:
            logger.error(e)
            logger.info(
                "Error %s while sending incomplete booking reminder for booking_id: %s",
                str(e),
                booking_id,
            )
            sentry_sdk.capture_exception(e)

    def is_eligible_for_b2b_alerts(self, booking):
        return (
            booking.source.channel_code
            in [
                BookingChannels.B2B.value,
                BookingChannels.TREEBO_INTERNAL.value,
                BookingChannels.TA.value,
            ]
            and booking.status
            not in [
                BookingStatus.CHECKED_OUT,
                BookingStatus.CANCELLED,
                BookingStatus.NOSHOW,
            ]
            and booking.source.application_code != BookingApplications.SU.value
            and self.is_booker_eligible_for_b2b_alerts(booking)
        )

    def is_booker_eligible_for_b2b_alerts(self, booking):
        booker_details = booking.travel_agent_details or booking.company_details
        if not booker_details:
            return False
        external_reference_id = booker_details.legal_details.external_reference_id
        disabled_booker_ids = (
            self.tenant_settings.get_setting_value(
                TenantSettingName.BOOKER_IDS_DISABLED_FOR_B2B_AlERTS.value
            )
            or []
        )
        return external_reference_id not in disabled_booker_ids

    @session_manager(commit=True)
    def schedule_incomplete_booking_communication_if_eligible(
        self,
        booking_aggregate,
        hotel_aggregate,
    ):
        if self.is_eligible_for_b2b_alerts(booking_aggregate.booking):
            self.job_scheduler_service.schedule_incomplete_booking_communication(
                hotel_aggregate,
                booking_aggregate,
                eta=dateutils.add(dateutils.current_datetime(), hours=2),
            )
            # Schedule Extra Email Alert if Booking ABW>=10
            # ETA should be 3 Days before the checkin At 11:00 AM
            # Don't Schedule the Slack Reminder in case of this Job
            if (
                booking_aggregate.booking.checkin_date
                - booking_aggregate.booking.created_at
            ).days >= 10:
                self.job_scheduler_service.schedule_incomplete_booking_communication(
                    hotel_aggregate=hotel_aggregate,
                    booking_aggregate=booking_aggregate,
                    eta=dateutils.add(
                        dateutils.datetime_at_midnight(
                            dateutils.subtract(
                                booking_aggregate.booking.checkin_date, days=3
                            )
                        ),
                        hours=11,
                    ),
                    schedule_slack_reminder=False,
                )
