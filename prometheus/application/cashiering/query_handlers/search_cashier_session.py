from object_registry import register_instance
from prometheus.application.cashiering.helpers.auth_helper import (
    fail_if_user_not_authorized_to_access_cashier_module,
)
from prometheus.application.decorators import set_hotel_context
from prometheus.common.error_codes.errors import ApplicationErrors
from prometheus.domain.billing.dto.cashier_session_search_query import (
    CashierSessionSearchQuery,
)
from prometheus.domain.billing.repositories import CashierSessionRepository
from prometheus.domain.billing.repositories.cash_register_repository import (
    CashRegisterRepository,
)
from prometheus.infrastructure.external_clients.aws_service_client import (
    AwsServiceClient,
)
from ths_common.exceptions import ValidationException


@register_instance(
    dependencies=[CashierSessionRepository, CashRegisterRepository, AwsServiceClient]
)
class SearchCashierSessionQueryHandler:
    DefaultSignedUrlExpirationSeconds = 7200

    def __init__(
        self,
        cashier_session_repository: CashierSessionRepository,
        cash_register_repository: CashRegisterRepository,
        aws_service_client,
    ):
        self.cashier_session_repository = cashier_session_repository
        self.cash_register_repository = cash_register_repository
        self.aws_service_client = aws_service_client

    @set_hotel_context()
    def handle(
        self,
        cash_register_id,
        query: CashierSessionSearchQuery,
        user_data,
        hotel_aggregate=None,
    ):
        fail_if_user_not_authorized_to_access_cashier_module(user_data)
        cash_register = self.cash_register_repository.load(cash_register_id)
        if cash_register.cash_register.vendor_id != query.vendor_id:
            raise ValidationException(
                ApplicationErrors.NO_CASHIER_SESSION_FOUND_ON_HOTEL
            )
        query.cash_register_ids = [cash_register_id]
        cashier_session_aggregates = self.cashier_session_repository.search(query)
        cashier_payments = []
        for cashier_session_aggregate in cashier_session_aggregates:
            cashier_payments.extend(cashier_session_aggregate.payments)
        cashier_payments = self._attach_payments_signed_url(cashier_payments)
        return cashier_session_aggregates

    def _attach_payments_signed_url(self, cashier_payments):
        for cashier_payment in cashier_payments:
            if cashier_payment.voucher_url:
                cashier_payment.set_voucher_signed_url(
                    self.aws_service_client.get_presigned_url_from_s3_url(
                        cashier_payment.voucher_url,
                        link_expires_in=self.DefaultSignedUrlExpirationSeconds,
                    )
                )
        return cashier_payments
