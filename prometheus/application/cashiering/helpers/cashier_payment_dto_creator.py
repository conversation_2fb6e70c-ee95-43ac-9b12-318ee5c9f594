from prometheus.domain.billing.aggregates.bill_aggregate import BillAggregate
from prometheus.domain.billing.dto.cash_counter_payment_data import CashierPaymentData
from prometheus.domain.billing.entities.payment import Payment
from prometheus.domain.booking.aggregates.booking_aggregate import BookingAggregate
from ths_common.constants.billing_constants import CashierPaymentTypes, PaymentTypes


def create_cashier_payment_dto(
    bill_aggregate: BillAggregate, booking_aggregate: BookingAggregate, payment: Payment
):
    booking_owner_name = (
        str(booking_aggregate.get_booking_owner().name) if booking_aggregate else None
    )
    payment_type = (
        CashierPaymentTypes.INFLOW
        if payment.payment_type == PaymentTypes.PAYMENT
        else CashierPaymentTypes.OUTFLOW
    )
    amount_in_payment_currency = (
        payment.amount_in_payment_currency
        if payment.amount_in_payment_currency
        else payment.amount
    )
    payment_dict = dict(
        date_of_payment=payment.date_of_payment,
        payment_mode=payment.payment_mode,
        payment_type=payment_type,
        payment_details=payment.payment_details,
        status=payment.status.value,
        paid_to=payment.paid_to,
        comment=payment.comment,
        amount=payment.amount,
        amount_in_payment_currency=amount_in_payment_currency,
        booking_id=booking_aggregate.booking_id if booking_aggregate else None,
        booking_reference_number=booking_aggregate.booking.reference_number
        if booking_aggregate
        else None,
        payment_mode_sub_type=payment.payment_mode_sub_type,
        pos_order_id=None,
        booking_owner_name=booking_owner_name,
        bill_id=bill_aggregate.bill_id,
        bill_payment_id=payment.payment_id,
    )
    payment_dto = CashierPaymentData.from_dict(payment_dict)
    return payment_dto
