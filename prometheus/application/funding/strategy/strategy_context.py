from prometheus.application.funding.strategy.equal_distribution_strategy import (
    EqualDistributionStrategy,
)
from prometheus.application.funding.strategy.lowest_first_strategy import (
    LowestFirstStrategy,
)
from ths_common.constants.funding_constants import FundingStrategy


class AutoFundingStrategyContext:
    @staticmethod
    def get_strategy(level):
        """Returns the appropriate strategy based on the level."""
        if level == FundingStrategy.LOWEST_FIRST.value:
            return LowestFirstStrategy()
        elif level == FundingStrategy.EQUAL_DISTRIBUTION.value:
            return EqualDistributionStrategy()
        else:
            raise ValueError(f"Invalid strategy level: {level}")
