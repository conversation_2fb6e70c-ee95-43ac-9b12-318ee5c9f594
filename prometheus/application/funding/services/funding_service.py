from object_registry import register_instance
from prometheus import crs_context
from prometheus.application.booking.helpers.billed_entity_service import (
    BilledEntityService,
)
from prometheus.application.funding.funding_amount_calculator_helper import (
    FundingAmountCalculatorHelper,
)
from prometheus.application.funding.strategy.strategy_context import (
    AutoFundingStrategyContext,
)
from prometheus.application.hotel_settings.tenant_settings import TenantSettings
from prometheus.domain.booking.entities.booking_funding import BookingFundingRequest
from prometheus.domain.booking.repositories.booking_funding_repository import (
    BookingFundingRepository,
)
from prometheus.domain.catalog.repositories import ResellerGstRepository
from prometheus.infrastructure.external_clients.company_profile_service_client import (
    CompanyProfileServiceClient,
)
from ths_common.constants.funding_constants import (
    TREEBO_EMAIL_ID,
    TREEBO_FUNDING_CUSTOMER_LEGAL_NAME,
    TREEBO_PHONE_NUMBER,
    FundingStatus,
    FundingStrategy,
    FundingType,
)
from ths_common.value_objects import Address, GSTDetails


@register_instance(
    dependencies=[
        BookingFundingRepository,
        FundingAmountCalculatorHelper,
        ResellerGstRepository,
        BilledEntityService,
        CompanyProfileServiceClient,
        TenantSettings,
    ]
)
class FundingService:
    def __init__(
        self,
        booking_funding_repository: BookingFundingRepository,
        funding_amount_calculator_helper: FundingAmountCalculatorHelper,
        reseller_gst_repository: ResellerGstRepository,
        billed_entity_service: BilledEntityService,
        company_profile_service_client: CompanyProfileServiceClient,
        tenant_settings: TenantSettings,
    ):
        self.booking_funding_repository = booking_funding_repository
        self.funding_amount_calculator_helper = funding_amount_calculator_helper
        self.reseller_gst_repository = reseller_gst_repository
        self.billed_entity_service = billed_entity_service
        self.company_profile_service_client = company_profile_service_client
        self.tenant_settings = tenant_settings

    def calculate_amount_for_funding(self, booking_aggregate, bill_aggregate):
        funding_amount_details = (
            self.funding_amount_calculator_helper.get_funding_amounts(booking_aggregate)
        )
        booking_funding_config = self.booking_funding_repository.load_funding_config(
            booking_id=booking_aggregate.booking_id
        )
        strategy_to_be_used = self._get_strategy(funding_amount_details)
        strategy = AutoFundingStrategyContext.get_strategy(strategy_to_be_used)
        return strategy.calculate_funding_amount(
            booking_aggregate,
            bill_aggregate,
            funding_amount_details,
            booking_funding_config,
        )

    @staticmethod
    def _get_strategy(funding_amount_details):
        if (
            funding_amount_details.auto_funding_amount
            and not funding_amount_details.manual_funding_amount
        ):
            return FundingStrategy.LOWEST_FIRST.value
        return FundingStrategy.EQUAL_DISTRIBUTION.value

    def create_auto_funding_request(self, booking_aggregate):
        tcp_details = self.funding_amount_calculator_helper.extract_tcp_details(
            booking_aggregate
        )
        if not tcp_details:
            return
        total_funding_amount = sum(tcp_detail.amount for tcp_detail in tcp_details)
        funding_request = BookingFundingRequest(
            booking_id=booking_aggregate.booking.booking_id,
            amount=total_funding_amount,
            funding_type=FundingType.AUTO_FUNDING,
            status=FundingStatus.CREATED,
        )
        self.booking_funding_repository.save_funding_request(funding_request)

    def invalidate_or_lock_funding_requests(self, booking_id, lock=False):
        funding_requests = self.booking_funding_repository.load_all_funding_requests(
            booking_id=booking_id,
            status=FundingStatus.CREATED.value,
        )
        if not funding_requests:
            return
        new_status = FundingStatus.LOCKED if lock else FundingStatus.VOIDED
        for funding_request in funding_requests:
            funding_request.status = new_status
        self.booking_funding_repository.update_funding_requests(funding_requests)

    def set_auto_fund_request_as_refunded(self, booking_id, reason):
        funding_request = self.booking_funding_repository.load_funding_request(
            booking_id=booking_id,
            status=FundingStatus.CREATED.value,
            funding_type=FundingType.AUTO_FUNDING.value,
        )
        if not funding_request:
            return
        funding_request.status = FundingStatus.REFUNDED
        funding_request.reason = reason
        self.booking_funding_repository.update_funding_request(funding_request)

    def add_customer_and_billed_entity_for_auto_funding(
        self, booking_aggregate, bill_aggregate
    ):
        reseller_gst_details = self._get_reseller_gst_details()
        company_detail = self._get_company_by_gstin(reseller_gst_details.gstin_num)

        customer_info = self._build_customer_info(
            reseller_gst_details=reseller_gst_details,
            company_detail=company_detail,
        )

        dummy_customer = booking_aggregate.add_dummy_customer(customer_info)

        self.billed_entity_service.add_billed_entity_for_auto_funding(
            dummy_customer, bill_aggregate
        )

    def _get_reseller_gst_details(self):
        state_id = crs_context.hotel_context.legal_state_id
        return self.reseller_gst_repository.load(state_id=state_id).gst_details

    def _get_company_by_gstin(self, gstin_num):
        if not gstin_num:
            return None

        company_data = (
            self.company_profile_service_client.search_company_profile_by_gstin(
                gstin=gstin_num
            )
        )
        sub_entities = company_data.get("sub_entities") if company_data else None
        return sub_entities[0] if sub_entities else None

    def _build_customer_info(
        self,
        reseller_gst_details,
        company_detail=None,
    ):
        external_ref_id = getattr(company_detail, "superhero_company_code", None)

        address = (
            self._convert_to_thsc_address(company_detail.registered_address)
            if company_detail and company_detail.registered_address
            else reseller_gst_details.address
        )

        if company_detail:
            gst_details = GSTDetails(
                legal_name=TREEBO_FUNDING_CUSTOMER_LEGAL_NAME,
                address=address,
                gstin_num=reseller_gst_details.gstin_num,
                has_lut=getattr(company_detail, "has_lut", False),
                is_sez=getattr(company_detail, "is_sez_applicable", False),
            )
        else:
            gst_details = GSTDetails(
                legal_name=TREEBO_FUNDING_CUSTOMER_LEGAL_NAME,
                address=reseller_gst_details.address,
                gstin_num=reseller_gst_details.gstin_num,
            )

        return {
            "first_name": TREEBO_FUNDING_CUSTOMER_LEGAL_NAME,
            "gst_details": gst_details,
            "email": TREEBO_EMAIL_ID,
            "phone": TREEBO_PHONE_NUMBER,
            "external_ref_id": external_ref_id,
        }

    @staticmethod
    def _convert_to_thsc_address(address):
        return Address(
            field_1=address.address_line_1,
            field_2=address.address_line_2,
            city=address.city,
            state=address.state,
            country=address.country,
            pincode=address.pincode,
        )

    def get_funding_reason(self, booking_id):
        """
        Logic:
            There can't be multiple manual funding or auto funding request.
            If manual funding is present then auto funding won't be applied.
        """
        funding_requests = self.booking_funding_repository.load_all_funding_requests(
            booking_id, status=FundingStatus.CREATED.value
        )

        for funding_request in funding_requests:
            if (
                funding_request.funding_type == FundingType.MANUAL_FUNDING
                and funding_request.amount > 0
            ):
                return funding_request.reason or "Processed Via Manual Funding"

        return "Lower price due to loyalty discount"
