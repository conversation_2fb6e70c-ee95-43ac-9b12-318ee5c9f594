from treebo_commons.money import Money

from prometheus.domain.billing.dto.bill_summary_dto import BillSummaryDto
from prometheus.domain.billing.entities.billed_entity import BilledEntityAccountVO


class NewInvoiceAmountDto(object):
    def __init__(
        self,
        invoice_charge_id: int,
        new_invoice_amount: str,
        pre_tax_amount: str = None,
        posttax_amount: str = None,
        billed_entity_account: BilledEntityAccountVO = None,
    ):
        self.invoice_charge_id = invoice_charge_id
        self.new_invoice_amount = new_invoice_amount
        self.pretax_amount = pre_tax_amount
        self.posttax_amount = posttax_amount
        self.billed_entity_account = billed_entity_account


class InvoiceChargeModificationDto(object):
    def __init__(
        self,
        invoice_charge_id,
        billed_entity_account: BilledEntityAccountVO,
        pretax_amount: Money = None,
        posttax_amount: Money = None,
    ):
        self.invoice_charge_id = invoice_charge_id
        self.billed_entity_account = billed_entity_account
        self.pretax_amount = pretax_amount
        self.posttax_amount = posttax_amount


class ModifyInvoicesDto(object):
    def __init__(
        self,
        invoice_id,
        invoice_version,
        invoice_charge_modification: [InvoiceChargeModificationDto],
    ):
        self.invoice_id = invoice_id
        self.invoice_version = invoice_version
        self.invoice_charge_modification = invoice_charge_modification


class ModifyLockedInvoicesDto(object):
    def __init__(
        self,
        transfer_payment_to_new_invoice_account: bool,
        modify_invoices: [ModifyInvoicesDto],
        comment: str = None,
        attachment_details: dict = None,
    ):
        self.transfer_payment_to_new_invoice_account = (
            transfer_payment_to_new_invoice_account
        )
        self.modify_invoices = modify_invoices
        self.comment = comment
        self.attachment_details = attachment_details


class AccountSummaryDto(object):
    # NOTE: The argument has default None value only because it is used with object_mapper in
    # `dto_mapper.py` module
    def __init__(
        self,
        billed_entity_account: BilledEntityAccountVO = None,
        accounts_summary: BillSummaryDto = None,
        folio_number=None,
    ):
        self.billed_entity_account = billed_entity_account
        self.account_summary = accounts_summary
        self.folio_number = folio_number

    def __eq__(self, other):
        return (
            isinstance(other, AccountSummaryDto)
            and self.billed_entity_account.billed_entity_id
            == other.billed_entity_account.billed_entity_id
            and self.billed_entity_account.account_number
            == other.billed_entity_account.account_number
        )

    def __hash__(self):
        return hash(
            str(self.billed_entity_account.billed_entity_id)
            + '_'
            + str(self.billed_entity_account.account_number)
        )
