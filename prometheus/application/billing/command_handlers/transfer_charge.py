from treebo_commons.utils import dateutils

from object_registry import register_instance
from prometheus import crs_context
from prometheus.application import crs_context_middleware
from prometheus.application.audit_trail.decorator import audit
from prometheus.application.decorators import session_manager
from prometheus.application.helpers.billed_entity_helper import (
    get_room_stay_default_billed_entity,
)
from prometheus.application.helpers.gst_details_helper import (
    get_gst_details_from_billed_entity,
)
from prometheus.application.helpers.ta_commission_helper import TACommissionHelper
from prometheus.application.hotel_settings.tenant_settings import TenantSettings
from prometheus.application.services.integration_event_application_service import (
    IntegrationEventApplicationService,
)
from prometheus.common.error_codes.errors import ApplicationErrors
from prometheus.domain.billing.aggregates.bill_aggregate import BillAggregate
from prometheus.domain.billing.domain_events.event_raiser.bill_event_raiser import (
    BillEventRaiser,
)
from prometheus.domain.billing.dto import ChargeData, ChargeSplitData
from prometheus.domain.billing.dto.allowance_data import AllowanceData
from prometheus.domain.billing.repositories import BillRepository
from prometheus.domain.billing.services import TaxService
from prometheus.domain.billing.services.add_allowance_service import AddAllowanceService
from prometheus.domain.booking.dtos import ExpenseDto
from prometheus.domain.booking.repositories import (
    BookingRepository,
    ExpenseItemRepository,
)
from prometheus.domain.catalog.repositories import (
    RoomTypeRepository,
    SkuCategoryRepository,
)
from prometheus.domain.policy.engine import RuleEngine
from prometheus.domain.policy.facts import Facts
from ths_common.constants.audit_trail_constants import AuditType
from ths_common.constants.billing_constants import BillAppId, ChargeSplitType
from ths_common.constants.booking_constants import ExpenseAddedBy, ExpenseStatus
from ths_common.constants.catalog_constants import SkuName
from ths_common.exceptions import ValidationException
from ths_common.utils.collectionutils import flatten_list
from ths_common.utils.dateutils import get_settlement_date
from ths_common.value_objects import ChargeItem, ExpenseChargeItemDetails


@register_instance(
    dependencies=[
        BillRepository,
        BookingRepository,
        ExpenseItemRepository,
        SkuCategoryRepository,
        RoomTypeRepository,
        TenantSettings,
        AddAllowanceService,
        TaxService,
        TACommissionHelper,
    ]
)
class TransferChargeCommandHandler:
    def __init__(
        self,
        bill_repository: BillRepository,
        booking_repository: BookingRepository,
        expense_item_repository: ExpenseItemRepository,
        sku_category_repository: SkuCategoryRepository,
        room_type_repository: RoomTypeRepository,
        tenant_settings: TenantSettings,
        add_allowance_service: AddAllowanceService,
        tax_service: TaxService,
        ta_commission_helper: TACommissionHelper,
    ):
        self.bill_repository = bill_repository
        self.booking_repository = booking_repository
        self.expense_item_repository = expense_item_repository
        self.sku_category_repository = sku_category_repository
        self.room_type_repository = room_type_repository
        self.tenant_settings = tenant_settings
        self.add_allowance_service = add_allowance_service
        self.tax_service = tax_service
        self.ta_commission_helper = ta_commission_helper

    @session_manager(commit=True)
    def handle(self, bill_id, resource_version, charge_ids, parsed_request, user_data):
        bill_aggregate = self.bill_repository.load_for_update(bill_id, resource_version)
        crs_context.set_user_data(user_data)

        if bill_aggregate.bill.app_id == BillAppId.POS_APP.value:
            raise ValidationException(
                ApplicationErrors.TRANSFER_CHARGE_NOT_ALLOWED_FOR_POS
            )

        booking_aggregate = self.booking_repository.load_booking_by_bill_id(
            bill_aggregate.bill_id, for_update=True
        )
        return self._transfer_charge(
            booking_aggregate,
            bill_aggregate,
            charge_ids,
            parsed_request['booking_id'],
            parsed_request.get('room_stay_id'),
        )

    @audit(audit_type=AuditType.CHARGE_TRANSFERRED)
    def _transfer_charge(
        self,
        source_booking_aggregate,
        source_bill_aggregate,
        charge_ids,
        destination_booking_id,
        destination_room_stay_id,
    ):
        destination_booking_aggregate = self.booking_repository.load_for_update(
            destination_booking_id
        )
        if source_booking_aggregate.hotel_id != destination_booking_aggregate.hotel_id:
            raise ValidationException(
                ApplicationErrors.CHARGE_TRANSFER_ONLY_ALLOWED_TO_BOOKINGS_OF_SAME_HOTEL
            )
        destination_bill_aggregate = self.bill_repository.load_for_update(
            destination_booking_aggregate.bill_id
        )
        crs_context_middleware.set_hotel_context(source_booking_aggregate.hotel_id)
        crs_context.set_current_booking(source_booking_aggregate)

        for charge_id in charge_ids:
            source_charge = source_bill_aggregate.get_charge(charge_id)
            source_charge.validate_transfer_allowed()
            RuleEngine.action_allowed(
                action="transfer_charge",
                facts=Facts(
                    booking_aggregate=source_booking_aggregate,
                    bill_aggregate=source_bill_aggregate,
                    action_payload=dict(charge_id=charge_id),
                ),
                fail_on_error=True,
            )
        rate_plan_charges, extra_charges, pos_charges = self._segregate_charges(
            source_bill_aggregate, charge_ids
        )

        if not self.tenant_settings.club_inclusion_with_room_rate_for_taxation(
            source_booking_aggregate.hotel_id
        ):
            destination_charges = []

            (
                transferred_charges,
                destination_bill_aggregate,
            ) = self._transfer_charge_without_clubbing(
                source_booking_aggregate,
                source_bill_aggregate,
                flatten_list(rate_plan_charges) + extra_charges,
                destination_booking_aggregate,
                destination_bill_aggregate,
                destination_room_stay_id,
            )
            destination_charges.extend(transferred_charges)

            if pos_charges:
                (
                    transferred_charges,
                    destination_bill_aggregate,
                ) = self._transfer_pos_charge(
                    source_booking_aggregate,
                    source_bill_aggregate,
                    pos_charges,
                    destination_booking_aggregate,
                    destination_bill_aggregate,
                    destination_room_stay_id,
                )
                destination_charges.extend(transferred_charges)

        else:
            destination_charges = []

            (
                transferred_charges,
                destination_bill_aggregate,
            ) = self._transfer_charge_after_clubbing(
                source_booking_aggregate,
                source_bill_aggregate,
                rate_plan_charges,
                destination_booking_aggregate,
                destination_bill_aggregate,
                destination_room_stay_id,
            )
            destination_charges.extend(transferred_charges)

            if extra_charges:
                (
                    transferred_charges,
                    destination_bill_aggregate,
                ) = self._transfer_charge_without_clubbing(
                    source_booking_aggregate,
                    source_bill_aggregate,
                    extra_charges,
                    destination_booking_aggregate,
                    destination_bill_aggregate,
                    destination_room_stay_id,
                )
                destination_charges.extend(transferred_charges)

            if pos_charges:
                (
                    transferred_charges,
                    destination_bill_aggregate,
                ) = self._transfer_pos_charge(
                    source_booking_aggregate,
                    source_bill_aggregate,
                    pos_charges,
                    destination_booking_aggregate,
                    destination_bill_aggregate,
                    destination_room_stay_id,
                )
                destination_charges.extend(transferred_charges)
        if (
            flatten_list(rate_plan_charges)
            and crs_context.is_treebo_tenant()
            and source_booking_aggregate.should_calculate_ta_commission()
        ):
            self.ta_commission_helper.recalculate_commission_on_booking(
                source_booking_aggregate, source_bill_aggregate
            )
            self.booking_repository.update(source_booking_aggregate)
        self.bill_repository.update(source_bill_aggregate)
        self.booking_repository.update(destination_booking_aggregate)
        self.bill_repository.update(destination_bill_aggregate)

        IntegrationEventApplicationService.create_bill_updated_event(
            booking_aggregate=source_booking_aggregate,
            bill_aggregate=source_bill_aggregate,
            user_action="charge_transferred",
        )
        IntegrationEventApplicationService.create_bill_updated_event(
            booking_aggregate=destination_booking_aggregate,
            bill_aggregate=destination_bill_aggregate,
            user_action="charge_transferred",
        )

        return destination_charges, destination_bill_aggregate

    @audit(audit_type=AuditType.CHARGE_ADDED)
    def _transfer_source_charge_to_destination_booking(
        self,
        source_charge,
        source_booking_aggregate,
        destination_booking_aggregate,
        destination_bill_aggregate: BillAggregate,
        destination_room_stay_id,
        inclusions,
        is_pos_charge=False,
    ):
        if destination_booking_aggregate.is_closed():
            raise ValidationException(
                ApplicationErrors.CHARGE_TRANSFER_ONLY_ALLOWED_TO_CONFIRMED_OR_CHECKEDIN_BOOKING
            )
        crs_context_middleware.set_hotel_context(destination_booking_aggregate.hotel_id)
        crs_context.set_current_booking(destination_booking_aggregate)
        if destination_room_stay_id:
            destination_room_stay = destination_booking_aggregate.get_room_stay(
                destination_room_stay_id
            )
        else:
            destination_room_stay = (
                destination_booking_aggregate.get_active_room_stays()[0]
            )
        for charge in inclusions + [source_charge]:
            if (
                charge.applicable_date < destination_room_stay.checkin_date
                or charge.applicable_date > destination_room_stay.checkout_date
            ):
                raise ValidationException(
                    ApplicationErrors.CHARGE_TRANSFER_NOT_ALLOWED_WHEN_APPLICABLE_DATE_NOT_IN_STAY_DATE_RANGE
                )
        (
            expense_dto,
            charge_item,
        ) = self._create_expense_dto_and_charge_item_from_source_charge(
            source_charge,
            source_booking_aggregate,
            destination_room_stay,
        )
        sku_category_aggregate = self.sku_category_repository.load(
            charge_item.sku_category_id
        )
        settlement_date = get_settlement_date(
            dateutils.current_datetime(), next_month=True
        )
        expense = destination_booking_aggregate.add_expense(
            expense_dto, settlement_date
        )
        default_billed_entity = get_room_stay_default_billed_entity(
            destination_room_stay,
            destination_booking_aggregate,
            destination_bill_aggregate,
        )
        if self.tenant_settings.hotel_uses_posttax_price(
            crs_context.hotel_context.hotel_id
        ):
            posttax_amount = sum(
                charge.get_posttax_amount_post_allowance()
                for charge in [source_charge] + inclusions
            )
            charge_dto = ChargeData(
                posttax_amount=posttax_amount,
                applicable_date=source_charge.applicable_date,
                comment=expense.comments,
                item=charge_item,
                charge_to=list(destination_room_stay.checked_in_guest_ids())
                or [gs.guest_id for gs in destination_room_stay.active_guest_stays()],
            )
        else:
            pretax_amount = sum(
                charge.get_pretax_amount_post_allowance()
                for charge in [source_charge] + inclusions
            )
            charge_dto = ChargeData(
                pretax_amount=pretax_amount,
                applicable_date=source_charge.applicable_date,
                comment=expense.comments,
                item=charge_item,
                charge_to=list(destination_room_stay.checked_in_guest_ids())
                or [gs.guest_id for gs in destination_room_stay.active_guest_stays()],
            )
        charge_splits = [
            ChargeSplitData(
                percentage=charge_split.percentage,
                billed_entity_account=destination_bill_aggregate.get_billed_entity_account_for_new_assignment(
                    default_billed_entity, charge_split.charge_type
                ),
                charge_type=charge_split.charge_type,
                bill_to_type=charge_split.bill_to_type,
            )
            for charge_split in source_charge.charge_splits
        ]
        charge_dto.charge_splits = charge_splits
        charge_dto.charge_split_type = ChargeSplitType.PERCENTAGE_SPLIT
        tax_updated_charge_dtos = self.tax_service.update_taxes(
            [charge_dto],
            buyer_gst_details=get_gst_details_from_billed_entity(
                destination_booking_aggregate, default_billed_entity
            ),
            seller_has_lut=self.tax_service.seller_has_lut(
                destination_booking_aggregate.booking.seller_model,
                crs_context.get_hotel_context(),
            ),
            hotel_id=crs_context.get_hotel_context().hotel_id,
        )

        added_charges = destination_bill_aggregate.add_charges(
            tax_updated_charge_dtos
        )  # 1 charge only
        added_charge = added_charges[0]
        expense.charge_id = added_charge.charge_id
        destination_bill_aggregate.consume_charge(
            added_charge, crs_context.get_hotel_context().current_date()
        )
        updated_destination_charge_item_details = added_charge.item.details
        updated_source_charge_item_details = source_charge.item.details
        if is_pos_charge:
            updated_destination_charge_item_details.update(
                dict(
                    is_pos_charge=True,
                    pos_order_id=source_charge.item.details.get('pos_order_id', None),
                    pos_order_number=source_charge.item.details.get(
                        'pos_order_number', None
                    ),
                    pos_bill_id=source_charge.item.details.get('pos_bill_id', None),
                    pos_bill_number=source_charge.item.details.get(
                        'pos_bill_number', None
                    ),
                    seller_name=source_charge.item.details.get('seller_name', None),
                )
            )

        updated_destination_charge_item_details.update(
            dict(
                source_booking_id=source_booking_aggregate.booking_id,
                source_charge_id=source_charge.charge_id,
            )
        )
        updated_source_charge_item_details.update(
            dict(
                is_transferred_to_other_booking=True,
            )
        )
        added_charge.update_charge_item_details(updated_destination_charge_item_details)
        source_charge.update_charge_item_details(updated_source_charge_item_details)

        return added_charge

    def _create_expense_dto_and_charge_item_from_source_charge(
        self,
        source_charge,
        source_booking_aggregate,
        destination_room_stay,
    ):
        if source_charge.is_room_rent():
            expense_item = self.expense_item_repository.load(
                expense_item_id='booking_modification'
            )
            destination_expense_dto = ExpenseDto(
                expense_item_id=expense_item.expense_item_id,
                room_stay_id=destination_room_stay.room_stay_id,
                status=ExpenseStatus.CONSUMED,
                added_by=ExpenseAddedBy.TREEBO
                if crs_context.is_treebo_tenant()
                else ExpenseAddedBy.HOTEL,
                created_at=dateutils.current_datetime(),
                comments='Transferred from Booking: %s'
                % source_booking_aggregate.booking.reference_number,
                applicable_date=source_charge.applicable_date,
                sku_id=expense_item.sku_id,
                sku_name=SkuName.TRANSFERRED_CHARGE.value,
                guests=destination_room_stay.checked_in_guest_ids(),
            )
        else:
            expense = source_booking_aggregate.get_expense_for_charge(
                source_charge.charge_id
            )
            if expense.sku_id:
                expense_item = self.expense_item_repository.load(sku_id=expense.sku_id)
            else:
                expense_item = self.expense_item_repository.load(
                    expense_item_id=expense.expense_item_id
                )
            destination_expense_dto = ExpenseDto(
                sku_id=expense.sku_id,
                sku_name=SkuName.TRANSFERRED_CHARGE.value,
                room_stay_id=destination_room_stay.room_stay_id,
                status=expense.status,
                comments='Transferred from Booking: %s'
                % source_booking_aggregate.booking.reference_number,
                added_by=expense.added_by,
                created_at=dateutils.current_datetime(),
                applicable_date=expense.applicable_date,
                expense_item_id=expense.expense_item_id,
                guests=destination_room_stay.checked_in_guest_ids(),
            )
        room_no = (
            destination_room_stay.room_allocation.room_no
            if destination_room_stay.room_allocation
            else None
        )
        room_type = self.room_type_repository.load_type_map()[
            destination_room_stay.room_type_id
        ].room_type.type

        source_item_details = source_charge.item.details
        expense_charge_item_details = ExpenseChargeItemDetails(
            room_stay_id=destination_room_stay.room_stay_id,
            room_no=room_no,
            room_type=room_type,
            room_type_code=destination_room_stay.room_type_id,
        )

        # Conditionally add additional attributes if 'is_touche_pos_charge' is True
        if source_item_details.get('is_touche_pos_charge'):
            expense_charge_item_details.interface_id = source_item_details.get(
                'interface_id'
            )
            expense_charge_item_details.pos_bill_id = source_item_details.get(
                'pos_bill_id'
            )
            expense_charge_item_details.revenue_center = source_item_details.get(
                'revenue_center'
            )
            expense_charge_item_details.waiter_id = source_item_details.get('waiter_id')
            expense_charge_item_details.serving_time = source_item_details.get(
                'serving_time'
            )
            expense_charge_item_details.workstation_id = source_item_details.get(
                'workstation_id'
            )
            expense_charge_item_details.is_touche_pos_charge = source_item_details.get(
                'is_touche_pos_charge'
            )

        destination_charge_item = ChargeItem(
            'Transferred Charge',
            expense_item.sku_category_id,
            expense_charge_item_details,
            item_id=expense_item.expense_item_id,
        )
        return destination_expense_dto, destination_charge_item

    @staticmethod
    def _segregate_charges(source_bill_aggregate, charge_ids):
        rate_plan_charges, extra_charges, pos_charges, grouped_pos_charges = (
            [],
            [],
            [],
            {},
        )
        for charge_id in charge_ids:
            source_charge = source_bill_aggregate.get_charge(charge_id)
            if source_charge.is_room_rent():
                rate_plan_charge = []
                addon_charges = [
                    source_bill_aggregate.get_charge(addon_charge_id)
                    for addon_charge_id in source_charge.addon_charge_ids
                    if charge_id in charge_ids
                ]
                rate_plan_charge.append(source_charge)
                rate_plan_charge.extend(addon_charges)
                rate_plan_charges.append(rate_plan_charge)

            elif source_charge.item.is_pos_charge():
                pos_charges.append(source_charge)

            elif not source_charge.is_inclusion_charge:
                extra_charges.append(source_charge)

        for pos_charge in pos_charges:
            pos_order_id = pos_charge.item.pos_order_id()
            sku_category_id = pos_charge.item.sku_category_id
            if not grouped_pos_charges.get(pos_order_id):
                grouped_pos_charges[pos_order_id] = {sku_category_id: [pos_charge]}
            else:
                if grouped_pos_charges[pos_order_id].get(sku_category_id):
                    grouped_pos_charges[pos_order_id][sku_category_id].append(
                        pos_charge
                    )
                else:
                    grouped_pos_charges[pos_order_id][sku_category_id] = [pos_charge]

        return rate_plan_charges, extra_charges, grouped_pos_charges

    def _transfer_charge_without_clubbing(
        self,
        source_booking_aggregate,
        source_bill_aggregate,
        source_charges,
        destination_booking_aggregate,
        destination_bill_aggregate,
        destination_room_stay_id,
    ):
        destination_charges = []

        for source_charge in source_charges:
            destination_charge = self._transfer_source_charge_to_destination_booking(
                source_charge=source_charge,
                source_booking_aggregate=source_booking_aggregate,
                destination_booking_aggregate=destination_booking_aggregate,
                destination_bill_aggregate=destination_bill_aggregate,
                destination_room_stay_id=destination_room_stay_id,
                inclusions=[],
            )
            destination_charges.append(destination_charge)

        for source_charge, destination_charge in zip(
            source_charges, destination_charges
        ):
            self._add_allowance_to_charge_split_and_raise_audit_trail_event(
                source_charge,
                destination_charge,
                source_booking_aggregate,
                source_bill_aggregate,
                destination_booking_aggregate,
            )

        return destination_charges, destination_bill_aggregate

    def _transfer_charge_after_clubbing(
        self,
        source_booking_aggregate,
        source_bill_aggregate,
        rate_plan_charges,
        destination_booking_aggregate,
        destination_bill_aggregate,
        destination_room_stay_id,
    ):
        destination_charges = []

        for rate_plan_charge in rate_plan_charges:
            room_stay_charge, inclusions = rate_plan_charge[0], rate_plan_charge[1:]

            destination_charge = self._transfer_source_charge_to_destination_booking(
                source_charge=room_stay_charge,
                source_booking_aggregate=source_booking_aggregate,
                destination_booking_aggregate=destination_booking_aggregate,
                destination_bill_aggregate=destination_bill_aggregate,
                destination_room_stay_id=destination_room_stay_id,
                inclusions=inclusions,
            )
            destination_charges.append(destination_charge)

        for rate_plan_charge, destination_charge in zip(
            rate_plan_charges, destination_charges
        ):
            for source_charge in rate_plan_charge:
                self._add_allowance_to_charge_split_and_raise_audit_trail_event(
                    source_charge,
                    destination_charge,
                    source_booking_aggregate,
                    source_bill_aggregate,
                    destination_booking_aggregate,
                )

        return destination_charges, destination_bill_aggregate

    def _transfer_pos_charge(
        self,
        source_booking_aggregate,
        source_bill_aggregate,
        pos_charges,
        destination_booking_aggregate,
        destination_bill_aggregate,
        destination_room_stay_id,
    ):
        destination_charges, source_destination_charge_pair = [], []

        for same_order_charges in pos_charges.values():
            for same_sku_charges in same_order_charges.values():
                destination_charge = (
                    self._transfer_source_charge_to_destination_booking(
                        source_charge=same_sku_charges[0],
                        source_booking_aggregate=source_booking_aggregate,
                        destination_booking_aggregate=destination_booking_aggregate,
                        destination_bill_aggregate=destination_bill_aggregate,
                        destination_room_stay_id=destination_room_stay_id,
                        inclusions=same_sku_charges[1:],
                        is_pos_charge=True,
                    )
                )
                destination_charges.append(destination_charge)
                source_destination_charge_pair.append(
                    (same_sku_charges, destination_charge)
                )

        for source_charges, destination_charge in source_destination_charge_pair:
            for source_charge in source_charges:
                self._add_allowance_to_charge_split_and_raise_audit_trail_event(
                    source_charge,
                    destination_charge,
                    source_booking_aggregate,
                    source_bill_aggregate,
                    destination_booking_aggregate,
                )

        return destination_charges, destination_bill_aggregate

    def _add_allowance_to_charge_split_and_raise_audit_trail_event(
        self,
        source_charge,
        destination_charge,
        source_booking_aggregate,
        source_bill_aggregate,
        destination_booking_aggregate,
    ):
        source_charge.comment = (
            'Charge Transferred to Booking: %s'
            % destination_booking_aggregate.booking.reference_number
        )
        crs_context.set_current_booking(source_booking_aggregate)
        for charge_split in source_charge.charge_splits:
            self.add_allowance_service.add_allowance_to_charge_split(
                source_bill_aggregate,
                source_charge,
                charge_split.charge_split_id,
                AllowanceData(
                    pretax_amount=charge_split.get_pretax_amount_post_allowance(),
                    remarks='Charge Transferred to Booking: %s'
                    % destination_booking_aggregate.booking.reference_number,
                    consume_at_creation=True,
                ),
                source_booking_aggregate,
            )
        BillEventRaiser(source_bill_aggregate).raise_charge_transferred_event(
            source_charge,
            destination_charge,
            destination_booking_aggregate.booking.reference_number,
            str(destination_booking_aggregate.get_booking_owner().name),
        )
