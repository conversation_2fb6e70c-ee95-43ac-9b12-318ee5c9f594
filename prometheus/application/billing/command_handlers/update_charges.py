from typing import List

from object_registry import register_instance
from prometheus import crs_context
from prometheus.application.billing.command_handlers.crs.update_charges import (
    CrsUpdateChargesCommandHandler,
)
from prometheus.application.billing.command_handlers.pos.update_charges import (
    PosUpdateCharges<PERSON>ommandHandler,
)
from prometheus.application.decorators import session_manager, set_hotel_context
from prometheus.common.error_codes.errors import ApplicationErrors
from prometheus.domain.billing.dto import EditChargeData
from prometheus.domain.billing.repositories import BillRepository
from ths_common.constants.billing_constants import BillAppId
from ths_common.exceptions import ValidationException


@register_instance(
    dependencies=[
        BillRepository,
        CrsUpdateCharges<PERSON>ommandHandler,
        PosUpdateChargesCommandHandler,
    ]
)
class UpdateChargesCommandHandler:
    def __init__(
        self,
        bill_repository: BillRepository,
        crs_update_charges_command_handler: CrsUpdateChargesCommandHand<PERSON>,
        pos_update_charges_command_handler: PosUpdateCharges<PERSON>ommand<PERSON>and<PERSON>,
    ):
        self.bill_repository = bill_repository
        self.crs_update_charges_command_handler = crs_update_charges_command_handler
        self.pos_update_charges_command_handler = pos_update_charges_command_handler

    @session_manager(commit=True)
    @set_hotel_context()
    def handle(
        self,
        bill_id,
        version,
        edit_dtos: List[EditChargeData],
        user_data,
        hotel_aggregate=None,
    ):
        if version is None:
            raise ValidationException(ApplicationErrors.BILL_VERSION_MANDATORY)

        bill_aggregate = self.bill_repository.load_for_update(bill_id, version)
        crs_context.set_current_bill(bill_aggregate)

        if bill_aggregate.bill.app_id == BillAppId.POS_APP.value:
            return self.pos_update_charges_command_handler.handle(
                bill_aggregate, edit_dtos, user_data
            )

        elif bill_aggregate.bill.app_id == BillAppId.CRS_APP.value:
            return self.crs_update_charges_command_handler.handle(
                bill_aggregate, edit_dtos, user_data
            )
