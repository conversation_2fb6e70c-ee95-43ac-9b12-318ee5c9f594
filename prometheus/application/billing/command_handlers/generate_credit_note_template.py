from object_registry import register_instance
from prometheus import crs_context
from prometheus.application import crs_context_middleware
from prometheus.application.billing.helpers.signed_url_generator import (
    SignedUrlGenerator,
)
from prometheus.application.billing.query_handlers.get_credit_note_template import (
    GetCreditNoteTemplateQueryHandler,
)
from prometheus.application.decorators import session_manager, set_hotel_context
from prometheus.application.services.integration_event_application_service import (
    IntegrationEventApplicationService,
)
from prometheus.async_job.job_registry import JobRegistry
from prometheus.async_job.job_result_dto import JobResultDto
from prometheus.domain.billing.repositories import CreditNoteRepository
from prometheus.infrastructure.external_clients.template_service_client import (
    TemplateFormat,
    TemplateNameSpace,
    TemplateService,
)
from ths_common.constants.integration_event_constants import IntegrationEventType
from ths_common.constants.scheduled_job_constants import JobName


@register_instance(
    dependencies=[
        GetCreditNoteTemplateQuery<PERSON><PERSON><PERSON>,
        CreditNoteRepository,
        Signed<PERSON>rl<PERSON>enerator,
        JobRegistry,
    ]
)
class GenerateCreditNoteTemplateCommandHandler:
    def __init__(
        self,
        get_credit_note_template_query_handler: GetCreditNoteTemplateQueryHandler,
        credit_note_repository: CreditNoteRepository,
        signed_url_generator: SignedUrlGenerator,
        job_registry: JobRegistry,
    ):
        self.get_credit_note_template_query_handler = (
            get_credit_note_template_query_handler
        )
        self.credit_note_repository = credit_note_repository
        self.signed_url_generator = signed_url_generator
        job_registry.register(
            JobName.CREDIT_NOTE_UPLOAD_ASYNC_JOB_NAME.value,
            self.handle,
        )

    @staticmethod
    def _get_credit_note_template_namespace(credit_note_aggregate, hotel_context):
        if credit_note_aggregate.is_reseller_issued_credit_note():
            return TemplateNameSpace.THS_RESELLER_CREDIT_NOTE.value
        else:
            return (
                TemplateNameSpace.THS_INDEPENDENT_HOTEL_CREDIT_NOTE.value
                if hotel_context.is_independent_hotel()
                else TemplateNameSpace.THS_HOTEL_CREDIT_NOTE.value
            )

    @session_manager(commit=True)
    @set_hotel_context()
    def handle(
        self, bill_id, credit_note_id, should_upload=True, hotel_aggregate=None
    ) -> JobResultDto:
        hotel_context = crs_context.get_hotel_context() if hotel_aggregate else None

        template = self.get_credit_note_template_query_handler.handle(
            bill_id, credit_note_id
        )

        credit_note_aggregate = self.credit_note_repository.load_for_update(
            credit_note_id
        )

        if not hotel_aggregate:
            crs_context_middleware.set_hotel_context(
                credit_note_aggregate.credit_note.vendor_id
            )
            hotel_context = crs_context.get_hotel_context()

        credit_note_url = None
        if should_upload:
            namespace = self._get_credit_note_template_namespace(
                credit_note_aggregate, hotel_context
            )
            credit_note_url = TemplateService().generate(
                namespace, template, TemplateFormat.PDF
            )
            credit_note_aggregate.update_template_url(credit_note_url)

        url_for_signed_url_gen = (
            credit_note_url
            if credit_note_url is not None
            else credit_note_aggregate.credit_note.credit_note_url
        )
        signed_url, expiration = self.signed_url_generator.generate_signed_url(
            url_for_signed_url_gen
        )

        if signed_url and expiration:
            credit_note_aggregate.set_signed_url(signed_url, expiration)

        if should_upload:
            self.credit_note_repository.update(credit_note_aggregate)

        # send integration event
        IntegrationEventApplicationService.create_credit_note_event(
            event_type=IntegrationEventType.CREDIT_NOTE_UPDATED,
            credit_note_aggregates=[credit_note_aggregate],
            user_action="generate_credit_note_template",
        )

        return JobResultDto.success(
            credit_note_aggregate=credit_note_aggregate, template=template
        )
