from object_registry import register_instance
from prometheus.application.decorators import session_manager, set_hotel_context
from prometheus.common.error_codes.errors import ApplicationErrors
from prometheus.domain.billing.repositories.credit_note_series_repository import (
    CreditNoteSequenceRepository,
)
from prometheus.domain.catalog.repositories import (
    HotelRepository,
    ResellerGstRepository,
)
from ths_common.constants.billing_constants import IssuedByType
from ths_common.exceptions import ValidationException


@register_instance(
    dependencies=[HotelRepository, ResellerGstRepository, CreditNoteSequenceRepository]
)
class BlockCreditNoteSequenceCommandHandler:
    def __init__(
        self,
        hotel_repository: HotelRepository,
        reseller_gst_repository: ResellerGstRepository,
        credit_note_series_repository: CreditNoteSequenceRepository,
    ):
        self.hotel_repository = hotel_repository
        self.reseller_gst_repository = reseller_gst_repository
        self.credit_note_series_repository = credit_note_series_repository

    @session_manager(commit=True)
    @set_hotel_context()
    def handle(
        self,
        hotel_id,
        issued_by_type,
        reason,
        user_data,
        block_hotel_credit_note_sequence=None,
        hotel_aggregate=None,
    ):
        if not hotel_aggregate:
            hotel_aggregate = self.hotel_repository.load(hotel_id)

        hotel_gstin = None
        if issued_by_type == IssuedByType.RESELLER:
            if block_hotel_credit_note_sequence is None:
                raise ValidationException(
                    ApplicationErrors.RESELLER_INVOICE_SERIES_BLOCK
                )
            reseller_gst_aggregate = self.reseller_gst_repository.load(
                hotel_aggregate.hotel.legal_state_id
            )
            gstin = reseller_gst_aggregate.gst_details.gstin_num
            hotel_gstin = hotel_aggregate.hotel.gstin_num
        else:
            gstin = hotel_aggregate.hotel.gstin_num
            block_hotel_credit_note_sequence = False

        (
            credit_note_number,
            hotel_credit_note_number,
        ) = self.credit_note_series_repository.block_credit_note_sequence(
            user_data.user_type,
            issued_by_type,
            hotel_id,
            gstin,
            reason,
            hotel_gstin,
            block_hotel_credit_note_sequence,
        )
        return credit_note_number, hotel_credit_note_number
