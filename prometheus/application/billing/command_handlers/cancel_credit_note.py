from treebo_commons.utils import dateutils

from object_registry import register_instance
from prometheus.application.decorators import session_manager, set_hotel_context
from prometheus.application.services.integration_event_application_service import (
    IntegrationEventApplicationService,
)
from prometheus.domain.billing.repositories import (
    CreditNoteRepository,
    InvoiceRepository,
)
from prometheus.domain.policy.engine import RuleEngine
from prometheus.domain.policy.facts import CreditNoteFacts
from ths_common.constants.integration_event_constants import IntegrationEventType


@register_instance(dependencies=[InvoiceRepository, CreditNoteRepository])
class CancelCreditNoteCommandHandler:
    def __init__(
        self,
        invoice_repository: InvoiceRepository,
        credit_note_repository: CreditNoteRepository,
    ):
        self.invoice_repository = invoice_repository
        self.credit_note_repository = credit_note_repository

    @session_manager(commit=True)
    @set_hotel_context()
    def handle(self, credit_note_id, user_data, hotel_aggregate=None):
        credit_note_aggregate = self.credit_note_repository.load(credit_note_id)

        RuleEngine.action_allowed(
            action="cancel_credit_note",
            facts=CreditNoteFacts(
                user_type=user_data.user_type,
                current_time=dateutils.current_datetime(),
                credit_note_aggregate=credit_note_aggregate,
            ),
            fail_on_error=True,
        )

        credit_note_aggregate.cancel_credit_note()
        (
            attached_invoices,
            invoice_ids_credit_note_amount,
        ) = credit_note_aggregate.get_attached_invoices_with_credit_note_amount()
        invoice_aggregates = self.invoice_repository.load_all_for_update(
            attached_invoices
        )
        grouped_invoices = {
            invoice_aggregate.invoice.invoice_id: invoice_aggregate
            for invoice_aggregate in invoice_aggregates
        }

        updated_invoice_aggregates = dict()
        for (
            invoice_charge,
            credit_note_amount,
        ) in invoice_ids_credit_note_amount.items():
            invoice_id, invoice_charge_id = invoice_charge[0], invoice_charge[1]
            invoice_aggregate = grouped_invoices.get(invoice_id)
            invoice_aggregate.reverse_credit_note_amount(
                invoice_charge_id, credit_note_amount
            )
            updated_invoice_aggregates[invoice_id] = invoice_aggregate

        self.credit_note_repository.update(credit_note_aggregate)
        self.invoice_repository.update_all(list(updated_invoice_aggregates.values()))

        # send integration event
        IntegrationEventApplicationService.create_credit_note_event(
            event_type=IntegrationEventType.CREDIT_NOTE_UPDATED,
            credit_note_aggregates=[credit_note_aggregate],
            invoice_aggregates=list(updated_invoice_aggregates.values()),
            user_action="cancel_credit_note",
        )

        return credit_note_aggregate
