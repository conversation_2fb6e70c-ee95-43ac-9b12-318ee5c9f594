import logging

from treebo_commons.utils import dateutils

from object_registry import register_instance
from prometheus import crs_context
from prometheus.application import crs_context_middleware
from prometheus.infrastructure.external_clients.catalog_service_client import (
    CatalogServiceClient,
)
from prometheus.infrastructure.external_clients.notification_service_client import (
    NotificationEmailIds,
)
from prometheus.infrastructure.external_clients.notification_service_constants import (
    ApprovedWhatsappTemplate,
    WhatsappMessageType,
)
from prometheus.infrastructure.external_clients.template_service_client import (
    TemplateNameSpace,
)
from shared_kernel.infrastructure.external_clients.communication_service_client import (
    CommunicationServiceClient,
)
from ths_common.constants.booking_constants import BookingChannels
from ths_common.constants.hotel_constants import BrandCodes
from ths_common.utils.common_utils import is_valid_email
from ths_common.value_objects import EmailAttachment

logger = logging.getLogger(__name__)


@register_instance(
    dependencies=[
        CommunicationServiceClient,
        CatalogServiceClient,
    ]
)
class GuestCommunicationService:
    def __init__(
        self,
        communication_service_client: CommunicationServiceClient,
        catalog_service_client: CatalogServiceClient,
    ):
        self.communication_service_client = communication_service_client
        self.catalog_service_client = catalog_service_client

    @staticmethod
    def is_sending_whatsapp_msg_required(booking_aggregate):
        brand_code = crs_context.get_hotel_context().brand_code
        return booking_aggregate.booking.source.channel_code in [
            BookingChannels.OTA.value,
            BookingChannels.DIRECT.value,
            BookingChannels.ASSISTED_SALES.value,
        ] and brand_code not in [BrandCodes.HOTELSUPERHERO]

    def _get_attachments_for_booking_confirmation_voucher(
        self,
        booking_voucher_url,
        booking_aggregate,
        payment_receipt_aggregate=None,
    ):
        attachments = [
            EmailAttachment(
                url=booking_voucher_url,
                filename='{}.pdf'.format(booking_aggregate.booking.reference_number),
            ).json()
        ]
        if payment_receipt_aggregate:
            attachments.append(
                self._get_payment_receipt_attachment(payment_receipt_aggregate)
            )
        return attachments

    @staticmethod
    def _get_payment_receipt_attachment(payment_receipt_aggregate):
        return EmailAttachment(
            url=payment_receipt_aggregate.payment_receipt.signed_url,
            filename='{}.pdf'.format(
                payment_receipt_aggregate.payment_receipt.payment_receipt_number
            ),
        ).json()

    def send_email(
        self,
        context_data,
        subject_line,
        receivers_email,
        attachments,
    ):
        self.communication_service_client.send_email(
            identifier=TemplateNameSpace.PAYMENT_CONFIRMATION_VOUCHER_VIA_EMAIL.value,
            context_data=context_data,
            to_emails=receivers_email,
            subject=subject_line,
            from_email=NotificationEmailIds.NOREPLY.value,
            attachments=attachments,
        )

    def send_confirmed_booking_voucher_on_whatsapp(
        self,
        receivers,
        booking_voucher_url,
        booking_aggregate,
        payment_receipt_aggregate=None,
    ):
        if booking_voucher_url:
            filename = '{}.pdf'.format(booking_aggregate.booking.reference_number)
            self.communication_service_client.send_whatsapp(
                identifier=None,
                context_data=dict(
                    message_type=WhatsappMessageType.DOCUMENT.value,
                    media_url=booking_voucher_url,
                    meta=dict(
                        filename=filename,
                        caption=ApprovedWhatsappTemplate.BOOKING_VOUCHER_ATTACHMENT.value,
                    ),
                ),
                receivers=receivers,
            )
        if payment_receipt_aggregate:
            self._send_payment_receipt_on_whatsapp(receivers, payment_receipt_aggregate)

    def send_payment_confirmation_on_whatsapp(
        self, receivers, payment_receipt_aggregate
    ):
        self._send_payment_receipt_on_whatsapp(receivers, payment_receipt_aggregate)

    def _send_payment_receipt_on_whatsapp(self, receivers, payment_receipt_aggregate):
        filename = '{}.pdf'.format(
            payment_receipt_aggregate.payment_receipt.payment_receipt_number
        )
        self.communication_service_client.send_whatsapp(
            identifier=None,
            context_data=dict(
                message_type=WhatsappMessageType.DOCUMENT.value,
                media_url=payment_receipt_aggregate.payment_receipt.signed_url,
                meta=dict(
                    filename=filename,
                    caption=ApprovedWhatsappTemplate.PAYMENT_RECEIPT_ATTACHMENT.value,
                ),
            ),
            receivers=receivers,
        )

    def send_confirmed_booking_voucher(
        self,
        booking_aggregate,
        bill_aggregate,
        booking_voucher_url,
        payment_receipt_aggregate=None,
    ):
        crs_context_middleware.set_hotel_context(booking_aggregate.booking.hotel_id)
        hotel_name = crs_context.get_hotel_context().hotel_name
        receivers_email = list({booking_aggregate.get_receiver_email_for_invoice()})
        receivers_phone = []
        invoice_receiver_phone = (
            booking_aggregate.get_receiver_phone_number_for_invoice()
        )
        if invoice_receiver_phone:
            receivers_phone.append(invoice_receiver_phone)
        if payment_receipt_aggregate:
            receivers_email.append(
                payment_receipt_aggregate.get_payment_receipt_receiver_email()
            )
            payment_receipt_receiver_phone = (
                payment_receipt_aggregate.get_payment_receipt_receiver_phone_number()
            )
            if payment_receipt_receiver_phone:
                receivers_phone.append(payment_receipt_receiver_phone)

        receivers_email = [
            receiver_email
            for receiver_email in receivers_email
            if is_valid_email(receiver_email)
        ]
        if receivers_email:
            self.send_email(
                context_data=dict(
                    hotel_name=hotel_name,
                    is_direct_booking=booking_aggregate.is_direct_booking(),
                ),
                subject_line=f"YOUR BOOKING IS CONFIRMED - {booking_aggregate.booking.reference_number} - {hotel_name}",
                receivers_email=receivers_email,
                attachments=self._get_attachments_for_booking_confirmation_voucher(
                    booking_voucher_url,
                    booking_aggregate,
                    payment_receipt_aggregate,
                ),
            )

        unique_receivers_phone = list(set(phone.number for phone in receivers_phone))
        if (
            self.is_sending_whatsapp_msg_required(booking_aggregate)
            and unique_receivers_phone
        ):
            self.send_confirmed_booking_voucher_on_whatsapp(
                receivers=unique_receivers_phone,
                booking_voucher_url=booking_voucher_url,
                booking_aggregate=booking_aggregate,
                payment_receipt_aggregate=payment_receipt_aggregate,
            )

    def send_payment_receipt(
        self,
        booking_aggregate,
        bill_aggregate,
        payment_receipt_aggregate,
    ):
        crs_context_middleware.set_hotel_context(booking_aggregate.booking.hotel_id)
        hotel_name = crs_context.get_hotel_context().hotel_name
        receivers_email = list(
            {
                booking_aggregate.get_receiver_email_for_invoice(),
                payment_receipt_aggregate.get_payment_receipt_receiver_email(),
            }
        )
        receivers_email = [
            receiver_email
            for receiver_email in receivers_email
            if is_valid_email(receiver_email)
        ]
        if receivers_email:
            self.send_email(
                context_data=dict(hotel_name=hotel_name),
                subject_line=f"PLEASE FIND YOUR PAYMENT RECEIPT FOR {booking_aggregate.booking.reference_number} - {hotel_name}",
                receivers_email=receivers_email,
                attachments=[
                    self._get_payment_receipt_attachment(payment_receipt_aggregate)
                ],
            )
        receivers_phone = []
        payment_receipt_receiver_phone = (
            payment_receipt_aggregate.get_payment_receipt_receiver_phone_number()
        )
        if payment_receipt_receiver_phone:
            receivers_phone.append(payment_receipt_receiver_phone)
        invoice_receiver_phone = (
            booking_aggregate.get_receiver_phone_number_for_invoice()
        )
        if invoice_receiver_phone:
            receivers_phone.append(invoice_receiver_phone)
        unique_receivers_phone = list(set(phone.number for phone in receivers_phone))
        if (
            self.is_sending_whatsapp_msg_required(booking_aggregate)
            and unique_receivers_phone
        ):
            self.send_payment_confirmation_on_whatsapp(
                receivers=unique_receivers_phone,
                payment_receipt_aggregate=payment_receipt_aggregate,
            )
