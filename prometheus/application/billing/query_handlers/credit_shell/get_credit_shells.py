from object_registry import register_instance
from prometheus.domain.billing.repositories.credit_shell_repository import (
    CreditShellRepository,
)
from prometheus.domain.booking.repositories import BookingRepository
from ths_common.exceptions import ValidationException


@register_instance(
    dependencies=[
        CreditShellRepository,
        BookingRepository,
    ]
)
class GetCreditShellsQueryHandler:
    def __init__(
        self,
        credit_shell_repository: CreditShellRepository,
        booking_repository: BookingRepository,
    ):
        self.credit_shell_repository = credit_shell_repository
        self.booking_repository = booking_repository

    def handle(self, **kwargs):
        booking_id = kwargs.get('booking_id')
        credit_shell_id = kwargs.get('credit_shell_id')
        bill_id = kwargs.get('bill_id')
        if booking_id:
            booking_aggregate = self.booking_repository.load(booking_id=booking_id)
            if not booking_aggregate:
                raise ValidationException(
                    message="Booking with booking_id: {} not found.".format(booking_id)
                )
            bill_id = booking_aggregate.booking.bill_id
        return self.credit_shell_repository.load_all_by_id(
            bill_id=bill_id, credit_shell_id=credit_shell_id
        )
