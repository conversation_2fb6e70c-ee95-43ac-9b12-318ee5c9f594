from typing import Set

from object_registry import register_instance
from prometheus.application.billing.helpers.payment_receipt_service import (
    PaymentReceiptService,
)
from prometheus.application.decorators import session_manager, set_hotel_context
from prometheus.domain.billing.entities.billed_entity import BilledEntityAccountVO
from prometheus.domain.billing.repositories import BillRepository
from ths_common.exceptions import NotModifiedException


@register_instance(dependencies=[BillRepository, PaymentReceiptService])
class GetBillQueryHandler:
    def __init__(
        self,
        bill_repository: BillRepository,
        payment_receipt_service: PaymentReceiptService,
    ):
        self.bill_repository = bill_repository
        self.payment_receipt_service = payment_receipt_service

    # TODO: Duplicate method from get_payments command handler
    @staticmethod
    def _set_payment_receipt_url_in_payments(payments, payment_receipt_map):
        for payment in payments:
            payment_receipt = payment_receipt_map.get(payment.payment_id)
            if payment_receipt:
                payment.payment_receipt_url = payment_receipt.signed_url

    @session_manager()
    @set_hotel_context()
    def handle(
        self,
        bill_id,
        last_fetched_version=None,
        use_raw_query=None,
        hotel_aggregate=None,
        exclude_fields: Set = None,
        shallow_response=False,
    ):
        if last_fetched_version:
            current_bill_version = self.bill_repository.get_current_bill_version(
                bill_id
            )
            if current_bill_version == last_fetched_version:
                raise NotModifiedException()

        bill_aggregate = self.bill_repository.load(
            bill_id,
            use_raw_query=use_raw_query,
            exclude_fields=exclude_fields,
            shallow_response=shallow_response,
        )
        if not exclude_fields or 'billed_entities' not in exclude_fields:
            for billed_entity in bill_aggregate.billed_entities:
                for account in billed_entity.accounts:
                    billed_entity_account = BilledEntityAccountVO(
                        billed_entity.billed_entity_id, account.account_number
                    )
                    account.net_balance = bill_aggregate.get_net_balance(
                        billed_entity_account
                    )

        payment_receipts = self.payment_receipt_service.get_payment_receipts(bill_id)
        self._set_payment_receipt_url_in_payments(
            bill_aggregate.payments, payment_receipts
        )
        return bill_aggregate
