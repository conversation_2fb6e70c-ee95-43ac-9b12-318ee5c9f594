from treebo_commons.money import Money

from object_registry import register_instance
from prometheus.application.hotel_settings.tenant_settings import TenantSettings
from prometheus.domain.billing.aggregates.bill_aggregate import BillAggregate
from prometheus.domain.billing.dto.bill_summary_dto import RefundDetailsDto
from prometheus.domain.billing.repositories import BillRepository
from ths_common.constants.billing_constants import PaymentReceiverTypes, PaymentTypes


@register_instance(dependencies=[BillRepository, TenantSettings])
class RefundDetailsQueryHandler:
    def __init__(
        self, bill_repository: BillRepository, tenant_settings: TenantSettings
    ):
        self.bill_repository = bill_repository
        self.tenant_settings = tenant_settings

    def handle(self, bill_id, parsed_request):
        bill_aggregate: BillAggregate = self.bill_repository.load(bill_id)
        total_pah_amount = Money(0, bill_aggregate.bill.base_currency)
        balance = Money(
            parsed_request['amount'].amount, bill_aggregate.bill.base_currency
        )
        pah_amount, ptt_amount, refundable_amount = self.get_payment_details(
            bill_aggregate
        )
        if balance - refundable_amount > Money(0, bill_aggregate.bill.base_currency):
            total_pah_amount = min(balance - refundable_amount, pah_amount)
            total_ptt_amount = balance - total_pah_amount
        else:
            total_pah_amount = total_pah_amount
            total_ptt_amount = balance

        return RefundDetailsDto(
            total_pah_amount=total_pah_amount,
            total_ptt_amount=total_ptt_amount,
        )

    def get_payment_details(self, bill_aggregate):
        pah_amount, ptt_amount, refundable_amount = (
            Money(0, bill_aggregate.bill.base_currency),
            Money(0, bill_aggregate.bill.base_currency),
            Money(0, bill_aggregate.bill.base_currency),
        )
        refund_rule = self.tenant_settings.get_refund_rule(
            hotel_id=bill_aggregate.vendor_id
        )
        if not refund_rule:
            return pah_amount, ptt_amount, refundable_amount

        refundable_payment_mode = refund_rule.refund_mode_priority_list

        for payment in bill_aggregate.payments:
            if not payment.is_active():
                continue
            if payment.payment_mode in refundable_payment_mode:
                refundable_amount = self._update_amount(refundable_amount, payment)
                continue
            if payment.payment_type == PaymentTypes.PAYMENT:
                if payment.paid_to == PaymentReceiverTypes.HOTEL:
                    pah_amount += payment.amount
                else:
                    ptt_amount += payment.amount
            elif payment.payment_type == PaymentTypes.REFUND:
                if payment.paid_by == PaymentReceiverTypes.HOTEL:
                    pah_amount -= payment.amount
                else:
                    ptt_amount -= payment.amount
        return pah_amount, ptt_amount, refundable_amount

    @staticmethod
    def _update_amount(current_amount, payment):
        if payment.payment_type == PaymentTypes.PAYMENT:
            current_amount += payment.amount
        elif payment.payment_type == PaymentTypes.REFUND:
            current_amount -= payment.amount
        return current_amount
