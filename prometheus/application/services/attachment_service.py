import logging
import urllib

from object_registry import register_instance
from prometheus import crs_context
from prometheus.application import crs_context_middleware
from prometheus.application.billing.helpers.signed_url_generator import (
    SignedUrlGenerator,
)
from prometheus.domain.booking.factories.attachment_factory import AttachmentFactory
from prometheus.domain.booking.repositories.attachment_repository import (
    AttachmentRepository,
)
from prometheus.domain.booking.repositories.booking_repository import BookingRepository
from prometheus.domain.booking.services.attachment_domain_service import (
    AttachmentDomainService,
)
from prometheus.domain.catalog.repositories.hotel_repository import HotelRepository
from prometheus.domain.hotel_config.repository import HotelConfigRepository
from prometheus.domain.policy.engine import RuleEngine
from prometheus.domain.policy.facts import Facts

logger = logging.getLogger(__name__)


@register_instance(
    dependencies=[
        BookingRepository,
        HotelRepository,
        AttachmentRepository,
        AttachmentDomainService,
        HotelConfigRepository,
        SignedUrlGenerator,
    ]
)
class AttachmentService:
    def __init__(
        self,
        booking_repository: BookingRepository,
        hotel_repository: HotelRepository,
        attachment_repository: AttachmentRepository,
        attachment_domain_service: AttachmentDomainService,
        hotel_config_repository: HotelConfigRepository,
        signed_url_generator: SignedUrlGenerator,
    ):
        self.booking_repository = booking_repository
        self.hotel_repository = hotel_repository
        self.attachment_repository = attachment_repository
        self.attachment_domain_service = attachment_domain_service
        self.hotel_config_repository = hotel_config_repository
        self.signed_url_generator = signed_url_generator

    def add_attachment(
        self,
        booking_id,
        attachment_data_list,
        source,
        user_data,
        hotel_aggregate=None,
        status=None,
    ):
        booking_aggregate = self.booking_repository.load(
            booking_id=booking_id,
            user_data=user_data,
            skip_expenses=True,
            skip_customers=False,
        )
        if not hotel_aggregate:
            crs_context_middleware.set_hotel_context(booking_aggregate.hotel_id)
        crs_context.set_current_booking(booking_aggregate)
        attachment_aggregates = [
            AttachmentFactory.create_attachment(
                booking_id, data, user_data, source, status
            )
            for data in attachment_data_list
        ]
        self.attachment_repository.save_all(attachment_aggregates)
        self.attachment_domain_service.register_attachment_added_event(
            attachment_aggregates
        )
        for attachment_aggregate in attachment_aggregates:
            if RuleEngine.action_allowed(
                action="access_attachment",
                facts=Facts(
                    user_type=attachment_aggregate.user_data.user_type,
                    attachment_aggregate=attachment_aggregate,
                ),
                fail_on_error=False,
            ):
                signed_url, _ = self.signed_url_generator.generate_signed_url(
                    urllib.parse.unquote(attachment_aggregate.attachment.original_url)
                )
                attachment_aggregate.attachment.signed_url = signed_url
        return attachment_aggregates
