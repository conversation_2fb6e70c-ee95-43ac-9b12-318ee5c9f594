from functools import wraps

from object_registry import get_event_handler


def dispatch_event(func):
    @wraps(func)
    def wrapper(*args, **kwargs):
        # NOTE: This import when encountered first time after server start, will load event_handlers.__init__ module,
        # as a result of which, all event handlers will be registered
        from prometheus.domain.domain_events.domain_event_registry import get_all_events

        value = func(*args, **kwargs)
        events = get_all_events()

        for event in events:
            event_handler = get_event_handler(event.__class__)
            if event_handler:
                event_handler.handle(event)

        return value

    return wrapper
