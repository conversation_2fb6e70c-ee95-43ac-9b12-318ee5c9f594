from typing import List, Union

from treebo_commons.utils import dateutils

from object_registry import register_instance
from prometheus import crs_context
from prometheus.application.booking.helpers.room_stay_dto_creator import (
    RoomStayDtoCreator,
)
from prometheus.application.helpers.gst_details_helper import (
    get_gst_details_from_billed_entity,
)
from prometheus.common.error_codes.errors import ApplicationErrors
from prometheus.domain.billing.aggregates.bill_aggregate import BillAggregate
from prometheus.domain.billing.dto import EditChargeData
from prometheus.domain.billing.entities.billed_entity import (
    BilledEntity,
    BilledEntityAccountVO,
)
from prometheus.domain.billing.entities.charge import Charge
from prometheus.domain.billing.services import TaxService
from prometheus.domain.booking.aggregates.booking_aggregate import BookingAggregate
from prometheus.domain.booking.entities import Expense, RoomStay
from prometheus.infrastructure.alerting.slack_alert_service_client import (
    SlackAlertServiceClient,
)
from ths_common.exceptions import ValidationException
from ths_common.value_objects import GSTDetails


@register_instance(
    dependencies=[RoomStayDtoCreator, TaxService, SlackAlertServiceClient]
)
class TaxCalculationHelper:
    def __init__(
        self,
        room_stay_dto_helper: RoomStayDtoCreator,
        tax_service: TaxService,
        alert_client: SlackAlertServiceClient,
    ):
        self.room_stay_dto_helper = room_stay_dto_helper
        self.tax_service = tax_service
        self.alert_client = alert_client

    def recompute_taxes_of_charges(
        self,
        booking_aggregate: BookingAggregate,
        bill_aggregate: BillAggregate,
        grouped_sku_categories,
    ):
        if not self._check_if_booking_is_eligible_for_tax_recalculation(
            booking_aggregate, bill_aggregate, grouped_sku_categories
        ):
            return
        charges: List[Charge] = bill_aggregate.get_active_charges()
        charges_to_process = {charge.charge_id for charge in charges}
        for _, room_stay in booking_aggregate.room_stay_dict.items():
            room_stay_charge_ids = room_stay.charge_id_map.values()
            for room_stay_charge_id in room_stay_charge_ids:
                room_stay_charge: Charge = bill_aggregate.get_charge(
                    room_stay_charge_id
                )
                if room_stay_charge.charge_id not in charges_to_process:
                    continue
                self.recalculate_taxes(
                    bill_aggregate,
                    booking_aggregate,
                    [room_stay_charge],
                    grouped_sku_categories,
                )
                charges_to_process.remove(room_stay_charge.charge_id)
                charges_to_process = charges_to_process - set(
                    room_stay_charge.addon_charge_ids
                )
        non_rate_plan_charges = bill_aggregate.get_charges(charges_to_process)
        self.recalculate_taxes(
            bill_aggregate,
            booking_aggregate,
            non_rate_plan_charges,
            grouped_sku_categories,
        )

    def apply_tax_on_linked_charges(
        self,
        linked_charges: List[Charge],
        bill_aggregate: BillAggregate,
        booking_aggregate: BookingAggregate,
        grouped_sku_categories,
    ):
        for linked_charge in linked_charges:
            charge_date = dateutils.to_date(linked_charge.applicable_date)
            expense: Expense = booking_aggregate.get_expense_for_charge(
                linked_charge.charge_id
            )
            room_stay: RoomStay = booking_aggregate.get_room_stay(expense.room_stay_id)
            charge_id = room_stay.charge_id_map.get(
                dateutils.date_to_ymd_str(charge_date)
            )
            room_stay_charge = bill_aggregate.get_charge(charge_id)
            linked_charge_edit_dto = self._build_linked_charge_edit_dto(
                booking_aggregate, linked_charge
            )
            linked_charge_edit_dto.apply_tax_from_room_charge(room_stay_charge)
            self._update_charge(
                bill_aggregate, linked_charge_edit_dto, grouped_sku_categories
            )

    def _build_linked_charge_edit_dto(self, booking_aggregate, linked_charge):
        linked_charge_edit_dto = EditChargeData.create_from_charge(linked_charge)
        hotel_uses_post_tax_price = (
            self.room_stay_dto_helper.tenant_settings.hotel_uses_posttax_price(
                booking_aggregate.hotel_id
            )
        )
        if hotel_uses_post_tax_price:
            linked_charge_edit_dto.pretax_amount = None
        else:
            linked_charge_edit_dto.posttax_amount = None
        return linked_charge_edit_dto

    @staticmethod
    def raise_if_accounts_has_conflicting_gst_details(
        accounts, bill_aggregate, booking_aggregate
    ):
        current_gst_details: Union[GSTDetails, None] = None
        for billed_entity_account in accounts:
            billed_entity = bill_aggregate.get_billed_entity(
                billed_entity_account.billed_entity_id
            )
            gst_details = get_gst_details_from_billed_entity(
                booking_aggregate, billed_entity
            )
            if (
                current_gst_details
                and not current_gst_details.has_same_tax_determiners(gst_details)
            ):
                raise ValidationException(
                    ApplicationErrors.CHARGE_CANNOT_BE_SPLIT_TO_DIFFERENT_GST_CONFIG
                )
            current_gst_details = gst_details

    def recalculate_taxes_on_billed_entity_account(
        self,
        account: BilledEntityAccountVO,
        bill_aggregate: BillAggregate,
        booking_aggregate,
        grouped_sku_categories,
    ):
        billed_entity = bill_aggregate.get_billed_entity(account.billed_entity_id)
        be_account = billed_entity.get_account(account.account_number)
        if be_account.locked or be_account.invoiced:
            raise ValidationException(
                ApplicationErrors.INVALID_STATE_OF_CHARGES_TO_UPDATE_TAX_DETERMINERS
            )
        charges_to_recalculate = bill_aggregate.get_active_charges_by_accounts(
            [account]
        )
        if charges_to_recalculate:
            self.recalculate_taxes(
                bill_aggregate,
                booking_aggregate,
                charges_to_recalculate,
                grouped_sku_categories,
            )
        return charges_to_recalculate

    def recalculate_taxes_on_billed_entity(
        self,
        billed_entity: BilledEntity,
        bill_aggregate: BillAggregate,
        booking_aggregate,
        grouped_sku_categories,
    ):
        active_accounts = {
            BilledEntityAccountVO(
                billed_entity.billed_entity_id, account.account_number
            )
            for account in billed_entity.accounts
            if not (account.is_locked() or account.is_invoiced())
        }
        charges_to_recalculate = bill_aggregate.get_active_charges_by_accounts(
            active_accounts
        )
        if charges_to_recalculate:
            self.recalculate_taxes(
                bill_aggregate,
                booking_aggregate,
                charges_to_recalculate,
                grouped_sku_categories,
            )
        return charges_to_recalculate

    def recalculate_taxes(
        self, bill_aggregate, booking_aggregate, charges, grouped_sku_categories
    ):
        taxable_items, charge_edit_dtos, inclusion_charges_dtos, linked_charges = (
            [],
            [],
            [],
            [],
        )
        seller_model = booking_aggregate.booking.seller_model
        hotel_id = booking_aggregate.hotel_id
        for charge in charges:
            if charge.is_inclusion_charge:
                continue
            if (
                charge.has_invoice_attached
                or charge.is_consumed
                or charge.has_allowance
            ):
                raise ValidationException(
                    ApplicationErrors.INVALID_STATE_OF_CHARGES_TO_UPDATE_TAX_DETERMINERS,
                    description="charge is invoiced or consumed or allowance passed",
                )
            self.raise_if_accounts_has_conflicting_gst_details(
                charge.linked_billed_entity_accounts, bill_aggregate, booking_aggregate
            )
            if charge.is_room_linked_addon_charge:
                linked_charges.append(charge)
                continue
            addon_charges = (
                bill_aggregate.get_charges(charge.addon_charge_ids)
                if charge.addon_charge_ids
                else None
            )
            charge_edit_dto, inclusion_charges_dto = self._build_charge_edit_dtos(
                charge, addon_charges, bill_aggregate, booking_aggregate, hotel_id
            )
            charge_edit_dtos.append(charge_edit_dto)
            inclusion_charges_dtos.extend(inclusion_charges_dto)
            taxable_items.extend(
                self.room_stay_dto_helper.build_taxable_items(
                    charge_edit_dto, inclusion_charges_dto, hotel_id=hotel_id
                )
            )
        if taxable_items:
            self.tax_service.calculate_taxes(
                taxable_items,
                buyer_gst_details=booking_aggregate.booking_owner_gst_details(),
                seller_has_lut=self.tax_service.seller_has_lut(
                    seller_model, crs_context.get_hotel_context()
                ),
                hotel_id=hotel_id,
            )
        for edit_data in charge_edit_dtos + inclusion_charges_dtos:
            self._update_charge(bill_aggregate, edit_data, grouped_sku_categories)

        self.apply_tax_on_linked_charges(
            linked_charges, bill_aggregate, booking_aggregate, grouped_sku_categories
        )

    @staticmethod
    def _update_charge(bill_aggregate, edit_data, grouped_sku_categories):
        bill_aggregate.update_charge_amounts(
            charge_id=edit_data.charge_id,
            pretax_amount=edit_data.pretax_amount,
            posttax_amount=edit_data.posttax_amount,
            tax_amount=edit_data.tax_amount,
            tax_details=edit_data.tax_details,
            grouped_sku_categories=grouped_sku_categories,
        )

    def _build_charge_edit_dtos(
        self, charge, inclusion_charges, bill_aggregate, booking_aggregate, hotel_id
    ):
        billed_entity = bill_aggregate.get_primary_billed_entity_of_charge(
            charge.charge_id
        )
        gst_details = get_gst_details_from_billed_entity(
            booking_aggregate, billed_entity
        )
        charge_edit_dto = EditChargeData.create_from_charge(
            charge, buyer_gst_details=gst_details
        )
        inclusion_charges_dto = (
            [
                EditChargeData.create_from_charge(ch, buyer_gst_details=gst_details)
                for ch in inclusion_charges
            ]
            if inclusion_charges
            else []
        )
        hotel_uses_post_tax_price = (
            self.room_stay_dto_helper.tenant_settings.hotel_uses_posttax_price(hotel_id)
        )
        for item in [charge_edit_dto] + inclusion_charges_dto:
            if hotel_uses_post_tax_price:
                item.pretax_amount = None
            else:
                item.posttax_amount = None
        return charge_edit_dto, inclusion_charges_dto

    def _check_if_booking_is_eligible_for_tax_recalculation(
        self,
        booking_aggregate: BookingAggregate,
        bill_aggregate: BillAggregate,
        grouped_sku_categories,
    ):
        if booking_aggregate.is_checked_in() or booking_aggregate.is_part_checked_in():
            self._notify_and_raise_error(
                booking_aggregate,
                ApplicationErrors.INVALID_STATE_OF_CHARGES_TO_UPDATE_TAX_DETERMINERS,
                description='Booking is checked in',
            )
        if booking_aggregate.is_closed() or booking_aggregate.is_part_checked_out():
            return False

        charges: List[Charge] = bill_aggregate.get_active_charges()
        if any(
            ch.has_invoice_attached or ch.is_consumed or ch.has_allowance
            for ch in charges
        ):
            self._notify_and_raise_error(
                booking_aggregate,
                ApplicationErrors.INVALID_STATE_OF_CHARGES_TO_UPDATE_TAX_DETERMINERS,
                description='charge is invoiced or consumed or allowance passed',
            )
        if bill_aggregate.has_any_active_charge_with_tax_slab_based_charge_splits(
            grouped_sku_categories
        ):
            self._notify_and_raise_error(
                booking_aggregate,
                ApplicationErrors.INVALID_STATE_OF_CHARGES_TO_UPDATE_TAX_DETERMINERS,
                description='Charges has splits and slab based taxation is enabled',
            )
        return True

    def _notify_and_raise_error(
        self, booking_aggregate: BookingAggregate, error, description
    ):
        error_payload = dict(
            booking_reference_id=booking_aggregate.booking.reference_number,
            hotel_id=booking_aggregate.hotel_id,
            error=f'{error.message}, {description}',
        )
        self.alert_client.record_event('tax_determiner_update_error', error_payload)
        raise ValidationException(error, description=description)
