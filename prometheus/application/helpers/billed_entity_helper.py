from typing import Dict

from prometheus.application.booking.dtos.new_booking_dto import NewBookingDto
from prometheus.domain.billing.entities.billed_entity import BilledEntity
from ths_common.constants.billing_constants import (
    BilledEntityCategory,
    PaymentReceiverTypes,
)
from ths_common.exceptions import ResourceNotFound

# New Methods
from ths_common.value_objects import GSTDetails, LegalDetails

BILLED_ENTITY_CATEGORY_TO_PAID_BY_MAP = {
    BilledEntityCategory.CONSUMING_GUESTS: PaymentReceiverTypes.GUEST,
    BilledEntityCategory.PRIMARY_GUEST: PaymentReceiverTypes.GUEST,
    BilledEntityCategory.TRAVEL_AGENT: PaymentReceiverTypes.TA,
    BilledEntityCategory.BOOKER_COMPANY: PaymentReceiverTypes.CORPORATE,
}


def get_room_stay_default_billed_entity(
    room_stay, booking_aggregate, bill_aggregate
) -> BilledEntity:
    billed_entity_category = booking_aggregate.get_default_billed_entity_category()
    return get_billed_entity_for_given_category(
        bill_aggregate, billed_entity_category, booking_aggregate, room_stay
    )


def get_billed_entity_for_given_category(
    bill_aggregate, billed_entity_category, booking_aggregate, room_stay
):
    if billed_entity_category == BilledEntityCategory.PRIMARY_GUEST:
        for guest_stay in room_stay.guest_stays:
            customer = booking_aggregate.get_customer(guest_stay.guest_id)
            if customer.is_primary:
                try:
                    return bill_aggregate.get_billed_entity(customer.billed_entity_id)
                except ResourceNotFound:
                    break
        # For older bookings: when booker name and guest names are same there will be no BE for guest,
        # will consider booker and guest as same
        billed_entity_category = BilledEntityCategory.BOOKER
    return bill_aggregate.get_billed_entity_for_category(billed_entity_category)


def attach_default_billed_entity_account_to_room_stay_charges(
    room_stay, charges, booking_aggregate, bill_aggregate
):
    default_billed_entity = get_room_stay_default_billed_entity(
        room_stay, booking_aggregate, bill_aggregate
    )
    bill_aggregate.attach_default_billed_entity_account_to_charges(
        charges=charges, billed_entity=default_billed_entity
    )


def build_charge_to_billed_entity_map_for_primary_guest_category(
    booking_aggregate, bill_aggregate
) -> Dict[int, BilledEntity]:
    room_stay_expense_charge_id_map = (
        booking_aggregate.get_room_stay_expense_charge_id_map()
    )
    charge_id_primary_guest_billed_entity_map = dict()
    for room_stay in booking_aggregate.room_stays:
        default_billed_entity = get_room_stay_default_billed_entity(
            room_stay, booking_aggregate, bill_aggregate
        )
        for charge_id in room_stay.charge_ids + room_stay_expense_charge_id_map.get(
            room_stay.room_stay_id, []
        ):
            charge_id_primary_guest_billed_entity_map[charge_id] = default_billed_entity
    return charge_id_primary_guest_billed_entity_map


def build_charge_to_billed_entity_map_for_extras(
    booking_aggregate, bill_aggregate, billed_entity_category
) -> Dict[int, BilledEntity]:
    room_stay_expense_charge_id_map = (
        booking_aggregate.get_room_stay_expense_charge_id_map()
    )
    charge_id_primary_guest_billed_entity_map = dict()
    for room_stay in booking_aggregate.room_stays:
        default_billed_entity = get_billed_entity_for_given_category(
            bill_aggregate, billed_entity_category, booking_aggregate, room_stay
        )
        for charge_id in room_stay.charge_ids + room_stay_expense_charge_id_map.get(
            room_stay.room_stay_id, []
        ):
            charge_id_primary_guest_billed_entity_map[charge_id] = default_billed_entity
    return charge_id_primary_guest_billed_entity_map


def get_paid_by_from_billed_entity(
    billed_entity: BilledEntity,
    default_be_category: BilledEntityCategory,
    bill_aggregate,
):
    if billed_entity.category == BilledEntityCategory.BOOKER:
        if billed_entity.secondary_category in [
            BilledEntityCategory.PRIMARY_GUEST,
            BilledEntityCategory.CONSUMING_GUESTS,
        ]:
            return PaymentReceiverTypes.GUEST
        return get_paid_by_from_billed_entity_category(
            default_be_category, bill_aggregate
        )
    return get_paid_by_from_billed_entity_category(
        billed_entity.category, bill_aggregate
    )


def get_paid_by_from_billed_entity_category(
    be_category: BilledEntityCategory, bill_aggregate
):
    if be_category in [
        BilledEntityCategory.PRIMARY_GUEST,
        BilledEntityCategory.CONSUMING_GUESTS,
    ]:
        return PaymentReceiverTypes.GUEST
    if be_category == BilledEntityCategory.BOOKER:
        billed_entity = bill_aggregate.get_billed_entity_for_category(be_category)
        if billed_entity.secondary_category in [
            BilledEntityCategory.PRIMARY_GUEST,
            BilledEntityCategory.CONSUMING_GUESTS,
        ]:
            return PaymentReceiverTypes.GUEST
        if bill_aggregate.get_billed_entity_for_category(
            BilledEntityCategory.TRAVEL_AGENT
        ):
            return PaymentReceiverTypes.TA
        if bill_aggregate.get_billed_entity_for_category(
            BilledEntityCategory.BOOKER_COMPANY
        ):
            return PaymentReceiverTypes.CORPORATE
        return PaymentReceiverTypes.GUEST
    return BILLED_ENTITY_CATEGORY_TO_PAID_BY_MAP.get(be_category)
