from object_registry import register_instance
from prometheus.application import crs_context_middleware
from prometheus.domain.catalog.repositories import HotelRepository, RoomRepository
from prometheus.domain.inventory.repositories.dnr_repository import DNRRepository
from ths_common.constants.inventory_constants import DNRType
from ths_common.exceptions import AggregateNotFound


@register_instance(dependencies=[HotelRepository, RoomRepository, DNRRepository])
class GetDNRsQueryHandler:
    def __init__(
        self,
        hotel_repository: HotelRepository,
        room_repository: RoomRepository,
        dnr_repository: DNRRepository,
    ):
        self.hotel_repository = hotel_repository
        self.room_repository = room_repository
        self.dnr_repository = dnr_repository

    def handle(self, hotel_id, get_dnr_data: dict):
        if not self.hotel_repository.exists(hotel_id):
            raise AggregateNotFound("HotelAggregate", hotel_id)

        crs_context_middleware.set_hotel_context(hotel_id)

        room_ids = get_dnr_data.get('room_ids')
        if not room_ids:
            room_ids = self.room_repository.fetch_room_ids_for_hotel(hotel_id)

        dnr_types = [
            dnr_type for dnr_type in DNRType.all() if dnr_type != DNRType.INACTIVE_ROOM
        ]
        if get_dnr_data.get('include_inactive_room_dnrs'):
            dnr_types.append(DNRType.INACTIVE_ROOM)

        dnr_aggregates = self.dnr_repository.load_dnrs(
            hotel_id=hotel_id,
            room_ids=room_ids,
            from_date=get_dnr_data.get('from_date'),
            to_date=get_dnr_data.get('to_date'),
            source=get_dnr_data.get('source'),
            types=dnr_types,
        )

        effective_status = get_dnr_data.get('effective_status')
        if effective_status:
            dnr_aggregates = [
                dnr_aggregate
                for dnr_aggregate in dnr_aggregates
                if dnr_aggregate.dnr.effective_status == effective_status
            ]

        limit = get_dnr_data.get('limit')
        offset = get_dnr_data.get('offset')

        if offset is not None and limit is not None:
            dnrs = [
                dnr_aggregate.dnr
                for dnr_aggregate in dnr_aggregates[offset : offset + limit]
            ]
        else:
            dnrs = [dnr_aggregate.dnr for dnr_aggregate in dnr_aggregates]
        return dnrs, len(dnr_aggregates)
