from object_registry import register_instance
from prometheus import crs_context
from prometheus.application import crs_context_middleware
from prometheus.application.audit_trail.decorator import audit_dnr, audit_housekeeping
from prometheus.application.decorators import session_manager
from prometheus.application.inventory.command_handlers.resolve_dnr import (
    ResolveDNRCommandHandler,
)
from prometheus.application.services.integration_event_application_service import (
    IntegrationEventApplicationService,
)
from prometheus.application.services.room_stay_overflow_service import (
    RoomStayOverflowService,
)
from prometheus.domain.catalog.repositories import HotelRepository, RoomRepository
from prometheus.domain.hotel_config.repository import HotelConfigRepository
from prometheus.domain.inventory.repositories import (
    RoomAllotmentRepository,
    RoomTypeInventoryRepository,
)
from prometheus.domain.inventory.repositories.dnr_repository import DNRRepository
from ths_common.constants.audit_trail_constants import DNRAuditType
from ths_common.constants.integration_event_constants import IntegrationEventType
from ths_common.exceptions import AggregateNotFound


@register_instance(
    dependencies=[
        HotelRepository,
        HotelConfigRepository,
        RoomRepository,
        RoomAllotmentRepository,
        RoomTypeInventoryRepository,
        DNRRepository,
        RoomStayOverflowService,
        ResolveDNRCommandHandler,
    ]
)
class BulkResolveDNRCommandHandler:
    def __init__(
        self,
        hotel_repository: HotelRepository,
        hotel_config_repository: HotelConfigRepository,
        room_repository: RoomRepository,
        room_allotment_repository: RoomAllotmentRepository,
        room_type_inventory_repository: RoomTypeInventoryRepository,
        dnr_repository: DNRRepository,
        room_stay_overflow_service: RoomStayOverflowService,
        dnr_resolve_handler: ResolveDNRCommandHandler,
    ):
        self.hotel_repository = hotel_repository
        self.hotel_config_repository = hotel_config_repository
        self.room_repository = room_repository
        self.room_allotment_repository = room_allotment_repository
        self.room_type_inventory_repository = room_type_inventory_repository
        self.dnr_repository = dnr_repository
        self.room_stay_overflow_service = room_stay_overflow_service
        self.dnr_resolve_handler = dnr_resolve_handler

    @session_manager(commit=True)
    @audit_dnr(audit_type=DNRAuditType.DNR_RESOLVED)
    @audit_housekeeping(action_performed="resolve_dnr")
    def handle(self, hotel_id, dnr_ids, user_data):
        hotel_aggregate = crs_context_middleware.set_hotel_context(hotel_id)
        hotel_context = crs_context.get_hotel_context()

        dnr_aggregates = self.dnr_repository.load_dnrs_by_id(hotel_id, dnr_ids)
        if len(dnr_aggregates) != len(dnr_ids):
            retrieved_dnr_ids = [agg.dnr.dnr_id for agg in dnr_aggregates]
            raise AggregateNotFound(
                "DNRAggregate", ",".join(set(dnr_ids) - set(retrieved_dnr_ids))
            )
        room_ids = [dnr_aggregate.dnr.room_id for dnr_aggregate in dnr_aggregates]
        room_aggregates = self.room_repository.load_multiple(
            hotel_id, room_ids=room_ids
        )
        room_id_to_room_aggregate_map = {
            room_aggregate.room.room_id: room_aggregate
            for room_aggregate in room_aggregates
        }

        grouped_room_allotment_aggregates = (
            self.room_allotment_repository.load_multiple(
                hotel_id=hotel_aggregate.hotel.hotel_id,
                room_ids=room_ids,
                for_update=True,
            )
        )
        start_date = min(
            [dnr_aggregate.dnr.start_date for dnr_aggregate in dnr_aggregates]
        )
        end_date = max([dnr_aggregate.dnr.end_date for dnr_aggregate in dnr_aggregates])

        room_type_inventory_aggregates = (
            self.room_type_inventory_repository.load_multiple(
                hotel_id=hotel_aggregate.hotel.hotel_id,
                room_type_ids=[
                    room_aggregate.room.room_type_id
                    for room_aggregate in room_aggregates
                ],
                from_date=start_date,
                to_date=end_date,
            )
        )
        room_type_to_inventory_map = {
            inventory_aggregate.room_type_inventory.room_type_id: inventory_aggregate
            for inventory_aggregate in room_type_inventory_aggregates
        }
        room_type_ids = set()
        for dnr_aggregate in dnr_aggregates:
            room_allotment_aggregate = grouped_room_allotment_aggregates[
                dnr_aggregate.dnr.room_id
            ]
            room_aggregate = room_id_to_room_aggregate_map[dnr_aggregate.dnr.room_id]
            room_type_inventory_aggregate = room_type_to_inventory_map[
                room_aggregate.room.room_type_id
            ]
            room_type_ids.add(room_aggregate.room.room_type_id)
            date_wise_room_type_availability_change = (
                self.dnr_resolve_handler.resolve_dnr(
                    dnr_aggregate,
                    room_allotment_aggregate,
                    room_type_inventory_aggregate,
                    user_data,
                )
            )
            IntegrationEventApplicationService.create_dnr_events(
                hotel_id,
                dnr_aggregate,
                {
                    room_aggregate.room.room_type_id: date_wise_room_type_availability_change
                },
                event_type=IntegrationEventType.DNR_RELEASED,
                user_action="resolve_dnr",
            )

            if dnr_aggregate.dnr.start_date <= hotel_context.current_date():
                if not room_allotment_aggregate.is_occupied():
                    room_allotment_aggregate.update_housekeeping_status_on_dnr_resolution()

        self.room_allotment_repository.update_all(
            grouped_room_allotment_aggregates.values()
        )
        self.dnr_repository.update_all(dnr_aggregates)
        self.room_type_inventory_repository.update_all(room_type_inventory_aggregates)

        for dnr_aggregate in dnr_aggregates:
            room_allotment_aggregate = grouped_room_allotment_aggregates[
                dnr_aggregate.dnr.room_id
            ]
            housekeeping_record = room_allotment_aggregate.housekeeping_record
            hotel_context = crs_context.get_hotel_context()
            if hotel_context.housekeeping_enabled:
                IntegrationEventApplicationService.create_housekeeping_status_event(
                    housekeeping_record=housekeeping_record, user_action="resolve_dnr"
                )

        self.room_stay_overflow_service.recompute_and_unmark_overflows(
            hotel_id,
            start_date,
            end_date,
            list(room_type_ids),
            user_action="resolve_dnr",
        )
        return [dnr_aggregate.dnr for dnr_aggregate in dnr_aggregates]
