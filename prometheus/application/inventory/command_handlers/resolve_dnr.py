from object_registry import register_instance
from prometheus import crs_context
from prometheus.application import crs_context_middleware
from prometheus.application.audit_trail.decorator import audit_dnr, audit_housekeeping
from prometheus.application.decorators import session_manager
from prometheus.application.services.integration_event_application_service import (
    IntegrationEventApplicationService,
)
from prometheus.application.services.room_stay_overflow_service import (
    RoomStayOverflowService,
)
from prometheus.domain.catalog.repositories import HotelRepository, RoomRepository
from prometheus.domain.hotel_config.repository import HotelConfigRepository
from prometheus.domain.inventory.repositories import (
    RoomAllotmentRepository,
    RoomTypeInventoryRepository,
)
from prometheus.domain.inventory.repositories.dnr_repository import DNRRepository
from prometheus.domain.inventory.services import DNRService
from prometheus.domain.policy.engine import RuleEngine
from prometheus.domain.policy.facts import DNRFacts
from ths_common.constants.audit_trail_constants import DNRAuditType
from ths_common.constants.integration_event_constants import IntegrationEventType


@register_instance(
    dependencies=[
        HotelRepository,
        HotelConfigRepository,
        RoomRepository,
        RoomAllotmentRepository,
        RoomTypeInventoryRepository,
        DNRRepository,
        DNRService,
        RoomStayOverflowService,
    ]
)
class ResolveDNRCommandHandler:
    def __init__(
        self,
        hotel_repository: HotelRepository,
        hotel_config_repository: HotelConfigRepository,
        room_repository: RoomRepository,
        room_allotment_repository: RoomAllotmentRepository,
        room_type_inventory_repository: RoomTypeInventoryRepository,
        dnr_repository: DNRRepository,
        dnr_service: DNRService,
        room_stay_overflow_service: RoomStayOverflowService,
    ):
        self.hotel_repository = hotel_repository
        self.hotel_config_repository = hotel_config_repository
        self.room_repository = room_repository
        self.room_allotment_repository = room_allotment_repository
        self.room_type_inventory_repository = room_type_inventory_repository
        self.dnr_repository = dnr_repository
        self.dnr_service = dnr_service
        self.room_stay_overflow_service = room_stay_overflow_service

    @session_manager(commit=True)
    @audit_dnr(audit_type=DNRAuditType.DNR_RESOLVED)
    @audit_housekeeping(action_performed="resolve_dnr")
    def handle(self, hotel_id, dnr_id, user_data):
        hotel_aggregate = crs_context_middleware.set_hotel_context(hotel_id)
        hotel_context = crs_context.get_hotel_context()

        dnr_aggregate = self.dnr_repository.load_dnr_for_update(hotel_id, dnr_id)

        room_aggregate = self.room_repository.load(
            room_id=dnr_aggregate.dnr.room_id, hotel_id=hotel_id
        )
        room_type_id = room_aggregate.room.room_type_id

        room_allotment_aggregate = self.room_allotment_repository.load_for_update(
            hotel_id=hotel_aggregate.hotel.hotel_id, room_id=room_aggregate.room.room_id
        )

        room_type_inventory_aggregates = (
            self.room_type_inventory_repository.load_multiple(
                hotel_id=hotel_aggregate.hotel.hotel_id,
                room_type_ids=[room_aggregate.room.room_type_id],
                from_date=dnr_aggregate.dnr.start_date,
                to_date=dnr_aggregate.dnr.end_date,
            )
        )
        room_type_inventory_aggregate = (
            room_type_inventory_aggregates[0]
            if room_type_inventory_aggregates
            else None
        )

        date_wise_room_type_availability_change = self.resolve_dnr(
            dnr_aggregate,
            room_allotment_aggregate,
            room_type_inventory_aggregate,
            user_data,
        )

        if dnr_aggregate.dnr.start_date <= hotel_context.current_date():
            if not room_allotment_aggregate.is_occupied():
                room_allotment_aggregate.update_housekeeping_status_on_dnr_resolution()

        self.room_allotment_repository.update(room_allotment_aggregate)
        self.dnr_repository.update(dnr_aggregate)
        self.room_type_inventory_repository.update(room_type_inventory_aggregate)

        IntegrationEventApplicationService.create_dnr_events(
            hotel_id,
            dnr_aggregate,
            {room_type_id: date_wise_room_type_availability_change},
            event_type=IntegrationEventType.DNR_RELEASED,
            user_action="resolve_dnr",
        )

        housekeeping_record = room_allotment_aggregate.housekeeping_record
        hotel_context = crs_context.get_hotel_context()
        if hotel_context.housekeeping_enabled:
            IntegrationEventApplicationService.create_housekeeping_status_event(
                housekeeping_record=housekeeping_record, user_action="resolve_dnr"
            )

        self.room_stay_overflow_service.recompute_and_unmark_overflows(
            hotel_id,
            dnr_aggregate.dnr.start_date,
            dnr_aggregate.dnr.end_date,
            [room_type_id],
            user_action="resolve_dnr",
        )
        return dnr_aggregate.dnr

    def resolve_dnr(
        self,
        dnr_aggregate,
        room_allotment_aggregate,
        room_type_inventory_aggregate,
        user_data,
    ):
        if not crs_context.should_bypass_privilege_checks():
            RuleEngine.action_allowed(
                action='resolve_dnr',
                facts=DNRFacts(
                    dnr_aggregate.dnr,
                    user_type=user_data.user_type,
                    hotel_context=crs_context.get_hotel_context(),
                ),
                fail_on_error=True,
            )
        date_wise_room_type_availability_change = self.dnr_service.resolve_dnr(
            dnr_aggregate, room_type_inventory_aggregate, room_allotment_aggregate
        )
        return date_wise_room_type_availability_change
