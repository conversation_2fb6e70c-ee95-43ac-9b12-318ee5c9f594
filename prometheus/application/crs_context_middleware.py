from object_registry import locate_instance
from prometheus import crs_context
from prometheus.domain.catalog.repositories import HotelRepository
from prometheus.domain.hotel_config.repository import HotelConfigRepository


def set_hotel_context(hotel_id):
    hotel_repository = locate_instance(HotelRepository)
    hotel_config_repository = locate_instance(HotelConfigRepository)
    hotel_aggregate = hotel_repository.load(hotel_id)
    hotel_config_aggregate = hotel_config_repository.load(hotel_id)
    crs_context.set_hotel_context(
        hotel_aggregate, hotel_config_aggregate=hotel_config_aggregate
    )
    return hotel_aggregate
