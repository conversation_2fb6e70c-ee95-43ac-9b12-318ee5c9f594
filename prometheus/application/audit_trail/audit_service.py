import logging
from typing import List

from treebo_commons.request_tracing.context import get_current_application_trace

from object_registry import register_instance
from prometheus import crs_context
from prometheus.application import crs_context_middleware
from prometheus.core.base_domain_event import BaseDomainEvent
from prometheus.domain.billing.entities.billed_entity import BilledEntityAccountVO
from prometheus.domain.billing.repositories import BillRepository
from prometheus.domain.booking.factories.audit_trail_factory import AuditTrailFactory
from prometheus.domain.booking.repositories.booking_audit_trail_repository import (
    BookingAuditTrailRepository,
)
from prometheus.domain.booking.repositories.booking_repository import BookingRepository
from prometheus.domain.booking.repositories.expense_item_repository import (
    ExpenseItemRepository,
)
from prometheus.domain.catalog.repositories import HotelRepository
from prometheus.domain.catalog.repositories.room_repository import RoomRepository
from prometheus.domain.catalog.repositories.room_type_repository import (
    RoomTypeRepository,
)
from prometheus.domain.catalog.repositories.sku_category_repository import (
    SkuCategoryRepository,
)
from prometheus.domain.inventory.factories.dnr_audit_trail_factory import (
    DNRAuditTrailFactory,
)
from prometheus.domain.inventory.repositories.dnr_audit_trail_repository import (
    DNRAuditTrailRepository,
)
from prometheus.domain.inventory.repositories.dnr_repository import DNRRepository
from ths_common.constants.audit_trail_constants import AuditType, DNRAuditType
from ths_common.value_objects import UserData

logger = logging.getLogger(__name__)


@register_instance(
    dependencies=[
        BookingAuditTrailRepository,
        BookingRepository,
        RoomTypeRepository,
        SkuCategoryRepository,
        DNRRepository,
        RoomRepository,
        DNRAuditTrailRepository,
        ExpenseItemRepository,
        HotelRepository,
        BillRepository,
    ]
)
class AuditService(object):
    def __init__(
        self,
        booking_audit_trail_repository: BookingAuditTrailRepository,
        booking_repository,
        room_type_repository,
        sku_category_repository,
        dnr_repository,
        room_repository,
        dnr_audit_trail_repository,
        expense_item_repository,
        hotel_repo,
        bill_repository,
    ):
        self.booking_audit_trail_repository = booking_audit_trail_repository
        self.booking_repository = booking_repository
        self.room_type_repository = room_type_repository
        self.sku_category_repository = sku_category_repository
        self.dnr_repository = dnr_repository
        self.room_repository = room_repository
        self.dnr_audit_trail_repository = dnr_audit_trail_repository
        self.expense_item_repository = expense_item_repository
        self.hotel_repo = hotel_repo
        self.bill_repository = bill_repository

    def audit_event(
        self,
        user_data: UserData,
        application,
        audit_type: AuditType,
        audit_time,
        domain_events: List[BaseDomainEvent],
        request_id=None,
        action_id=None,
    ):
        booking_id = crs_context.get_current_booking_id()
        assert booking_id is not None, "Set booking ID context"

        logger.debug(
            "Domain Events raised in the API: %s",
            [event.event_type() for event in domain_events],
        )

        if not domain_events:
            return

        # TODO: It's again loading entire booking and bill aggregate, outside of actual flow. This is clearly
        #  duplicate query, and must be eliminated
        booking_aggregate = crs_context.get_current_booking()
        if not booking_aggregate:
            booking_aggregate = self.booking_repository.load(booking_id)

        current_business_date = crs_context.get_hotel_context().current_date()
        room_stay_map = {
            room_stay.room_stay_id: room_stay
            for room_stay in booking_aggregate.get_all_room_stays()
        }
        customer_map = {
            customer.customer_id: customer
            for customer in booking_aggregate.get_all_customers()
        }

        bill_aggregate = crs_context.get_current_bill()
        if not bill_aggregate:
            bill_aggregate = self.bill_repository.load(booking_aggregate.bill_id)
        if not bill_aggregate:
            billed_entities = self.bill_repository.load_billed_entities(
                booking_aggregate.bill_id, include_deleted=True
            )
        else:
            billed_entities = bill_aggregate._billed_entities

        billed_entity_map = {
            billed_entity.billed_entity_id: billed_entity
            for billed_entity in billed_entities
        }
        folio_name_map = {
            BilledEntityAccountVO(
                folio.billed_entity_id, folio.account_number
            ): bill_aggregate.get_folio_name(folio.folio_number)
            for folio in bill_aggregate.folios
        }
        room_type_map = self.room_type_repository.load_type_map()
        sku_category_map = {
            sku_category_aggregate.sku_category.sku_category_id: sku_category_aggregate.sku_category
            for sku_category_aggregate in self.sku_category_repository.load_all()
        }
        expense_item_map = {
            expense_item.expense_item_id: expense_item
            for expense_item in self.expense_item_repository.load_all(
                include_linked=True
            )
        }

        audit_payload = []
        for domain_event in domain_events:
            domain_event.update_mapping(
                customer_map=customer_map,
                room_type_map=room_type_map,
                room_stay_map=room_stay_map,
                sku_category_map=sku_category_map,
                expense_item_map=expense_item_map,
                billed_entity_map=billed_entity_map,
                folio_name_map=folio_name_map,
            )
            audit_payload.append(domain_event.serialize_event())

        audit_trail_aggregate = AuditTrailFactory.create_audit_trail(
            user=user_data.user,
            user_type=user_data.user_type,
            application=application,
            timestamp=audit_time,
            request_id=request_id,
            booking_id=booking_id,
            audit_type=audit_type,
            audit_payload={"domain_events": audit_payload},
            action_id=action_id,
            current_business_date=current_business_date,
            auth_id=user_data.user_auth_id,
            application_trace=get_current_application_trace(),
        )
        self.booking_audit_trail_repository.save(audit_trail_aggregate)

    def audit_dnr_event(
        self,
        user_data: UserData,
        application,
        audit_type: DNRAuditType,
        audit_time,
        domain_events: List[BaseDomainEvent],
        request_id=None,
    ):
        if not domain_events:
            return

        for domain_event in domain_events:
            assert hasattr(
                domain_event, 'dnr_id'
            ), "All DNR Domain Events should have dnr_id as attribute"

        dnr_id = domain_events[0].dnr_id
        dnr_aggregate = self.dnr_repository.load_dnr(dnr_id)
        crs_context_middleware.set_hotel_context(dnr_aggregate.dnr.hotel_id)
        room_aggregate = self.room_repository.load(
            hotel_id=dnr_aggregate.dnr.hotel_id, room_id=dnr_aggregate.dnr.room_id
        )
        audit_payload = []
        for domain_event in domain_events:
            domain_event.update_mapping(
                room_id_map={room_aggregate.room.room_id: room_aggregate.room}
            )
            audit_payload.append(domain_event.serialize_event())

        audit_trail_aggregate = DNRAuditTrailFactory.create_audit_trail(
            user=user_data.user,
            user_type=user_data.user_type,
            application=application,
            timestamp=audit_time,
            request_id=request_id,
            dnr_id=dnr_id,
            audit_type=audit_type,
            audit_payload={"domain_events": audit_payload},
            application_trace=get_current_application_trace(),
        )
        self.dnr_audit_trail_repository.save(audit_trail_aggregate)
