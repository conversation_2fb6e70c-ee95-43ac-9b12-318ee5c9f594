from prometheus.domain.policy.errors import PolicyError
from prometheus.domain.policy.facts.facts import Facts
from prometheus.domain.policy.rule.base import BaseRule
from ths_common.exceptions import PolicyAuthException


class HotelInvoiceGenerationRule(BaseRule):
    def allow(self, facts: Facts, privileges=None):
        if not facts.user_type_super_admin():
            raise PolicyAuthException(
                error=PolicyError.NOT_AUTHORIZED_TO_GENERATE_HOTEL_TO_RESELLER_INVOICE
            )

        return True
