from prometheus.domain.policy.errors import PolicyError
from prometheus.domain.policy.facts.facts import Facts
from prometheus.domain.policy.rule.base import BaseRule
from ths_common.constants.user_constants import PrivilegeCode
from ths_common.exceptions import PolicyAuthException


class EditBillingInstructionRule(BaseRule):
    def allow(self, facts: Facts, privileges=None):
        if (
            facts.is_cancelled_or_noshow_booking()
            and PrivilegeCode.EDIT_NOSHOW_AND_CANC_BOOKING not in privileges
        ):
            raise PolicyAuthException(
                error=PolicyError.BOOKING_MODIFICATION_NOT_ALLOWED_FOR_CANCELLED_OR_NOSHOW_BOOKING
            )

        if (
            not facts.hotel_live()
            and PrivilegeCode.FULL_ACCESS_IF_HOTEL_NOT_LIVE not in privileges
        ):
            raise PolicyAuthException(
                error=PolicyError.BOOKING_MODIFICATION_NOT_ALLOWED_BEFORE_HOTEL_LIVE_DATE
            )

        return True
