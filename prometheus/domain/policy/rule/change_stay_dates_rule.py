from prometheus import crs_context
from prometheus.domain.policy.errors import PolicyError
from prometheus.domain.policy.facts.facts import Facts
from prometheus.domain.policy.facts.room_stay_facts import RoomStayFacts
from prometheus.domain.policy.rule.base import BaseRule
from ths_common.constants.user_constants import PrivilegeCode
from ths_common.exceptions import PolicyAuthException


class ChangeStayDatesRule(BaseRule):
    def allow(self, facts: Facts, privileges=None):
        assert isinstance(facts, RoomStayFacts)
        if crs_context.is_treebo_tenant():
            if (
                PrivilegeCode.EDIT_BOOKING_FOR_CH not in privileges
                or facts.get_channel_code()
                not in privileges[PrivilegeCode.EDIT_BOOKING_FOR_CH]
            ):
                raise PolicyAuthException(
                    error=PolicyError.STAY_DATES_CHANGE_NOT_ALLOWED_FOR_BOOKING_FROM_THIS_CHANNEL
                )

        if facts.is_backdated_booking_received():
            raise PolicyAuthException(
                error=PolicyError.CANNOT_MOVE_ROOM_STAY_TO_BACK_DATED_CHECKIN
            )

        if (
            not facts.hotel_live()
            and PrivilegeCode.FULL_ACCESS_IF_HOTEL_NOT_LIVE not in privileges
        ):
            raise PolicyAuthException(
                error=PolicyError.STAY_DATES_CANNOT_BE_CHANGED_FOR_BOOKING_PRIOR_TO_HOTEL_LAUNCH
            )

        return True
