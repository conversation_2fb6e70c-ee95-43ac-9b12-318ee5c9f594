from prometheus.domain.policy.facts.facts import Facts
from ths_common.constants.billing_constants import PaymentInstruction


class ExpenseV3Facts(Facts):
    def __init__(
        self,
        expense,
        current_time=None,
        user_type=None,
        action_payload=None,
        hotel_context=None,
        **aggregates
    ):
        self.expense = expense
        super().__init__(
            current_time=current_time,
            user_type=user_type,
            action_payload=action_payload,
            hotel_context=hotel_context,
            **aggregates
        )

    def is_credit_folio(self):
        for bi in self.expense.get('billing_instructions') or []:
            if (
                bi.payment_instruction.value
                in PaymentInstruction.PAY_AFTER_CHECKOUT.value
            ):
                return True
        return False
