from treebo_commons.utils.dateutils import current_datetime


class CriticalTask:
    def __init__(self, type_, entity_name, entity_ids, expiry_time=None):
        self.type_ = type_
        self.entity_name = entity_name
        self.entity_ids = entity_ids
        self.expiry_time = expiry_time

    @property
    def remaining_time(self):
        if not self.expiry_time:
            return None
        now = current_datetime()
        remaining = self.expiry_time - now
        if remaining.total_seconds() < 0:
            return 0
        return remaining.total_seconds

    @property
    def count(self):
        return len(self.entity_ids)

    @property
    def type(self):
        return self.type_
