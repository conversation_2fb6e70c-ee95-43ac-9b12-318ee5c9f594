class CriticalTaskSearchQuery:
    def __init__(self, hotel_id, types=None):
        self.hotel_id = hotel_id
        self.types = types if types is not None else []

    @property
    def critical_checkins(self):
        if self.types == []:
            return True
        return 'critical_checkin' in self.types

    @property
    def critical_checkouts(self):
        if self.types == []:
            return True
        return 'critical_checkout' in self.types

    @property
    def pending_web_checkins(self):
        if self.types == []:
            return True
        return 'pending_web_checkins' in self.types

    @property
    def open_cashier_sessions(self):
        if self.types == []:
            return True
        return 'open_cashier_sessions' in self.types
