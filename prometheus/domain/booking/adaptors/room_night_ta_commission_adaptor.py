from treebo_commons.money import Money
from treebo_commons.money.constants import CurrencyType

from prometheus import crs_context
from prometheus.domain.booking.entities.room_night_commission import (
    RoomNightTACommission,
)
from prometheus.domain.booking.models import TACommissionModel
from prometheus.infrastructure.database.base_db_to_domain_entity_adaptor import (
    BaseAdaptor,
)
from ths_common.constants.booking_constants import TACommissionStatus


class RoomNightTACommissionAdaptor(BaseAdaptor):
    def to_db_entity(self, domain_entity: RoomNightTACommission, **kwargs):
        return TACommissionModel(
            booking_id=kwargs['booking_id'],
            room_stay_id=kwargs['room_stay_id'],
            ta_commission_id=domain_entity.ta_commission_id,
            linked_ta_commission_id=domain_entity.linked_ta_commission_id,
            status=domain_entity.status.value,
            deleted=domain_entity.deleted,
            is_allowance=domain_entity.is_allowance,
            pretax_amount=domain_entity.pretax_amount.amount,
            posttax_amount=domain_entity.posttax_amount.amount,
            room_night_price=domain_entity.room_night_price.amount,
            applicable_on=domain_entity.applicable_on,
            reissued_on=domain_entity.reissued_on,
            locked_on=domain_entity.locked_on,
        )

    def to_domain_entity(self, db_entity: TACommissionModel, **kwargs):
        base_currency = (
            crs_context.hotel_context.base_currency
            if (crs_context.hotel_context and crs_context.hotel_context.base_currency)
            else CurrencyType.INR
        )
        return RoomNightTACommission(
            ta_commission_id=db_entity.ta_commission_id,
            linked_ta_commission_id=db_entity.linked_ta_commission_id,
            status=TACommissionStatus(db_entity.status),
            pretax_amount=Money(db_entity.pretax_amount, currency=base_currency),
            posttax_amount=Money(db_entity.posttax_amount, currency=base_currency),
            room_night_price=Money(db_entity.room_night_price, currency=base_currency),
            applicable_on=db_entity.applicable_on,
            reissued_on=db_entity.reissued_on,
            locked_on=db_entity.locked_on,
            deleted=db_entity.deleted,
            is_allowance=db_entity.is_allowance,
            dirty=False,
            new=False,
        )
