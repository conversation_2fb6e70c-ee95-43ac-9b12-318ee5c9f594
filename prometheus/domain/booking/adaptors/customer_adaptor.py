from treebo_commons.utils import dateutils

from prometheus.domain.booking.entities.customer import Customer
from prometheus.domain.booking.models import CustomerModel
from prometheus.infrastructure.database.base_db_to_domain_entity_adaptor import (
    BaseAdaptor,
)
from ths_common.constants.booking_constants import Genders, ProfileTypes, Salutation
from ths_common.value_objects import (
    Address,
    CustomerVisaDetails,
    EmploymentDetails,
    GSTDetails,
    GuestMetadata,
    GuestPreference,
    IDProof,
    LoyaltyProgramDetails,
    PassportDetails,
    PhoneNumber,
    TravelBaseDetails,
    TravelDetails,
    VIPDetails,
)


class CustomerAdaptor(BaseAdaptor):
    def to_db_entity(self, domain_entity, booking_id):
        address = domain_entity.address
        gst_details = domain_entity.gst_details
        id_proof = domain_entity.id_proof
        customer_model = CustomerModel(
            customer_id=domain_entity.customer_id,
            booking_id=booking_id,
            external_ref_id=domain_entity.external_ref_id,
            profile_type=domain_entity.profile_type.value
            if domain_entity.profile_type
            else None,
            salutation=domain_entity.salutation.value
            if domain_entity.salutation
            else None,
            first_name=domain_entity.first_name,
            last_name=domain_entity.last_name,
            gender=domain_entity.gender.value if domain_entity.gender else None,
            age=domain_entity.age,
            nationality=domain_entity.nationality,
            country_code=domain_entity.phone.country_code
            if domain_entity.phone
            else None,
            phone=domain_entity.phone.number if domain_entity.phone else None,
            email=domain_entity.email,
            image_url=domain_entity.image_url,
            user_profile_id=domain_entity.user_profile_id,
            date_of_birth=domain_entity.date_of_birth,
            is_primary=domain_entity.is_primary,
            travel_details=domain_entity.travel_details.to_json()
            if domain_entity.travel_details
            else None,
            employment_details=domain_entity.employment_details.to_json()
            if domain_entity.employment_details
            else None,
            eregcard_url=domain_entity.eregcard_url,
            eregcard_status=domain_entity.eregcard_status,
            verifier_signature=domain_entity.verifier_signature,
            is_vip=domain_entity.is_vip,
            guest_preferences=domain_entity.guest_preferences.to_json()
            if domain_entity.guest_preferences
            else None,
            guest_metadata=GuestMetadata.to_json(domain_entity.guest_metadata)
            if domain_entity.guest_metadata
            else None,
            billed_entity_id=domain_entity.billed_entity_id,
            company_billed_entity_id=domain_entity.company_billed_entity_id,
            vip_details=domain_entity.vip_details.to_json()
            if domain_entity.vip_details
            else None,
            loyalty_program_details=domain_entity.loyalty_program_details.to_json()
            if domain_entity.loyalty_program_details
            else None,
            passport_details=domain_entity.passport_details.to_json()
            if domain_entity.passport_details
            else None,
            visa_details=domain_entity.visa_details.to_json()
            if domain_entity.visa_details
            else None,
            arrival_departure_details={
                'arrival_details': domain_entity.arrival_details.to_json()
                if domain_entity.arrival_details
                else None,
                'departure_details': domain_entity.departure_details.to_json()
                if domain_entity.departure_details
                else None,
            },
            dummy=domain_entity.dummy,
            deleted=domain_entity.deleted,
        )

        if address:
            customer_model.set_address(address)
        else:
            customer_model.remove_address()

        if gst_details:
            customer_model.set_gst_details(gst_details)
        else:
            customer_model.remove_gst_details()

        if id_proof:
            customer_model.set_id_proof(id_proof)
        else:
            customer_model.remove_id_proof()
        return customer_model

    def to_domain_entity(self, db_entity):
        address, phone, gst_details, id_proof = None, None, None, None
        if (
            db_entity.addr_field1
            or db_entity.addr_field2
            or db_entity.addr_city
            or db_entity.addr_state
            or db_entity.addr_country
            or db_entity.addr_pincode
        ):
            address = Address(
                field_1=db_entity.addr_field1,
                field_2=db_entity.addr_field2,
                city=db_entity.addr_city,
                state=db_entity.addr_state,
                country=db_entity.addr_country,
                pincode=db_entity.addr_pincode,
            )

        if db_entity.phone is not None:
            phone = PhoneNumber(
                number=db_entity.phone, country_code=db_entity.country_code
            )

        if (
            db_entity.gst_addr_field1
            or db_entity.gst_addr_field2
            or db_entity.gst_addr_city
            or db_entity.gst_addr_state
            or db_entity.gst_addr_country
            or db_entity.gst_addr_pincode
        ):
            gst_address = Address(
                field_1=db_entity.gst_addr_field1,
                field_2=db_entity.gst_addr_field2,
                city=db_entity.gst_addr_city,
                state=db_entity.gst_addr_state,
                country=db_entity.gst_addr_country,
                pincode=db_entity.gst_addr_pincode,
            )
        else:
            gst_address = None

        if gst_address or db_entity.legal_name or db_entity.gstin_num:
            gst_details = GSTDetails(
                legal_name=db_entity.legal_name,
                gstin_num=db_entity.gstin_num,
                address=gst_address,
                is_sez=db_entity.is_sez,
                has_lut=db_entity.has_lut,
            )

        if (
            db_entity.id_proof_type
            or db_entity.id_kyc_url
            or db_entity.id_number
            or db_entity.id_proof_country_code
        ):
            id_proof = IDProof(
                id_proof_type=db_entity.id_proof_type
                if db_entity.id_proof_type
                else None,
                id_kyc_url=db_entity.id_kyc_url,
                id_number=db_entity.id_number,
                id_proof_country_code=db_entity.id_proof_country_code,
                issued_date=db_entity.id_proof_issued_date,
                issued_place=db_entity.id_proof_issued_place,
                attachment_id=db_entity.id_proof_attachment_id,
            )

        customer = Customer(
            customer_id=db_entity.customer_id,
            external_ref_id=db_entity.external_ref_id,
            profile_type=ProfileTypes(db_entity.profile_type)
            if db_entity.profile_type
            else None,
            salutation=Salutation(db_entity.salutation)
            if db_entity.salutation
            else None,
            first_name=db_entity.first_name,
            last_name=db_entity.last_name,
            gender=Genders(db_entity.gender) if db_entity.gender else None,
            age=db_entity.age,
            address=address,
            phone=phone,
            email=db_entity.email,
            image_url=db_entity.image_url,
            id_proof=id_proof,
            gst_details=gst_details,
            deleted=db_entity.deleted,
            nationality=db_entity.nationality,
            created_at=dateutils.localize_datetime(db_entity.created_at),
            modified_at=dateutils.localize_datetime(db_entity.modified_at),
            user_profile_id=db_entity.user_profile_id
            if db_entity.user_profile_id
            else None,
            date_of_birth=db_entity.date_of_birth,
            is_primary=db_entity.is_primary,
            travel_details=TravelDetails.from_json(db_entity.travel_details)
            if db_entity.travel_details
            else None,
            employment_details=EmploymentDetails.from_json(db_entity.employment_details)
            if db_entity.employment_details
            else None,
            eregcard_url=db_entity.eregcard_url,
            eregcard_status=db_entity.eregcard_status,
            verifier_signature=db_entity.verifier_signature,
            is_vip=db_entity.is_vip,
            guest_preferences=GuestPreference.from_json(db_entity.guest_preferences)
            if db_entity.guest_preferences
            else None,
            guest_metadata=GuestMetadata.from_json(db_entity.guest_metadata)
            if db_entity.guest_metadata
            else None,
            billed_entity_id=db_entity.billed_entity_id,
            company_billed_entity_id=db_entity.company_billed_entity_id,
            vip_details=VIPDetails.from_json(db_entity.vip_details)
            if db_entity.vip_details
            else None,
            loyalty_program_details=LoyaltyProgramDetails.from_json(
                db_entity.loyalty_program_details
            )
            if db_entity.loyalty_program_details
            else None,
            passport_details=PassportDetails.from_json(db_entity.passport_details)
            if db_entity.passport_details
            else None,
            visa_details=CustomerVisaDetails.from_json(db_entity.visa_details)
            if db_entity.visa_details
            else None,
            arrival_details=TravelBaseDetails.from_json(
                db_entity.arrival_departure_details.get('arrival_details')
            )
            if db_entity.arrival_departure_details
            and db_entity.arrival_departure_details.get('arrival_details')
            else None,
            departure_details=TravelBaseDetails.from_json(
                db_entity.arrival_departure_details.get('departure_details')
            )
            if db_entity.arrival_departure_details
            and db_entity.arrival_departure_details.get('departure_details')
            else None,
            dirty=False,
            new=False,
            dummy=db_entity.dummy,
        )
        return customer
