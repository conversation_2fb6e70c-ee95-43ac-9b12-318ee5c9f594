from prometheus.domain.booking.aggregates.attachment_aggregate import (
    AttachmentAggregate,
)
from prometheus.domain.booking.entities.attachment import Attachment
from ths_common.utils import id_generator_utils
from ths_common.value_objects import UserData


class AttachmentFactory(object):
    @staticmethod
    def create_attachment(
        booking_id, attachment_data: dict, user_data: UserData, source, status=None
    ):
        attachment_id = id_generator_utils.random_id_generator('ATT')
        uploaded_by = user_data.user_auth_id
        attachment = Attachment(
            attachment_id=attachment_id,
            booking_id=booking_id,
            original_url=attachment_data.get("url"),
            display_name=attachment_data.get("display_name"),
            file_type=attachment_data.get("file_type"),
            attachment_group=attachment_data.get("attachment_group"),
            source=source,
            uploaded_by=uploaded_by,
            status=status,
        )
        return AttachmentAggregate(attachment=attachment, user_data=user_data)
