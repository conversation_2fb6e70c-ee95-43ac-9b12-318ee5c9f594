from collections import Counter

from treebo_commons.utils import dateutils

from prometheus.core.globals import crs_context
from prometheus.domain.booking.errors import BookingErrors
from prometheus.domain.booking.exceptions import InvalidActionError
from prometheus.domain.booking.peek_machine import PeekMachine
from ths_common.constants.booking_constants import BookingActions, BookingStatus
from ths_common.constants.user_constants import PrivilegeCode


class BookingStateMachine(object):
    def __init__(self, model, states, initial_status):
        self.model = model
        assert hasattr(model, 'on_state_transition')
        self.machine = PeekMachine(
            model=self,
            states=states,
            initial=initial_status,
            auto_transitions=False,
            after_state_change='on_state_transition',
            name="Booking",
        )
        self.setup_state_transition()

    def on_state_transition(self, previous_state=None):
        if previous_state and previous_state.value is not self.state:
            raise InvalidActionError(error=BookingErrors.BOOKING_STATES_OUT_OF_SYNC)

        self.model.on_state_transition(BookingStatus(self.state))

    def setup_state_transition(self):
        transitions = [
            {
                'trigger': BookingActions.CONFIRM.value,
                'source': [BookingStatus.TEMPORARY.value, BookingStatus.RESERVED.value],
                'dest': BookingStatus.CONFIRMED.value,
            },
            {
                'trigger': BookingActions.CHECKIN.value,
                'source': [
                    BookingStatus.CONFIRMED.value,
                    BookingStatus.RESERVED.value,
                    BookingStatus.PART_CHECKIN.value,
                    BookingStatus.PART_CHECKOUT.value,
                    BookingStatus.CHECKED_OUT.value,
                ],
                'dest': BookingStatus.CHECKED_IN.value,
                'unless': ['_is_soft_booking', 'is_future_booking'],
            },
            {
                'trigger': BookingActions.CHECKIN.value,
                'source': [BookingStatus.PART_CHECKOUT.value],
                'dest': BookingStatus.PART_CHECKOUT.value,
                'unless': ['_is_soft_booking', 'is_future_booking'],
            },
            {
                'trigger': BookingActions.CHECKIN.value,
                'source': [
                    BookingStatus.CONFIRMED.value,
                    BookingStatus.PART_CHECKIN.value,
                    BookingStatus.RESERVED.value,
                ],
                'dest': BookingStatus.PART_CHECKIN.value,
                'unless': ['_is_soft_booking', 'is_future_booking'],
            },
            {
                'trigger': BookingActions.CHECKOUT.value,
                'source': [
                    BookingStatus.CHECKED_IN.value,
                    BookingStatus.PART_CHECKOUT.value,
                ],
                'dest': BookingStatus.CHECKED_OUT.value,
            },
            {
                'trigger': BookingActions.CANCEL.value,
                'source': [
                    BookingStatus.TEMPORARY.value,
                    BookingStatus.CONFIRMED.value,
                    BookingStatus.RESERVED.value,
                ],
                'dest': BookingStatus.CANCELLED.value,
            },
            {
                'trigger': BookingActions.NOSHOW.value,
                'source': [BookingStatus.CONFIRMED.value, BookingStatus.RESERVED.value],
                'dest': BookingStatus.NOSHOW.value,
                'conditions': [
                    '_has_current_time_crossed_midnight_of_checkin_datetime'
                ],
                'unless': ['_is_soft_booking'],
            },
            {
                'trigger': 'part_checkout',  # Trigger internal to domain
                'source': [
                    BookingStatus.CHECKED_IN.value,
                    BookingStatus.PART_CHECKIN.value,
                    BookingStatus.PART_CHECKOUT.value,
                    BookingStatus.CHECKED_OUT.value,
                ],
                'dest': BookingStatus.PART_CHECKOUT.value,
            },
            {
                'trigger': 'part_checkin',  # Trigger internal to domain
                'source': [
                    BookingStatus.CONFIRMED.value,
                    BookingStatus.PART_CHECKIN.value,
                    BookingStatus.CHECKED_IN.value,
                    BookingStatus.CHECKED_OUT.value,
                    BookingStatus.PART_CHECKOUT.value,
                    BookingStatus.RESERVED.value,
                ],
                'dest': BookingStatus.PART_CHECKIN.value,
                'unless': ['_is_soft_booking', 'is_future_booking'],
            },
            {
                'trigger': BookingActions.UNDO_CANCEL.value,
                'source': [BookingStatus.CANCELLED.value],
                'conditions': ['_previously_in_confirmed_state'],
                'dest': BookingStatus.CONFIRMED.value,
            },
            {
                'trigger': BookingActions.UNDO_NOSHOW.value,
                'source': [BookingStatus.NOSHOW.value],
                'conditions': ['_previously_in_confirmed_state'],
                'dest': BookingStatus.CONFIRMED.value,
            },
            {
                'trigger': BookingActions.UNDO_CHECKIN.value,
                'source': [
                    BookingStatus.CHECKED_IN.value,
                    BookingStatus.PART_CHECKIN.value,
                ],
                'conditions': ['_previously_in_confirmed_state'],
                'dest': BookingStatus.CONFIRMED.value,
            },
            {
                'trigger': BookingActions.UNDO_CANCEL.value,
                'source': [BookingStatus.CANCELLED.value],
                'conditions': ['_previously_in_reserved_state'],
                'dest': BookingStatus.RESERVED.value,
            },
            {
                'trigger': BookingActions.UNDO_CANCEL.value,
                'source': [BookingStatus.CANCELLED.value],
                'conditions': ['_previously_in_temp_state', '_hold_till_not_expired'],
                'dest': BookingStatus.TEMPORARY.value,
            },
            {
                'trigger': BookingActions.UNDO_NOSHOW.value,
                'source': [BookingStatus.NOSHOW.value],
                'conditions': ['_previously_in_reserved_state'],
                'dest': BookingStatus.RESERVED.value,
            },
            {
                'trigger': BookingActions.UNDO_CHECKIN.value,
                'source': [
                    BookingStatus.CHECKED_IN.value,
                    BookingStatus.PART_CHECKIN.value,
                ],
                'conditions': ['_previously_in_reserved_state'],
                'dest': BookingStatus.RESERVED.value,
            },
            {
                'trigger': BookingActions.UNDO_CONFIRM.value,
                'source': [BookingStatus.CONFIRMED.value],
                'dest': BookingStatus.RESERVED.value,
            },
        ]
        self.machine.add_transitions(transitions)

    def _previously_in_reserved_state(self, previous_state):
        return not previous_state or previous_state == BookingStatus.RESERVED

    def _previously_in_confirmed_state(self, previous_state):
        return previous_state and previous_state == BookingStatus.CONFIRMED

    def _previously_in_temp_state(self, previous_state):
        return previous_state and previous_state == BookingStatus.TEMPORARY

    def _hold_till_not_expired(self, previous_state):
        return self.model.booking.hold_till > dateutils.current_datetime()

    def _has_current_time_crossed_midnight_of_checkin_datetime(self):
        if not self.model.booking.checkin_date:
            return False

        privileges = crs_context.privileges_as_dict
        current_datetime = dateutils.current_datetime()
        if (
            privileges
            and PrivilegeCode.CAN_CANCEL_WALKIN_BOOKING_BEFORE_TIME in privileges
        ):
            checkin_date = self.model.booking.checkin_date.date()
            cancellation_time = privileges[
                PrivilegeCode.CAN_CANCEL_WALKIN_BOOKING_BEFORE_TIME
            ][0]
            if checkin_date < current_datetime.date():
                return True
            elif checkin_date > current_datetime.date():
                return False
            return current_datetime.time().hour >= int(cancellation_time)

        # TODO: How to pass current_time here?
        return current_datetime > dateutils.datetime_at_midnight(
            dateutils.add(self.model.booking.checkin_date, days=1)
        )

    def _is_soft_booking(self):
        return self.is_temporary()

    def is_future_booking(self):
        # NOTE: This check is required, because now, allowed_actions will read every time room_stays is read
        # in booking aggregate.
        # So now, when room stays is accessed to populate checkin_date in booking, at that time, this value will be none
        if not self.model.booking.checkin_date:
            return False
        hotel_context = crs_context.get_hotel_context()
        return (
            dateutils.to_date(self.model.booking.checkin_date)
            > hotel_context.current_date()
        )

    def trigger_state_transition(self, room_stays, previous_state=None):
        counter = Counter([rs.status for rs in room_stays])

        if counter.get(BookingStatus.RESERVED, 0) == len(room_stays):
            if (
                not self.is_temporary()
                and not self.is_confirmed()
                and not self.is_reserved()
            ):
                self.undo_checkin(previous_state=previous_state)

        elif counter.get(BookingStatus.CHECKED_OUT, 0) == len(room_stays):
            if not self.is_checked_out():
                self.checkout()

        elif counter.get(BookingStatus.CHECKED_IN, 0) == len(room_stays):
            if not self.is_checked_in():
                success = self.checkin()
                if not success:
                    raise InvalidActionError(
                        error=BookingErrors.BOOKING_CHECKIN_NOT_ALLOWED
                    )

        elif 0 < counter.get(BookingStatus.CHECKED_OUT, 0) < len(
            room_stays
        ) or 0 < counter.get(BookingStatus.PART_CHECKOUT, 0):
            self.part_checkout()

        elif 0 < counter.get(BookingStatus.CHECKED_IN, 0) < len(
            room_stays
        ) or 0 < counter.get(BookingStatus.PART_CHECKIN, 0):
            success = self.part_checkin()
            if not success:
                raise InvalidActionError(
                    error=BookingErrors.BOOKING_CHECKIN_NOT_ALLOWED
                )
