import datetime

from treebo_commons.money import Money
from treebo_commons.utils import dateutils

from prometheus import crs_context
from prometheus.domain.booking.domain_events.addon_modified import AddonModifiedEvent
from prometheus.domain.booking.errors import BookingErrors
from prometheus.domain.booking.exceptions import (
    AddonOutOfStayDate,
    InvalidAddonConfiguration,
)
from prometheus.domain.domain_events.domain_event_registry import register_event
from ths_common.base_entity import EntityChangeTracker
from ths_common.constants.booking_constants import AddonRelativeDate, ExpenseAddedBy
from ths_common.exceptions import ValidationException
from ths_common.utils.common_utils import safe_strip


class Addon(EntityChangeTracker):
    # NOTE: 1st bit is for charge_checkin, 2nd bit is for charge_other_days, 3rd bit is for charge_checkout
    BITMAP_TO_DATE_RANGE = {
        "001": (AddonRelativeDate.CHECKOUT, AddonRelativeDate.CHECKOUT),
        "010": (
            AddonRelativeDate.CHECKIN_PLUS_ONE,
            AddonRelativeDate.CHECKOUT_MINUS_ONE,
        ),
        "011": (AddonRelativeDate.CHECKIN_PLUS_ONE, AddonRelativeDate.CHECKOUT),
        "100": (AddonRelativeDate.CHECKIN, AddonRelativeDate.CHECKIN),
        "110": (AddonRelativeDate.CHECKIN, AddonRelativeDate.CHECKOUT_MINUS_ONE),
        "111": (AddonRelativeDate.CHECKIN, AddonRelativeDate.CHECKOUT),
    }

    def __init__(
        self,
        addon_id,
        booking_id,
        room_stay_id,
        name,
        pretax_price,
        posttax_price,
        quantity,
        status,
        expense_item_id,
        sku_id=None,
        charge_type=None,
        bill_to_type=None,
        charge_checkin=None,
        charge_checkout=None,
        charge_other_days=None,
        start_relative: AddonRelativeDate = None,
        end_relative: AddonRelativeDate = None,
        linked=False,
        start_date=None,
        end_date=None,
        version=1,
        deleted=False,
        is_rate_plan_addon=False,
        dirty=True,
        new=True,
        added_by=None,
    ):
        super().__init__(dirty=dirty, new=new)
        self.addon_id = addon_id
        self.booking_id = booking_id
        self.room_stay_id = room_stay_id
        self.expense_item_id = expense_item_id
        self.sku_id = sku_id
        self.name = safe_strip(name)
        if pretax_price:
            self.pretax_price = pretax_price
        else:
            self.pretax_price = None

        if posttax_price:
            self.posttax_price = posttax_price
        else:
            self.posttax_price = None

        if not self.pretax_price and not self.posttax_price:
            base_currency = crs_context.hotel_context.base_currency
            self.pretax_price = Money('0', base_currency)

        if added_by:
            self.added_by = added_by
        else:
            self.added_by = (
                ExpenseAddedBy.TREEBO
                if crs_context.is_treebo_tenant()
                else ExpenseAddedBy.HOTEL
            )

        self.quantity = quantity
        self.charge_checkin = charge_checkin
        self.charge_checkout = charge_checkout
        self.charge_other_days = charge_other_days
        self.status = status
        self.charge_type = charge_type
        self.bill_to_type = bill_to_type
        self.deleted = deleted
        self.version = version
        self.linked = linked
        self.start_relative = start_relative
        self.end_relative = end_relative
        self.start_date = start_date
        self.end_date = end_date
        self.is_rate_plan_addon = is_rate_plan_addon

    @classmethod
    def get_start_and_end_relative(
        cls, charge_checkin, charge_other_days, charge_checkout
    ):
        bitmap = "{cin}{other}{cout}".format(
            cin=1 if charge_checkin else 0,
            other=1 if charge_other_days else 0,
            cout=1 if charge_checkout else 0,
        )
        start_relative, end_relative = cls.BITMAP_TO_DATE_RANGE.get(bitmap)
        return start_relative, end_relative

    def set_addon_dates_v1(
        self,
        charge_checkin,
        charge_checkout,
        charge_other_days,
        room_checkin_date: datetime.date,
        room_checkout_date: datetime.date,
    ):
        if not (charge_checkin or charge_checkout or charge_other_days):
            raise ValidationException(error=BookingErrors.INVALID_ADDON_CONFIGURATION)

        if charge_checkin and charge_checkout and not charge_other_days:
            raise ValidationException(
                error=BookingErrors.NON_CONSECUTIVE_ADDONS_NOT_SUPORTED
            )

        self.charge_checkin = charge_checkin
        self.charge_checkout = charge_checkout
        self.charge_other_days = charge_other_days
        self.start_relative, self.end_relative = self.get_start_and_end_relative(
            charge_checkin, charge_other_days, charge_checkout
        )
        self.start_date = self.start_relative.get_absolute_date(
            room_checkin_date, room_checkout_date
        )
        self.end_date = self.end_relative.get_absolute_date(
            room_checkin_date, room_checkout_date
        )

        if self.start_date > self.end_date:
            raise InvalidAddonConfiguration()

        if self.linked and (
            self.start_date == room_checkout_date or self.end_date == room_checkout_date
        ):
            raise ValidationException(
                error=BookingErrors.LINKED_ADDON_NOT_ALLOWED_ON_CHECKOUT_DATE
            )

        self.mark_dirty()

    def set_start_end_dates_v2(
        self,
        start_relative,
        start_date,
        end_relative,
        end_date,
        room_stay_start_date: datetime.date,
        room_stay_end_date: datetime.date,
    ):
        self.start_relative = start_relative
        self.end_relative = end_relative
        self.start_date = start_date
        self.end_date = end_date

        if self.start_relative:
            self.start_date = self.start_relative.get_absolute_date(
                room_stay_start_date, room_stay_end_date
            )
        if self.end_relative:
            self.end_date = self.end_relative.get_absolute_date(
                room_stay_start_date, room_stay_end_date
            )

        if self.start_date > self.end_date:
            raise InvalidAddonConfiguration()

        if self.linked and (
            self.start_date == room_stay_end_date or self.end_date == room_stay_end_date
        ):
            raise ValidationException(
                error=BookingErrors.LINKED_ADDON_NOT_ALLOWED_ON_CHECKOUT_DATE
            )

        self.mark_dirty()

    def update_addon_date_range(
        self, checkin_date: datetime.date, checkout_date: datetime.date
    ):
        if self.start_relative:
            self.start_date = self.start_relative.get_absolute_date(
                checkin_date, checkout_date
            )
        elif self.start_date < checkin_date:
            self.start_date = checkin_date

        if self.end_relative:
            self.end_date = self.end_relative.get_absolute_date(
                checkin_date, checkout_date
            )
        elif self.end_date > checkout_date:
            self.end_date = checkout_date

        if self.start_date > self.end_date:
            raise InvalidAddonConfiguration()

        self.mark_dirty()

    def update_single_day_addon_date(self, addon_date: datetime.date):
        assert self.start_date == self.end_date, (
            "This method is to be used only for inclusion addons which are on "
            "single date"
        )
        self.start_date = addon_date
        self.end_date = addon_date
        self.mark_dirty()

    @classmethod
    def from_dict(cls, addon_dict: dict, addon_id, booking_id):
        addon_dict['addon_id'] = addon_id
        addon_dict['booking_id'] = booking_id
        return cls(**addon_dict)

    def update_price(self, pretax_price, posttax_price, raise_event=None):
        attr = None
        if pretax_price is not None:
            old_value = self.pretax_price
            self.pretax_price = pretax_price
            new_value = self.pretax_price
            self.posttax_price = None
            attr = 'pretax_price'
        elif posttax_price is not None:
            old_value = self.posttax_price
            self.posttax_price = posttax_price
            new_value = self.posttax_price
            self.pretax_price = None
            attr = 'posttax_price'

        if raise_event and old_value != new_value:
            register_event(
                AddonModifiedEvent(
                    booking_id=self.booking_id,
                    addon_id=self.addon_id,
                    addon_name=self.name,
                    expense_item_id=self.expense_item_id,
                    room_stay_id=self.room_stay_id,
                    attribute=attr,
                    new_value=new_value,
                    old_value=old_value,
                )
            )

        self.mark_dirty()

    def update_quantity(self, quantity, raise_event=None):
        old_value = self.quantity
        if quantity is not None:
            self.quantity = quantity
        new_value = self.quantity

        if raise_event and old_value != new_value:
            register_event(
                AddonModifiedEvent(
                    booking_id=self.booking_id,
                    addon_id=self.addon_id,
                    addon_name=self.name,
                    expense_item_id=self.expense_item_id,
                    room_stay_id=self.room_stay_id,
                    attribute='quantity',
                    new_value=new_value,
                    old_value=old_value,
                )
            )
        self.mark_dirty()

    def update_charge_type(self, charge_type, bill_to_type=None, raise_event=None):
        old_charge_type = self.charge_type
        if charge_type is not None:
            self.charge_type = charge_type
        new_charge_type = self.charge_type

        old_bill_to_type = self.bill_to_type
        if bill_to_type is not None:
            self._update_bill_to_type(bill_to_type)
        new_bill_to_type = self.bill_to_type

        if raise_event and (
            old_charge_type != new_charge_type or old_bill_to_type != new_bill_to_type
        ):
            old_value = "{0}:{1}".format(old_charge_type, old_bill_to_type)
            new_value = "{0}:{1}".format(new_charge_type, new_bill_to_type)
            register_event(
                AddonModifiedEvent(
                    booking_id=self.booking_id,
                    addon_id=self.addon_id,
                    addon_name=self.name,
                    expense_item_id=self.expense_item_id,
                    room_stay_id=self.room_stay_id,
                    attribute='charge_type',
                    new_value=new_value,
                    old_value=old_value,
                )
            )
        self.mark_dirty()

    def _update_bill_to_type(self, bill_to_type):
        self.bill_to_type = bill_to_type
        self.mark_dirty()

    def update_applicable_on(
        self,
        charge_checkin,
        charge_other_days,
        charge_checkout,
        room_stay,
        raise_event=None,
    ):
        is_applicable_on_changed = False
        start_date_old_value = dateutils.date_to_ymd_str(self.start_date)
        end_date_old_value = dateutils.date_to_ymd_str(self.end_date)
        if charge_checkin is not None and self.charge_checkin != charge_checkin:
            is_applicable_on_changed = True
            self.charge_checkin = charge_checkin

        if (
            charge_other_days is not None
            and self.charge_other_days != charge_other_days
        ):
            is_applicable_on_changed = True
            self.charge_other_days = charge_other_days

        if charge_checkout is not None and self.charge_checkout != charge_checkout:
            is_applicable_on_changed = True
            self.charge_checkout = charge_checkout

        self.set_addon_dates_v1(
            charge_checkin=self.charge_checkin,
            charge_checkout=self.charge_checkout,
            charge_other_days=self.charge_other_days,
            room_checkin_date=room_stay.stay_start,
            room_checkout_date=room_stay.stay_end,
        )

        start_date_new_value = dateutils.date_to_ymd_str(self.start_date)
        end_date_new_value = dateutils.date_to_ymd_str(self.end_date)

        if raise_event and is_applicable_on_changed:
            register_event(
                AddonModifiedEvent(
                    booking_id=self.booking_id,
                    addon_id=self.addon_id,
                    addon_name=self.name,
                    expense_item_id=self.expense_item_id,
                    attribute='start_date',
                    new_value=start_date_new_value,
                    room_stay_id=self.room_stay_id,
                    old_value=start_date_old_value,
                )
            )
            register_event(
                AddonModifiedEvent(
                    booking_id=self.booking_id,
                    addon_id=self.addon_id,
                    addon_name=self.name,
                    expense_item_id=self.expense_item_id,
                    attribute='end_date',
                    new_value=end_date_new_value,
                    room_stay_id=self.room_stay_id,
                    old_value=end_date_old_value,
                )
            )
        self.mark_dirty()

    def update_addon_start_end_date(
        self,
        start_relative,
        end_relative,
        start_date,
        end_date,
        room_checkin: datetime.date,
        room_checkout: datetime.date,
        raise_event=None,
    ):
        is_applicable_from_changed = False
        is_applicable_to_changed = False
        start_date_old_value = {}
        start_date_new_value = {}
        end_date_old_value = {}
        end_date_new_value = {}

        if (start_relative or start_date) and (end_relative or end_date):
            start_date_old_value['start_date'] = dateutils.date_to_ymd_str(
                self.start_date
            )
            end_date_old_value['end_date'] = dateutils.date_to_ymd_str(self.end_date)
            is_applicable_from_changed = True
            is_applicable_to_changed = True
            self.set_start_end_dates_v2(
                start_relative,
                start_date,
                end_relative,
                end_date,
                room_checkin,
                room_checkout,
            )
            start_date_new_value['start_date'] = dateutils.date_to_ymd_str(
                self.start_date
            )
            end_date_new_value['end_date'] = dateutils.date_to_ymd_str(self.end_date)

        elif (start_relative or start_date) and not (end_relative or end_date):
            start_date_old_value['start_date'] = dateutils.date_to_ymd_str(
                self.start_date
            )
            is_applicable_from_changed = True
            self.set_start_end_dates_v2(
                start_relative,
                start_date,
                self.end_relative,
                self.end_date,
                room_checkin,
                room_checkout,
            )
            start_date_new_value['start_date'] = dateutils.date_to_ymd_str(
                self.start_date
            )
        elif (end_relative or end_date) and not (start_relative or start_date):
            end_date_old_value['end_date'] = dateutils.date_to_ymd_str(self.end_date)
            is_applicable_to_changed = True
            self.set_start_end_dates_v2(
                self.start_relative,
                self.start_date,
                end_relative,
                end_date,
                room_checkin,
                room_checkout,
            )
            end_date_new_value['end_date'] = dateutils.date_to_ymd_str(self.end_date)

        if self.start_date > self.end_date:
            raise InvalidAddonConfiguration()

        if raise_event and is_applicable_from_changed:
            register_event(
                AddonModifiedEvent(
                    booking_id=self.booking_id,
                    addon_id=self.addon_id,
                    addon_name=self.name,
                    expense_item_id=self.expense_item_id,
                    attribute='start_date',
                    new_value=start_date_new_value,
                    room_stay_id=self.room_stay_id,
                    old_value=start_date_old_value,
                )
            )
        if raise_event and is_applicable_to_changed:
            register_event(
                AddonModifiedEvent(
                    booking_id=self.booking_id,
                    addon_id=self.addon_id,
                    addon_name=self.name,
                    expense_item_id=self.expense_item_id,
                    attribute='end_date',
                    new_value=end_date_new_value,
                    room_stay_id=self.room_stay_id,
                    old_value=end_date_old_value,
                )
            )
        self.mark_dirty()

    def update_from_dict(self, update_addon_data: dict, room_stay, raise_event=False):
        if (
            update_addon_data.get('pretax_price') is not None
            or update_addon_data.get('posttax_price') is not None
        ):
            self.update_price(
                update_addon_data.get('pretax_price'),
                update_addon_data.get('posttax_price'),
                raise_event,
            )

        if update_addon_data.get('quantity') is not None:
            self.update_quantity(update_addon_data.get('quantity'), raise_event)

        if self.linked and update_addon_data.get('charge_type') is not None:
            raise ValidationException(
                description="Charge type of linked addon can not be edited"
            )
        if self.linked and update_addon_data.get('bill_to_type') is not None:
            raise ValidationException(
                description="Charge type of linked addon can not be edited"
            )

        if (
            update_addon_data.get('charge_type') is not None
            or update_addon_data.get('bill_to_type') is not None
        ) and not self.linked:
            self.update_charge_type(
                update_addon_data.get('charge_type'),
                update_addon_data.get('bill_to_type'),
                raise_event,
            )

        if (
            update_addon_data.get('charge_checkin') is not None
            or update_addon_data.get('charge_other_days') is not None
            or update_addon_data.get('charge_checkout') is not None
        ):
            self.update_applicable_on(
                update_addon_data.get('charge_checkin'),
                update_addon_data.get('charge_other_days'),
                update_addon_data.get('charge_checkout'),
                room_stay=room_stay,
                raise_event=raise_event,
            )

        if (
            update_addon_data.get('start_relative')
            or update_addon_data.get('start_date')
            or update_addon_data.get('end_relative')
            or update_addon_data.get('end_date')
        ):
            self.update_addon_start_end_date(
                update_addon_data.get('start_relative'),
                update_addon_data.get('end_relative'),
                update_addon_data.get('start_date'),
                update_addon_data.get('end_date'),
                room_checkin=room_stay.stay_start,
                room_checkout=room_stay.stay_end,
                raise_event=raise_event,
            )
        self.mark_dirty()
