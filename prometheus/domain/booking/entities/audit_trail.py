from ths_common.base_entity import EntityChangeTracker
from ths_common.constants.audit_trail_constants import AuditType


class AuditTrail(EntityChangeTracker):
    def __init__(
        self,
        audit_id,
        user,
        user_type,
        application,
        timestamp,
        booking_id,
        audit_type: AuditType,
        audit_payload,
        request_id=None,
        action_id=None,
        current_business_date=None,
        auth_id=None,
        application_trace=None,
        dirty=True,
        new=True,
    ):
        super().__init__(dirty=dirty, new=new)
        self.audit_id = audit_id
        self.user = user
        self.user_type = user_type
        self.application = application
        self.timestamp = timestamp
        self.request_id = request_id
        self.booking_id = booking_id
        self.action_id = action_id
        self.audit_type = audit_type
        self.audit_payload = audit_payload
        self.current_business_date = current_business_date
        self.auth_id = auth_id
        self.application_trace = application_trace
