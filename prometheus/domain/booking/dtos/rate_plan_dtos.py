class RatePlanInclusionFrequency:
    def __init__(self, count, day_of_serving, frequency_type):
        self.count = count
        self.day_of_serving = day_of_serving
        self.frequency_type = frequency_type

    def to_json(self):
        return dict(
            count=self.count,
            day_of_serving=self.day_of_serving,
            frequency_type=self.frequency_type,
        )

    @classmethod
    def from_json(cls, frequency_data):
        return RatePlanInclusionFrequency(
            count=frequency_data['count'],
            day_of_serving=frequency_data['day_of_serving'],
            frequency_type=frequency_data['frequency_type'],
        )


class RatePlanInclusionOffering:
    def __init__(self, quantity, offering_type):
        self.quantity = quantity
        self.offering_type = offering_type

    def to_json(self):
        return dict(quantity=self.quantity, offering_type=self.offering_type)

    @classmethod
    def from_json(cls, offering_data):
        return RatePlanInclusionOffering(
            quantity=offering_data['quantity'],
            offering_type=offering_data['offering_type'],
        )


class RatePlanInclusion:
    def __init__(
        self,
        sku_id,
        name,
        frequency: RatePlanInclusionFrequency,
        offering: RatePlanInclusionOffering,
    ):
        self.sku_id = sku_id
        self.name = name
        self.frequency = frequency
        self.offering = offering

    def to_json(self):
        return dict(
            sku_id=self.sku_id,
            name=self.name,
            frequency=self.frequency.to_json(),
            offering=self.offering.to_json(),
        )

    @classmethod
    def from_json(cls, inclusion_data):
        return RatePlanInclusion(
            sku_id=inclusion_data['sku_id'],
            name=inclusion_data['name'],
            frequency=RatePlanInclusionFrequency.from_json(inclusion_data['frequency']),
            offering=RatePlanInclusionOffering.from_json(inclusion_data['offering']),
        )


class RatePlanPackage:
    def __init__(self, package_id, package_name, inclusions: [RatePlanInclusion]):
        self.package_id = package_id
        self.inclusions = inclusions
        self.package_name = package_name

    def to_json(self):
        return dict(
            package_id=self.package_id,
            package_name=self.package_name,
            inclusions=[inc.to_json() for inc in self.inclusions],
        )

    @classmethod
    def from_json(cls, package_data):
        return RatePlanPackage(
            package_id=package_data['package_id'],
            package_name=package_data.get('package_name'),
            inclusions=[
                RatePlanInclusion.from_json(inc) for inc in package_data['inclusions']
            ],
        )


class RatePlanCancellationPolicy:
    def __init__(
        self,
        cancellation_charge_unit,
        cancellation_charge_value,
        cancellation_duration_before_checkin_end,
        cancellation_duration_before_checkin_start,
    ):
        self.cancellation_charge_unit = cancellation_charge_unit
        self.cancellation_charge_value = cancellation_charge_value
        self.cancellation_duration_before_checkin_end = (
            cancellation_duration_before_checkin_end
        )
        self.cancellation_duration_before_checkin_start = (
            cancellation_duration_before_checkin_start
        )

    def to_json(self):
        return dict(
            cancellation_charge_unit=self.cancellation_charge_unit,
            cancellation_charge_value=str(self.cancellation_charge_value),
            cancellation_duration_before_checkin_end=self.cancellation_duration_before_checkin_end,
            cancellation_duration_before_checkin_start=self.cancellation_duration_before_checkin_start,
        )

    @classmethod
    def from_json(cls, cancellation_data):
        return RatePlanCancellationPolicy(
            cancellation_charge_unit=cancellation_data.get('cancellation_charge_unit'),
            cancellation_charge_value=cancellation_data.get(
                'cancellation_charge_value'
            ),
            cancellation_duration_before_checkin_end=cancellation_data.get(
                'cancellation_duration_before_checkin_end'
            ),
            cancellation_duration_before_checkin_start=cancellation_data.get(
                'cancellation_duration_before_checkin_start'
            ),
        )


class RatePlanChildPolicy:
    def __init__(self, charge_per_child, child_allowed, unit_of_charge):
        self.charge_per_child = charge_per_child
        self.child_allowed = child_allowed
        self.unit_of_charge = unit_of_charge

    def to_json(self):
        return dict(
            charge_per_child=self.charge_per_child,
            child_allowed=self.child_allowed,
            unit_of_charge=self.unit_of_charge,
        )

    @classmethod
    def from_json(cls, child_data):
        return RatePlanChildPolicy(
            charge_per_child=child_data.get('charge_per_child'),
            child_allowed=child_data['child_allowed'],
            unit_of_charge=child_data.get('unit_of_charge'),
        )


class RatePlanPaymentPolicies:
    def __init__(
        self,
        advance_payment_percentage,
        days_before_checkin_to_make_payment,
        unit_of_payment_percentage,
        occupancy_percentage,
    ):
        self.advance_payment_percentage = advance_payment_percentage
        self.days_before_checkin_to_make_payment = days_before_checkin_to_make_payment
        self.unit_of_payment_percentage = unit_of_payment_percentage
        self.occupancy_percentage = occupancy_percentage

    def to_json(self):
        return dict(
            advance_payment_percentage=str(self.advance_payment_percentage),
            days_before_checkin_to_make_payment=self.days_before_checkin_to_make_payment,
            unit_of_payment_percentage=self.unit_of_payment_percentage,
            occupancy_percentage=str(self.occupancy_percentage),
        )

    @classmethod
    def from_json(cls, payment_data):
        days_before_checkin_to_make_payment = payment_data.get(
            'days_before_checkin_to_make_payment'
        )
        if days_before_checkin_to_make_payment is not None:
            days_before_checkin_to_make_payment = int(
                days_before_checkin_to_make_payment
            )
        return RatePlanPaymentPolicies(
            advance_payment_percentage=payment_data.get('advance_payment_percentage'),
            days_before_checkin_to_make_payment=days_before_checkin_to_make_payment,
            unit_of_payment_percentage=payment_data.get('unit_of_payment_percentage'),
            occupancy_percentage=payment_data.get('occupancy_percentage'),
        )

    def __lt__(self, other):
        if not isinstance(other, RatePlanPaymentPolicies):
            return NotImplemented
        """
        Sort by days_before_checkin_to_make_payment, occupancy_percentage, advance_payment_percentage
        larger days_before_checkin_to_make_payment
        smaller occupancy_percentage
        larger payment_percentage
        """
        return (
            self.days_before_checkin_to_make_payment,
            -self.rounded_occupancy_percentage,
            self.rounded_payment_percentage,
        ) < (
            other.days_before_checkin_to_make_payment,
            -other.rounded_occupancy_percentage,
            other.rounded_payment_percentage,
        )

    @property
    def rounded_payment_percentage(self):
        try:
            return float(self.advance_payment_percentage)
        except (ValueError, TypeError):
            return 0

    @property
    def rounded_occupancy_percentage(self):
        try:
            return float(self.occupancy_percentage)
        except (ValueError, TypeError):
            return 101

    def __eq__(self, other):
        if not isinstance(other, RatePlanPaymentPolicies):
            return False
        return (
            self.days_before_checkin_to_make_payment
            == other.days_before_checkin_to_make_payment
            and self.rounded_occupancy_percentage == other.rounded_occupancy_percentage
            and self.rounded_payment_percentage == other.rounded_payment_percentage
        )

    def __hash__(self):
        return hash(
            (
                self.days_before_checkin_to_make_payment,
                self.rounded_occupancy_percentage,
                self.rounded_payment_percentage,
            )
        )


class RatePlanRewardPolicy:
    def __init__(self, redeemable):
        self.redeemable = redeemable

    def to_json(self):
        return dict(redeemable=self.redeemable)

    @classmethod
    def from_json(cls, reward_data):
        return RatePlanRewardPolicy(redeemable=reward_data['redeemable'])


class RatePlanPolicies:
    def __init__(
        self,
        cancellation_policies: [RatePlanCancellationPolicy] = None,
        child_policy: RatePlanChildPolicy = None,
        payment_policies: [RatePlanPaymentPolicies] = None,
        reward_policy: RatePlanRewardPolicy = None,
    ):
        self.cancellation_policies = cancellation_policies
        self.child_policy = child_policy
        self.payment_policies = payment_policies
        self.reward_policy = reward_policy

    def to_json(self):
        return dict(
            cancellation_policies=(
                [policy.to_json() for policy in self.cancellation_policies]
                if self.cancellation_policies
                else None
            ),
            child_policy=self.child_policy.to_json() if self.child_policy else None,
            payment_policies=(
                [policy.to_json() for policy in self.payment_policies]
                if self.payment_policies
                else None
            ),
            reward_policy=self.reward_policy.to_json() if self.reward_policy else None,
        )

    @classmethod
    def from_json(cls, policies_data):
        return RatePlanPolicies(
            cancellation_policies=(
                [
                    RatePlanCancellationPolicy.from_json(policy)
                    for policy in policies_data.get('cancellation_policies')
                ]
                if policies_data.get('cancellation_policies')
                else None
            ),
            child_policy=(
                RatePlanChildPolicy.from_json(policies_data['child_policy'])
                if policies_data.get('child_policy')
                else None
            ),
            payment_policies=(
                [
                    RatePlanPaymentPolicies.from_json(policy)
                    for policy in policies_data.get('payment_policies')
                ]
                if policies_data.get('payment_policies')
                else None
            ),
            reward_policy=(
                RatePlanRewardPolicy.from_json(policies_data['reward_policy'])
                if policies_data.get('reward_policy')
                else None
            ),
        )


class RatePlanRestrictions:
    def __init__(self, maximum_los, minimum_abw, minimum_los, maximum_abw=None):
        self.maximum_los = maximum_los
        self.minimum_abw = minimum_abw
        self.minimum_los = minimum_los
        self.maximum_abw = maximum_abw

    def to_json(self):
        return dict(
            minimum_los=self.minimum_los,
            minimum_abw=self.minimum_abw,
            maximum_los=self.maximum_los,
            maximum_abw=self.maximum_abw,
        )

    @classmethod
    def from_json(cls, restriction_data):
        return RatePlanRestrictions(
            minimum_los=restriction_data['minimum_los'],
            minimum_abw=restriction_data['minimum_abw'],
            maximum_los=restriction_data['maximum_los'],
            maximum_abw=restriction_data.get('maximum_abw'),
        )


class RatePlanNonRoomNightInclusion:
    def __init__(self, price, sku_id):
        self.price = price
        self.sku_id = sku_id

    def to_json(self):
        return dict(price=self.price, sku_id=self.sku_id)

    @classmethod
    def from_json(cls, inclusion_data):
        return RatePlanNonRoomNightInclusion(
            price=inclusion_data['price'], sku_id=inclusion_data['sku_id']
        )


class RatePlanCommissionDetails:
    def __init__(self, commission_percent, commission_type):
        self.commission_type = commission_type
        self.commission_percent = commission_percent

    def to_json(self):
        return dict(
            commission_type=self.commission_type,
            commission_percent=self.commission_percent,
        )

    @classmethod
    def from_json(cls, commission_data):
        return RatePlanCommissionDetails(
            commission_type=commission_data['commission_type'],
            commission_percent=commission_data['commission_percent'],
        )


class FlexiRatePlanDetails:
    def __init__(self, policies: RatePlanPolicies):
        self.policies = policies

    @classmethod
    def from_json(cls, json):
        return FlexiRatePlanDetails(policies=RatePlanPolicies.from_json(json))


class RatePlanDTO:
    def __init__(
        self,
        rate_plan_reference_id,
        rate_plan_code,
        name,
        package: RatePlanPackage = None,
        policies: RatePlanPolicies = None,
        restrictions: RatePlanRestrictions = None,
        non_room_night_inclusions: [RatePlanNonRoomNightInclusion] = None,
        print_rate=True,
        suppress_rate=False,
        commission_details: RatePlanCommissionDetails = None,
    ):
        self.rate_plan_reference_id = rate_plan_reference_id
        self.rate_plan_code = rate_plan_code
        self.name = name
        self.package = package
        self.policies = policies
        self.restrictions = restrictions
        self.non_room_night_inclusions = non_room_night_inclusions
        self.print_rate = print_rate
        self.suppress_rate = suppress_rate
        self.commission_details = commission_details

    @classmethod
    def create(cls, rate_plan_data):
        inclusions = []
        for inc in rate_plan_data['package_data']['inclusions']:
            frequency = RatePlanInclusionFrequency(
                count=inc['frequency']['count'],
                day_of_serving=inc['frequency']['day_of_serving'],
                frequency_type=inc['frequency']['frequency_type'],
            )
            offering = RatePlanInclusionOffering(
                quantity=inc['offering']['offered_quantity'],
                offering_type=inc['offering']['offering_type'],
            )
            inclusions.append(
                RatePlanInclusion(
                    inc['sku_id'], inc['display_name'], frequency, offering
                )
            )
        package = RatePlanPackage(
            package_id=rate_plan_data['package_data']['package_id'],
            package_name=rate_plan_data['package_data'].get('package_name'),
            inclusions=inclusions,
        )

        policies_data = rate_plan_data.get('policies')

        cancellation_data = (
            policies_data.get('cancellation_policies') if policies_data else None
        )
        cancellation_policies = []
        if cancellation_data:
            for policy in cancellation_data:
                cancellation_policies.append(
                    RatePlanCancellationPolicy(
                        cancellation_charge_unit=policy.get('cancellation_charge_unit'),
                        cancellation_charge_value=policy.get(
                            'cancellation_charge_value'
                        ),
                        cancellation_duration_before_checkin_end=policy.get(
                            'cancellation_duration_before_checkin_end'
                        ),
                        cancellation_duration_before_checkin_start=policy.get(
                            'cancellation_duration_before_checkin_start'
                        ),
                    )
                )

        child_data = policies_data.get('child_policy') if policies_data else None
        child_policy = (
            RatePlanChildPolicy(
                charge_per_child=child_data.get('charge_per_child'),
                child_allowed=child_data['child_allowed'],
                unit_of_charge=child_data.get('unit_of_charge'),
            )
            if child_data
            else None
        )
        reward_policy = policies_data.get('reward_policy') if policies_data else None
        reward_policy = (
            (RatePlanRewardPolicy(redeemable=reward_policy['redeemable']))
            if reward_policy
            else None
        )
        payment_data = policies_data.get('payment_policies') if policies_data else None
        payment_policies = []
        if payment_data:
            for policy in payment_data:
                payment_policies.append(
                    (
                        RatePlanPaymentPolicies(
                            advance_payment_percentage=policy.get(
                                'advance_payment_percentage'
                            ),
                            days_before_checkin_to_make_payment=policy.get(
                                'days_before_checkin_to_make_payment'
                            ),
                            unit_of_payment_percentage=policy.get(
                                'unit_of_payment_percentage'
                            ),
                            occupancy_percentage=policy.get('occupancy_percentage'),
                        )
                    )
                )

        policies = RatePlanPolicies(
            cancellation_policies=cancellation_policies,
            child_policy=child_policy,
            payment_policies=payment_policies,
            reward_policy=reward_policy,
        )

        restrictions = (
            RatePlanRestrictions(
                minimum_los=rate_plan_data['restrictions'].get('minimum_los'),
                minimum_abw=rate_plan_data['restrictions'].get('minimum_abw'),
                maximum_los=rate_plan_data['restrictions'].get('maximum_los'),
                maximum_abw=rate_plan_data['restrictions'].get('maximum_abw'),
            )
            if rate_plan_data.get('restrictions')
            else None
        )

        non_room_night_inclusions = []
        if rate_plan_data.get('non_room_night_inclusion_rates'):
            for inc in rate_plan_data['non_room_night_inclusion_rates']:
                non_room_night_inclusions.append(
                    RatePlanNonRoomNightInclusion(inc.get('price'), inc.get('sku_id'))
                )

        return RatePlanDTO(
            rate_plan_reference_id=rate_plan_data['rate_plan_id'],
            rate_plan_code=rate_plan_data['short_code'],
            name=rate_plan_data['name'],
            package=package,
            policies=policies,
            restrictions=restrictions,
            non_room_night_inclusions=non_room_night_inclusions,
            print_rate=rate_plan_data.get('print_rate')
            if rate_plan_data.get('print_rate') is not None
            else True,
            suppress_rate=rate_plan_data.get('suppress_rate')
            if rate_plan_data.get('suppress_rate') is not None
            else False,
            commission_details=RatePlanCommissionDetails(
                commission_percent=rate_plan_data.get("commission_percent"),
                commission_type=rate_plan_data.get("commission_type"),
            ),
        )

    def create_flexi_rate_plan_dto(self, flexi_rate_plan_details: FlexiRatePlanDetails):
        return RatePlanDTO(
            rate_plan_reference_id=self.rate_plan_reference_id,
            rate_plan_code=self.rate_plan_code,
            name=self.name,
            policies=flexi_rate_plan_details.policies,
            print_rate=self.print_rate,
            suppress_rate=self.suppress_rate,
        )

    def override_policies(self, new_policies: RatePlanPolicies):
        self.policies = new_policies
