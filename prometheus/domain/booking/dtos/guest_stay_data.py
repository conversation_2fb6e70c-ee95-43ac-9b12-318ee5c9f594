from treebo_commons.utils import dateutils

from prometheus.domain.booking.exceptions import InvalidStayDatesError
from ths_common.constants.booking_constants import AgeGroup, BookingStatus
from ths_common.exceptions import ValidationException


class GuestAllocationData(object):
    def __init__(
        self,
        assigned_by,
        checkin_date=None,
        checkout_date=None,
        guest: dict = None,
        guest_id=None,
    ):
        if not guest and not guest_id:
            raise ValidationException(
                message="Either guest or guest_id is required for creating GuestAllocation"
            )
        self.assigned_by = assigned_by
        self.checkin_date = checkin_date
        self.checkout_date = checkout_date
        self.guest = guest
        self.guest_id = guest_id


class GuestStayData(object):
    def __init__(
        self,
        status: BookingStatus,
        age_group: AgeGroup,
        checkin_date: str,
        checkout_date: str,
        actual_checkin_date: str = None,
        actual_checkout_date: str = None,
        guest_allocation_dto=None,
    ):
        if not checkin_date or not checkout_date:
            raise ValidationException(
                message="Checkin date or checkout date is required for creating GuestStay"
            )
        self.status = status
        self.age_group = age_group
        self.checkin_date = checkin_date
        self.checkout_date = checkout_date
        self.actual_checkin_date = actual_checkin_date
        self.actual_checkout_date = actual_checkout_date
        self.guest_allocation_data = guest_allocation_dto

    def override_checkin_time(self, override_checkin_time):
        self.checkin_date = dateutils.datetime_at_given_time(
            self.checkin_date, override_checkin_time
        )

    def override_checkout_time(self, override_checkout_time):
        self.checkout_date = dateutils.datetime_at_given_time(
            self.checkout_date, override_checkout_time
        )

    @staticmethod
    def create(
        guest_stay: dict,
        room_stay_checkin=None,
        room_stay_checkout=None,
        override_checkin_time=None,
        override_checkout_time=None,
    ):
        guest_allocation_dto = None
        if guest_stay.get('guest'):
            guest = guest_stay.get('guest')

            guest_allocation_dto = GuestAllocationData(
                guest=guest, assigned_by=None, checkin_date=None, checkout_date=None
            )
        checkin_date = (
            dateutils.localize_datetime(guest_stay.get('checkin_date'))
            if guest_stay.get('checkin_date')
            else room_stay_checkin
        )
        checkout_date = (
            dateutils.localize_datetime(guest_stay.get('checkout_date'))
            if guest_stay.get('checkout_date')
            else room_stay_checkout
        )

        if override_checkin_time is not None:
            checkin_date = dateutils.datetime_at_given_time(
                checkin_date, override_checkin_time
            )
        if override_checkout_time is not None:
            checkout_date = dateutils.datetime_at_given_time(
                checkout_date, override_checkout_time
            )

        if not checkin_date or not checkout_date:
            raise InvalidStayDatesError(
                description="Checkin and checkout date are required for adding Guest Stay"
            )

        if checkin_date >= checkout_date:
            raise InvalidStayDatesError(
                description="GuestStay checkin date should be less than checkout date",
                extra_payload=dict(
                    checkin_date=dateutils.date_to_ymd_str(checkin_date),
                    checkout_date=dateutils.date_to_ymd_str(checkout_date),
                ),
            )

        guest_stay_data = GuestStayData(
            BookingStatus.RESERVED,
            guest_stay['age_group'],
            checkin_date,
            checkout_date,
            guest_allocation_dto=guest_allocation_dto,
        )
        return guest_stay_data
