# coding=utf-8
"""
booking overflow repository
"""
from collections import defaultdict
from typing import List

from sqlalchemy import tuple_
from treebo_commons.utils.dateutils import date_range

from object_registry import register_instance
from prometheus.common.decorators import timed
from prometheus.domain.booking.aggregates.room_stay_overflow_aggregate import (
    RoomStayOverflowAggregate,
)
from prometheus.domain.booking.entities.room_stay_overflow import RoomStayOverflow
from prometheus.domain.booking.models import RoomStayOverflowModel
from prometheus.infrastructure.database.base_repository import BaseRepository
from ths_common.exceptions import DatabaseError


@register_instance()
class RoomStayOverflowRepository(BaseRepository):
    def to_aggregate(self, room_stay_overflow_model: RoomStayOverflowModel):
        room_stay_overflow_entity = RoomStayOverflow(
            hotel_id=room_stay_overflow_model.hotel_id,
            booking_id=room_stay_overflow_model.booking_id,
            room_stay_id=room_stay_overflow_model.room_stay_id,
            room_type_id=room_stay_overflow_model.room_type_id,
            start_date=room_stay_overflow_model.start_date,
            end_date=room_stay_overflow_model.end_date,
            dirty=False,
            new=False,
        )

        return RoomStayOverflowAggregate(room_stay_overflow_entity)

    def from_aggregate(self, aggregate):
        room_stay_overflow_aggregate = aggregate
        room_stay_overflow = room_stay_overflow_aggregate.room_stay_overflow
        return RoomStayOverflowModel(
            hotel_id=room_stay_overflow.hotel_id,
            booking_id=room_stay_overflow.booking_id,
            room_stay_id=room_stay_overflow.room_stay_id,
            room_type_id=room_stay_overflow.room_type_id,
            start_date=room_stay_overflow.start_date,
            end_date=room_stay_overflow.end_date,
        )

    def save(self, room_stay_overflow_aggregate):
        room_stay_overflow_model = self.from_aggregate(room_stay_overflow_aggregate)
        try:
            self._save(room_stay_overflow_model)
            self.flush_session()
        except Exception:
            raise DatabaseError

    def save_all(self, room_stay_overflow_aggregates):
        models = [
            self.from_aggregate(aggregate)
            for aggregate in room_stay_overflow_aggregates
        ]
        self._save_all(models)
        self.flush_session()

    def _create_room_stay_overflow_aggregates(self, room_stay_overflow_models):
        room_stay_overflow_aggregates = list()
        for room_stay_overflow_model in room_stay_overflow_models:
            room_stay_overflow_aggregate = self.to_aggregate(room_stay_overflow_model)
            room_stay_overflow_aggregates.append(room_stay_overflow_aggregate)

        return room_stay_overflow_aggregates

    def get_overflowed_room_stays_for_date(self, hotel_id, date, room_type_id):
        q = (
            self.query(RoomStayOverflowModel)
            .filter(RoomStayOverflowModel.hotel_id == hotel_id)
            .filter(RoomStayOverflowModel.room_type_id == room_type_id)
            .filter(RoomStayOverflowModel.start_date <= date)
            .filter(RoomStayOverflowModel.end_date >= date)
            .order_by(RoomStayOverflowModel.created_at)
        )

        room_stay_overflow_models = q.all()
        return self._create_room_stay_overflow_aggregates(room_stay_overflow_models)

    def get_overflowed_room_stays_for_dates(
        self, hotel_id, room_type_ids, start_date, end_date
    ):
        q = (
            self.query(RoomStayOverflowModel)
            .filter(RoomStayOverflowModel.hotel_id == hotel_id)
            .filter(RoomStayOverflowModel.room_type_id.in_(room_type_ids))
            .filter(RoomStayOverflowModel.start_date <= end_date)
            .filter(RoomStayOverflowModel.end_date >= start_date)
            .order_by(RoomStayOverflowModel.created_at)
        )

        room_stay_overflow_models = q.all()
        return self._create_room_stay_overflow_aggregates(room_stay_overflow_models)

    def get_overflow_count_for_date(self, hotel_id, date, room_type_id):
        q = (
            self.query(RoomStayOverflowModel)
            .filter(RoomStayOverflowModel.hotel_id == hotel_id)
            .filter(RoomStayOverflowModel.room_type_id == room_type_id)
            .filter(RoomStayOverflowModel.start_date <= date)
            .filter(RoomStayOverflowModel.end_date >= date)
        )

        return q.count()

    def get_overflow_count_for_dates(
        self, hotel_id, start_date, end_date, room_type_id
    ):
        q = (
            self.query(RoomStayOverflowModel)
            .filter(RoomStayOverflowModel.hotel_id == hotel_id)
            .filter(RoomStayOverflowModel.room_type_id == room_type_id)
            .filter(RoomStayOverflowModel.start_date <= end_date)
            .filter(RoomStayOverflowModel.end_date >= start_date)
        )

        date_wise_overflow_count = defaultdict(int)
        overflows = q.all()
        for date in date_range(start_date, end_date, end_inclusive=True):
            date_wise_overflow_count[date] = len(
                [
                    overflow
                    for overflow in overflows
                    if overflow.start_date <= date <= overflow.end_date
                ]
            )

        return date_wise_overflow_count

    def get_overflowed_room_stays_for_booking(self, booking_id):
        q = (
            self.query(RoomStayOverflowModel)
            .filter(RoomStayOverflowModel.booking_id == booking_id)
            .order_by(RoomStayOverflowModel.created_at)
        )

        room_stay_overflow_models = q.all()
        return self._create_room_stay_overflow_aggregates(room_stay_overflow_models)

    def get_overflowed_room_stays_for_bookings(self, booking_ids):
        q = self.query(RoomStayOverflowModel).filter(
            RoomStayOverflowModel.booking_id.in_(booking_ids)
        )

        room_stay_overflow_models = q.all()
        return self._create_room_stay_overflow_aggregates(room_stay_overflow_models)

    @timed
    def get_overflowed_room_stay_ids_for_bookings(self, booking_ids):
        q = self.query(
            RoomStayOverflowModel.booking_id, RoomStayOverflowModel.room_stay_id
        ).filter(RoomStayOverflowModel.booking_id.in_(booking_ids))

        booking_wise_room_stay_ids = defaultdict(set)
        for booking_id, room_stay_id in q.all():
            booking_wise_room_stay_ids[booking_id].add(room_stay_id)
        return booking_wise_room_stay_ids

    def load_room_stay_overflow(self, booking_id, room_stay_id):
        q = (
            self.query(RoomStayOverflowModel)
            .filter(RoomStayOverflowModel.booking_id == booking_id)
            .filter(RoomStayOverflowModel.room_stay_id == room_stay_id)
        )

        room_stay_overflow_models = q.all()
        aggregates = self._create_room_stay_overflow_aggregates(
            room_stay_overflow_models
        )
        if len(aggregates) == 1:
            return aggregates[0]

    def is_room_stay_overflowing(self, booking_id, room_stay_id):
        q = (
            self.query(RoomStayOverflowModel)
            .filter(RoomStayOverflowModel.booking_id == booking_id)
            .filter(RoomStayOverflowModel.room_stay_id == room_stay_id)
        )

        return q.count() > 0

    def delete_overflow_tags(self, booking_id, room_stay_ids):
        q = self.query(RoomStayOverflowModel).filter(
            RoomStayOverflowModel.booking_id == booking_id
        )

        if room_stay_ids:
            q = q.filter(RoomStayOverflowModel.room_stay_id.in_(room_stay_ids))

        q.delete(synchronize_session=False)
        self.flush_session()

    def delete_overflow_tag(self, booking_id, room_stay_id=None):
        q = self.query(RoomStayOverflowModel).filter(
            RoomStayOverflowModel.booking_id == booking_id
        )

        if room_stay_id:
            q = q.filter(RoomStayOverflowModel.room_stay_id == room_stay_id)

        deleted = q.delete()
        self.flush_session()
        return deleted

    def delete_overflows(
        self, room_stay_overflow_aggregates: List[RoomStayOverflowAggregate]
    ):
        booking_room_stay_id_tuple = [
            (aggregate.booking_id, aggregate.room_stay_id)
            for aggregate in room_stay_overflow_aggregates
        ]
        self.query(RoomStayOverflowModel).filter(
            tuple_(
                RoomStayOverflowModel.booking_id, RoomStayOverflowModel.room_stay_id
            ).in_(booking_room_stay_id_tuple)
        ).delete(synchronize_session=False)
