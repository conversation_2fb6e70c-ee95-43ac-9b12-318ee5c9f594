# coding=utf-8
"""
booking invoice group repository
"""
from sqlalchemy import String
from sqlalchemy.dialects.postgresql.array import ARRAY
from sqlalchemy.sql.expression import cast

from object_registry import register_instance
from prometheus.domain.booking.adaptors.booking_invoice_group_adaptor import (
    BookingInvoiceGroupDBAdapter,
)
from prometheus.domain.booking.aggregates.booking_invoice_group_aggregate import (
    BookingInvoiceGroupAggregate,
)
from prometheus.domain.booking.entities import BookingInvoiceGroup
from prometheus.domain.booking.models import BookingInvoiceGroupModel
from prometheus.infrastructure.database.base_repository import BaseRepository
from ths_common.constants.booking_constants import InvoiceGroupStatus
from ths_common.exceptions import AggregateNotFound, OutdatedVersion


@register_instance()
class BookingInvoiceGroupRepository(BaseRepository):
    """
    Booking invoice group repository
    """

    def save(self, booking_invoice_group_aggregate):
        """

        Args:
            booking_invoice_group_aggregate:

        Returns:

        """
        booking_invoice_group = booking_invoice_group_aggregate.booking_invoice_group
        booking_invoice_group_model = BookingInvoiceGroupDBAdapter.to_db_model(
            booking_invoice_group
        )
        self._save(booking_invoice_group_model)
        self.flush_session()

    def update(self, booking_invoice_group_aggregate):
        """

        Args:
            booking_invoice_group_aggregates:

        Returns:

        """
        booking_invoice_group_aggregate.increment_version()
        booking_invoice_group = booking_invoice_group_aggregate.booking_invoice_group
        booking_invoice_group_model = BookingInvoiceGroupDBAdapter.to_db_model(
            booking_invoice_group
        )
        self._update(booking_invoice_group_model)
        self.flush_session()

    def update_all(self, booking_invoice_group_aggregates):
        """

        Args:
            booking_invoice_group_aggregates:

        Returns:

        """
        booking_invoice_group_models = []
        for booking_invoice_group_aggregate in booking_invoice_group_aggregates:
            booking_invoice_group = (
                booking_invoice_group_aggregate.booking_invoice_group
            )
            if not booking_invoice_group.is_dirty():
                continue
            booking_invoice_group_aggregate.increment_version()
            booking_invoice_group_model = BookingInvoiceGroupDBAdapter.to_db_model(
                booking_invoice_group
            )
            booking_invoice_group_models.append(booking_invoice_group_model)

        self._update_all(booking_invoice_group_models)
        self.flush_session()

    def _load(self, booking_invoice_group_model):
        booking_invoice_group = BookingInvoiceGroupDBAdapter.to_entity(
            booking_invoice_group_model
        )
        return BookingInvoiceGroupAggregate(booking_invoice_group)

    def load_for_update_for_booking_id(
        self,
        booking_id,
        exclude_preview=False,
        order_by_request_time=False,
        exclude_cancelled=False,
        include_statuses=None,
    ):
        """
        Load for update all the invoice groups for a given booking_id
        Args:
            booking_id: bill id to be used as the filter
            exclude_preview:
            order_by_request_time:
            exclude_cancelled
            include_statuses

        Returns:

        """
        booking_invoice_group_models = self.filter(
            BookingInvoiceGroupModel,
            BookingInvoiceGroupModel.booking_id == booking_id,
            for_update=True,
        )

        if include_statuses:
            booking_invoice_group_models = booking_invoice_group_models.filter(
                BookingInvoiceGroupModel.status.in_(include_statuses)
            )

        if exclude_preview:
            booking_invoice_group_models = self.exclude(
                booking_invoice_group_models,
                BookingInvoiceGroupModel.status == InvoiceGroupStatus.PREVIEW.value,
            )

        if exclude_cancelled:
            booking_invoice_group_models = self.exclude(
                booking_invoice_group_models,
                BookingInvoiceGroupModel.status == InvoiceGroupStatus.CANCELLED.value,
            )

        if order_by_request_time:
            booking_invoice_group_models.order_by(
                BookingInvoiceGroupModel.request_datetime
            )

        return [
            self._load(booking_invoice_group_model)
            for booking_invoice_group_model in booking_invoice_group_models
        ]

    def load_for_booking_id(
        self,
        booking_id,
        exclude_preview=False,
        only_preview=False,
        order_by_request_time_desc=False,
    ):
        """
        Load for update all the invoice groups for a given booking_id
        Args:
            booking_id: bill id to be used as the filter
            exclude_preview:
            only_preview:
            order_by_request_time_desc:

        Returns:

        """
        booking_invoice_group_models = self.filter(
            BookingInvoiceGroupModel, BookingInvoiceGroupModel.booking_id == booking_id
        )
        if exclude_preview:
            booking_invoice_group_models = self.exclude(
                booking_invoice_group_models,
                BookingInvoiceGroupModel.status == InvoiceGroupStatus.PREVIEW.value,
            )

        if only_preview:
            booking_invoice_group_models = booking_invoice_group_models.filter(
                BookingInvoiceGroupModel.status == InvoiceGroupStatus.PREVIEW.value
            )

        if order_by_request_time_desc:
            booking_invoice_group_models.order_by(
                BookingInvoiceGroupModel.request_datetime.desc()
            )

        return [
            self._load(booking_invoice_group_model)
            for booking_invoice_group_model in booking_invoice_group_models
        ]

    def load(self, booking_invoice_group_id):
        """
        load invoice
        Args:
            booking_invoice_group_id:

        Returns:

        """
        booking_invoice_group_model = self.get(
            BookingInvoiceGroupModel, booking_invoice_group_id=booking_invoice_group_id
        )
        if not booking_invoice_group_model:
            raise AggregateNotFound(
                "BookingInvoiceGroupAggregate", booking_invoice_group_id
            )
        return self._load(booking_invoice_group_model)

    def load_for_update(self, booking_invoice_group_id, version=None):
        booking_invoice_group_model = self.get_for_update(
            BookingInvoiceGroupModel, booking_invoice_group_id=booking_invoice_group_id
        )
        if not booking_invoice_group_model:
            raise AggregateNotFound(
                "BookingInvoiceGroupAggregate", booking_invoice_group_id
            )
        if version is not None and booking_invoice_group_model.version != version:
            raise OutdatedVersion(
                'BookingInvoiceGroupAggregate',
                version,
                booking_invoice_group_model.version,
            )
        return self._load(booking_invoice_group_model)

    def load_all_for_update(self, invoice_ids):
        booking_invoice_group_models = self.filter(
            BookingInvoiceGroupModel, for_update=True
        ).filter(
            BookingInvoiceGroupModel.invoice_ids.overlap(
                cast(invoice_ids, ARRAY(String))
            )
        )
        return [
            self._load(booking_invoice_group_model)
            for booking_invoice_group_model in booking_invoice_group_models
        ]

    def load_all(self, invoice_ids):
        booking_invoice_group_models = self.filter(BookingInvoiceGroupModel).filter(
            BookingInvoiceGroupModel.invoice_ids.overlap(
                cast(invoice_ids, ARRAY(String))
            )
        )
        return [
            self._load(booking_invoice_group_model)
            for booking_invoice_group_model in booking_invoice_group_models
        ]
