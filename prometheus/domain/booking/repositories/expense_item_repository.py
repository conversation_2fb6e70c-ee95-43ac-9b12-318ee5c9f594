from typing import List

import sqlalchemy
from sqlalchemy.sql.expression import or_

from object_registry import register_instance
from prometheus.domain.booking.exceptions import ExpenseItemNotFound
from prometheus.domain.catalog.entities.expense_item import ExpenseItem
from prometheus.domain.catalog.models import ExpenseItemModel
from prometheus.infrastructure.database.base_repository import BaseRepository


@register_instance()
class ExpenseItemRepository(BaseRepository):
    def to_aggregate(self, expense_item_model: ExpenseItemModel):
        return ExpenseItem(
            expense_item_id=expense_item_model.expense_item_id,
            sku_id=expense_item_model.sku_id,
            name=expense_item_model.name,
            description=expense_item_model.description,
            short_name=expense_item_model.short_name,
            sku_category_id=expense_item_model.sku_category_id,
            linked=expense_item_model.linked,
            addon_code=expense_item_model.addon_code,
            external_item_code=expense_item_model.external_item_code,
            id=expense_item_model.id,
            deleted=expense_item_model.deleted,
        )

    def from_aggregate(self, expense_item: ExpenseItem):
        return ExpenseItemModel(
            expense_item_id=expense_item.expense_item_id,
            sku_id=expense_item.sku_id,
            name=expense_item.name,
            description=expense_item.description,
            short_name=expense_item.short_name,
            sku_category_id=expense_item.sku_category_id,
            linked=expense_item.linked,
            addon_code=expense_item.addon_code,
            external_item_code=expense_item.external_item_code,
            id=expense_item.id,
            deleted=expense_item.deleted,
        )

    def save(self, expense_item):
        expense_model = self.from_aggregate(expense_item)
        self._save(expense_model)
        self.flush_session()
        expense_item.mark_clean()
        return expense_model.expense_item_id

    def save_all(self, expense_items: List[ExpenseItem]):
        expense_models = [
            self.from_aggregate(expense_item) for expense_item in expense_items
        ]
        self._save_all(expense_models)
        self.flush_session()
        for expense_item in expense_items:
            expense_item.mark_clean()

    def update(self, expense_item):
        if not expense_item.is_dirty():
            return
        expense_model = self.from_aggregate(expense_item)
        self._update(expense_model)
        self.flush_session()
        expense_item.mark_clean()
        return expense_model.expense_item_id

    def load(self, expense_item_id=None, sku_id=None):
        try:
            if sku_id:
                expense_item_model = (
                    self.session()
                    .query(ExpenseItemModel)
                    .filter(ExpenseItemModel.sku_id == sku_id)
                    .one()
                )
            else:
                expense_item_model = (
                    self.session()
                    .query(ExpenseItemModel)
                    .filter(ExpenseItemModel.expense_item_id == expense_item_id)
                    .one()
                )
        except sqlalchemy.orm.exc.NoResultFound:
            raise ExpenseItemNotFound()

        return self.to_aggregate(expense_item_model)
        # return self.get(ExpenseItemModel, expense_item_id=expense_item_id)

    def load_all(self, include_linked=None, expense_item_ids=None):
        query = self.session().query(ExpenseItemModel)
        if not include_linked:
            query = query.filter(
                or_(ExpenseItemModel.linked == False, ExpenseItemModel.linked == None)
            )

        if expense_item_ids:
            query = query.filter(ExpenseItemModel.expense_item_id.in_(expense_item_ids))

        expense_item_models = query.all()
        return [
            self.to_aggregate(expense_item_model)
            for expense_item_model in expense_item_models
        ]
