from prometheus.domain.booking.entities.attachment import Attachment
from ths_common.constants.booking_constants import AttachmentStatus
from ths_common.value_objects import UserData


class AttachmentAggregate(object):
    def __init__(self, attachment: Attachment, user_data: UserData):
        self.attachment = attachment
        self.user_data = user_data

    def mark_deleted(self):
        self.attachment.deleted = True

    def update_attachment(self, attachment_update):
        if 'status' in attachment_update and attachment_update.get('status'):
            self.attachment.status = attachment_update.get('status')

        if 'rejection_reason' in attachment_update and attachment_update.get(
            'rejection_reason'
        ):
            self.attachment.rejection_reason = attachment_update.get('rejection_reason')

    def is_attachment_verified(self):
        if self.attachment.status == AttachmentStatus.VERIFIED:
            return True
        return False

    def is_attachment_rejected(self):
        if self.attachment.status == AttachmentStatus.REJECTED:
            return True
        return False
