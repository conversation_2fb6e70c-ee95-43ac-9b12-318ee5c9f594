from enum import Enum
from typing import Callable, Iterable

from prometheus.reporting.finance_erp_reporting.utils import get_state_code


class B2BBookingIssues(Enum):
    GSTIN_DIFFERENT_STATE = {
        "issue": "gstin-different",
        "email_content": "<b>GSTIN is not in the same state as the hotel</b><br>"
        "You booked a hotel in {hotel_state}. The billing details provided is of {gstin_state}.<br><br>"
        ">> Please verify or provide the GSTIN of {hotel_state} if applicable.",
    }
    MULTIPLE_GSTIN_ADDRESSES = {
        "issue": "multiple-gstin",
        "email_content": "<b>GSTIN has multiple address</b><br>"
        "You booked a hotel in {hotel_state} with a GST number {gstin}."
        "The given GST number has different addresses in the billing information. "
        "Following is the list<br>"
        "{gstin_multiple_addresses}<br><br>"
        ">> Please verify or provide the correct billing address if applicable.",
    }
    SAME_GUEST_NAMES = {
        "issue": "same-guest-names",
        "email_content": "<b>Same guest names in multiple rooms</b><br>"
        "The guest names mentioned in this booking for all guests are the same. "
        "Please see the names in the attached voucher.<br><br> "
        ">> Kindly share the correct guest name(s).",
    }
    GUEST_NAMES_DUMMY = {
        "issue": "guest-names-dummy",
        "email_content": "<b>Guest names are not correct</b><br>"
        "There are some incorrect/dummy names of the guests mentioned in this booking."
        " We cannot show these names on the invoice later if required. "
        "Please see the names in the attached voucher.<br><br>"
        ">> Please verify and share the correct guest name(s).",
    }
    ADMIN_NAME_GUEST = {
        "issue": "admin-guest-match",
        "email_content": "<b>Admin name is the same as the guest name</b><br>"
        "In this booking, one or more guest names are the same as the admin name ({admin_name})."
        " Please see the names in the attached voucher.<br><br>"
        ">> Please verify and share the correct guest name(s).",
    }


class B2bBookingIssuesService(object):
    def __init__(
        self,
        billing_entity,
        sibling_sub_entities,
        issues=None,
    ):
        self.billing_entity = billing_entity
        self.sibling_sub_entities = sibling_sub_entities
        if issues is None:
            self.issues = {}

    def identify_issue_with_booking(self, booking_aggregate, hotel_aggregate):
        """
        Identify the issue with the booking
        """
        issue_checks: Iterable[Callable[[], None]] = [
            lambda: self._check_guest_names(booking_aggregate),
        ]
        if self.billing_entity:
            issue_checks.extend(
                [
                    lambda: self._check_hotel_billing_le_state_mismatch(
                        hotel_aggregate
                    ),
                    lambda: self._check_multiple_gstin_addresses(hotel_aggregate),
                ]
            )

        for issue_check in issue_checks:
            issue_check()

    def _add_issue(self, message, extra_payload=None):
        if extra_payload is None:
            extra_payload = {}
        self.issues[message] = extra_payload

    def _check_hotel_billing_le_state_mismatch(self, hotel_aggregate):
        gstin = self.billing_entity.gstin()
        if gstin:
            hotel = hotel_aggregate.hotel
            hotel_state = hotel.state.name
            billing_entity_state = self.billing_entity.registered_address.state
            if hotel_state != billing_entity_state and get_state_code(
                hotel_state
            ) != get_state_code(billing_entity_state):
                self._add_issue(
                    B2BBookingIssues.GSTIN_DIFFERENT_STATE,
                    {
                        "hotel_state": hotel.state.name,
                        "gstin_state": self.billing_entity.registered_address.state,
                    },
                )

    def _check_multiple_gstin_addresses(self, hotel_aggregate):
        sub_entity_gstin_number = self.billing_entity.gstin()
        if sub_entity_gstin_number:
            gstin_multiple_addresses = []
            for sibling_sub_entity in self.sibling_sub_entities:
                gstin = sibling_sub_entity.gstin()
                if (
                    gstin
                    and self.billing_entity.superhero_company_code
                    != sibling_sub_entity.superhero_company_code
                    and sub_entity_gstin_number.upper() == gstin.upper()
                ):
                    gstin_multiple_addresses.append(
                        str(sibling_sub_entity.registered_address)
                    )
            if gstin_multiple_addresses:
                gstin_multiple_addresses.append(
                    str(self.billing_entity.registered_address)
                )
                self._add_issue(
                    B2BBookingIssues.MULTIPLE_GSTIN_ADDRESSES,
                    {
                        "gstin": sub_entity_gstin_number,
                        "hotel_state": hotel_aggregate.hotel.state.name,
                        "gstin_multiple_addresses": "<br>".join(
                            f"{i+1}. {address}"
                            for i, address in enumerate(gstin_multiple_addresses)
                        ),
                    },
                )

    def _check_guest_names(self, booking_aggregate):
        room_guest_names = []
        customer_id_guest_name_dict = {}
        for customer in booking_aggregate.customers:
            customer_id_guest_name_dict[
                customer.customer_id
            ] = customer.name.full_name_with_first_and_last_name.lower()
        for room_stay in booking_aggregate.get_active_room_stays():
            for guest in room_stay.active_guest_stays():
                room_guest_names.append(
                    customer_id_guest_name_dict[guest.guest_allocation.guest_id]
                )

        if len(room_guest_names) != len(set(room_guest_names)):
            self._add_issue(B2BBookingIssues.SAME_GUEST_NAMES)

        for guest_name in room_guest_names:
            guest_name_lower = guest_name.lower()

            if (
                guest_name_lower.startswith("room") and "-guest" in guest_name_lower
            ) or guest_name_lower == 'empty':
                self._add_issue(B2BBookingIssues.GUEST_NAMES_DUMMY)

            sub_entity_admin = (
                self.billing_entity.primary_poc() if self.billing_entity else None
            )
            if sub_entity_admin and sub_entity_admin.name.lower() == guest_name_lower:
                self._add_issue(
                    B2BBookingIssues.ADMIN_NAME_GUEST, {"admin_name": guest_name_lower}
                )
