from datetime import datetime
from typing import List

from prometheus.domain.booking.aggregates.booking_aggregate import BookingAggregate
from prometheus.domain.booking.dtos.guest_checkout_data import (
    CheckoutActionRequest,
    GuestCheckoutRequestData,
    RoomCheckoutRequestData,
)
from prometheus.domain.booking.errors import BookingErrors
from ths_common.exceptions import ValidationException


class BookingCheckoutRequestGenerator(object):
    @staticmethod
    def generate_checkout_request(
        checkout_action_data: [CheckoutActionRequest],
        request_datetime: datetime,
        booking_aggregate: BookingAggregate,
    ):
        """
        Build the checkout request object which can be consumed by the booking domain.
        NOTE: the checkout time per guest has the fallback to expected checkout datetime when the action time is
        more than that.
        Args:
            checkout_action_data: The checkout action request from the api
            request_datetime: The operation time of the checkout action
            booking_aggregate: The booking whose checkout is being considered.

        Returns: RoomCheckoutRequestData

        """
        checkout_requests = []
        for action_data in checkout_action_data:
            guest_checkout_request_data = []
            guest_ids = action_data.guest_ids.copy()
            room_stay = booking_aggregate.get_room_stay(action_data.room_stay_id)
            room_checkout_datetime = (
                request_datetime
                if request_datetime < room_stay.checkout_date
                else room_stay.checkout_date
            )
            for guest_stay in room_stay.guest_stays:
                guest_id = guest_stay.get_allocated_guest()
                if guest_id in action_data.guest_ids:
                    checkout_datetime = (
                        request_datetime
                        if request_datetime < guest_stay.checkout_date
                        else guest_stay.checkout_date
                    )
                    guest_checkout_request_data.append(
                        GuestCheckoutRequestData(
                            guest_stay.guest_stay_id, guest_id, checkout_datetime
                        )
                    )
                    guest_ids.remove(guest_id)
            if len(guest_ids) > 0:
                raise ValidationException(
                    BookingErrors.INVALID_GUEST_SELECTION,
                    description="Guest selected: {} for room_stay_id: {} should belong to "
                    "the room and should be in checked-in state".format(
                        action_data.guest_ids, action_data.room_stay_id
                    ),
                )
            checkout_requests.append(
                RoomCheckoutRequestData(
                    action_data.room_stay_id,
                    room_checkout_datetime,
                    guest_checkout_request_data,
                    room_stay.room_allocation.room_allotment_id,
                    room_stay.room_allocation.room_id,
                )
            )
        return checkout_requests

    @staticmethod
    def override_checkout_time_if_beyond_max_allowed_end_time(
        checkout_requests: List[RoomCheckoutRequestData], max_allowed_end_times
    ):
        """

        :param checkout_requests:
        :param max_allowed_end_times: A dict of structure {room_allotment_id: max_allowed_datetime}
        :return:
        """
        for checkout_request in checkout_requests:
            max_allowed_end_time = max_allowed_end_times.get(
                checkout_request.room_allotment_id
            )
            if checkout_request.checkout_datetime > max_allowed_end_time:
                checkout_request.checkout_datetime = max_allowed_end_time

            for guest_checkout_request in checkout_request.guest_checkout_request_data:
                if guest_checkout_request.checkout_datetime > max_allowed_end_time:
                    guest_checkout_request.checkout_datetime = max_allowed_end_time
