from typing import List

from prometheus.core.base_domain_event import MergeableDomain<PERSON>vent
from prometheus.domain.booking.domain_events.schema.booking import (
    RoomStayNoShowReversedEventSchema,
)
from prometheus.domain.booking.dtos.guest_stay_event_data import GuestStayEventData
from ths_common.constants.domain_event_constants import DomainEvent


class RoomStayNoShowReversedEvent(MergeableDomainEvent):
    def __init__(self, booking_id, guest_stays: List[GuestStayEventData]):
        self.booking_id = booking_id
        self.guest_stays = guest_stays

    def serialize(self):
        serialized = (
            RoomStayNoShowReversedEventSchema(many=True).dump(self.guest_stays).data
        )
        return serialized

    def update_mapping(self, **kwargs):
        room_type_map = kwargs.get('room_type_map')
        for guest_stay in self.guest_stays:
            guest_stay.room_type_name = room_type_map.get(
                guest_stay.room_type_id
            ).room_type.type

    def event_type(self):
        return DomainEvent.ROOM_STAY_NOSHOW_REVERSED

    def merge(self, mergeable_domain_event):
        self.guest_stays.extend(mergeable_domain_event.guest_stays)

    def can_merge(self, mergeable_domain_event):
        return isinstance(mergeable_domain_event, RoomStayNoShowReversedEvent)
