# coding=utf-8

from prometheus.core.base_domain_event import BaseDomainEvent
from prometheus.domain.booking.domain_events.schema.booking import (
    RatePlanChangedEventSchema,
)
from ths_common.constants.domain_event_constants import DomainEvent


class RoomPlanChangedEvent(BaseDomainEvent):
    def __init__(
        self,
        room_stay_id,
        room_type_id,
        new_rate_plan_id,
        old_rate_plan_id,
        new_rate_plan_name,
        old_rate_plan_name,
    ):
        self.room_stay_id = room_stay_id
        self.room_type_id = room_type_id
        self.new_rate_plan_id = new_rate_plan_id
        self.old_rate_plan_id = old_rate_plan_id
        self.new_rate_plan_name = new_rate_plan_name
        self.old_rate_plan_name = old_rate_plan_name
        self.room_type_name = None

    def serialize(self):
        serialized = RatePlanChangedEventSchema().dump(self).data
        return serialized

    def update_mapping(self, **kwargs):
        room_type_map = kwargs.get('room_type_map')
        if room_type_map and room_type_map.get(self.room_type_id):
            self.room_type_name = room_type_map.get(self.room_type_id).room_type.type

    def event_type(self):
        return DomainEvent.RATE_PLAN_CHANGED
