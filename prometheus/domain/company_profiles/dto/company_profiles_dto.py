from typing import Dict, Optional

from ths_common.utils.common_utils import safe_strip


class Address:
    def __init__(self, address_line_1, address_line_2, city, country, pincode, state):
        self.address_line_1 = address_line_1
        self.address_line_2 = address_line_2
        self.city = city
        self.country = country
        self.pincode = pincode
        self.state = state

    def __str__(self):
        address_str = f"{self.address_line_1}"
        if self.address_line_2:
            address_str += f", {self.address_line_2}"
        address_str += f", {self.city}, {self.state}, {self.country}, {self.pincode}"
        return address_str


class PhoneNumber:
    def __init__(self, country_code, number):
        self.country_code = country_code
        self.number = number


class AlternatePhoneNumber:
    def __init__(self, country_code, number):
        self.country_code = country_code
        self.number = number


class POC:
    def __init__(
        self,
        contact_types,
        department,
        designation,
        email_ids,
        name,
        phone_number,
        poc_type,
        user_id,
        **kwargs,
    ):
        self.contact_types = contact_types
        self.department = department
        self.designation = designation
        self.email_ids = email_ids
        self.name = name
        self.phone_number = PhoneNumber(**phone_number) if phone_number else None
        self.poc_type = poc_type
        self.user_id = user_id
        self.source = kwargs.get("source", None)


class StatutoryDetail:
    def __init__(self, attachment_url, field_name, value):
        self.attachment_url = attachment_url
        self.field_name = field_name
        self.value = value


class FolioRule:
    def __init__(self, is_btc_allowed, is_credit_enabled, sku_category):
        self.is_btc_allowed = is_btc_allowed
        self.is_credit_enabled = is_credit_enabled
        self.sku_category = sku_category


class OTACommissionDetail:
    def __init__(self, commission_type, commission_value, post_commission_amount):
        self.commission_type = commission_type
        self.commission_value = commission_value
        self.post_commission_amount = post_commission_amount


class RegisteredAddress(Address):
    pass


class CommunicationAddress(Address):
    pass


class ParentEntity:
    def __init__(
        self,
        client_internal_code,
        parent_entity_id,
        point_of_contacts,
        registered_address,
        statutory_details,
        superhero_company_code,
        trade_name,
        is_sez_applicable,
        folio_rules: Optional[Dict] = None,
        email_id=None,
        phone_number=None,
        **kwargs,
    ):
        self.client_internal_code = client_internal_code
        self.is_sez_applicable = is_sez_applicable
        self.folio_rules = (
            [FolioRule(**rule) for rule in folio_rules] if folio_rules else []
        )
        self.parent_entity_id = parent_entity_id
        self.point_of_contacts = (
            [POC(**poc) for poc in point_of_contacts] if point_of_contacts else []
        )
        self.registered_address = (
            RegisteredAddress(**registered_address) if registered_address else None
        )
        self.statutory_details = (
            [StatutoryDetail(**detail) for detail in statutory_details]
            if statutory_details
            else []
        )
        self.superhero_company_code = superhero_company_code
        self.trade_name = trade_name
        self.email_id = email_id
        self.phone_number = PhoneNumber(**phone_number) if phone_number else None


class SubEntity:
    def __init__(
        self,
        client_internal_code,
        communication_address,
        created_at,
        email_id,
        has_lut,
        is_sez_applicable,
        legal_entity_name,
        parent_entity_id,
        parent_superhero_company_code,
        point_of_contacts,
        registered_address,
        status,
        statutory_details,
        sub_entity_id,
        superhero_company_code,
        trade_name,
        phone_number,
        **kwargs,
    ):
        self.client_internal_code = client_internal_code
        self.communication_address = (
            CommunicationAddress(**communication_address)
            if communication_address
            else None
        )
        self.created_at = created_at
        self.email_id = email_id
        self.has_lut = has_lut
        self.is_sez_applicable = is_sez_applicable
        self.legal_entity_name = legal_entity_name
        self.parent_entity_id = parent_entity_id
        self.parent_superhero_company_code = parent_superhero_company_code
        self.point_of_contacts = (
            [POC(**poc) for poc in point_of_contacts] if point_of_contacts else []
        )
        self.registered_address = (
            RegisteredAddress(**registered_address) if registered_address else None
        )
        self.status = status
        self.statutory_details = (
            [StatutoryDetail(**detail) for detail in statutory_details]
            if statutory_details
            else None
        )
        self.sub_entity_id = sub_entity_id
        self.superhero_company_code = superhero_company_code
        self.trade_name = trade_name
        self.phone_number = PhoneNumber(**phone_number) if phone_number else None

    def gstin(self):
        if self.statutory_details:
            for detail in self.statutory_details:
                if detail.field_name == "gst":
                    return safe_strip(detail.value)
        return None

    def sales_poc(self):
        for poc in self.point_of_contacts:
            if poc.department == 'sales' and poc.source == 'b2b':
                return poc
        return None

    def inside_sales_poc(self):
        for poc in self.point_of_contacts:
            if poc.department == 'inside_sales' and poc.source == 'b2b':
                return poc
        return None

    def primary_poc(self):
        for poc in self.point_of_contacts:
            if poc.department == 'booking' and poc.source == 'b2b':
                return poc
        return None

    def is_dummy_poc(self):
        DUMMY_POC_EMAIL_IDS = [
            '<EMAIL>',
            '<EMAIL>',
        ]
        if self.primary_poc() and any(
            email in self.primary_poc().email_ids for email in DUMMY_POC_EMAIL_IDS
        ):
            return True
        return False


class Error:
    def __init__(self, code, developer_message, extra_payload, message):
        self.code = code
        self.developer_message = developer_message
        self.extra_payload = extra_payload
        self.message = message


class CompanyProfilesDTO:
    def __init__(self, data, errors, meta):
        self.data = {
            "parent_entities": [
                ParentEntity(**entity) for entity in data["parent_entities"]
            ],
            "sub_entities": [SubEntity(**entity) for entity in data["sub_entities"]],
        }
        self.errors = [Error(**error) for error in errors]
        self.meta = meta


class SubEntitiesDTO:
    def __init__(self, sub_entities):
        self.sub_entities = (
            [SubEntity(**entity) for entity in sub_entities] if sub_entities else []
        )
