# coding=utf-8
"""
Models
"""

from sqlalchemy import (
    <PERSON><PERSON><PERSON>,
    Column,
    Date,
    DateTime,
    Integer,
    String,
    UniqueConstraint,
)
from sqlalchemy.dialects.postgresql import ARRAY, JSON
from sqlalchemy.orm import relationship
from treebo_commons.multitenancy.sqlalchemy.db_engine import Base

from prometheus.infrastructure.database.common_models import ItemCodeDBType
from shared_kernel.infrastructure.database.orm_base import DeleteMixin, TimeStampMixin


class ResellerGSTModel(Base, TimeStampMixin, DeleteMixin):
    """
    ResellerGST
    """

    __tablename__ = "reseller_gst"

    id = Column('id', String, primary_key=True)
    state_id = Column('state_id', Integer)
    state_name = Column('state_name', String)
    gstin_num = Column('gstin_num', String)
    date_of_registration = Column('date_of_registration', Date)
    address = Column('address', String)


class HotelRoomTypeConfigModel(Base, TimeStampMixin, DeleteMixin):
    """
    Hotel Room Types
    """

    __tablename__ = "hotel_room_type_config"

    room_type_config_id = Column('room_type_config_id', Integer, primary_key=True)
    hotel_id = Column('hotel_id', String, nullable=False, index=True)
    room_type_id = Column('room_type_id', String, index=True)
    count = Column('count', Integer)
    base_pax = Column('base_pax', Integer)
    max_adult = Column('max_pax', Integer)
    max_child = Column('max_child', Integer)
    max_adult_plus_children = Column('max_adult_plus_children', Integer)
    status = Column('status', String, nullable=False)  # Enum(RoomTypeStatus)
    UniqueConstraint('hotel_id', 'room_type_id')


class HotelModel(Base, TimeStampMixin, DeleteMixin):
    """
    Hotel
    """

    __tablename__ = "hotel"

    hotel_id = Column('hotel_id', String, primary_key=True)
    name = Column('name', String)
    area = Column('area', String)
    status = Column('status', String, nullable=False)  # Enum(HotelStatus)

    # GST Details
    legal_name = Column('legal_name', String)
    legal_address = Column('legal_address', String)
    gstin_num = Column('gstin_num', String)

    switch_over_time = Column('switch_over_time', String)
    checkin_grace_time = Column('checkin_grace_time', Integer, default=360)
    checkout_grace_time = Column('checkout_grace_time', Integer, default=360)

    # Catalog related details
    checkin_time = Column('checkin_time', String)
    checkout_time = Column('checkout_time', String)
    free_late_checkout_time = Column('free_late_checkout_time', String)
    launched_date = Column('launched_date', Date)
    state_id = Column('state_id', Integer)
    state_name = Column('state_name', String)
    city_id = Column('city_id', Integer)
    city_name = Column('city_name', String)
    pincode = Column('pincode', String)
    country = Column('country', String)
    legal_signature = Column('legal_signature', String)
    legal_city_id = Column('legal_city_id', Integer)
    legal_city_name = Column('legal_city_name', String)
    legal_state_id = Column('legal_state_id', Integer)
    legal_state_name = Column('legal_state_name', String)
    legal_pincode = Column('legal_pincode', String)
    housekeeping_enabled = Column('housekeeping_enabled', Boolean)
    brands = Column('brands', ARRAY(String))
    has_lut = Column('has_lut', Boolean)
    base_currency = Column('base_currency', String)
    timezone = Column('timezone', String)
    phone_number = Column('phone_number', String)
    country_code = Column('country_code', String)
    email = Column('email', String)
    logo = Column('logo', String)
    current_business_date = Column(Date)
    system_freeze_time = Column(String)
    pan_number = Column(String)
    tan_number = Column(String)
    tin_number = Column(String)
    forex_license_no = Column(String)
    msme_number = Column(String)
    is_test = Column(Boolean)
    cin_number = Column(String)

    room_type_configs = relationship(
        'HotelRoomTypeConfigModel',
        primaryjoin='HotelRoomTypeConfigModel.hotel_id == foreign(HotelModel.hotel_id)',
        lazy='joined',
        uselist=True,
    )


class RoomTypeModel(Base, TimeStampMixin, DeleteMixin):
    """
    Room Type
    """

    __tablename__ = "room_type"

    room_type_id = Column('room_type_id', String, primary_key=True)
    type = Column('type', String)


class RoomModel(Base, TimeStampMixin, DeleteMixin):
    """
    Room
    """

    __tablename__ = "room"

    room_id = Column('room_id', Integer, primary_key=True)
    hotel_id = Column('hotel_id', String, nullable=False, index=True)
    room_type_id = Column('room_type_id', String, nullable=False, index=True)
    room_number = Column('room_number', String, nullable=False)
    status = Column('status', String, nullable=False)  # Enum(RoomStatus)


class ExpenseItemModel(Base, TimeStampMixin, DeleteMixin):
    """
    Expense Categories
    """

    __tablename__ = "expense_item"

    id = Column("id", Integer, primary_key=True)
    expense_item_id = Column('expense_item_id', String)
    sku_id = Column('sku_id', String, nullable=True)
    name = Column("name", String)
    description = Column("description", String)
    short_name = Column("short_name", String)
    sku_category_id = Column("sku_category_id", String)
    linked = Column("linked", Boolean, nullable=True)
    addon_code = Column("addon_code", String, nullable=True)
    external_item_code = Column("external_item_code", String, nullable=True)
    deleted = Column("deleted", Boolean)

    __table_args__ = (UniqueConstraint('expense_item_id'),)


class SkuCategoryModel(Base, TimeStampMixin, DeleteMixin):
    """
    Sku Items
    """

    __tablename__ = "sku_category"

    item_code = Column('item_code', ItemCodeDBType)
    name = Column("name", String)
    sku_category_id = Column("sku_category_id", String, primary_key=True)
    status = Column("status", String)
    has_slab_based_taxation = Column(Boolean)


class HouseKeeperModel(Base, TimeStampMixin):
    __tablename__ = 'housekeeper'

    hotel_id = Column('hotel_id', String, primary_key=True)
    housekeeper_id = Column('housekeeper_id', Integer, primary_key=True)
    name = Column('name', String)


class NightAuditModel(Base, TimeStampMixin):
    __tablename__ = 'night_audit'

    night_audit_id = Column(String, primary_key=True)
    hotel_id = Column(String)
    job_id = Column(String)
    business_date = Column(Date)
    status = Column(String)
    start_time = Column(DateTime(timezone=True))
    end_time = Column(DateTime(timezone=True))
    vendors_with_pending_critical_tasks = Column(JSON)

    __table_args__ = (UniqueConstraint('hotel_id', 'business_date'),)
