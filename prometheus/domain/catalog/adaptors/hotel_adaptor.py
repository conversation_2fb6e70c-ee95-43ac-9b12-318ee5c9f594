# coding=utf-8
from treebo_commons.money.constants import CurrencyType

from prometheus.domain.catalog.entities.hotel import Hotel
from prometheus.domain.catalog.models import HotelModel
from ths_common.constants.catalog_constants import HotelStatus
from ths_common.value_objects import City, State


class HotelAdaptor:
    def to_db_entity(self, domain_entity: Hotel):
        return HotelModel(
            hotel_id=domain_entity.hotel_id,
            name=domain_entity.name,
            area=domain_entity.area,
            status=domain_entity.status.value,
            legal_name=domain_entity.legal_name,
            legal_address=domain_entity.legal_address,
            gstin_num=domain_entity.gstin_num,
            checkin_time=domain_entity.checkin_time,
            checkout_time=domain_entity.checkout_time,
            free_late_checkout_time=domain_entity.free_late_checkout_time,
            switch_over_time=domain_entity.switch_over_time,
            deleted=domain_entity.deleted,
            checkin_grace_time=domain_entity.checkin_grace_time,
            checkout_grace_time=domain_entity.checkout_grace_time,
            launched_date=domain_entity.launched_date,
            state_id=domain_entity.state.id,
            state_name=domain_entity.state.name,
            city_id=domain_entity.city.id,
            city_name=domain_entity.city.name,
            pincode=domain_entity.pincode,
            country=domain_entity.country,
            legal_signature=domain_entity.legal_signature,
            legal_city_id=domain_entity.legal_city.id
            if domain_entity.legal_city
            else None,
            legal_city_name=domain_entity.legal_city.name
            if domain_entity.legal_city
            else None,
            legal_state_id=domain_entity.legal_state.id
            if domain_entity.legal_state
            else None,
            legal_state_name=domain_entity.legal_state.name
            if domain_entity.legal_state
            else None,
            legal_pincode=domain_entity.legal_pincode,
            housekeeping_enabled=domain_entity.housekeeping_enabled,
            brands=domain_entity.brands,
            has_lut=domain_entity.has_lut,
            base_currency=domain_entity.base_currency.value,
            timezone=domain_entity.timezone,
            phone_number=domain_entity.phone_number,
            country_code=domain_entity.country_code,
            email=domain_entity.email,
            logo=domain_entity.logo,
            current_business_date=domain_entity.current_business_date,
            system_freeze_time=domain_entity.system_freeze_time,
            pan_number=domain_entity.pan_number,
            tan_number=domain_entity.tan_number,
            tin_number=domain_entity.tin_number,
            forex_license_no=domain_entity.forex_license_no,
            msme_number=domain_entity.msme_number,
            is_test=domain_entity.is_test,
            cin_number=domain_entity.cin_number,
        )

    def to_domain_entity(self, db_entity):
        base_currency = (
            CurrencyType(db_entity.base_currency)
            if db_entity.base_currency
            else CurrencyType.INR
        )
        return Hotel(
            hotel_id=db_entity.hotel_id,
            name=db_entity.name,
            area=db_entity.area,
            status=HotelStatus(db_entity.status),
            legal_name=db_entity.legal_name,
            legal_address=db_entity.legal_address,
            gstin_num=db_entity.gstin_num,
            checkin_time=db_entity.checkin_time,
            checkout_time=db_entity.checkout_time,
            free_late_checkout_time=db_entity.free_late_checkout_time,
            checkin_grace_time=db_entity.checkin_grace_time,
            checkout_grace_time=db_entity.checkout_grace_time,
            switch_over_time=db_entity.switch_over_time,
            deleted=db_entity.deleted,
            launched_date=db_entity.launched_date,
            state=State(state_id=db_entity.state_id, name=db_entity.state_name),
            city=City(city_id=db_entity.city_id, name=db_entity.city_name),
            pincode=db_entity.pincode,
            country=db_entity.country,
            legal_signature=db_entity.legal_signature,
            legal_city=City(
                city_id=db_entity.legal_city_id, name=db_entity.legal_city_name
            )
            if db_entity.legal_city_name
            else None,
            legal_state=State(
                state_id=db_entity.legal_state_id, name=db_entity.legal_state_name
            )
            if db_entity.legal_state_name
            else None,
            legal_pincode=db_entity.legal_pincode,
            housekeeping_enabled=db_entity.housekeeping_enabled,
            brands=[brand for brand in db_entity.brands] if db_entity.brands else None,
            has_lut=db_entity.has_lut,
            base_currency=base_currency,
            timezone=db_entity.timezone,
            phone_number=db_entity.phone_number,
            country_code=db_entity.country_code,
            email=db_entity.email,
            logo=db_entity.logo,
            current_business_date=db_entity.current_business_date,
            pan_number=db_entity.pan_number,
            tan_number=db_entity.tan_number,
            tin_number=db_entity.tin_number,
            forex_license_no=db_entity.forex_license_no,
            msme_number=db_entity.msme_number,
            is_test=db_entity.is_test,
            cin_number=db_entity.cin_number,
        )
