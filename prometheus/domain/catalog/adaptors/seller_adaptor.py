from treebo_commons.money.constants import CurrencyType

from prometheus.domain.catalog.entities.seller import Seller
from prometheus.infrastructure.database.base_db_to_domain_entity_adaptor import (
    BaseAdaptor,
)
from shared_kernel.infrastructure.database.common_models import SellerModel
from ths_common.value_objects import City, State


class SellerAdaptor(BaseAdaptor):
    def to_db_entity(self, domain_entity: Seller, **kwargs):
        seller = domain_entity
        # noinspection PyArgumentList
        return SellerModel(
            seller_id=seller.seller_id,
            seller_category_id=seller.category_id,
            name=seller.name,
            state_id=seller.state.id,
            state_name=seller.state.name,
            city_id=seller.city.id,
            city_name=seller.city.name,
            pincode=seller.pincode,
            legal_name=seller.legal_name,
            legal_address=seller.legal_address,
            gstin_num=seller.gstin_num,
            legal_signature=seller.legal_signature,
            legal_city_id=seller.legal_city.id,
            legal_city_name=seller.legal_city.name,
            legal_state_id=seller.legal_state.id,
            legal_state_name=seller.legal_state.name,
            legal_pincode=seller.legal_pincode,
            status=seller.status,
            base_currency=seller.base_currency.value,
            timezone=seller.timezone,
            current_business_date=seller.current_business_date,
            hotel_id=seller.hotel_id,
            seller_config=seller.seller_config,
        )

    def to_domain_entity(self, db_entity: SellerModel, **kwargs):
        seller_model = db_entity
        currency = (
            CurrencyType(db_entity.base_currency)
            if db_entity.base_currency
            else CurrencyType.INR
        )
        return Seller(
            seller_id=seller_model.seller_id,
            name=seller_model.name,
            state=State(seller_model.state_id, seller_model.state_name),
            city=City(seller_model.city_id, seller_model.city_name),
            pincode=seller_model.pincode,
            legal_name=seller_model.legal_name,
            legal_address=seller_model.legal_address,
            gstin_num=seller_model.gstin_num,
            legal_signature=seller_model.legal_signature,
            legal_city=City(seller_model.legal_city_id, seller_model.legal_city_name),
            legal_state=State(
                seller_model.legal_state_id, seller_model.legal_state_name
            ),
            legal_pincode=seller_model.legal_pincode,
            category_id=seller_model.seller_category_id,
            status=seller_model.status,
            base_currency=currency,
            timezone=seller_model.timezone,
            hotel_id=seller_model.hotel_id,
            current_business_date=seller_model.current_business_date,
            seller_config=seller_model.seller_config,
        )
