from prometheus.domain.catalog.dtos.sku_category_dto import SkuCategoryDto
from prometheus.domain.catalog.entities.sku_category import SkuCategory


class SkuCategoryAggregate(object):
    def __init__(self, sku_category: SkuCategory):
        self._sku_category = sku_category

    @property
    def sku_category(self):
        return self._sku_category

    @property
    def sku_category_id(self):
        return self._sku_category.sku_category_id

    def update_from_catalog(self, sku_category_dto: SkuCategoryDto):
        self._sku_category._item_code = sku_category_dto.item_code
        self._sku_category._name = sku_category_dto.name
