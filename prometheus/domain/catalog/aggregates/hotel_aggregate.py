from typing import List

from treebo_commons.utils import dateutils

from prometheus.domain.catalog.entities.hotel import Hotel
from prometheus.domain.catalog.entities.room_type_config import HotelRoomTypeConfig
from prometheus.domain.catalog.errors import CatalogError
from prometheus.domain.catalog.exceptions import CatalogException
from ths_common.exceptions import ValidationException
from ths_common.value_objects import Occupancy

FISCAL_YEAR_START_MONTH = 4


class HotelAggregate(object):
    def __init__(self, hotel: Hotel, room_type_configs: List[HotelRoomTypeConfig]):
        self.hotel = hotel
        self.room_type_configs = room_type_configs

    @property
    def hotel_id(self):
        return self.hotel.hotel_id

    @property
    def base_currency(self):
        return self.hotel.base_currency

    @property
    def forex_license_no(self):
        return self.hotel.forex_license_no

    def is_active(self):
        return self.hotel.is_active

    def has_room_type_config_for_room_type(self, room_type_id):
        return any(
            room_type_id == room_type_config.room_type_id
            for room_type_config in self.room_type_configs
        )

    def get_room_type_config(self, room_type_id):
        for room_type_config in self.room_type_configs:
            if room_type_config.room_type_id == room_type_id:
                return room_type_config
        return None

    def occupancy_supported(self, room_type_id, occupancy: Occupancy):
        room_type_config = self.get_room_type_config(room_type_id)
        if not room_type_config:
            raise ValidationException(error=CatalogError.ROOM_TYPE_CONFIG_NOT_FOUND)
        return room_type_config.occupancy_supported(occupancy)

    def mark_room_type_config_status_inactive(self, room_type_id):
        room_type_config = self.get_room_type_config(room_type_id)
        room_type_config.mark_status_inactive()

    def add_room_type_config(self, room_type_config_dto):
        room_type_config_entity = HotelRoomTypeConfig(
            room_type_config_id=room_type_config_dto.room_type_config_id,
            room_type_id=room_type_config_dto.room_type_id,
            base_pax=room_type_config_dto.base_pax,
            max_adult=room_type_config_dto.max_adult,
            max_child=room_type_config_dto.max_child,
            max_adult_plus_children=room_type_config_dto.max_adult_plus_children,
            status=room_type_config_dto.status,
        )
        self.room_type_configs.append(room_type_config_entity)

    def update_room_type_config(self, room_type_config_dto):
        room_type_config = self.get_room_type_config(room_type_config_dto.room_type_id)

        if room_type_config.occupancy != room_type_config_dto.occupancy:
            room_type_config.update_occupancy(
                room_type_config_dto.max_adult,
                room_type_config_dto.max_child,
                room_type_config_dto.max_adult_plus_children,
            )

    def update_hotel(self, hotel_dto):
        hotel_entity = self.hotel
        hotel_entity.name = hotel_dto.name
        hotel_entity.area = hotel_dto.area
        hotel_entity.launched_date = hotel_dto.launched_date
        hotel_entity.state = hotel_dto.state
        hotel_entity.city = hotel_dto.city
        hotel_entity.pincode = hotel_dto.pincode
        hotel_entity.legal_signature = hotel_dto.legal_signature
        hotel_entity.brands = hotel_dto.brands
        hotel_entity.housekeeping_enabled = hotel_dto.housekeeping_enabled
        hotel_entity.has_lut = hotel_dto.has_lut
        hotel_entity.base_currency = hotel_dto.base_currency
        hotel_entity.timezone = hotel_dto.timezone
        hotel_entity.phone_number = hotel_dto.phone_number
        hotel_entity.country_code = hotel_dto.country_code
        hotel_entity.email = hotel_dto.email
        hotel_entity.logo = hotel_dto.logo
        hotel_entity.pan_number = hotel_dto.pan_number
        hotel_entity.tan_number = hotel_dto.tan_number
        hotel_entity.tin_number = hotel_dto.tin_number
        hotel_entity.forex_license_no = hotel_dto.forex_license_no
        hotel_entity.cin_number = hotel_dto.cin_number
        if not hotel_entity.is_active and hotel_dto.current_business_date:
            hotel_entity.current_business_date = hotel_dto.current_business_date
        hotel_entity.status = hotel_dto.status
        hotel_entity.msme_number = hotel_dto.msme_number
        hotel_entity.is_test = hotel_dto.is_test

        if not self.is_active():
            self.update_gst_details(hotel_dto)
            self.update_time_configurations(hotel_dto)

        elif not self._has_gstin_changed(
            hotel_dto
        ) and not self._has_legal_state_changed(hotel_dto):
            self.update_gst_details(hotel_dto)

        self._handle_disallowed_attribute_update(hotel_entity, hotel_dto)

    @staticmethod
    def _handle_disallowed_attribute_update(hotel_entity, hotel_dto):
        disallowed_attribute_update = dict()

        if (
            hotel_entity.gstin_num is not None
            and hotel_entity.gstin_num != hotel_dto.gstin_num
        ):
            disallowed_attribute_update['gstin_num'] = hotel_dto.gstin_num

        if hotel_entity.switch_over_time != hotel_dto.switch_over_time:
            disallowed_attribute_update['switch_over_time'] = hotel_dto.switch_over_time

        if hotel_entity.checkin_grace_time != hotel_dto.checkin_grace_time:
            disallowed_attribute_update[
                'checkin_grace_time'
            ] = hotel_dto.checkin_grace_time

        if hotel_entity.checkout_grace_time != hotel_dto.checkout_grace_time:
            disallowed_attribute_update[
                'checkout_grace_time'
            ] = hotel_dto.checkout_grace_time

        if hotel_entity.checkin_time != hotel_dto.checkin_time:
            disallowed_attribute_update['checkin_time'] = hotel_dto.checkin_time

        if hotel_entity.checkout_time != hotel_dto.checkout_time:
            disallowed_attribute_update['checkout_time'] = hotel_dto.checkout_time

        if hotel_entity.free_late_checkout_time != hotel_dto.free_late_checkout_time:
            disallowed_attribute_update[
                'free_late_checkout_time'
            ] = hotel_dto.free_late_checkout_time

        if disallowed_attribute_update:
            raise CatalogException(
                error=CatalogError.UNSUPPORTED_ATTRIBUTE_UPDATE_FROM_CATALOG_RECEIVED,
                extra_payload=dict(
                    hotel_id=hotel_entity.hotel_id,
                    disallowed_attributes=disallowed_attribute_update,
                ),
            )

    def _has_gstin_changed(self, hotel_dto):
        if not self.hotel.gstin_num:
            return False

        if self.hotel.gstin_num == hotel_dto.gstin_num:
            return False

        return True

    def _has_legal_state_changed(self, hotel_dto):
        if not self.hotel.legal_state:
            return False

        if (
            hotel_dto.legal_state
            and self.hotel.legal_state.id == hotel_dto.legal_state.id
        ):
            return False

        return True

    def update_gst_details(self, hotel_dto):
        hotel_entity = self.hotel
        hotel_entity.gstin_num = hotel_dto.gstin_num
        hotel_entity.legal_name = hotel_dto.legal_name
        hotel_entity.legal_address = hotel_dto.legal_address
        hotel_entity.legal_city = hotel_dto.legal_city
        hotel_entity.legal_state = hotel_dto.legal_state
        hotel_entity.legal_pincode = hotel_dto.legal_pincode

    def update_time_configurations(self, hotel_dto):
        hotel_entity = self.hotel
        hotel_entity.switch_over_time = hotel_dto.switch_over_time
        hotel_entity.checkin_grace_time = hotel_dto.checkin_grace_time
        hotel_entity.checkout_grace_time = hotel_dto.checkout_grace_time
        hotel_entity.checkin_time = hotel_dto.checkin_time
        hotel_entity.checkout_time = hotel_dto.checkout_time
        hotel_entity.free_late_checkout_time = hotel_dto.free_late_checkout_time
        hotel_entity.system_freeze_time = hotel_dto.system_freeze_time

    def rollover_current_business_date(self):
        self.hotel.current_business_date = dateutils.add(
            self.hotel.current_business_date, days=1
        )

    def is_first_day_of_fiscal_year(self):
        """
        Checks if the hotel's current business date is the first day of the fiscal year.
        """
        business_date = self.hotel.current_business_date
        return business_date.month == FISCAL_YEAR_START_MONTH and business_date.day == 1
