class RoomTypeDto(object):
    def __init__(self, room_type_id, crs_room_type_code, type):
        self.room_type_id = room_type_id
        self.crs_room_type_code = crs_room_type_code
        self.type = type

    @staticmethod
    def create_from_catalog_data(catalog_room_type_data):
        data = catalog_room_type_data
        return RoomTypeDto(
            room_type_id=data.get('code'),
            crs_room_type_code=data.get('crs_room_type_code'),
            type=data.get('type'),
        )
