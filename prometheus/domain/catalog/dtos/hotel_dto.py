from treebo_commons.money.constants import CurrencyType

from ths_common.constants.catalog_constants import HotelStatus
from ths_common.value_objects import BankDetails, City, State


class HotelDto(object):
    def __init__(
        self,
        hotel_id,
        name,
        area,
        status,
        legal_name,
        checkin_time,
        checkout_time,
        free_late_checkout_time,
        legal_address,
        gstin_num,
        brands,
        checkin_grace_time=None,
        checkout_grace_time=None,
        switch_over_time=None,
        launched_date=None,
        state=None,
        city=None,
        pincode=None,
        country=None,
        legal_signature=None,
        legal_city=None,
        legal_state=None,
        legal_pincode=None,
        housekeeping_enabled=None,
        bank_details: BankDetails = None,
        has_lut=False,
        base_currency=CurrencyType.INR,
        timezone=None,
        phone_number=None,
        country_code=None,
        email=None,
        logo=None,
        current_business_date=None,
        system_freeze_time=None,
        pan_number=None,
        tin_number=None,
        tan_number=None,
        forex_license_no=None,
        msme_number=None,
        is_test=False,
        cin_number=None,
    ):
        self.hotel_id = hotel_id
        self.name = name
        self.area = area
        self.status = status
        self.legal_name = legal_name
        self.legal_address = legal_address
        self.gstin_num = gstin_num
        self.switch_over_time = switch_over_time
        self.checkin_grace_time = checkin_grace_time
        self.checkout_grace_time = checkout_grace_time
        self.checkin_time = checkin_time
        self.checkout_time = checkout_time
        self.free_late_checkout_time = free_late_checkout_time
        self.launched_date = launched_date
        self.state = state
        self.city = city
        self.pincode = pincode
        self.country = country
        self.legal_signature = legal_signature
        self.legal_city = legal_city
        self.legal_state = legal_state
        self.legal_pincode = legal_pincode
        self.bank_details = bank_details
        self.brands = brands
        self.housekeeping_enabled = housekeeping_enabled
        self.has_lut = has_lut
        self.base_currency = base_currency
        self.timezone = timezone
        self.phone_number = phone_number
        self.country_code = country_code
        self.email = email
        self.logo = logo
        self.current_business_date = current_business_date
        self.system_freeze_time = system_freeze_time
        self.pan_number = pan_number
        self.tin_number = tin_number
        self.tan_number = tan_number
        self.forex_license_no = forex_license_no
        self.msme_number = msme_number
        self.is_test = is_test
        self.cin_number = cin_number

    @staticmethod
    def create_from_catalog_data(catalog_property_data):
        data = catalog_property_data
        guest_facing_details = data.get('guest_facing_details')
        status = (
            HotelStatus.ACTIVE if data.get('status') == "LIVE" else HotelStatus.INACTIVE
        )
        location = data.get('location')
        property_details = data.get('property_details')
        legal_city, legal_state = location.get('legal_city'), location.get(
            'legal_state'
        )
        bank_details = property_details.get('bank_details')
        brands = (
            [
                brand.get('code')
                for brand in data.get('brands')
                if brand.get('status') == 'ACTIVE'
            ]
            if data.get('brands')
            else None
        )

        return HotelDto(
            hotel_id=data.get("id"),
            name=data.get('name').get('new_name'),
            area=location.get('postal_address'),
            status=status,
            legal_name=data.get('name').get('legal_name'),
            legal_address=location.get('legal_address'),
            gstin_num=property_details.get('gstin'),
            checkin_time=guest_facing_details.get('checkin_time'),
            checkout_time=guest_facing_details.get('checkout_time'),
            checkin_grace_time=guest_facing_details.get('checkin_grace_time'),
            checkout_grace_time=guest_facing_details.get('checkout_grace_time'),
            free_late_checkout_time=guest_facing_details.get('free_late_checkout_time'),
            switch_over_time=guest_facing_details.get('switch_over_time'),
            launched_date=data.get("launched_date"),
            state=State(
                state_id=location.get('state').get("id"),
                name=location.get("state").get("name"),
            ),
            city=City(
                city_id=location.get('city').get("id"),
                name=location.get("city").get("name"),
            ),
            pincode=str(location.get("pincode")),
            country="India",
            legal_signature=property_details.get('legal_signature'),
            legal_city=City(city_id=legal_city.get('id'), name=legal_city.get('name'))
            if legal_city
            else None,
            legal_state=State(
                state_id=legal_state.get('id'), name=legal_state.get('name')
            )
            if legal_state
            else None,
            legal_pincode=str(location.get('legal_pincode'))
            if location.get('legal_pincode')
            else None,
            bank_details=BankDetails.from_json(bank_details),
            brands=brands,
            housekeeping_enabled=property_details.get("is_housekeeping_enabled"),
            has_lut=property_details.get("has_lut"),
            base_currency=CurrencyType(data.get("base_currency_code"))
            if data.get("base_currency_code")
            else CurrencyType.INR,
            timezone=data.get("timezone"),
            phone_number=property_details.get("reception_mobile"),
            country_code=data.get("country_code"),
            email=property_details.get("email"),
            logo=data.get("logo"),
            current_business_date=data.get('current_business_date'),
            system_freeze_time=guest_facing_details.get('system_freeze_time'),
            pan_number=property_details.get('pan'),
            tin_number=property_details.get('tin'),
            tan_number=property_details.get('tan'),
            forex_license_no=property_details.get('forex_license_no'),
            msme_number=property_details.get('msme_number'),
            is_test=data.get('is_test') or False,
            cin_number=property_details.get('cin_number'),
        )

    def __str__(self):
        return "Hotel Id: {0}, Name: {1}, State: {2}, City: {3}, PinCode: {4}".format(
            self.hotel_id, self.name, self.state, self.city, self.pincode
        )
