from object_registry import register_instance
from prometheus.domain.catalog.aggregates.hotel_staff_aggregate import (
    HotelStaffAggregate,
)
from prometheus.domain.catalog.entities.housekeeper import HouseKeeper
from prometheus.domain.catalog.models import HouseKeeperModel
from prometheus.infrastructure.database.base_repository import BaseRepository


@register_instance()
class HotelStaffRepository(BaseRepository):
    def to_aggregate(self, **kwargs):
        housekeeper_models = kwargs['housekeeper_models']
        hotel_id = kwargs['hotel_id']
        housekeepers = []
        for housekeeper_model in housekeeper_models:
            housekeepers.append(
                HouseKeeper(
                    housekeeper_id=housekeeper_model.housekeeper_id,
                    name=housekeeper_model.name,
                )
            )
        return HotelStaffAggregate(hotel_id=hotel_id, housekeepers=housekeepers)

    def from_aggregate(self, aggregate=None):
        housekeepers = aggregate.housekeepers
        housekeeper_models = []
        for housekeeper in housekeepers:
            housekeeper_models.append(
                HouseKeeperModel(
                    housekeeper_id=housekeeper.housekeeper_id,
                    name=housekeeper.name,
                    hotel_id=aggregate.hotel_id,
                )
            )
        return housekeeper_models

    def save(self, hotel_staff_aggregate):
        housekeeper_models = self.from_aggregate(hotel_staff_aggregate)
        self._save_all(housekeeper_models)
        self.flush_session()

    def save_all(self, hotel_staff_aggregates):
        housekeeper_models = []
        for hotel_staff_aggregate in hotel_staff_aggregates:
            housekeeper_models.extend(self.from_aggregate(hotel_staff_aggregate))
        self._save_all(housekeeper_models)
        self.flush_session()

    def update(self, hotel_staff_aggregate):
        housekeeper_models = self.from_aggregate(hotel_staff_aggregate)
        self._update_all(housekeeper_models)
        self.flush_session()

    def update_all(self, hotel_staff_aggregates):
        housekeeper_models = []
        for hotel_staff_aggregate in hotel_staff_aggregates:
            housekeeper_models.extend(self.from_aggregate(hotel_staff_aggregate))
        self._save_all(housekeeper_models)
        self.flush_session()

    def load(self, hotel_id):
        housekeeper_models = self.filter(
            HouseKeeperModel, HouseKeeperModel.hotel_id == hotel_id
        ).all()
        hotel_staff_aggregate = self.to_aggregate(
            hotel_id=hotel_id, housekeeper_models=housekeeper_models
        )
        return hotel_staff_aggregate

    def load_for_update(self, hotel_id):
        housekeeper_models = self.filter(
            HouseKeeperModel,
            HouseKeeperModel.hotel_id == hotel_id,
            for_update=True,
            nowait=False,
        ).all()
        hotel_staff_aggregate = self.to_aggregate(
            hotel_id=hotel_id, housekeeper_models=housekeeper_models
        )
        return hotel_staff_aggregate

    def delete_housekeeper(self, hotel_id, housekeeper_id):
        self.filter(
            HouseKeeperModel,
            HouseKeeperModel.hotel_id == hotel_id,
            HouseKeeperModel.housekeeper_id == housekeeper_id,
        ).delete(synchronize_session=False)
        self.flush_session()
