# coding=utf-8

from prometheus.domain.catalog.aggregates.hotel_aggregate import HotelAggregate
from prometheus.domain.catalog.entities.hotel import Hotel


class HotelFactory:
    @staticmethod
    def create_hotel(hotel_dto):
        hotel_entity = Hotel(
            hotel_id=hotel_dto.hotel_id,
            name=hotel_dto.name,
            area=hotel_dto.area,
            status=hotel_dto.status,
            legal_name=hotel_dto.legal_name,
            legal_address=hotel_dto.legal_address,
            gstin_num=hotel_dto.gstin_num,
            checkin_time=hotel_dto.checkin_time,
            checkout_time=hotel_dto.checkout_time,
            free_late_checkout_time=hotel_dto.free_late_checkout_time,
            switch_over_time=hotel_dto.switch_over_time,
            launched_date=hotel_dto.launched_date,
            state=hotel_dto.state,
            city=hotel_dto.city,
            pincode=hotel_dto.pincode,
            legal_signature=hotel_dto.legal_signature,
            legal_pincode=hotel_dto.legal_pincode,
            legal_city=hotel_dto.legal_city,
            legal_state=hotel_dto.legal_state,
            brands=hotel_dto.brands,
            base_currency=hotel_dto.base_currency,
            timezone=hotel_dto.timezone,
            phone_number=hotel_dto.phone_number,
            country_code=hotel_dto.country_code,
            email=hotel_dto.email,
            logo=hotel_dto.logo,
            current_business_date=hotel_dto.current_business_date,
            system_freeze_time=hotel_dto.system_freeze_time,
            pan_number=hotel_dto.pan_number,
            tan_number=hotel_dto.tan_number,
            tin_number=hotel_dto.tan_number,
            forex_license_no=hotel_dto.forex_license_no,
            msme_number=hotel_dto.msme_number,
            is_test=hotel_dto.is_test,
            cin_number=hotel_dto.cin_number,
        )

        return HotelAggregate(hotel=hotel_entity, room_type_configs=[])
