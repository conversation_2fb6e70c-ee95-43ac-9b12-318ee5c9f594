from prometheus.domain.catalog.aggregates.reseller_gst_aggregate import (
    ResellerGSTAggregate,
)
from prometheus.domain.catalog.entities.reseller_gst import ResellerGST
from ths_common.value_objects import Address, State


class ResellerGSTFactory:
    @staticmethod
    def create_reseller_gst(reseller_gst_detail_dto):
        reseller_gst_entity = ResellerGST(
            id=reseller_gst_detail_dto.id,
            gstin_num=reseller_gst_detail_dto.gstin_number,
            state=State(
                reseller_gst_detail_dto.state_id, reseller_gst_detail_dto.state_name
            ),
            address=', '.join(
                [
                    reseller_gst_detail_dto.address_line_1,
                    reseller_gst_detail_dto.address_line_2,
                    reseller_gst_detail_dto.address_city,
                    reseller_gst_detail_dto.state_name,
                    reseller_gst_detail_dto.address_pincode,
                ]
            ),
            date_of_registration=reseller_gst_detail_dto.date_of_registration,
            reseller_legal_name=reseller_gst_detail_dto.reseller_legal_name,
        )

        return ResellerGSTAggregate(reseller_gst_detail=reseller_gst_entity)
