# coding=utf-8
from prometheus.domain.catalog.aggregates.room_aggregate import RoomAggregate
from prometheus.domain.catalog.entities.room import Room


class RoomFactory:
    @staticmethod
    def create_room(room_dto):
        room_entity = Room(
            room_id=room_dto.room_id,
            hotel_id=room_dto.hotel_id,
            room_type_id=room_dto.room_type_id,
            room_number=room_dto.room_number,
            status=room_dto.status,
        )
        return RoomAggregate(room_entity)
