from prometheus.domain.catalog.aggregates.sku_category_aggregate import (
    SkuCategoryAggregate,
)
from prometheus.domain.catalog.entities.sku_category import SkuCategory
from ths_common.constants.catalog_constants import SkuCategoryStatus


class SkuCategoryFactory:
    @staticmethod
    def create_sku_category(sku_category_dto):
        sku_category = SkuCategory(
            sku_category_id=sku_category_dto.sku_category_id,
            item_code=sku_category_dto.item_code,
            name=sku_category_dto.name,
            status=SkuCategoryStatus(sku_category_dto.status),
            has_slab_based_taxation=sku_category_dto.has_slab_based_taxation,
        )

        return SkuCategoryAggregate(sku_category)
