from treebo_commons.money.constants import CurrencyType

from ths_common.constants.catalog_constants import HotelStatus
from ths_common.utils.common_utils import extract_state_code_from_gstin
from ths_common.value_objects import Address, GSTDetails


class Hotel(object):
    def __init__(
        self,
        hotel_id,
        name,
        area,
        status: HotelStatus,
        legal_name,
        checkin_time,
        checkout_time,
        free_late_checkout_time,
        legal_address,
        gstin_num,
        checkin_grace_time=None,
        checkout_grace_time=None,
        switch_over_time=None,
        deleted=False,
        launched_date=None,
        state=None,
        city=None,
        pincode=None,
        country=None,
        legal_signature=None,
        legal_city=None,
        legal_state=None,
        legal_pincode=None,
        housekeeping_enabled=False,
        brands=None,
        has_lut=False,
        base_currency: CurrencyType = CurrencyType.INR,
        timezone=None,
        phone_number=None,
        country_code=None,
        email=None,
        logo=None,
        current_business_date=None,
        system_freeze_time=None,
        pan_number=None,
        tan_number=None,
        tin_number=None,
        forex_license_no=None,
        msme_number=None,
        is_test=False,
        cin_number=None,
    ):
        self.hotel_id = hotel_id
        self.name = name
        self.area = area
        self.status = status
        self.legal_name = legal_name
        self.legal_address = legal_address
        self.gstin_num = gstin_num
        # checkin_grace_time & checkout_grace_time used in critical_task_service
        self.checkin_grace_time = checkin_grace_time
        self.checkout_grace_time = checkout_grace_time
        self.switch_over_time = switch_over_time
        self.checkin_time = checkin_time
        self.checkout_time = checkout_time
        self.free_late_checkout_time = free_late_checkout_time
        self.deleted = deleted
        self.launched_date = launched_date
        self.state = state
        self.city = city
        self.pincode = pincode
        self.country = country
        self.legal_signature = legal_signature
        self.legal_city = legal_city
        self.legal_state = legal_state
        self.legal_pincode = legal_pincode
        self.housekeeping_enabled = housekeeping_enabled
        self.brands = brands
        self.has_lut = has_lut
        self.base_currency = base_currency
        self.timezone = timezone
        self.phone_number = phone_number
        self.country_code = country_code
        self.email = email
        self.logo = logo
        self.current_business_date = current_business_date
        self.system_freeze_time = system_freeze_time
        self.pan_number = pan_number
        self.tan_number = tan_number
        self.tin_number = tin_number
        self.forex_license_no = forex_license_no
        self.msme_number = msme_number
        self.is_test = is_test
        self.cin_number = cin_number

    @property
    def gst_details(self):
        return GSTDetails(
            legal_name=self.legal_name,
            gstin_num=self.gstin_num,
            address=self.gst_address,
            has_lut=self.has_lut,
        )

    @property
    def address(self):
        return Address(
            field_1=self.area,
            field_2=None,
            city=self.city.name,
            state=self.state.name,
            country=self.country,
            pincode=self.pincode,
        )

    @property
    def gst_address(self):
        return Address(
            field_1=self.legal_address,
            field_2=None,
            city=self.legal_city.name if self.legal_city else None,
            state=self.legal_state.name if self.legal_state else None,
            country=self.country,
            pincode=self.legal_pincode,
        )

    @property
    def is_active(self):
        return self.status == HotelStatus.ACTIVE

    @property
    def state_code(self):
        if self.gstin_num:
            return extract_state_code_from_gstin(self.gstin_num)
        else:
            return None

    @property
    def legal_state_id(self):
        return self.legal_state.id if self.legal_state else self.state.id

    @property
    def state_id(self):
        return self.state.id
