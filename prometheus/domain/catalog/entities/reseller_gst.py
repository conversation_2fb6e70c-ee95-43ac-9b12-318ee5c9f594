from ths_common.value_objects import Address, GSTDetails, State


class ResellerGST(object):
    def __init__(
        self,
        id,
        gstin_num,
        state: State,
        address,
        date_of_registration,
        reseller_legal_name="Treebo Hospitality Ventures Private Limited (formerly known as Ruptub Solutions Private Limited)",
    ):
        self.reseller_gst_id = id
        self.state = state
        self.gstin_num = gstin_num
        self.date_of_registration = date_of_registration
        self.address = address
        self.reseller_legal_name = reseller_legal_name

    @property
    def gst_detail(self):
        return GSTDetails(
            legal_name=self.reseller_legal_name,
            gstin_num=self.gstin_num,
            address=Address(
                field_1=self.address,
                field_2="",
                city="",
                state=self.state.name,
                country=None,
                pincode=None,
            ),
        )
