from ths_common.base_entity import EntityChangeTracker
from ths_common.constants.inventory_constants import (
    HouseKeepingStatus,
    RoomCurrentStatus,
)
from ths_common.value_objects import Occupancy


class HKOccupancyAudit(EntityChangeTracker):
    def __init__(
        self,
        housekeeping_occupancy_audit_id,
        hotel_id,
        room_id,
        hk_occupancy_count,
        fd_occupancy: Occupancy,
        hk_room_status: HouseKeepingStatus,
        fd_room_status: RoomCurrentStatus,
        housekeeper_id=None,
        hk_comments=None,
        applicable_for=None,
        dirty=True,
        new=True,
    ):
        super().__init__(dirty=dirty, new=new)
        self.housekeeping_occupancy_audit_id = housekeeping_occupancy_audit_id
        self.hotel_id = hotel_id
        self.room_id = room_id
        self.hk_occupancy_count = hk_occupancy_count
        self.fd_occupancy = fd_occupancy
        self.hk_room_status = hk_room_status
        self.fd_room_status = fd_room_status
        self.housekeeper_id = housekeeper_id
        self.hk_comments = hk_comments
        self.applicable_for = applicable_for
