from ths_common.base_entity import EntityChangeTracker
from ths_common.constants.inventory_constants import AllottedFor


class RoomAllotment(EntityChangeTracker):
    __slots__ = (
        'allotment_id',
        'start_time',
        'actual_start_time',
        'expected_end_time',
        'actual_end_time',
        'allotted_for',
        'deleted',
    )

    def __init__(
        self,
        allotment_id,
        start_time,
        expected_end_time,
        allotted_for,
        actual_start_time=None,
        actual_end_time=None,
        deleted=False,
        dirty=True,
        new=True,
    ):
        super().__init__(dirty=dirty, new=new)
        self.allotment_id = allotment_id
        self.start_time = start_time
        self.actual_start_time = actual_start_time
        self.expected_end_time = expected_end_time
        self.actual_end_time = actual_end_time
        self.allotted_for = allotted_for
        self.deleted = deleted

    def delete(self):
        self.deleted = True
        self.mark_dirty()

    def update_actual_end_time(self, actual_end_time):
        self.actual_end_time = actual_end_time
        self.mark_dirty()

    def update_expected_end_time(self, expected_end_time):
        self.expected_end_time = expected_end_time
        self.mark_dirty()

    def update_start_time(self, start_time):
        self.start_time = start_time
        self.mark_dirty()

    def update_actual_start_time(self, actual_start_time):
        self.actual_start_time = actual_start_time
        self.mark_dirty()

    def update_allotted_for(self, allotted_for):
        self.allotted_for = allotted_for
        self.mark_dirty()

    @property
    def end_time(self):
        return self.actual_end_time if self.actual_end_time else self.expected_end_time

    def is_allotted_for_stay(self):
        return self.allotted_for == AllottedFor.STAY
