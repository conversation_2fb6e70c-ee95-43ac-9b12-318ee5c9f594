class InventoryAuditTrail(object):
    def __init__(
        self,
        audit_id,
        hotel_id,
        room_type_id,
        date,
        available_count,
        integration_event_id,
        user_type,
        application,
        user_action,
        booking_id,
        application_trace=None,
    ):
        self.audit_id = audit_id
        self.hotel_id = hotel_id
        self.room_type_id = room_type_id
        self.date = date
        self.available_count = available_count
        self.integration_event_id = integration_event_id
        self.user_type = user_type
        self.application = application
        self.user_action = user_action
        self.booking_id = booking_id
        self.application_trace = application_trace
