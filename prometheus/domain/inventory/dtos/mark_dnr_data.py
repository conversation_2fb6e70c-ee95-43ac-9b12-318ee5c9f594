from ths_common.constants.inventory_constants import DNRSource
from ths_common.value_objects import DNRSubType, DNRType


class MarkDnrData(object):
    def __init__(
        self,
        room_id,
        from_date,
        to_date,
        source: DNRSource,
        type: DNRType,
        subtype: DNRSubType,
        assigned_by,
        comments,
    ):
        self.room_id = room_id
        self.from_date = from_date
        self.to_date = to_date
        self.source = source
        self.type = type
        self.subtype = subtype
        self.assigned_by = assigned_by
        self.comments = comments

    def json(self):
        return dict(
            room_id=self.room_id,
            from_date=self.from_date,
            to_date=self.to_date,
            source=self.source,
            type=self.type,
            subtype=self.subtype,
            assigned_by=self.assigned_by,
            comments=self.comments,
        )
