from treebo_commons.utils.dateutils import ymd_str_to_date


class HKOccupancy:
    __slots__ = ('count', 'applicable_for', 'remarks')

    def __init__(self, count, applicable_for, remarks):
        self.count = count
        self.applicable_for = applicable_for
        self.remarks = remarks

    def __eq__(self, other):
        if isinstance(self, other.__class__):
            return (
                self.count == other.count
                and self.applicable_for == other.applicable_for
            )
        return False

    def to_json(self):
        return {
            "count": self.count,
            "applicable_for": str(self.applicable_for),
            "remarks": self.remarks,
        }

    @staticmethod
    def from_json(json):
        return HKOccupancy(
            count=json.get('count'),
            applicable_for=ymd_str_to_date(json.get('applicable_for')),
            remarks=json.get('remarks'),
        )
