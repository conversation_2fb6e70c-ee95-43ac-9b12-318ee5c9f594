# coding=utf-8
"""
cashier session factory
"""
from prometheus.domain.billing.aggregates.cashier_session_aggregate import (
    CashierSessionAggregate,
)
from prometheus.domain.billing.entities.cashier_session import CashierSession
from ths_common.utils.id_generator_utils import random_id_generator
from ths_common.value_objects import CashCounterAmount


class CashierSessionFactory:
    @classmethod
    def create_new_cashier_session(
        cls,
        vendor_id,
        opening_balance: CashCounterAmount,
        status,
        opening_balance_in_base_currency,
        opened_by,
        closed_by,
        cash_register_id,
        session_number,
        session_start_datetime,
    ):
        cashier_session_id = random_id_generator()
        cashier_session = CashierSession(
            cashier_session_id=cashier_session_id,
            session_number=session_number,
            start_datetime=session_start_datetime,
            end_datetime=None,
            status=status,
            opening_balance_in_base_currency=opening_balance_in_base_currency,
            opened_by=opened_by,
            closed_by=closed_by,
            vendor_id=vendor_id,
            cash_register_id=cash_register_id,
        )
        cashier_session_aggregate = CashierSessionAggregate(
            cashier_session=cashier_session,
            cash_counter_payments=None,
            opening_balance=[],
            closing_balance=[],
        )
        cashier_session_aggregate.add_opening_balance(opening_balance)
        cashier_session_aggregate.check_invariance()
        return cashier_session_aggregate
