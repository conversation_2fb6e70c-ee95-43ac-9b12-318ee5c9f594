import logging
from decimal import ROUND_HALF_EVEN, Decimal

from treebo_commons.money import Money

from object_registry import register_instance
from prometheus import crs_context
from prometheus.application.helpers.gst_details_helper import (
    get_gst_details_from_billed_entity,
)
from prometheus.application.hotel_settings.tenant_settings import TenantSettings
from prometheus.core.globals import HotelContext
from prometheus.domain.billing.dto.allowance_data import AllowanceData
from prometheus.domain.billing.dto.taxable_item import TaxableItem
from prometheus.domain.billing.services import TaxService
from ths_common.value_objects import TaxDetail

logger = logging.getLogger(__name__)


@register_instance(dependencies=[TenantSettings, TaxService])
class AddAllowanceService:
    def __init__(self, tenant_settings: TenantSettings, tax_service: TaxService):
        self.tenant_settings = tenant_settings
        self.tax_service = tax_service

    def add_allowances_for_credit_note(
        self, credit_note_aggregate, booking_aggregate, bill_aggregate, grouped_invoices
    ):
        for line_item in credit_note_aggregate.credit_note_line_items:
            invoice_charge = grouped_invoices.get(
                line_item.invoice_id
            ).get_invoice_charge(line_item.invoice_charge_id)
            charge = bill_aggregate.get_charge(invoice_charge.charge_id)
            charge_split = charge.get_split(
                invoice_charge.charge_split_ids[0]
            )  # Always one now
            allowance_data = AllowanceData(
                pretax_amount=charge_split.get_pretax_amount_post_allowance(),
                remarks='Nullifying charge split for credit note %s'
                % credit_note_aggregate.credit_note_id,
                billed_entity_account=credit_note_aggregate.credit_note.billed_entity_account,
                consume_at_creation=True,
            )
            allowance = self.add_allowance_to_charge_split(
                bill_aggregate,
                charge,
                charge_split.charge_split_id,
                allowance_data,
                booking_aggregate,
            )
            if allowance:
                allowance.update_credit_note_id(credit_note_aggregate.credit_note_id)
            line_item.charge_id = charge.charge_id

    def add_allowance_to_charge_split(
        self,
        bill_aggregate,
        charge,
        charge_split_id,
        allowance_dto,
        booking_aggregate,
    ):
        if not allowance_dto.pretax_amount and not allowance_dto.posttax_amount:
            return
        hotel_context: HotelContext = crs_context.get_hotel_context()
        charge_split = charge.get_split(charge_split_id)
        pretax_amount, posttax_amount = None, None
        if allowance_dto.pretax_amount:
            if not allowance_dto.pretax_amount.currency:
                allowance_dto.pretax_amount = Money(
                    allowance_dto.pretax_amount.amount, bill_aggregate.base_currency
                )
            pretax_amount = (
                charge_split.get_pretax_amount_post_allowance()
                - allowance_dto.pretax_amount
            )
        if allowance_dto.posttax_amount:
            if not allowance_dto.posttax_amount.currency:
                allowance_dto.posttax_amount = Money(
                    allowance_dto.posttax_amount.amount, bill_aggregate.base_currency
                )
            posttax_amount = (
                charge_split.get_posttax_amount_post_allowance()
                - allowance_dto.posttax_amount
            )
        sku_category_id = (
            'stay'
            if self.tenant_settings.club_inclusion_with_room_rate_for_taxation(
                hotel_context.hotel_id
            )
            and charge.is_inclusion_charge
            else charge.item.sku_category_id
        )
        billed_entity = bill_aggregate.get_primary_billed_entity_of_charge(
            charge.charge_id
        )

        taxable_items = self.tax_service.calculate_taxes(
            [
                TaxableItem(
                    charge_id=charge.charge_id,
                    pretax_amount=pretax_amount,
                    posttax_amount=posttax_amount,
                    applicable_date=charge.applicable_date,
                    sku_category_id=sku_category_id,
                )
            ],
            buyer_gst_details=get_gst_details_from_billed_entity(
                booking_aggregate, billed_entity
            ),
            seller_has_lut=self.tax_service.seller_has_lut(
                booking_aggregate.booking.seller_model, hotel_context
            ),
            hotel_id=bill_aggregate.vendor_id,
        )

        final_tax_amount = taxable_items[0].tax_amount
        allowance_total_tax = (
            charge_split.get_tax_amount_post_allowance() - final_tax_amount
        )
        allowance_dto.update_tax_amount(allowance_total_tax)

        charge_split_tax_details_grouped_by_tax_type = {
            td.tax_type: td for td in charge_split.get_tax_details_post_allowance()
        }

        total_tax_from_breakup = Decimal('0')
        allowance_tax_details = []
        for tax_detail in taxable_items[0].tax_details:
            try:
                charge_split_current_tax_detail = (
                    charge_split_tax_details_grouped_by_tax_type.get(
                        tax_detail.tax_type
                    )
                )

                tax_type_tax_amount = (
                    charge_split_current_tax_detail.tax_amount - tax_detail.tax_amount
                )
                if tax_type_tax_amount.amount == Decimal("0"):
                    tax_type_tax_percentage = Decimal("0")
                else:
                    tax_type_tax_percentage = (
                        tax_type_tax_amount.amount
                        / allowance_dto.pretax_amount.amount
                        * 100
                    )
                    tax_type_tax_percentage = tax_type_tax_percentage.quantize(
                        Decimal('.01'), rounding=ROUND_HALF_EVEN
                    )

                total_tax_from_breakup += tax_type_tax_amount.amount
                allowance_tax_details.append(
                    TaxDetail(
                        tax_detail.tax_type,
                        percentage=tax_type_tax_percentage,
                        amount=tax_type_tax_amount,
                    )
                )
            except Exception:
                print("ss")

        if total_tax_from_breakup != allowance_total_tax.amount:
            logger.warning(
                "Tax Breakup total sum: %s doesn't match allowance total tax: %s, for bill_id: %s and "
                "charge_id: %s, charge_split_id: %s. Final tax amount of charge after allowance: %s",
                total_tax_from_breakup,
                allowance_total_tax,
                bill_aggregate.bill_id,
                charge.charge_id,
                charge_split_id,
                final_tax_amount,
            )

        allowance_dto.update_tax_details(allowance_tax_details)
        allowance = bill_aggregate.add_allowance(
            charge.charge_id,
            charge_split.charge_split_id,
            allowance_dto,
            posting_date=hotel_context.current_date(),
            post_allowance_tax_details=taxable_items[0].tax_details,
        )
        return allowance
