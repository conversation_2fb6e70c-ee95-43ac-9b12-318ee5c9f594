import copy
import logging
from collections import defaultdict, namedtuple
from datetime import timed<PERSON><PERSON>
from decimal import Decimal
from typing import List

import sentry_sdk
from dateutil import parser
from treebo_commons.money import Money
from treebo_commons.utils import dateutils
from treebo_commons.utils.dateutils import local_timezone

from object_registry import register_instance
from prometheus import crs_context
from prometheus.application.hotel_settings.tenant_settings import TenantSettings
from prometheus.common.helpers.invoice_generation_helper import InvoiceGenerationHelper
from prometheus.domain.billing.dto.invoice_url_data import InvoiceUrlData
from prometheus.domain.billing.services.invoice_summary_template_service import (
    InvoiceSummaryTemplateService,
)
from prometheus.domain.booking.entities.room_stay import RoomStay
from prometheus.infrastructure.external_clients.aws_service_client import (
    AwsServiceClient,
)
from prometheus.infrastructure.external_clients.template_service_client import (
    TemplateFormat,
    TemplateNameSpace,
    TemplateService,
)
from schema_instances import get_schema_obj
from ths_common.constants.billing_constants import (
    ChargeStatus,
    ChargeTypes,
    PaymentStatus,
    PaymentTypes,
)
from ths_common.constants.tenant_settings_constants import TenantSettingName
from ths_common.utils.collectionutils import flatten_list
from ths_common.value_objects import GSTDetails, InvoiceIssuedByInfo, TaxDetail

logger = logging.getLogger(__name__)


@register_instance(
    dependencies=[
        AwsServiceClient,
        TenantSettings,
        InvoiceSummaryTemplateService,
    ]
)
class InvoiceTemplateService(object):
    RoomChargeKey = namedtuple('RoomChargeKey', ['room_number', 'charge_tos'])
    DefaultSignedUrlExpirationHours = 2
    DefaultSignedUrlExpirationSeconds = DefaultSignedUrlExpirationHours * 3600
    _INVOICE_TEMPLATE_GENERATION_CHARGES_MAX_THRESHOLD = 750

    def __init__(
        self,
        aws_service_client,
        tenant_settings,
        invoice_summary_template_service: InvoiceSummaryTemplateService,
    ):
        self.aws_service_client = aws_service_client
        self.tenant_settings = tenant_settings
        self.invoice_summary_template_service = invoice_summary_template_service

    def generate_invoice_templates(
        self,
        occupancy,
        bill_aggregate,
        booking_aggregate,
        invoice_aggregates,
        sku_category_map,
        rate_plan_mapping,
        include_bank_details=False,
        should_upload=False,
        hotel_aggregate=None,
        booking_meta=None,
        is_proforma_invoice=False,
        charge_room_stay_info_map=None,
        company_details=None,
        travel_agent_details=None,
        booked_allowances_invoiced=None,
    ):
        templates, company_or_ta_details = [], None
        invoice_url_map = dict()
        invoice_summary_enabled = self.tenant_settings.get_setting_value(
            TenantSettingName.INVOICE_SUMMARY_ENABLED.value
        )

        for invoice_aggregate in invoice_aggregates:
            if (
                invoice_summary_enabled
                and len(invoice_aggregate.invoice_charges)
                >= self._INVOICE_TEMPLATE_GENERATION_CHARGES_MAX_THRESHOLD
            ):
                (
                    invoice_template,
                    invoice_url_data,
                ) = self.invoice_summary_template_service.generate_invoice_templates(
                    bill_aggregate,
                    booking_aggregate,
                    invoice_aggregate,
                    include_bank_details=include_bank_details,
                    should_upload=should_upload,
                    hotel_aggregate=hotel_aggregate,
                    booking_meta=booking_meta,
                    is_proforma_invoice=is_proforma_invoice,
                )
            else:
                if company_details or travel_agent_details:
                    billed_entity = bill_aggregate.get_billed_entity(
                        invoice_aggregate.invoice.billed_entity_account.billed_entity_id
                    )
                    if (
                        travel_agent_details
                        and billed_entity.is_billed_entity_travel_agent()
                    ):
                        company_or_ta_details = (
                            self._create_issued_by_from_company_details(
                                travel_agent_details
                            )
                        )
                    elif company_details:
                        company_or_ta_details = (
                            self._create_issued_by_from_company_details(company_details)
                        )

                if is_proforma_invoice:
                    booked_allowances_invoiced = (
                        self._derive_allowances_to_invoiced_for_proforma_invoice(
                            bill_aggregate, invoice_aggregate
                        )
                    )

                invoice_template, invoice_url_data = self._generate_invoice_template(
                    occupancy,
                    bill_aggregate,
                    booking_aggregate,
                    invoice_aggregate,
                    sku_category_map,
                    rate_plan_mapping,
                    include_bank_details=include_bank_details,
                    should_upload=should_upload,
                    hotel_aggregate=hotel_aggregate,
                    booking_meta=booking_meta,
                    is_proforma_invoice=is_proforma_invoice,
                    charge_room_stay_info_map=charge_room_stay_info_map,
                    company_or_ta_details=company_or_ta_details,
                    booked_allowances_invoiced=booked_allowances_invoiced,
                )

            templates.append(invoice_template)
            invoice_url_map[invoice_aggregate.invoice.invoice_id] = invoice_url_data

        return templates, invoice_url_map

    def _generate_invoice_template(
        self,
        occupancy,
        bill_aggregate,
        booking_aggregate,
        invoice_aggregate,
        sku_category_map,
        rate_plan_mapping,
        include_bank_details=False,
        should_upload=False,
        hotel_aggregate=None,
        booking_meta=None,
        is_proforma_invoice=False,
        charge_room_stay_info_map=None,
        company_or_ta_details=None,
        booked_allowances_invoiced=None,
    ):
        charge_items_by_room_charge_key = self._get_charge_details_for_template(
            invoice_aggregate,
            bill_aggregate,
            sku_category_map,
            rate_plan_mapping,
            charge_room_stay_info_map=charge_room_stay_info_map,
            booked_allowances_invoiced=booked_allowances_invoiced,
        )
        room_charge_keys = charge_items_by_room_charge_key.keys()
        charge_items = charge_items_by_room_charge_key.values()
        billed_entity_category = self._derive_billed_entity_category(
            bill_aggregate, invoice_aggregate
        )
        average_room_stay_price = bill_aggregate.average_posttax_room_stay_amount(
            charge_ids=[inc.charge_id for inc in invoice_aggregate.invoice_charges]
        )

        # generate template
        invoice_template = dict(
            vendor_context=dict(
                time_zone=str(local_timezone()),
                currency=bill_aggregate.bill.base_currency.value,
            ),
            invoice=invoice_aggregate,
            booking=invoice_aggregate.invoice.parent_info,
            charge_items=charge_items,
            bill_summary=bill_aggregate,
            folio_number=self._folio_number(invoice_aggregate, bill_aggregate),
            booking_meta=booking_meta,
            payments=self._get_payment_details_for_template(
                bill_aggregate, invoice_aggregate
            ),
            bank_details=invoice_aggregate.invoice.issued_by.bank_details
            if include_bank_details
            else None,
            category_wise_cummulative_taxes=self._get_cummulative_tax_for_template(
                invoice_aggregate, bill_aggregate
            ),
            vendor_meta=dict(
                vendor_logo=hotel_aggregate.hotel.logo if hotel_aggregate else None,
                vendor_pan_number=hotel_aggregate.hotel.pan_number
                if hotel_aggregate
                else None,
                vendor_tan_number=hotel_aggregate.hotel.tan_number
                if hotel_aggregate
                else None,
                vendor_tin_number=hotel_aggregate.hotel.tin_number
                if hotel_aggregate
                else None,
                vendor_msme_number=hotel_aggregate.hotel.msme_number
                if hotel_aggregate
                else None,
                vendor_cin_number=hotel_aggregate.hotel.cin_number
                if hotel_aggregate
                else None,
            ),
            printed_by=crs_context.user_data.user
            if crs_context.user_data and crs_context.user_data.user
            else invoice_aggregate.invoice.generated_by,
            printed_on=dateutils.current_datetime(local_timezone()),
            adult_count=occupancy.adult,
            child_count=occupancy.child,
            billed_entity_category=billed_entity_category,
            average_room_stay_price=average_room_stay_price,
        )

        self.convert_actual_checkin_checkout_date_in_isoformat(invoice_template)
        self._populate_check_in_check_out_dates(
            booking_aggregate, invoice_template, room_charge_keys
        )

        if company_or_ta_details:
            invoice_template['company_or_ta_details'] = company_or_ta_details

        # upload template
        invoice_url = None
        if should_upload:
            namespace = (
                self._get_invoice_template_namespace(
                    invoice_aggregate, crs_context.get_hotel_context()
                )
                if not is_proforma_invoice
                else self._get_proforma_invoice_template_namespace(invoice_aggregate)
            )
            from prometheus.common.serializers import HotelBookingInvoiceTemplateSchema

            template_json = (
                get_schema_obj(HotelBookingInvoiceTemplateSchema)
                .dump(invoice_template)
                .data
            )
            invoice_url = TemplateService().generate(
                namespace, template_json, TemplateFormat.PDF
            )

        url_for_signed_url_gen = (
            invoice_url
            if invoice_url is not None
            else invoice_aggregate.invoice.invoice_url
        )
        signed_url, expiration = self._generate_signed_url(
            invoice_aggregate, url_for_signed_url_gen
        )

        return invoice_template, (
            InvoiceUrlData(invoice_url, signed_url, expiration),
            invoice_aggregate.invoice.version,
        )

    @staticmethod
    def _populate_check_in_check_out_dates(
        booking_aggregate, invoice_template, room_charge_keys
    ):
        room_numbers = [rs.room_number for rs in room_charge_keys if rs.room_number]
        room_stay_ids = booking_aggregate.get_all_room_stay_ids_from_room_number(
            room_numbers
        )
        InvoiceGenerationHelper.populate_check_in_check_out_dates_in_invoice_template(
            booking_aggregate,
            invoice_template,
            room_stay_ids,
        )

    @staticmethod
    def convert_actual_checkin_checkout_date_in_isoformat(invoice_template):
        try:
            if invoice_template["booking"].get("actual_checkin_date"):
                invoice_template["booking"][
                    "actual_checkin_date"
                ] = dateutils.isoformat_datetime(
                    parser.isoparse(invoice_template["booking"]["actual_checkin_date"])
                )
            if invoice_template["booking"].get("actual_checkout_date"):
                invoice_template["booking"][
                    "actual_checkout_date"
                ] = dateutils.isoformat_datetime(
                    parser.isoparse(invoice_template["booking"]["actual_checkout_date"])
                )
        except Exception as e:
            logger.exception(e)
            sentry_sdk.capture_exception(e)

    def _get_cummulative_tax_for_template(self, invoice_aggregate, bill_aggregate):
        sku_category_to_inv_charge_mapping = defaultdict(list)
        for inv_charge in invoice_aggregate.invoice_charges:
            sku_category_to_inv_charge_mapping[
                inv_charge.charge_item.sku_category_id
            ].append(inv_charge)

        cummulative_taxes = []
        for sku_category_id, inv_charges in sku_category_to_inv_charge_mapping.items():
            inv_charge_ids = [inv_charge.charge_id for inv_charge in inv_charges]
            invoice_charges = bill_aggregate.filter_charges_by_ids(inv_charge_ids)
            tax_type_to_tax_details_mapping = defaultdict(list)
            for inv_charge in invoice_charges:
                for tax_detail in inv_charge.tax_details:
                    tax_type_to_tax_details_mapping[tax_detail.tax_type].append(
                        tax_detail
                    )
            for tax_type, tax_details in tax_type_to_tax_details_mapping.items():
                tax_amount = sum([tax_detail.amount for tax_detail in tax_details])
                cummulative_taxes.append(
                    dict(
                        category=sku_category_id,
                        tax_amount=tax_amount,
                        tax_type=tax_type,
                    )
                )

        return cummulative_taxes

    def _get_charge_details_for_template(
        self,
        invoice_aggregate,
        bill_aggregate,
        sku_category_map,
        rate_plan_mapping,
        charge_room_stay_info_map=None,
        booked_allowances_invoiced=None,
    ):
        template_charges = self._build_invoice_template_line_items(
            bill_aggregate,
            invoice_aggregate,
            sku_category_map,
            rate_plan_mapping,
            booked_allowances_invoiced=booked_allowances_invoiced,
        )

        # Grouping of Template Charge Line Item, as per room_number and guest names
        charge_entries_map = dict()
        for index, template_charge in enumerate(template_charges):
            charge_to_key = str(sorted(template_charge.get('charge_to_ids')))
            room_no_key = template_charge.get('charge_item_details').get(
                'room_no', 'dummy_{}'.format(index)
            )
            key = InvoiceTemplateService.RoomChargeKey(room_no_key, charge_to_key)

            charge_item = charge_entries_map.get(key)

            if not charge_item:
                room_info = template_charge.get('charge_item_details')
                if charge_room_stay_info_map and charge_room_stay_info_map.get(
                    template_charge.get('charge_id')
                ):
                    room_info.update(
                        charge_room_stay_info_map.get(template_charge.get('charge_id'))
                    )

                charge_item = dict(
                    room_info=room_info,
                    guests=invoice_aggregate.invoice.get_users(
                        template_charge.get('charge_to_ids')
                    ),
                    charges=[],
                )

            charge_item['charges'].append(template_charge)
            charge_entries_map[key] = charge_item

        return charge_entries_map

    def _build_invoice_template_line_items(
        self,
        bill_aggregate,
        invoice_aggregate,
        sku_category_map,
        rate_plan_mapping,
        booked_allowances_invoiced=None,
    ):
        show_allowance_as_separate_line_item = (
            self.tenant_settings.show_allowance_as_separate_line_item()
        )

        pos_charges = [
            inv_charge
            for inv_charge in invoice_aggregate.invoice_charges
            if inv_charge.charge_item.is_pos_charge()
        ]

        # Group POS charges to 1 line item per (order_id, hsn_code) tuple
        grouped_pos_charges = defaultdict(list)
        for pos_charge in pos_charges:
            # TODO: Fix this properly
            unique_id = (
                pos_charge.charge_item.pos_order_id()
                or pos_charge.charge_item.pos_order_number()
            )
            hsn_code = self._get_hsn_sac_code(pos_charge, sku_category_map)
            sku_category_id = pos_charge.charge_item.sku_category_id
            pos_charge_group_key = (unique_id, hsn_code, sku_category_id)
            grouped_pos_charges[pos_charge_group_key].append(pos_charge)

        non_pos_charges = [
            inv_charge
            for inv_charge in invoice_aggregate.invoice_charges
            if not inv_charge.charge_item.is_pos_charge()
        ]

        addon_charge_ids = []
        base_currency = bill_aggregate.bill.base_currency
        for charge in bill_aggregate.charges:
            if charge.addon_charge_ids:
                addon_charge_ids.extend(charge.addon_charge_ids)

        # Create template charge line item for non_pos_charge and pos_charge
        template_charges = []
        for invoice_charge in non_pos_charges:
            if invoice_charge.charge_id in addon_charge_ids:
                continue

            non_room_rent_charge_components = []
            charge = bill_aggregate.get_charge(invoice_charge.charge_id)
            pretax_amount, posttax_amount, tax_amount = 0, 0, 0
            allowance_pretax_amount, allowance_posttax_amount, allowance_tax_amount = (
                Money(currency=base_currency),
                Money(currency=base_currency),
                Money(currency=base_currency),
            )
            (
                tax_details,
                allowance_tax_details,
                tax_details_post_allowance,
                allowances,
                charge_allowances,
            ) = ([], [], [], [], [])
            for split_id in invoice_charge.charge_split_ids:
                charge_split = charge.get_split(split_id)
                pretax_amount += charge_split.pre_tax
                posttax_amount += charge_split.post_tax
                tax_amount += charge_split.tax
                tax_details = self.concatenate_tax_details(
                    tax_details, charge_split.tax_details
                )
                if charge_split.is_invoiced:
                    charge_allowances.extend(
                        charge_split.get_allowances_for_invoice_id(
                            invoice_aggregate.invoice.invoice_id
                        )
                    )
                else:
                    for allowance in charge_split.active_allowances():
                        if allowance.status == ChargeStatus.CONSUMED:
                            charge_allowances.append(allowance)
                        else:
                            if (
                                booked_allowances_invoiced
                                and dict(
                                    charge_id=invoice_charge.charge_id,
                                    charge_split_id=split_id,
                                    allowance_id=allowance.allowance_id,
                                )
                                in booked_allowances_invoiced
                            ):
                                charge_allowances.append(allowance)
                allowances.extend(charge_allowances)

                for allowance in charge_allowances:
                    allowance_pretax_amount += allowance.pretax_amount
                    allowance_posttax_amount += allowance.posttax_amount
                    allowance_tax_amount += allowance.tax_amount
                    allowance_tax_details = self.concatenate_tax_details(
                        allowance_tax_details, allowance.tax_details
                    )
            tax_details_post_allowance = self.concatenate_tax_details(
                tax_details,
                [tax_detail.negate() for tax_detail in allowance_tax_details],
            )

            tax_details_post_allowance = self.rectify_tax_details_percentage(
                tax_details_post_allowance,
                pretax_amount,
                allowance_pretax_amount,
            )

            if charge.addon_charge_ids:
                addon_charges_dict = defaultdict(list)
                for addon_charge in bill_aggregate.filter_charges_by_ids(
                    charge.addon_charge_ids
                ):
                    for cs in addon_charge.charge_splits:
                        if (
                            cs.billed_entity_account
                            == invoice_aggregate.invoice.billed_entity_account
                            and addon_charge.is_active
                        ):
                            addon_charges_dict.setdefault(
                                addon_charge.charge_id, []
                            ).append(cs)

                addon_charges = flatten_list(list(addon_charges_dict.values()))
                addon_total_posttax_amount = sum(
                    [charge.post_tax for charge in addon_charges]
                )
                addon_total_pretax_amount = sum(
                    [charge.pre_tax for charge in addon_charges]
                )
                posttax_amount = posttax_amount + addon_total_posttax_amount
                pretax_amount = pretax_amount + addon_total_pretax_amount
                addon_allowances = []
                for addon_charge_id in addon_charges_dict.keys():
                    for split in addon_charges_dict.get(addon_charge_id):
                        tax_details = self.concatenate_tax_details(
                            tax_details, split.tax_details
                        )
                        tax_amount += split.tax
                        if split.is_invoiced:
                            addon_allowances.extend(
                                split.get_allowances_for_invoice_id(
                                    invoice_aggregate.invoice.invoice_id
                                )
                            )
                        else:
                            for allowance in split.active_allowances():
                                if allowance.status == ChargeStatus.CONSUMED:
                                    addon_allowances.append(allowance)
                                else:
                                    if (
                                        booked_allowances_invoiced
                                        and dict(
                                            charge_id=addon_charge_id,
                                            charge_split_id=split.charge_split_id,
                                            allowance_id=allowance.allowance_id,
                                        )
                                        in booked_allowances_invoiced
                                    ):
                                        addon_allowances.append(allowance)
                allowances.extend(addon_allowances)

                for allowance in addon_allowances:
                    allowance_pretax_amount += allowance.pretax_amount
                    allowance_posttax_amount += allowance.posttax_amount
                    allowance_tax_amount += allowance.tax_amount
                    allowance_tax_details = self.concatenate_tax_details(
                        allowance_tax_details, allowance.tax_details
                    )
                tax_details_post_allowance = self.concatenate_tax_details(
                    tax_details,
                    [tax_detail.negate() for tax_detail in allowance_tax_details],
                )

                tax_details_post_allowance = self.rectify_tax_details_percentage(
                    tax_details_post_allowance,
                    pretax_amount,
                    allowance_pretax_amount,
                )

            if charge.charge_components:
                non_room_rent_charge_components = list(
                    {
                        component.name
                        for component in charge.charge_components
                        if component.name != RoomStay.CHARGE_COMPONENT_NAME
                    }
                )

            if show_allowance_as_separate_line_item:
                for allowance in allowances:
                    allowance_dict = self._build_charge_dict_for_template(
                        invoice_charge,
                        non_room_rent_charge_components,
                        sku_category_map,
                        -allowance.pretax_amount,
                        -allowance.posttax_amount,
                        [tax_detail.negate() for tax_detail in allowance.tax_details],
                        rate_plan_mapping,
                        charge.posting_date,
                    )

                    template_charges.append(allowance_dict)

                non_pos_template_charge_dict = self._build_charge_dict_for_template(
                    invoice_charge,
                    non_room_rent_charge_components,
                    sku_category_map,
                    pretax_amount,
                    posttax_amount,
                    tax_details,
                    rate_plan_mapping,
                    charge.posting_date,
                )

                template_charges.append(non_pos_template_charge_dict)

            else:
                non_pos_template_charge_dict = self._build_charge_dict_for_template(
                    invoice_charge,
                    non_room_rent_charge_components,
                    sku_category_map,
                    pretax_amount - allowance_pretax_amount,
                    posttax_amount - allowance_posttax_amount,
                    tax_details_post_allowance,
                    rate_plan_mapping,
                    charge.posting_date,
                )

                template_charges.append(non_pos_template_charge_dict)

        for pos_charge_key, pos_charges in grouped_pos_charges.items():
            pretax_amount = sum([charge.pretax_amount for charge in pos_charges])
            posttax_amount = sum([charge.posttax_amount for charge in pos_charges])
            allowance_pretax_amount, allowance_tax_amount, allowance_posttax_amount = (
                Money(currency=base_currency),
                Money(currency=base_currency),
                Money(currency=base_currency),
            )
            allowance_tax_details, tax_details_post_allowance = [], []
            for invoice_charge in pos_charges:
                charge = bill_aggregate.get_charge(invoice_charge.charge_id)
                for split_id in invoice_charge.charge_split_ids:
                    charge_split = charge.get_split(split_id)

                    if charge_split.is_invoiced:
                        allowances = charge_split.get_allowances_for_invoice_id(
                            invoice_aggregate.invoice.invoice_id
                        )
                    else:
                        allowances = charge_split.active_allowances()
                    for allowance in allowances:
                        allowance_pretax_amount += allowance.pretax_amount
                        allowance_posttax_amount += allowance.posttax_amount
                        allowance_tax_amount += allowance.tax_amount
                        allowance_tax_details = self.concatenate_tax_details(
                            allowance_tax_details, allowance.tax_details
                        )
            cumulative_tax_details = dict()
            for ch in pos_charges:
                for tax in ch.tax_details:
                    if tax.tax_type not in cumulative_tax_details:
                        cumulative_tax_details[tax.tax_type] = dict(
                            percentage=tax.percentage, amount=tax.amount
                        )
                    else:
                        cumulative_tax_details[tax.tax_type]['amount'] += tax.amount

            tax_details = [
                TaxDetail(tax_type, tax_details['percentage'], tax_details['amount'])
                for tax_type, tax_details in cumulative_tax_details.items()
            ]

            charge = bill_aggregate.get_charge(pos_charges[0].charge_id)
            pos_template_charge_dict = self._build_charge_dict_for_template(
                pos_charges[0],
                [],
                sku_category_map,
                pretax_amount,
                posttax_amount,
                tax_details,
                rate_plan_mapping,
                charge.posting_date,
            )

            template_charges.append(pos_template_charge_dict)

        return template_charges

    def _build_charge_dict_for_template(
        self,
        invoice_charge,
        charge_component,
        sku_category_map,
        pretax_amount,
        posttax_amount,
        tax_details: List[TaxDetail],
        rate_plan_mapping,
        posting_date,
    ):
        tax = {
            tax.tax_type: dict(percentage=tax.percentage, amount=tax.amount)
            for tax in tax_details
        }
        description = self._get_charge_description(invoice_charge, rate_plan_mapping)
        reference = self._get_charge_reference(invoice_charge)
        return dict(
            applicable_date=dateutils.to_date(invoice_charge.applicable_date),
            services=self._get_service_description(invoice_charge),
            charge_category=invoice_charge.charge_item.sku_category_id,
            description=description,
            reference=reference,
            other_charge_component_names=charge_component,
            item_code=self._get_hsn_sac_code(invoice_charge, sku_category_map),
            pretax_amount=pretax_amount,
            posttax_amount=posttax_amount,
            tax=tax,
            tax_breakup=tax_details,
            charge_to_ids=invoice_charge.charge_to_ids,
            charge_item_details=invoice_charge.charge_item.details,
            charged_entity_id=invoice_charge.charge_item.details.get(
                'charged_entity_id'
            ),
            charge_id=invoice_charge.charge_id,
            posting_date=posting_date,
        )

    @staticmethod
    def _get_charge_description(invoice_charge, rate_plan_mapping):
        if invoice_charge.charge_item.is_pos_charge():
            description = invoice_charge.charge_item.sku_category_id.title()
        else:
            description = invoice_charge.charge_item.name
        if invoice_charge.charge_item.details.get('rate_plan_reference_id'):
            rate_plan = rate_plan_mapping.get(
                invoice_charge.charge_item.details['rate_plan_reference_id']
            )
            inclusions = (
                [inc.name for inc in rate_plan.package.inclusions if inc.name]
                if rate_plan.package.inclusions
                else None
            )
            if inclusions:
                rate_plan_inclusions = ' + '.join(inclusions)
                description = "{description} + {rate_plan_inclusions}".format(
                    description=description, rate_plan_inclusions=rate_plan_inclusions
                )
        return description

    @staticmethod
    def _get_charge_reference(invoice_charge):
        reference = ""
        if invoice_charge.charge_item.details.get('is_pos_charge'):
            if invoice_charge.charge_item.details.get('seller_name'):
                reference += invoice_charge.charge_item.details['seller_name']
            if invoice_charge.charge_item.details.get('pos_bill_number'):
                reference = (
                    reference
                    + ': '
                    + invoice_charge.charge_item.details['pos_bill_number']
                )

        return reference

    @staticmethod
    def _get_hsn_sac_code(invoice_charge, sku_category_map):
        sku_category_aggregate = sku_category_map.get(
            invoice_charge.charge_item.sku_category_id
        )
        return sku_category_aggregate.sku_category.item_code

    @staticmethod
    def _get_payment_details_for_template(bill_aggregate, invoice_aggregate):
        payments_for_template = []
        payment_splits = bill_aggregate.get_payment_splits_for_invoice_templates(
            invoice_aggregate.invoice.billed_entity_account
        )
        for payment_id, payment_split in payment_splits.items():
            payment = bill_aggregate.get_payment(payment_id)
            if payment.status == PaymentStatus.CANCELLED:
                continue
            for split in payment_split:
                payments_for_template.append(
                    dict(
                        date_of_payment=dateutils.to_date(payment.date_of_payment),
                        id="{}-{}-{}".format(
                            invoice_aggregate.invoice.bill_id,
                            payment.payment_id,
                            split.payment_split_id,
                        ),
                        payment_type=split.payment_type.label,
                        payment_mode=split.payment_mode,
                        amount=split.amount,
                        comment=payment.comment,
                    )
                )
        credit_charge_split = bill_aggregate.get_charge_splits_by_type(
            invoice_aggregate.invoice.billed_entity_account, ChargeTypes.CREDIT
        )
        for charge_id, charge_split in credit_charge_split.items():
            charge = bill_aggregate.get_charge(charge_id)
            if charge.status == ChargeStatus.CANCELLED:
                continue
            payments_for_template.append(
                dict(
                    date_of_payment=dateutils.to_date(charge.applicable_date),
                    id="{}-{}-{}".format(
                        invoice_aggregate.invoice.bill_id,
                        charge.charge_id,
                        charge_split.charge_split_id,
                    ),
                    payment_type=charge_split.charge_type.label,
                    payment_mode='',
                    amount=charge_split.get_posttax_amount_post_allowance(),
                    comment=charge.comment,
                )
            )
        return payments_for_template

    @staticmethod
    def _folio_number(invoice_aggregate, bill_aggregate):
        folio = bill_aggregate.get_folio(
            billed_entity_account=invoice_aggregate.invoice.billed_entity_account
        )
        return folio.folio_number if folio else None

    @staticmethod
    def _get_invoice_template_namespace(invoice_aggregate, hotel_context):
        if invoice_aggregate.is_void():
            if invoice_aggregate.is_reseller_issued_invoice():
                return TemplateNameSpace.THS_VOID_RESELLER_INVOICE.value
            else:
                return (
                    TemplateNameSpace.THS_INDEPENDENT_VOID_HOTEL_INVOICE.value
                    if hotel_context.is_independent_hotel()
                    else TemplateNameSpace.THS_VOID_HOTEL_INVOICE.value
                )

        else:
            if invoice_aggregate.is_reseller_issued_invoice():
                return TemplateNameSpace.THS_RESELLER_INVOICE.value
            else:
                return (
                    TemplateNameSpace.THS_INDEPENDENT_HOTEL_INVOICE.value
                    if hotel_context.is_independent_hotel()
                    else TemplateNameSpace.THS_HOTEL_INVOICE.value
                )

    @staticmethod
    def _get_proforma_invoice_template_namespace(invoice_aggregate):
        # TODO: Once templates are replicated make it similar to get_invoice_template_namespace method
        return (
            TemplateNameSpace.THS_VOID_HOTEL_PROFORMA_INVOICE.value
            if invoice_aggregate.is_void()
            else TemplateNameSpace.THS_HOTEL_PROFORMA_INVOICE.value
        )

    def _generate_signed_url(self, invoice_aggregate, invoice_url):
        if not invoice_url:
            logger.warning(
                "Not generating signed url."
                "No invoice url is present for invoice: {}".format(
                    invoice_aggregate.invoice.invoice_id
                )
            )
            return None, None

        presigned_url = self.aws_service_client.get_presigned_url_from_s3_url(
            s3_url=invoice_url, link_expires_in=self.DefaultSignedUrlExpirationSeconds
        )
        return presigned_url, dateutils.today() + timedelta(
            hours=self.DefaultSignedUrlExpirationHours
        )

    @staticmethod
    def concatenate_tax_details(tax_details_1, tax_details_2):
        final_tax_details = copy.deepcopy(tax_details_1)
        for outer_entry in tax_details_2:
            for inner_entry in final_tax_details:
                if inner_entry.is_same_tax_type(outer_entry):
                    inner_entry.amount += outer_entry.amount
                    break
            else:
                final_tax_details.append(copy.copy(outer_entry))
        return final_tax_details

    def rectify_tax_details_percentage(
        self,
        tax_details_post_allowance,
        pretax_amount=None,
        allowance_pretax_amount=None,
    ):
        return [
            TaxDetail(
                tax_type=tax_detail.tax_type,
                percentage=self._calculate_tax_percentage_for_charge_line_items(
                    tax_detail, pretax_amount, allowance_pretax_amount
                ),
                amount=tax_detail.amount,
            )
            for tax_detail in tax_details_post_allowance
        ]

    @staticmethod
    def _calculate_tax_percentage_for_charge_line_items(
        tax_detail, pretax_amount, allowance_pretax_amount
    ):
        if pretax_amount and allowance_pretax_amount is not None:
            if (pretax_amount - allowance_pretax_amount).amount > Decimal('0'):
                percentage = (
                    tax_detail.amount
                    / (pretax_amount - allowance_pretax_amount).amount
                    * 100
                ).amount
            else:
                percentage = Decimal('0')
        else:
            percentage = tax_detail.percentage
        return percentage

    @staticmethod
    def _create_issued_by_from_company_details(company_details):
        issued_by = InvoiceIssuedByInfo.create_empty_instance()
        issued_by.gst_details = GSTDetails(
            address=company_details.legal_details.address,
            legal_name=company_details.legal_details.legal_name,
            gstin_num=company_details.legal_details.tin,
            is_sez=company_details.legal_details.is_sez,
            has_lut=company_details.legal_details.has_lut,
        )
        issued_by.phone = company_details.legal_details.phone
        issued_by.email = company_details.legal_details.email
        return issued_by

    @staticmethod
    def _get_service_description(invoice_charge):
        service_description = (
            invoice_charge.charge_item.name.title()
            if not invoice_charge.charge_item.is_pos_charge()
            else invoice_charge.charge_item.sku_category_id.title()
        )
        if invoice_charge.charge_item.name == 'Transferred Charge':
            service_description = (
                invoice_charge.charge_item.sku_category_id.title()
                + ' (Transferred Charge)'
            )
        return service_description

    @staticmethod
    def _derive_allowances_to_invoiced_for_proforma_invoice(
        bill_aggregate, invoice_aggregate
    ):
        allowances_to_be_invoiced = []
        for inv_charge in invoice_aggregate.invoice_charges:
            charge = bill_aggregate.get_charge(inv_charge.charge_id)
            for charge_split in charge.charge_splits:
                if charge_split.charge_split_id in inv_charge.charge_split_ids:
                    if charge_split.active_allowances():
                        for allowance in charge_split.active_allowances():
                            allowance_to_be_invoiced = dict(
                                charge_id=inv_charge.charge_id,
                                charge_split_id=charge_split.charge_split_id,
                                allowance_id=allowance.allowance_id,
                            )
                            allowances_to_be_invoiced.append(allowance_to_be_invoiced)
        return allowances_to_be_invoiced

    @staticmethod
    def _derive_billed_entity_category(bill_aggregate, invoice_aggregate):
        billed_entity_category = None
        billed_entity = bill_aggregate.get_billed_entity(
            invoice_aggregate.invoice.billed_entity_account.billed_entity_id
        )
        if billed_entity:
            billed_entity_category = billed_entity.category.value
        return billed_entity_category
