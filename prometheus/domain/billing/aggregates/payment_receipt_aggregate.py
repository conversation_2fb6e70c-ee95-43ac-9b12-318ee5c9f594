from prometheus.domain.billing.entities.payment_receipt import (
    PaymentReceipt,
    PaymentReceiptReceiverInfo,
)


class PaymentReceiptAggregate(object):
    def __init__(self, payment_receipt: PaymentReceipt):
        self.payment_receipt = payment_receipt

    @property
    def deleted(self):
        return self.payment_receipt.deleted

    def set_payment_receipt_receiver_info(self, payment_receipt_receiver_info):
        self.payment_receipt.set_payment_receipt_receiver_info(
            payment_receipt_receiver_info
        )

    def set_affected_room_nos(self, affected_room_nos=None):
        self.payment_receipt.set_affected_room_nos(affected_room_nos)

    def set_payment_receipt_number(self, payment_receipt_number):
        self.payment_receipt.set_payment_receipt_number(payment_receipt_number)

    def update_payment_receipt_date(self, payment_receipt_date):
        self.payment_receipt.update_payment_receipt_date(payment_receipt_date)

    def update_payment_receipt_receiver_info(self, payment_receipt_receiver_info):
        self.payment_receipt.update_payment_receipt_receiver_info(
            payment_receipt_receiver_info
        )

    def update_affected_room_nos(self, affected_room_nos=None):
        self.payment_receipt.update_affected_room_nos(affected_room_nos)

    def update_payment_receipt_url(self, payment_receipt_url):
        self.payment_receipt.update_payment_receipt_url(payment_receipt_url)

    def update_payment_signed_url(self, signed_url, expiry):
        self.payment_receipt.update_payment_signed_url(signed_url)
        self.payment_receipt.update_payment_signed_url_expiry_time(expiry)

    def remove_old_urls(self):
        self.payment_receipt.update_payment_receipt_url(None)
        self.payment_receipt.update_payment_signed_url(None)
        self.payment_receipt.update_payment_signed_url_expiry_time(None)

    def delete(self):
        self.payment_receipt.delete()

    def get_payment_receipt_receiver_email(self):
        return self.payment_receipt.payment_receipt_receiver_info.email

    def get_payment_receipt_receiver_phone_number(self):
        return self.payment_receipt.payment_receipt_receiver_info.phone
