from prometheus.domain.billing.entities.credit_shell_refund import CreditShellRefund


class CreditShellRefundAggregate(object):
    def __init__(
        self,
        credit_shell_refund: CreditShellRefund,
    ):
        self.credit_shell_refund = credit_shell_refund

    @property
    def bill_id(self):
        return self.credit_shell_refund.bill_id

    @property
    def credit_shell_id(self):
        return self.credit_shell_refund.credit_shell_id

    @property
    def booking_id(self):
        return self.credit_shell_refund.booking_id
