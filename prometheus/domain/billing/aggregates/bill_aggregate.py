# coding=utf-8
"""
Bill aggregate
"""
import datetime
from collections import defaultdict, namedtuple
from decimal import Decimal
from typing import Dict, Iterable, List, Set, Tuple

from treebo_commons.money import Money
from treebo_commons.money.constants import CurrencyType
from treebo_commons.utils import dateutils

from prometheus import crs_context
from prometheus.domain.billing.domain_events.allowance_cancelled import (
    AllowanceCancelledEvent,
)
from prometheus.domain.billing.domain_events.allowance_passed import (
    AllowancePassedEvent,
)
from prometheus.domain.billing.domain_events.allowance_posted import (
    AllowancePostedEvent,
)
from prometheus.domain.billing.domain_events.bill_amount_updated import (
    BillAmountUpdatedEvent,
)
from prometheus.domain.billing.domain_events.bill_recreated import BillReCreatedEvent
from prometheus.domain.billing.domain_events.charge_added import ChargeAddedEvent
from prometheus.domain.billing.domain_events.charge_deleted import ChargeDeletedEvent
from prometheus.domain.billing.domain_events.charge_reinstated import (
    ChargeReInstatedEvent,
)
from prometheus.domain.billing.domain_events.payment_added import PaymentAddedEvent
from prometheus.domain.billing.domain_events.payment_redistributed import (
    PaymentRedistributedEvent,
)
from prometheus.domain.billing.domain_events.refund_added import RefundAddedEvent
from prometheus.domain.billing.dto.account_summary_dto import AccountSummaryDomainDto
from prometheus.domain.billing.dto.bill_summary_dto import (
    BillSummaryDto,
    CreditSummaryDto,
    DebitSummaryDto,
)
from prometheus.domain.billing.dto.billed_entity_data import BilledEntityData
from prometheus.domain.billing.dto.charge_data import ChargeData
from prometheus.domain.billing.dto.chargesplit_data import ChargeSplitData
from prometheus.domain.billing.dto.payment_data import PaymentData
from prometheus.domain.billing.dto.payment_event_data import PaymentEventData
from prometheus.domain.billing.dto.payment_split_data import PaymentSplitData
from prometheus.domain.billing.dto.payment_split_event_data import PaymentSplitEventData
from prometheus.domain.billing.entities.bill import Bill
from prometheus.domain.billing.entities.billed_entity import (
    BilledEntity,
    BilledEntityAccountVO,
    BillingInstructionVO,
)
from prometheus.domain.billing.entities.charge import Charge
from prometheus.domain.billing.entities.charge_split import ChargeSplit
from prometheus.domain.billing.entities.folio import Folio
from prometheus.domain.billing.entities.payment import Payment
from prometheus.domain.billing.entities.payment_split import PaymentSplit
from prometheus.domain.billing.errors import BillingErrors
from prometheus.domain.billing.exceptions import BillingError, SpotCreditIssueError
from prometheus.domain.domain_events.domain_event_registry import register_event
from prometheus.domain.policy.engine import RuleEngine
from prometheus.domain.policy.facts.access_entity_facts import AccessEntityFacts
from ths_common.constants.billing_constants import (
    BillAppId,
    BilledEntityCategory,
    BilledEntityStatus,
    ChargeBillToTypes,
    ChargeSplitType,
    ChargeStatus,
    ChargeSubTypes,
    ChargeTypes,
    PaymentChannels,
    PaymentInstruction,
    PaymentModes,
    PaymentReceiverTypes,
    PaymentStatus,
    PaymentTypes,
)
from ths_common.exceptions import ResourceNotFound, ValidationException
from ths_common.utils.collectionutils import flatten_list
from ths_common.utils.common_utils import group_list
from ths_common.utils.id_generator_utils import generate_short_random_id
from ths_common.value_objects import BillParentInfo, RoomChargeItemDetails


class BillAggregate(object):
    """
    Bill Aggregate
    Root entities: Payment
    Non-root entities: Guest
    """

    GroupKey = namedtuple('GroupKey', ['charge_type', 'bill_to_type', 'user'])
    BilledEntityGroupKey = namedtuple(
        'BilledEntityGroupKey', ['charge_type', 'billed_entity_account']
    )

    def __init__(
        self,
        bill: Bill,
        payments: [Payment],
        charges: [Charge],
        billed_entities: List[BilledEntity] = None,
        folios: List[Folio] = None,
    ):
        self.bill = bill
        self._payments = payments if payments else []
        self._charges = charges if charges else []
        self._billed_entities = billed_entities if billed_entities else []
        self._folios = folios or []
        self.charge_dict = {ch.charge_id: ch for ch in self._charges}
        self.payment_dict = {p.payment_id: p for p in self._payments}
        self.billed_entity_dict = {b.billed_entity_id: b for b in self._billed_entities}
        # TODO: This need to implemented properly separate tech debt to move PF to crs

        if self.is_crs_bill():
            self._populate_account_type_if_missing()

        self.current_max_charge_id = None
        if not crs_context.should_bypass_access_entity_checks():
            RuleEngine.action_allowed(
                action='access_entity',
                facts=AccessEntityFacts(
                    user_data=crs_context.user_data,
                    entity_vendor_id=bill.vendor_id,
                    entity_type="bill",
                ),
                fail_on_error=True,
            )

    def _populate_account_type_if_missing(self):
        if not self.is_crs_bill():
            return

        if all(
            account.account_type in {ChargeTypes.CREDIT, ChargeTypes.NON_CREDIT}
            for billed_entity in self.billed_entities
            for account in billed_entity.accounts
        ):
            # AccountType for all accounts is already set.
            return

        account_wise_charge_types = defaultdict(set)
        for charge in self.charges:
            for split in charge.charge_splits:
                if split.charge_type:
                    account_wise_charge_types[split.billed_entity_account].add(
                        split.charge_type
                    )

        for payment in self.payments:
            charge_type = (
                ChargeTypes.NON_CREDIT
                if payment.is_payment_or_refund()
                else ChargeTypes.CREDIT
            )
            for split in payment.payment_splits:
                account_wise_charge_types[split.billed_entity_account].add(charge_type)

        for billed_entity in self._billed_entities:
            for account in billed_entity.accounts:
                if not account.account_type:
                    charge_types = account_wise_charge_types[
                        BilledEntityAccountVO(
                            billed_entity.billed_entity_id, account.account_number
                        )
                    ]
                    account.set_assigned_charge_types(charge_types)

    @property
    def app_id(self):
        return self.bill.app_id

    @property
    def version(self):
        return self.bill.version

    @property
    def bill_date(self):
        return self.bill.bill_date

    @property
    def bill_id(self):
        return self.bill.bill_id

    @property
    def vendor_id(self):
        return self.bill.vendor_id

    @property
    def vendor_details(self):
        return self.bill.vendor_details

    @property
    def parent_reference_number(self):
        return self.bill.parent_reference_number

    @property
    def parent_info(self):
        return self.bill.parent_info

    @property
    def base_currency(self):
        return self.bill.base_currency

    @property
    def total_non_credit_invoiced_amount(self):
        return self.get_invoiced_amount(only_noncredit=True)

    @property
    def total_non_credit_reversal_amount(self):
        return self.get_credit_note_amount(only_noncredit=True)

    @property
    def total_debit(self):
        return self.total_posttax_amount()

    @property
    def total_debit_pretax(self):
        return self.total_pretax_amount()

    @property
    def total_debit_tax(self):
        return self.total_debit - self.total_debit_pretax

    @property
    def total_pay_after_checkout_debit(self):
        return self.total_credit_posttax_amount()

    @property
    def summary(self):
        total_confirmed_payment = self.get_total_payment(only_confirmed=True)
        total_unconfirmed_payment = self.get_total_payment() - total_confirmed_payment
        total_refund = self.refund_amount
        total_credit = (
            total_confirmed_payment + total_unconfirmed_payment - total_refund
        )
        total_pah_payment = self.get_pah_amount(PaymentReceiverTypes.HOTEL)
        credit_summary = CreditSummaryDto(
            total_confirmed_payment=total_confirmed_payment,
            total_unconfirmed_payment=total_unconfirmed_payment,
            total_credit_offered=Money("0", self.bill.base_currency),
            total_refund=total_refund,
            total_credit=total_credit,
            total_pah_payment=total_pah_payment,
        )

        total_credit_type_allowance = self.get_total_allowances(
            charge_type_filter=[ChargeTypes.CREDIT]
        )
        total_non_credit_type_allowance = self.get_total_allowances(
            charge_type_filter=[ChargeTypes.NON_CREDIT]
        )
        total_allowance = total_credit_type_allowance + total_non_credit_type_allowance

        total_debit_payable_after_checkout = self.total_posttax_amount(
            charge_type_filter=[ChargeTypes.CREDIT]
        )
        total_debit_payable_at_checkout = self.total_posttax_amount(
            charge_type_filter=[ChargeTypes.NON_CREDIT]
        )
        total_debit = (
            total_debit_payable_after_checkout + total_debit_payable_at_checkout
        )

        total_credit_charge = (
            total_debit_payable_after_checkout + total_credit_type_allowance
        )
        total_non_credit_charge = (
            total_debit_payable_at_checkout + total_non_credit_type_allowance
        )
        total_spot_credit = self.total_posttax_amount(
            charge_type_filter=[ChargeTypes.CREDIT],
            charge_sub_type=[ChargeSubTypes.SPOT_CREDIT],
        )
        total_charge = total_credit_charge + total_non_credit_charge
        total_debit_pretax = self.total_pretax_amount()
        debit_summary = DebitSummaryDto(
            total_charge=total_charge,
            total_allowance=total_allowance,
            total_debit=total_debit,
            total_debit_pretax=total_debit_pretax,
            total_credit_charge=total_credit_charge,
            total_non_credit_charge=total_non_credit_charge,
            total_credit_allowance=total_credit_type_allowance,
            total_non_credit_allowance=total_non_credit_type_allowance,
            total_debit_payable_after_checkout=total_debit_payable_after_checkout,
            total_debit_payable_at_checkout=total_debit_payable_at_checkout,
            total_spot_credit=total_spot_credit,
        )
        return BillSummaryDto(credit_summary, debit_summary)

    def get_account_summary(
        self,
        billed_entity_account,
        charge_type=None,
        invoiced_booked_charges=None,
        invoiced_booked_allowances=None,
    ) -> BillSummaryDto:
        total_confirmed_payment = self.get_total_payment(
            only_confirmed=True, billed_entity_account=billed_entity_account
        )
        total_unconfirmed_payment = (
            self.get_total_payment(billed_entity_account=billed_entity_account)
            - total_confirmed_payment
        )
        total_refund = self.get_total_refund(billed_entity_account)

        total_credit = (
            total_confirmed_payment + total_unconfirmed_payment - total_refund
        )
        total_pah_payment = self.get_pah_amount(
            PaymentReceiverTypes.HOTEL, billed_entity_account
        )

        credit_summary = CreditSummaryDto(
            total_confirmed_payment=total_confirmed_payment,
            total_unconfirmed_payment=total_unconfirmed_payment,
            total_credit_offered=Money("0", self.bill.base_currency),
            total_refund=total_refund,
            total_credit=total_credit,
            total_pah_payment=total_pah_payment,
        )

        total_credit_type_allowance = self.get_total_allowances(
            charge_type_filter=[ChargeTypes.CREDIT],
            billed_entity_account=billed_entity_account,
            include_booked_allowances=invoiced_booked_allowances,
        )
        total_non_credit_type_allowance = self.get_total_allowances(
            charge_type_filter=[ChargeTypes.NON_CREDIT],
            billed_entity_account=billed_entity_account,
            include_booked_allowances=invoiced_booked_allowances,
        )
        total_allowance = total_credit_type_allowance + total_non_credit_type_allowance

        total_debit_payable_after_checkout = self.total_posttax_amount(
            charge_type_filter=[ChargeTypes.CREDIT],
            billed_entity_account=billed_entity_account,
            include_booked_charge_ids=invoiced_booked_charges,
            include_booked_allowances=invoiced_booked_allowances,
        )
        total_debit_payable_at_checkout = self.total_posttax_amount(
            charge_type_filter=[ChargeTypes.NON_CREDIT],
            billed_entity_account=billed_entity_account,
            include_booked_charge_ids=invoiced_booked_charges,
            include_booked_allowances=invoiced_booked_allowances,
        )

        total_debit = (
            total_debit_payable_after_checkout + total_debit_payable_at_checkout
        )

        total_credit_charge = (
            total_debit_payable_after_checkout + total_credit_type_allowance
        )
        total_non_credit_charge = (
            total_debit_payable_at_checkout + total_non_credit_type_allowance
        )
        total_spot_credit = self.total_posttax_amount(
            charge_type_filter=[ChargeTypes.CREDIT],
            charge_sub_type=[ChargeSubTypes.SPOT_CREDIT],
            billed_entity_account=billed_entity_account,
            include_booked_charge_ids=invoiced_booked_charges,
            include_booked_allowances=invoiced_booked_allowances,
        )
        total_charge = total_credit_charge + total_non_credit_charge
        total_debit_pretax = self.total_pretax_amount()
        debit_summary = DebitSummaryDto(
            total_charge=total_charge,
            total_allowance=total_allowance,
            total_debit=total_debit,
            total_debit_pretax=total_debit_pretax,
            total_credit_charge=total_credit_charge,
            total_non_credit_charge=total_non_credit_charge,
            total_credit_allowance=total_credit_type_allowance,
            total_non_credit_allowance=total_non_credit_type_allowance,
            total_debit_payable_after_checkout=total_debit_payable_after_checkout,
            total_debit_payable_at_checkout=total_debit_payable_at_checkout,
            total_spot_credit=total_spot_credit,
        )
        return BillSummaryDto(credit_summary, debit_summary, charge_type=charge_type)

    @property
    def status(self):
        return self.bill.status

    @property
    def payments(self):
        return [payment for payment in self._payments if not payment.deleted]

    @property
    def charges(self):
        return [charge for charge in self._charges if not charge.deleted]

    @property
    def billed_entities(self):
        return [be for be in self._billed_entities if not be.deleted]

    @property
    def folios(self):
        return [folio for folio in self._folios if not folio.deleted]

    @property
    def active_charges(self):
        return [charge for charge in self._charges if charge.is_active]

    @property
    def active_created_charges(self):
        return [
            charge
            for charge in self._charges
            if charge.is_active and charge.status == ChargeStatus.CREATED
        ]

    @property
    def net_paid_amount(self):
        payment_amount = Money(0, self.bill.base_currency)
        for payment in self._payments:
            if not payment.is_active():
                continue
            if payment.payment_type == PaymentTypes.PAYMENT:
                payment_amount += payment.amount
            elif payment.payment_type == PaymentTypes.REFUND:
                payment_amount -= payment.amount
        return payment_amount

    def get_net_payment(self, exclude_modes=None):
        payment_amount = Money(0, self.bill.base_currency)
        for payment in self._payments:
            if not payment.is_active() or (
                exclude_modes and payment.payment_mode in exclude_modes
            ):
                continue
            if payment.payment_type == PaymentTypes.PAYMENT:
                payment_amount += payment.amount
            elif payment.payment_type == PaymentTypes.REFUND:
                payment_amount -= payment.amount
        return payment_amount

    def _get_total_payment_of_type(
        self, payment_type, billed_entity_account=None, only_confirmed=None
    ) -> Money:
        payment_amount = Money(0, self.bill.base_currency)
        for payment in self._payments:
            if not payment.is_active():
                continue

            if payment.payment_type != payment_type:
                continue

            if only_confirmed and not payment.confirmed:
                continue

            if billed_entity_account:
                payment_splits = [
                    split
                    for split in payment.payment_splits
                    if split.billed_entity_account == billed_entity_account
                ]
            else:
                payment_splits = payment.payment_splits

            if not payment_splits:
                continue

            payment_amount += sum(split.amount for split in payment_splits)
        return payment_amount

    def get_total_payment(
        self, billed_entity_account=None, only_confirmed=None
    ) -> Money:
        return self._get_total_payment_of_type(
            PaymentTypes.PAYMENT,
            billed_entity_account=billed_entity_account,
            only_confirmed=only_confirmed,
        )

    def get_total_refund(self, billed_entity_account=None) -> Money:
        return self._get_total_payment_of_type(
            PaymentTypes.REFUND, billed_entity_account=billed_entity_account
        )

    def get_total_credit_offered(self, billed_entity_account=None) -> Money:
        return self.total_posttax_amount(
            {ChargeTypes.CREDIT}, billed_entity_account=billed_entity_account
        )

    def get_net_paid_amount(self, billed_entity_account) -> Money:
        return self.get_total_payment(billed_entity_account) - self.get_total_refund(
            billed_entity_account
        )

    def get_net_balance(
        self,
        billed_entity_account,
        include_booked_charge_ids=None,
        include_booked_allowances=None,
    ) -> Money:
        net_paid_amount = self.get_net_paid_amount(billed_entity_account)
        total_non_credit_charge = self.total_posttax_amount(
            {ChargeTypes.NON_CREDIT},
            billed_entity_account=billed_entity_account,
            include_booked_charge_ids=include_booked_charge_ids,
            include_booked_allowances=include_booked_allowances,
        )
        return total_non_credit_charge - net_paid_amount

    @property
    def paid_amount(self):
        return self.get_total_payment()

    @property
    def net_payment_not_accounted_in_invoice(self):
        net_payment_applicable = self.net_paid_amount - (
            self.get_invoiced_amount(only_noncredit=True)
            + self.get_credit_note_amount(only_noncredit=True)
        )
        return net_payment_applicable

    @property
    def net_payable(self):
        return (
            self.total_posttax_amount({ChargeTypes.NON_CREDIT}) - self.net_paid_amount
        )

    @property
    def refund_amount(self):
        return Money(
            sum(
                payment.amount
                for payment in self._payments
                if payment.is_active() and payment.payment_type == PaymentTypes.REFUND
            ),
            self.bill.base_currency,
        )

    def get_total_allowances(
        self,
        charge_type_filter=None,
        billed_entity_account=None,
        include_booked_allowances=None,
    ):
        charge_types = (
            ChargeTypes.all_options(as_set=True)
            if not charge_type_filter
            else charge_type_filter
        )
        total_allowance = Money("0", self.bill.base_currency)
        if billed_entity_account:
            for charge in self.get_active_charges():
                for split in charge.charge_splits:
                    if split.charge_type in charge_types:
                        for allowance in split.allowances:
                            if (
                                isinstance(include_booked_allowances, list)
                                and len(include_booked_allowances) == 0
                                and allowance.status == ChargeStatus.CREATED
                            ):
                                continue
                            if allowance.billed_entity_account == billed_entity_account:
                                if allowance.status == ChargeStatus.CONSUMED:
                                    total_allowance += allowance.posttax_amount
                                    continue
                                if include_booked_allowances:
                                    if (
                                        dict(
                                            allowance_id=allowance.allowance_id,
                                            charge_id=charge.charge_id,
                                            charge_split_id=split.charge_split_id,
                                        )
                                        in include_booked_allowances
                                    ):
                                        total_allowance += allowance.posttax_amount
                                else:
                                    total_allowance += allowance.posttax_amount
        else:
            for charge in self.get_active_charges():
                for split in charge.charge_splits:
                    if split.charge_type in charge_types:
                        for allowance in split.allowances:
                            total_allowance += allowance.posttax_amount

        return total_allowance

    def total_posttax_amount(
        self,
        charge_type_filter=None,
        billed_entity_account=None,
        include_booked_charge_ids=None,
        include_booked_allowances=None,
        charge_sub_type=None,
    ) -> Money:
        charge_types = (
            ChargeTypes.all_options(as_set=True)
            if not charge_type_filter
            else charge_type_filter
        )
        if self.is_pos_bill():
            return Money(
                sum(
                    ch.get_posttax_amount_post_allowance()
                    for ch in self.charges
                    if ch.type in charge_types
                    and ch.status in {ChargeStatus.CREATED, ChargeStatus.CONSUMED}
                ),
                self.bill.base_currency,
            )
        else:
            if billed_entity_account:
                total_account_charge = Money("0", self.bill.base_currency)
                for charge in self.get_active_charges():
                    if isinstance(include_booked_charge_ids, list):
                        if (
                            charge.status
                            in [ChargeStatus.CREATED, ChargeStatus.PREVIEW]
                            and charge.charge_id not in include_booked_charge_ids
                        ):
                            continue
                    for split in charge.charge_splits:
                        if split.charge_type not in charge_types:
                            continue
                        if (
                            charge_sub_type
                            and split.charge_sub_type not in charge_sub_type
                        ):
                            continue
                        if split.billed_entity_account == billed_entity_account:
                            total_account_charge += split.post_tax

                        for allowance in split.allowances:
                            if (
                                isinstance(include_booked_allowances, list)
                                and len(include_booked_allowances) == 0
                                and allowance.status == ChargeStatus.CREATED
                            ):
                                continue
                            if allowance.status == ChargeStatus.CANCELLED:
                                continue
                            if allowance.billed_entity_account == billed_entity_account:
                                if allowance.status == ChargeStatus.CONSUMED:
                                    total_account_charge -= allowance.posttax_amount
                                    continue
                                if include_booked_allowances:
                                    if (
                                        dict(
                                            allowance_id=allowance.allowance_id,
                                            charge_id=charge.charge_id,
                                            charge_split_id=split.charge_split_id,
                                        )
                                        in include_booked_allowances
                                    ):
                                        total_account_charge -= allowance.posttax_amount
                                else:
                                    total_account_charge -= allowance.posttax_amount
                return total_account_charge

            elif charge_type_filter:
                return Money(
                    sum(
                        split.get_posttax_amount_post_allowance()
                        for ch in self.charges
                        if ch.status
                        in {
                            ChargeStatus.CREATED,
                            ChargeStatus.CONSUMED,
                            ChargeStatus.PREVIEW,
                        }
                        for split in ch.charge_splits
                        if (
                            split.charge_type in charge_types
                            and (
                                charge_sub_type is None
                                or split.charge_sub_type in charge_sub_type
                            )
                        )
                    ),
                    self.bill.base_currency,
                )
            else:
                return Money(
                    sum(
                        ch.get_posttax_amount_post_allowance()
                        for ch in self.charges
                        if ch.status
                        in {
                            ChargeStatus.CREATED,
                            ChargeStatus.CONSUMED,
                            ChargeStatus.PREVIEW,
                        }
                    ),
                    self.bill.base_currency,
                )

    def total_posttax_amount_post_allowance(self, charge_type_filter=None):
        return self.total_posttax_amount(charge_type_filter=charge_type_filter)

    def total_pretax_amount(self):
        return Money(
            sum(
                ch.get_pretax_amount_post_allowance()
                for ch in self._charges
                if not ch.deleted
                and ch.status in {ChargeStatus.CREATED, ChargeStatus.CONSUMED}
            ),
            self.bill.base_currency,
        )

    def average_pretax_room_stay_amount(self):
        room_charges_with_addons = [
            self.get_pre_tax_room_stay_amount(ch.charge_id)
            for ch in self.charges
            if ch.status in {ChargeStatus.CREATED, ChargeStatus.CONSUMED}
            and ch.is_room_rent()
        ]
        return sum(room_charges_with_addons) / len(room_charges_with_addons)

    def total_tax_amount(self):
        return Money(
            sum(
                ch.get_tax_amount_post_allowance()
                for ch in self._charges
                if not ch.deleted
                and ch.status in {ChargeStatus.CREATED, ChargeStatus.CONSUMED}
            ),
            self.bill.base_currency,
        )

    def get_payment_percentage(self):
        total_amount = self.total_posttax_amount()
        if total_amount.amount == 0:
            return 100
        paid_amount = total_amount - self.net_payable
        return float((paid_amount.amount / total_amount.amount) * 100)

    def update_parent_info(
        self, parent_info: BillParentInfo, parent_reference_number=None
    ):
        self.bill.parent_info = parent_info.dict()
        if parent_reference_number is not None:
            self.bill.parent_reference_number = parent_reference_number

    def update_fee(self, fee_type, fee):
        self.bill.update_fee(fee_type, fee)

    def update_fees(self, fees):
        self.bill.update_fees(fees)

    def get_fees(self, fee_type):
        fee = self.bill.fees.get(fee_type, 0)
        return Money(str(fee), self.base_currency)

    @property
    def total_fees(self):
        fee = sum(self.bill.fees.values())
        return Money(str(fee), self.base_currency)

    def has_fee_due(self):
        if not self.total_fees or not self.net_payable or self.net_paid_amount:
            return False
        return True

    def increment_version(self):
        self.bill.version += 1

    def void(self):
        self.bill.void()

    def current_version(self):
        return self.bill.version

    def get_invoiced_amount(self, only_noncredit=False):
        if only_noncredit:
            amount = sum(
                split.get_invoiced_amount()
                for ch in self._charges
                if ch.is_active
                for split in ch.charge_splits
                if split.is_invoiced and split.charge_type == ChargeTypes.NON_CREDIT
            )
        else:
            amount = sum(
                split.get_invoiced_amount()
                for ch in self._charges
                if ch.is_active
                for split in ch.charge_splits
                if split.is_invoiced
            )
        return Money(amount, self.bill.base_currency)

    def _is_charge_assigned_to_locked_account(self, charge):
        if self.is_pos_bill():
            return False
        for charge_split in charge.charge_splits:
            billed_entity = self.get_billed_entity(
                charge_split.billed_entity_account.billed_entity_id
            )
            account = billed_entity.get_account(
                charge_split.billed_entity_account.account_number
            )
            if account.is_locked():
                return True
        return False

    def _is_payment_assigned_to_locked_account(self, payment):
        if self.is_pos_bill():
            return False
        for payment_split in payment.payment_splits:
            billed_entity = self.get_billed_entity(
                payment_split.billed_entity_account.billed_entity_id
            )
            account = billed_entity.get_account(
                payment_split.billed_entity_account.account_number
            )
            if account.is_locked():
                return True
        return False

    def update_invoice_id(
        self, charge_id, charge_split_ids, invoice_id, override=False
    ):
        # TODO: This is directly updating ChargeSplit, rather than going through Charge
        charge = self.get_charge(charge_id)

        if charge.status != ChargeStatus.CONSUMED:
            raise ValidationException(
                BillingErrors.INVALID_CHARGE_SPLIT_STATE_FOR_INVOICING
            )

        for charge_split_id in charge_split_ids:
            charge_split = charge.get_split(charge_split_id)
            charge_split.update_invoice_id(invoice_id, override)

    def clear_all_invoice_ids(self):
        for charge in self.charges:
            charge.clear_invoice_ids()

    def clear_invoice_id_from_charges(self, charge_map=None):
        if charge_map:
            for charge in self.charges:
                if charge.charge_id in charge_map.keys():
                    charge.clear_invoice_ids_for_splits(
                        charge_split_ids=list(charge_map[charge.charge_id])
                    )

    def add_linked_addon_charge(self, charge, new_linked_addon_charge_dto: ChargeData):
        charges = self.add_charges([new_linked_addon_charge_dto])
        charge.linked_addon_charge_ids.append(charges[0].charge_id)
        charges[0].is_room_linked_addon_charge = True
        return charges[0]

    def add_inclusion_charge(self, charge, new_inclusion_charge_dto: ChargeData):
        charges = self.add_charges([new_inclusion_charge_dto])
        charge.attach_addon_charge(charges[0])
        charges[0].is_inclusion_charge = True
        return charges[0]

    def add_inclusion_charges(
        self, room_charge, new_inclusion_charge_dtos: List[ChargeData]
    ):
        charges = self.add_charges(new_inclusion_charge_dtos)
        room_charge.attach_addon_charges(charges)
        for ch in charges:
            ch.is_inclusion_charge = True
        return charges

    def add_charges(self, charge_dtos: [ChargeData], raise_event=True):
        if self.current_max_charge_id is not None:
            next_charge_id = self.current_max_charge_id
        else:
            next_charge_id = (
                max([charge.charge_id for charge in self._charges])
                if self._charges
                else 0
            )

        charges, non_roomstay_charges = [], []
        for charge_dto in charge_dtos:
            next_charge_id += 1
            if (
                charge_dto.status not in (ChargeStatus.CREATED, ChargeStatus.PREVIEW)
                and self.bill.app_id != BillAppId.POS_APP.value
            ):
                raise ValidationException(BillingErrors.CHARGE_CREATE_STATE_ERROR)
            if charge_dto.pretax_amount and not charge_dto.pretax_amount.currency:
                charge_dto.pretax_amount = Money(
                    charge_dto.pretax_amount.amount, self.bill.base_currency
                )
            if charge_dto.posttax_amount and not charge_dto.posttax_amount.currency:
                charge_dto.posttax_amount = Money(
                    charge_dto.posttax_amount.amount, self.bill.base_currency
                )

            charge = Charge(
                next_charge_id,
                charge_dto.pretax_amount,
                charge_dto.tax_amount,
                charge_dto.tax_details,
                charge_dto.posttax_amount,
                charge_dto.status,
                None,
                charge_dto.applicable_date,
                charge_dto.comment,
                charge_dto.created_by,
                charge_dto.item,
                charge_dto.charge_split_type,
                charge_to=charge_dto.charge_to,
                charge_type=charge_dto.type,
                bill_to_type=charge_dto.bill_to_type,
                split_allowed=charge_dto.split_allowed,
                is_inclusion_charge=charge_dto.is_inclusion_charge
                if charge_dto.is_inclusion_charge
                else False,
            )

            if charge_dto.charge_splits:
                for charge_split_dto in charge_dto.charge_splits:
                    if not charge_split_dto.billed_entity_account:
                        # TODO rohit: Which case would come here? And what billed entity is set, if it happens
                        # Validate that it shouldn't use incorrect account type
                        continue
                    billed_entity = self.get_billed_entity(
                        charge_split_dto.billed_entity_account.billed_entity_id
                    )
                    if (
                        (
                            charge_split_dto.charge_type == ChargeTypes.CREDIT
                            and not billed_entity.allow_pay_after_checkout()
                        )
                        and charge_split_dto.charge_sub_type
                        != ChargeSubTypes.SPOT_CREDIT
                    ):
                        raise BillingError(
                            error=BillingErrors.CREDIT_CHARGE_ONLY_BILL_TO_COMPANY_ERROR
                        )

                    account = billed_entity.create_account_if_not_exists(
                        charge_split_dto.charge_type,
                        charge_split_dto.billed_entity_account.account_number,
                    )

                    self._validate_account_for_charge_assignment(
                        charge_split_dto.charge_type, account
                    )
                    charge_split_dto.billed_entity_account.account_number = (
                        account.account_number
                    )

                    billed_entity_account = (
                        self._correct_charge_account_type_if_incorrect(
                            account, billed_entity, charge_split_dto.charge_type
                        )
                    )
                    if billed_entity_account:
                        charge_split_dto.billed_entity_account = billed_entity_account

                    self.add_folio_if_not_exists(
                        charge_split_dto.billed_entity_account.billed_entity_id,
                        charge_split_dto.billed_entity_account.account_number,
                    )

                charge.update_charge_splits(
                    charge_split_dtos=charge_dto.charge_splits, app_id=self.bill.app_id
                )

            if charge_dto.status == ChargeStatus.CONSUMED:
                # Use ChargeConsumeCommand
                self.consume_charge(charge, crs_context.current_business_date)

            charge.check_invariance(bill_id=self.bill_id)
            charges.append(charge)

        self.current_max_charge_id = next_charge_id

        old_bill_amount = (
            self.total_posttax_amount() if not self.bill.is_new() else None
        )
        self._charges.extend(charges)
        self.charge_dict.update({ch.charge_id: ch for ch in charges})
        new_bill_amount = (
            self.total_posttax_amount() if not self.bill.is_new() else None
        )

        if not self.bill.is_new() and old_bill_amount != new_bill_amount:
            register_event(
                BillAmountUpdatedEvent(
                    old_bill_amount=old_bill_amount, new_bill_amount=new_bill_amount
                )
            )
        is_transferred_charge = False
        if not self.bill.is_new() and raise_event:
            for charge in charges:
                if charge.item.name != "RoomStay":
                    non_roomstay_charges.append(charge)
                if charge.item.name == 'Transferred Charge':
                    is_transferred_charge = True

            if is_transferred_charge:
                register_event(
                    ChargeAddedEvent(
                        charges=non_roomstay_charges,
                        destination_folio_name_map={
                            BilledEntityAccountVO(
                                folio.billed_entity_id, folio.account_number
                            ): self.get_folio_name(folio.folio_number)
                            for folio in self.folios
                        },
                    )
                )
            elif non_roomstay_charges:
                register_event(ChargeAddedEvent(charges=non_roomstay_charges))
        return charges

    @staticmethod
    def _validate_account_for_charge_assignment(charge_type, account):
        if account.is_locked():
            raise BillingError(error=BillingErrors.ACCOUNT_IS_LOCKED)
        if account.is_allowance_account():
            raise BillingError(
                error=BillingErrors.CHARGE_ADDITION_NOT_ALLOWED_ON_ALLOWANCE_ACCOUNT
            )

    def _correct_charge_account_type_if_incorrect(
        self, account, billed_entity, charge_type
    ):
        # Update account, if invalid credit/non-credit account is passed
        if (
            charge_type == ChargeTypes.NON_CREDIT
            and not account.is_non_credit_account()
        ):
            bea = self.get_billed_entity_account_for_new_assignment(
                billed_entity, ChargeTypes.NON_CREDIT
            )
            self.add_folio_if_not_exists(bea.billed_entity_id, bea.account_number)
            return BilledEntityAccountVO(
                billed_entity_id=billed_entity.billed_entity_id,
                account_number=bea.account_number,
            )
        elif charge_type == ChargeTypes.CREDIT and not account.is_credit_account():
            bea = self.get_billed_entity_account_for_new_assignment(
                billed_entity, ChargeTypes.CREDIT
            )
            self.add_folio_if_not_exists(bea.billed_entity_id, bea.account_number)
            return BilledEntityAccountVO(
                billed_entity_id=billed_entity.billed_entity_id,
                account_number=bea.account_number,
            )

    @staticmethod
    def _validate_account_type_for_charge(account, charge_type):
        if (
            charge_type == ChargeTypes.NON_CREDIT
            and not account.is_non_credit_account()
        ):
            raise BillingError(
                error=BillingErrors.NON_CREDIT_CHARGE_DISALLOWED_ON_CREDIT_ACCOUNT
            )

        elif charge_type == ChargeTypes.CREDIT and not account.is_credit_account():
            raise BillingError(
                error=BillingErrors.CREDIT_CHARGE_DISALLOWED_ON_NON_CREDIT_ACCOUNT
            )

    def _correct_account_type_if_incorrect(
        self, account, billed_entity, payment, payment_split
    ):
        # Update account, if invalid credit/non-credit account is passed
        if payment.is_payment_or_refund() and not account.is_non_credit_account():
            bea = billed_entity.get_account_for_new_assignment(ChargeTypes.NON_CREDIT)
            payment_split.billed_entity_account = BilledEntityAccountVO(
                billed_entity_id=billed_entity.billed_entity_id,
                account_number=bea.account_number,
            )
        elif not payment.is_payment_or_refund() and not account.is_credit_account():
            bea = billed_entity.get_account_for_new_assignment(ChargeTypes.CREDIT)
            payment_split.billed_entity_account = BilledEntityAccountVO(
                billed_entity_id=billed_entity.billed_entity_id,
                account_number=bea.account_number,
            )
        self.add_folio_if_not_exists(
            payment_split.billed_entity_account.billed_entity_id,
            payment_split.billed_entity_account.account_number,
        )

    @staticmethod
    def _validate_account_type(account, payment):
        if payment.is_payment_or_refund() and not account.is_non_credit_account():
            raise BillingError(error=BillingErrors.PAYMENT_DISALLOWED_ON_CREDIT_ACCOUNT)
        elif not payment.is_payment_or_refund() and not account.is_credit_account():
            raise BillingError(
                error=BillingErrors.PAYMENT_DISALLOWED_ON_NON_CREDIT_ACCOUNT
            )

    def update_charge_amounts(
        self,
        charge_id,
        pretax_amount,
        tax_amount,
        posttax_amount,
        tax_details,
        charge_components=None,
        override=False,
        grouped_sku_categories=None,
    ):
        """
        Args:
            charge_id:
            pretax_amount:
            tax_amount:
            posttax_amount:
            tax_details:
            charge_components:
            override: updates charge even if attached to an invoice
            grouped_sku_categories:
        Returns: list of updated charge objects

        """
        # calculate tax
        old_bill_amount = self.total_posttax_amount()
        updated_charges = []
        if pretax_amount and not pretax_amount.currency:
            pretax_amount = Money(pretax_amount.amount, self.bill.base_currency)
        if posttax_amount and not posttax_amount.currency:
            posttax_amount = Money(posttax_amount.amount, self.bill.base_currency)
        charge = self.get_charge(charge_id)
        if self.is_crs_bill():
            if self._is_charge_assigned_to_locked_account(charge):
                raise BillingError(error=BillingErrors.ACCOUNT_IS_LOCKED)
        if grouped_sku_categories:
            if self.is_tax_slab_based_charge_splits(charge_id, grouped_sku_categories):
                raise BillingError(
                    error=BillingErrors.EDIT_CHARGE_AMOUNT_NOT_ALLOWED_FOR_TAX_SLAB_BASED_CHARGE_SPLITS
                )

        charge.update_amount(
            pretax_amount,
            tax_amount,
            posttax_amount,
            tax_details,
            charge_components=charge_components,
            override=override,
        )

        if charge.linked_addon_charge_ids:
            linked_addon_charges = self.get_charges(charge.linked_addon_charge_ids)

            for ch in linked_addon_charges:
                ch.apply_tax_from_room_charge(charge)
                if ch.is_dirty():
                    updated_charges.append(ch)

        updated_charges.append(charge)

        new_bill_amount = self.total_posttax_amount()
        if old_bill_amount != new_bill_amount:
            register_event(
                BillAmountUpdatedEvent(
                    old_bill_amount=old_bill_amount, new_bill_amount=new_bill_amount
                )
            )

        return updated_charges

    def update_charge_type(self, charge_id, type):
        charge = self.get_charge(charge_id)
        if self.is_pos_bill():
            charge.update_type(type)
        return charge

    def update_charge_bill_to_type(self, charge_id, bill_to_type):
        charge = self.get_charge(charge_id)
        if self.is_pos_bill():
            charge.update_bill_to_type(bill_to_type)
        return charge

    def add_payments(
        self,
        payment_dtos: [PaymentData],
        raise_event=True,
    ):
        next_payment_id = (
            max([payment.payment_id for payment in self._payments])
            if self._payments
            else 0
        )
        payments = []
        for payment_dto in payment_dtos:
            next_payment_id += 1
            amount = payment_dto.amount
            if not payment_dto.amount.currency:
                amount = Money(payment_dto.amount.amount, self.bill.base_currency)

            if amount.currency != self.bill.base_currency:
                raise ValidationException(
                    BillingErrors.CURRENCY_ERROR,
                    description="add payment: Wrong base currency {currency} for payment".format(
                        currency=amount.currency
                    ),
                )
            if not payment_dto.date_of_payment:
                payment_dto.date_of_payment = dateutils.datetime_at_given_time(
                    crs_context.get_hotel_context().current_date(),
                    crs_context.get_hotel_context().checkin_time,
                )
            payment = Payment(
                next_payment_id,
                amount,
                payment_dto.date_of_payment,
                payment_dto.payment_mode,
                payment_dto.payment_type,
                payment_dto.payment_details,
                payment_dto.status,
                payment_dto.paid_to,
                payment_dto.payment_channel,
                payment_dto.payment_ref_id,
                payment_dto.paid_by,
                payment_dto.comment,
                payment_dto.amount_in_payment_currency,
                payment_dto.payment_mode_sub_type,
                payment_dto.payer,
                confirmed=payment_dto.confirmed,
                payor_billed_entity_id=payment_dto.payor_billed_entity_id,
                refund_reason=payment_dto.refund_reason,
                payout_details=payment_dto.payout_details,
            )

            if payment_dto.payment_splits:
                for payment_split in payment_dto.payment_splits:
                    if not payment_split.amount.currency:
                        payment_split.amount = Money(
                            payment_split.amount.amount, self.bill.base_currency
                        )

                    if self.is_crs_bill():
                        billed_entity = self.get_billed_entity(
                            payment_split.billed_entity_account.billed_entity_id
                        )
                        account = billed_entity.create_account_if_not_exists(
                            payment_dto.get_billed_entity_account_type(),
                            payment_split.billed_entity_account.account_number,
                        )
                        self.add_folio_if_not_exists(
                            billed_entity.billed_entity_id, account.account_number
                        )
                        if account.is_locked():
                            raise BillingError(error=BillingErrors.ACCOUNT_IS_LOCKED)

                        self._correct_account_type_if_incorrect(
                            account, billed_entity, payment, payment_split
                        )

                        if payment.payment_type == PaymentTypes.REFUND:
                            refund_amount = payment_split.amount
                            if refund_amount > self.get_net_paid_amount(
                                payment_split.billed_entity_account
                            ):
                                raise ValidationException(
                                    BillingErrors.REFUND_AMOUNT_GREATER_THAN_TOTAL_PAID_AMOUNT,
                                    extra_payload=dict(
                                        refund_amount=refund_amount,
                                        net_paid_amount=self.get_net_paid_amount(
                                            payment_split.billed_entity_account
                                        ),
                                        billed_entity_account=payment_split.billed_entity_account.to_json(),
                                    ),
                                )

                payment.add_payment_splits(payment_dto.payment_splits)
            payments.append(payment)

        if raise_event:
            payment_entities = []
            refunds = []
            for payment in payments:
                if payment.payment_type == PaymentTypes.REFUND:
                    refunds.append(payment)
                elif payment.payment_type == PaymentTypes.PAYMENT:
                    payment_entities.append(payment)
            if payment_entities:
                register_event(PaymentAddedEvent(payments=payment_entities))
            if refunds:
                register_event(RefundAddedEvent(payments=refunds))

        self._payments.extend(payments)
        self.payment_dict.update({p.payment_id: p for p in payments})
        return payments

    def get_payment(self, payment_id) -> Payment:
        payment = self.payment_dict.get(payment_id)
        if not payment or payment.deleted:
            raise ResourceNotFound(
                "Payment",
                description="BillAggregate:Payment not found in {}:{}".format(
                    self.bill.bill_id, payment_id
                ),
            )
        return payment

    def get_payment_splits(
        self,
        billed_entity_account: BilledEntityAccountVO,
        payment_type=None,
        payment_splits_priority=None,
    ) -> Dict[int, PaymentSplit]:
        payment_splits = {
            p.payment_id: p.get_payment_split(billed_entity_account)
            for p in self.payments
            if p.is_active()
            and (payment_type is None or p.payment_type == payment_type)
        }
        if payment_splits_priority and payment_splits:
            return self.categorize_and_sort_payment_splits(
                payment_splits, payment_splits_priority
            )
        return {
            payment_id: payment_split
            for payment_id, payment_split in payment_splits.items()
            if payment_split
        }

    def get_payment_splits_of_billed_entities(
        self,
        billed_entity_ids,
    ) -> Dict[int, List[PaymentSplit]]:
        payment_to_split_mapper: Dict[int, List[PaymentSplit]] = {}
        for p in self.get_active_payments():
            ps_ids_of_be = [
                ps.payment_split_id
                for ps in p.payment_splits
                if ps.billed_entity_account.billed_entity_id in billed_entity_ids
            ]
            if ps_ids_of_be:
                payment_to_split_mapper[p.payment_id] = ps_ids_of_be
        return payment_to_split_mapper

    def get_charge_splits_by_type(
        self, billed_entity_account: BilledEntityAccountVO, charge_type=None
    ) -> Dict[int, ChargeSplit]:
        charge_splits = {
            c.charge_id: c.get_charge_splits(billed_entity_account, charge_type)
            for c in self.charges
            if c.is_active
        }
        return {
            charge_id: charge_split
            for charge_id, charge_split in charge_splits.items()
            if charge_split
        }

    def get_charge_splits_of_account(
        self,
        billed_entity_account: BilledEntityAccountVO,
        charge_type=None,
        exclude_invoiced=False,
    ) -> Dict[int, List[ChargeSplit]]:
        return {
            c.charge_id: c.get_charge_splits_by_account(
                billed_entity_account, charge_type, exclude_invoiced
            )
            for c in self.charges
            if c.is_active
        }

    def get_active_payments(self):
        return [payment for payment in self.payments if payment.is_active()]

    def link_refund_to_source_payment_id(self, refund_id, source_payment_id):
        refund = self.get_payment(refund_id)
        refund.link_source_id(source_payment_id)

    def get_charge(self, charge_id) -> Charge:
        charge = self.charge_dict.get(charge_id)
        if not charge or charge.deleted:
            raise ResourceNotFound(
                "Charge",
                description="BillAggregate:Charge not found in {}:{}".format(
                    self.bill.bill_id, charge_id
                ),
            )
        return charge

    def get_primary_billed_entity_of_charge(self, charge_id):
        charge = self.get_charge(charge_id)
        primary_charge_split = charge.charge_splits[0]
        billed_entity = self.get_billed_entity(
            primary_charge_split.billed_entity_account.billed_entity_id
        )
        return billed_entity

    def get_primary_billed_entity_map(self, charge_ids):
        return {
            charge_id: self.get_primary_billed_entity_of_charge(charge_id)
            for charge_id in charge_ids
        }

    def get_charges(self, charge_ids) -> List[Charge]:
        return [ch for ch in self.charges if ch.charge_id in charge_ids]

    def get_active_charges(self, charge_ids=None) -> List[Charge]:
        if charge_ids:
            return [
                self.charge_dict.get(cid)
                for cid in charge_ids
                if self.charge_dict.get(cid).is_active
            ]
        return [ch for ch in self._charges if ch.is_active]

    def get_active_charges_by_accounts(
        self, accounts: Iterable[BilledEntityAccountVO]
    ) -> List[Charge]:
        return [
            ch
            for ch in self._charges
            if ch.is_active and ch.linked_billed_entity_accounts.intersection(accounts)
        ]

    def get_pre_tax_room_stay_amount(self, room_stay_charge_id):
        room_stay_charge = self.get_charge(room_stay_charge_id)
        if not room_stay_charge.is_active:
            base_currency = (
                crs_context.hotel_context.base_currency
                if (
                    crs_context.hotel_context
                    and crs_context.hotel_context.base_currency
                )
                else CurrencyType.INR
            )
            return Money(0, base_currency)
        active_addon_charges = (
            self.get_active_charges(room_stay_charge.addon_charge_ids)
            if room_stay_charge.addon_charge_ids
            else []
        )
        return sum(
            ch.pretax_amount_post_allowance
            for ch in [room_stay_charge] + active_addon_charges
        )

    def get_all_consumed_charges(self):
        return [charge for charge in self._charges if charge.is_consumed]

    def filter_charges(self, types=None, status=None):
        types = ChargeTypes.all_options(as_set=True) if not types else types
        status = ChargeStatus.all_options(as_set=True) if not status else status
        return [
            ch
            for ch in self._charges
            if not ch.deleted and ch.status in status
            for split in ch.charge_splits
            if split.charge_type in types
        ]

    def get_uninvoiced_cancellation_no_show_charges(self):
        return [
            charge
            for charge in self._charges
            if charge.is_active
            and not charge.is_completely_invoiced()
            and charge.is_cancellation_no_show()
        ]

    def filter_and_get_charges(
        self, charge_ids, allowed_charge_status=None
    ) -> List[Charge]:
        if not allowed_charge_status:
            allowed_charge_status = ChargeStatus.all_options(as_set=True)
        return [
            ch
            for ch in self.charges
            if ch.charge_id in charge_ids and ch.status in allowed_charge_status
        ]

    def filter_charges_by_ids(self, charge_ids):
        return [charge for charge in self.charges if charge.charge_id in charge_ids]

    def cancel_charges(self, charge_ids, raise_event=True, posting_date=None):
        old_bill_amount = self.total_posttax_amount()
        charge_ids = set(charge_ids)
        cancelled_charges = []

        inclusion_charge_ids_to_cancel = set(
            flatten_list(
                [
                    c.addon_charge_ids
                    for c in self.filter_and_get_charges(charge_ids)
                    if c.addon_charge_ids and c.is_active
                ]
            )
        )
        inclusion_charge_ids_to_cancel = [
            ch
            for ch in inclusion_charge_ids_to_cancel
            if not self.charge_dict.get(ch).deleted
        ]

        charge_ids.update(inclusion_charge_ids_to_cancel)

        for charge_id in charge_ids:
            charge = self.get_charge(charge_id)
            if charge.is_cancelled or charge.is_consumed:
                continue

            if self._is_charge_assigned_to_locked_account(charge):
                raise BillingError(error=BillingErrors.ACCOUNT_IS_LOCKED)

            charge.cancel()
            if charge.charge_id not in inclusion_charge_ids_to_cancel:
                cancelled_charges.append(charge)

        new_bill_amount = self.total_posttax_amount()
        if old_bill_amount != new_bill_amount:
            register_event(
                BillAmountUpdatedEvent(
                    old_bill_amount=old_bill_amount, new_bill_amount=new_bill_amount
                )
            )
        return cancelled_charges

    def delete_all_charges(self, charges_to_keep=None):
        if charges_to_keep is None:
            charges_to_keep = []

        deleted_charges = []
        for charge in self.charges:
            if charge in charges_to_keep:
                continue
            if (
                not charge.is_consumed
                or charge.posting_date == crs_context.get_hotel_context().current_date()
            ):
                charge.delete()
                deleted_charges.append(charge)

        if deleted_charges:
            register_event(ChargeDeletedEvent(charges=deleted_charges))

        return deleted_charges

    def delete_charges(self, charge_ids_to_be_deleted):
        # NOTE: Called from checkin reversal
        deleted_charges = []
        old_bill_amount = self.total_posttax_amount()
        for charge_id in charge_ids_to_be_deleted:
            charge = self.get_charge(charge_id)
            if self._is_charge_assigned_to_locked_account(charge):
                raise BillingError(error=BillingErrors.ACCOUNT_IS_LOCKED)

            charge.delete()
            deleted_charges.append(charge)

        if deleted_charges:
            register_event(ChargeDeletedEvent(charges=deleted_charges))

        new_bill_amount = self.total_posttax_amount()
        if old_bill_amount != new_bill_amount:
            register_event(
                BillAmountUpdatedEvent(
                    old_bill_amount=old_bill_amount, new_bill_amount=new_bill_amount
                )
            )

        return deleted_charges

    def remove_guests_from_charge_split(self, charge_ids, guest_ids_to_be_removed):
        # TODO: Check where this is called. And update the method to update charge_to in charge only
        updated_charges = []
        for charge_id in charge_ids:
            charge = self.get_charge(charge_id)
            if self._is_charge_assigned_to_locked_account(charge):
                raise BillingError(error=BillingErrors.ACCOUNT_IS_LOCKED)
            if charge.is_cancelled:
                continue
            charge_split_data = [
                ChargeSplitData(charge_to=charge_split.charge_to)
                for charge_split in charge.charge_splits
                if charge_split.charge_to not in guest_ids_to_be_removed
            ]
            charge.update_charge_splits(
                charge_split_data,
                charge_split_type=ChargeSplitType.EQUAL_SPLIT,
                override=True,
            )
            updated_charges.append(charge)
        return updated_charges

    def cancel_unused_charges(self, charge_ids):
        old_bill_amount = self.total_posttax_amount()
        cancelled_charges = []
        for charge_id in charge_ids:
            charge = self.get_charge(charge_id)
            if charge.is_unused:
                charge.cancel()
                cancelled_charges.append(charge)

        new_bill_amount = self.total_posttax_amount()
        if old_bill_amount != new_bill_amount:
            register_event(
                BillAmountUpdatedEvent(
                    old_bill_amount=old_bill_amount, new_bill_amount=new_bill_amount
                )
            )
        return cancelled_charges

    def _add_new_accounts(self, billed_entity_accounts: Set[Tuple[int, str]]):
        billed_entity_new_account_map = dict()
        for billed_entity_id, charge_type in billed_entity_accounts:
            billed_entity = self.get_billed_entity(billed_entity_id)
            account = billed_entity.add_new_account(charge_type=charge_type)
            self.add_folio_if_not_exists(billed_entity_id, account.account_number)
            billed_entity_new_account_map[
                (billed_entity_id, charge_type)
            ] = BilledEntityAccountVO(
                billed_entity.billed_entity_id, account.account_number
            )
        return billed_entity_new_account_map

    def move_cancelled_charges_to_created_state(self, charge_ids):
        old_bill_amount = self.total_posttax_amount()
        undo_cancelled = []

        inclusion_charge_ids_to_cancel = flatten_list(
            [
                c.addon_charge_ids
                for c in self.filter_and_get_charges(charge_ids)
                if c.addon_charge_ids
            ]
        )
        inclusion_charge_ids_to_cancel = [
            ch
            for ch in inclusion_charge_ids_to_cancel
            if not self.charge_dict.get(ch).deleted
        ]

        charge_ids_to_cancel = charge_ids + inclusion_charge_ids_to_cancel
        charge_ids_to_cancel = set(charge_ids_to_cancel)

        # NOTE: Moved from noshow_and_cancellation_service._handle_noshow_cancellation_charge_and_invoice_reversal
        # method
        # Add only 1 account, if account is locked
        # Account locking condition is not there. This is creating new account, because of the assumption that there
        # is a cancellation charge and invoice added on original account, when these charges were cancelled during
        # booking cancellation
        # Ideally, we should add cancellation charge to a completely new account
        billed_entity_new_account_map = self._add_new_accounts(
            {
                (
                    charge_split.billed_entity_account.billed_entity_id,
                    charge_split.charge_type,
                )
                for charge_id in charge_ids_to_cancel
                for charge_split in self.get_charge(charge_id).charge_splits
            }
        )

        for charge_id in charge_ids_to_cancel:
            for charge_split in self.get_charge(charge_id).charge_splits:
                charge_split.update_billed_entity_account(
                    billed_entity_new_account_map[
                        (
                            charge_split.billed_entity_account.billed_entity_id,
                            charge_split.charge_type,
                        )
                    ]
                )

        for charge_id in charge_ids_to_cancel:
            charge = self.get_charge(charge_id)
            if self._is_charge_assigned_to_locked_account(charge):
                raise BillingError(error=BillingErrors.ACCOUNT_IS_LOCKED)

            charge.undo_cancel()

            undo_cancelled.append(charge)

        if undo_cancelled:
            register_event(ChargeReInstatedEvent(charges=undo_cancelled))

        new_bill_amount = self.total_posttax_amount()
        if old_bill_amount != new_bill_amount:
            register_event(
                BillAmountUpdatedEvent(
                    old_bill_amount=old_bill_amount, new_bill_amount=new_bill_amount
                )
            )
        return undo_cancelled

    def move_cancelled_allowances_to_created_state(self, cancelled_allowances):
        for cancelled_allowance in cancelled_allowances:
            charge = self.get_charge(cancelled_allowance.get('charge_id'))
            charge_split = charge.get_split(cancelled_allowance.get('charge_split_id'))
            allowance = charge_split.get_allowance(
                cancelled_allowance.get('allowance_id')
            )
            billed_entity_account = allowance.billed_entity_account.billed_entity_id.get_account_for_new_assignment(
                charge.type
            )
            allowance.update_invoice_id(None)
            allowance.created()
            self.add_folio_if_not_exists(
                billed_entity_account.billed_entity_id,
                billed_entity_account.account_number,
            )
            if not billed_entity_account == allowance.billed_entity_account:
                cancelled_allowance.update_billed_entity_account(billed_entity_account)
                charge_split.update_billed_entity_account(billed_entity_account)

    def undo_charge_consumption(self, charge_ids):
        old_bill_amount = self.total_posttax_amount()
        reverted_consumed_charges = []
        for charge_id in charge_ids:
            charge = self.get_charge(charge_id)
            charge.remove_room_details()
            if self._is_charge_assigned_to_locked_account(charge):
                raise BillingError(error=BillingErrors.ACCOUNT_IS_LOCKED)
            if (
                charge.is_consumed
                and charge.posting_date
                == crs_context.get_hotel_context().current_date()
                and not self._is_charge_assigned_to_locked_account(charge)
            ):
                charge.unpost()

        new_bill_amount = self.total_posttax_amount()
        if old_bill_amount != new_bill_amount:
            register_event(
                BillAmountUpdatedEvent(
                    old_bill_amount=old_bill_amount, new_bill_amount=new_bill_amount
                )
            )
        return reverted_consumed_charges

    def delete_all_payments(self):
        for payment in self.payments:
            if not payment.is_posted():
                payment.delete()

    def get_all_uninvoiced_consumed_charge_map(
        self, booked_charges_to_be_excluded=None
    ):
        charge_map = defaultdict(lambda: defaultdict(lambda: set()))
        for charge in self.charges:
            if charge.is_credit_note_reversal_charge():
                continue
            if charge.status != ChargeStatus.CONSUMED:
                continue
            if booked_charges_to_be_excluded:
                if charge.charge_id in booked_charges_to_be_excluded:
                    continue
            for charge_split in charge.charge_splits:
                if charge_split.is_invoiced:
                    continue
                # final hashed key for the grouping which need to be a tuple
                key = BillAggregate.BilledEntityGroupKey(
                    charge_split.charge_type, charge_split.billed_entity_account
                )
                charge_map[key][charge.charge_id].add(charge_split.charge_split_id)
        return charge_map

    def get_all_uninvoiced_charge_map(
        self, booked_charges_to_be_excluded=None, billed_entity_account=None
    ):
        charge_map = defaultdict(lambda: defaultdict(lambda: set()))
        for charge in self.charges:
            if charge.status == ChargeStatus.CANCELLED:
                continue
            if charge.is_credit_note_reversal_charge():
                continue
            if booked_charges_to_be_excluded:
                if charge.charge_id in booked_charges_to_be_excluded:
                    continue
            for charge_split in charge.charge_splits:
                if charge_split.is_invoiced:
                    continue
                if (
                    billed_entity_account
                    and billed_entity_account != charge_split.billed_entity_account
                ):
                    continue
                # final hashed key for the grouping which need to be a tuple
                key = BillAggregate.BilledEntityGroupKey(
                    charge_split.charge_type, charge_split.billed_entity_account
                )
                charge_map[key][charge.charge_id].add(charge_split.charge_split_id)
        return charge_map

    def get_charge_and_split_ids_for_users(
        self,
        users,
        exclude_invoiced=True,
        charge_status_filter=None,
        include_only_charge_ids=None,
        include_cancellation_no_show_charges=False,
    ):
        """
        Get the charge split ids marked to the list of users.
        Args:
            users: the set/list of users for which charge_split ids need to be fetched.
            exclude_invoiced: set this flag to false if does not want to exclude invoiced
            charge_status_filter: filter based on charge status
            include_only_charge_ids: include charge ids if true
            include_cancellation_no_show_charges: include cancel now-show charges if true

        Returns: charge_map dict(charge_id: [charge_split_ids])
        """
        charge_status_filter = (
            ChargeStatus.all_options()
            if not charge_status_filter
            else charge_status_filter
        )
        charge_map = defaultdict(lambda: set())
        for charge in self.charges:
            if charge.is_credit_note_reversal_charge():
                continue
            if charge.status not in charge_status_filter:
                continue
            if (
                include_only_charge_ids is not None
                and charge.charge_id not in include_only_charge_ids
            ):
                continue
            if (
                (not include_cancellation_no_show_charges)
                and charge.charge_id
                and charge.is_cancellation_no_show()
            ):
                continue
            for charge_split in charge.charge_splits:
                if exclude_invoiced and charge_split.is_invoiced:
                    continue
                if charge_split.charge_to in users or (
                    include_cancellation_no_show_charges
                    and charge.is_cancellation_no_show()
                ):
                    charge_map[charge.charge_id].add(charge_split.charge_split_id)
        return charge_map

    def get_charge_and_split_ids_for_billed_entity_accounts(
        self,
        billed_entity_accounts,
        exclude_invoiced=True,
        charge_status_filter=None,
        booked_charges_to_be_excluded=None,
    ):
        """
        Get the charge split ids marked to the list of users.
        Args:
            billed_entity_accounts: the set/list of billed entity accounts for which charge_split ids need to be
            fetched.
            exclude_invoiced: set this flag to false if does not want to exclude invoiced
            charge_status_filter: filter based on charge status
            booked_charges_to_be_excluded: exclude booked_charges if true

        Returns: charge_map dict(charge_id: [charge_split_ids])
        """
        charge_status_filter = (
            ChargeStatus.all_options()
            if not charge_status_filter
            else charge_status_filter
        )
        charge_map = defaultdict(lambda: defaultdict(lambda: set()))
        for charge in self.charges:
            if booked_charges_to_be_excluded:
                if charge.charge_id in booked_charges_to_be_excluded:
                    continue
            if charge.is_credit_note_reversal_charge():
                continue
            if charge.status not in charge_status_filter:
                continue
            for charge_split in charge.charge_splits:
                if exclude_invoiced and charge_split.is_invoiced:
                    continue
                if charge_split.billed_entity_account in billed_entity_accounts:
                    # final hashed key for the grouping which need to be a tuple
                    key = BillAggregate.BilledEntityGroupKey(
                        charge_split.charge_type, charge_split.billed_entity_account
                    )
                    charge_map[key][charge.charge_id].add(charge_split.charge_split_id)
        return charge_map

    @staticmethod
    def group_by_billed_entity_accounts(charges):
        charge_map = defaultdict(lambda: defaultdict(lambda: set()))
        for charge in charges:
            for charge_split in charge.charge_splits:
                # final hashed key for the grouping which need to be a tuple
                key = BillAggregate.BilledEntityGroupKey(
                    charge_split.charge_type, charge_split.billed_entity_account
                )
                charge_map[key][charge.charge_id].add(charge_split.charge_split_id)
        return charge_map

    def filter_charge_ids(
        self,
        charge_ids,
        only_consumed=False,
        exclude_invoiced=False,
        exclude_cancelled=False,
    ):
        """

        Args:
            charge_ids:
            only_consumed:
            exclude_invoiced:
            exclude_cancelled:

        Returns:

        """
        charge_map = defaultdict(lambda: set())
        for charge_id in charge_ids:
            charge = self.get_charge(charge_id)
            if charge.is_credit_note_reversal_charge():
                continue
            if only_consumed and charge.status != ChargeStatus.CONSUMED:
                continue
            if exclude_cancelled and charge.status == ChargeStatus.CANCELLED:
                continue
            for charge_split in charge.charge_splits:
                if exclude_invoiced and charge_split.is_invoiced:
                    continue
                charge_map[charge.charge_id].add(charge_split.charge_split_id)
        return charge_map

    def filter_charge_split_map(self, charge_split_map, exclude_cancelled=False):
        charge_map = defaultdict(lambda: set())
        for charge_id, charge_split_ids in charge_split_map.items():
            charge = self.get_charge(charge_id)
            if exclude_cancelled and charge.status == ChargeStatus.CANCELLED:
                continue
            charge_splits = [
                charge.get_split(charge_split_id, include_deleted=True)
                for charge_split_id in charge_split_ids
            ]
            charge_split_ids = [
                split.charge_split_id for split in charge_splits if not split.deleted
            ]
            if charge_split_ids:
                charge_map[charge.charge_id] = charge_split_ids
        return charge_map

    def get_assigned_users_for_charges(self, charge_map):
        """

        Args:
            charge_map:

        Returns:

        """
        charge_to_ids = set()
        for charge_id, split_ids in charge_map.items():
            charge = self.get_charge(charge_id)
            if charge.charge_to:
                charge_to_ids.update(set(charge.charge_to))
            for split_id in split_ids:
                if charge.get_split(split_id).charge_to:
                    charge_to_ids.add(charge.get_split(split_id).charge_to)
        return charge_to_ids

    def group_by(
        self,
        charge_map: dict,
        group_by_type: bool = False,
        group_by_bill_to_type: bool = False,
        group_by_user: bool = False,
        grouped_charge_map=None,
    ):
        """
        Returns a created/updated grouped charge map based on charge_tye and/or charge bill to type and/or user.
        Args:
            charge_map:
            group_by_type:
            group_by_bill_to_type:
            group_by_user:
            grouped_charge_map:

        Returns: dict(GroupKey('charge_type', 'bill_to_type', 'user'): dict(charge_id: [charge_split_id]))

        """
        grouped_charge_map = (
            grouped_charge_map
            if grouped_charge_map
            else defaultdict(lambda: defaultdict(lambda: set()))
        )

        for charge_id, charge_split_ids in charge_map.items():
            charge = self.get_charge(charge_id)

            charge_type_key = (
                charge.type.value if group_by_type else ChargeTypes.NON_CREDIT
            )
            bill_to_type_key = (
                charge.bill_to_type.value
                if group_by_bill_to_type
                else ChargeBillToTypes.GUEST
            )

            for charge_split_id in charge_split_ids:
                charge_split = charge.get_split(charge_split_id)
                user_key = charge_split.charge_to if group_by_user else None

                # final hashed key for the grouping which need to be a tuple
                key = BillAggregate.GroupKey(
                    charge_type_key, bill_to_type_key, user_key
                )

                grouped_charge_map[key][charge_id].add(charge_split_id)

        return grouped_charge_map

    def check_invariance(self):
        # TODO add more validations
        for charge in self.charges:
            charge.check_invariance(bill_id=self.bill_id)

        if self.is_crs_bill():
            billed_entity_accounts = [
                split.billed_entity_account
                for ch in self.charges
                for split in ch.charge_splits
            ]
            for billed_entity_account in billed_entity_accounts:
                if not billed_entity_account:
                    continue
                billed_entity = self.get_billed_entity(
                    billed_entity_account.billed_entity_id
                )
                account = billed_entity.get_account(
                    billed_entity_account.account_number
                )
                if not account:
                    raise ValidationException(
                        BillingErrors.CHARGE_SPLIT_CANNOT_EXIST_WITHOUT_BILLED_ENTITY_ACCOUNT
                    )

    @staticmethod
    def merge_charge_maps(charge_maps):
        """
        Merges charge_maps into one
        Args:
            charge_maps: List of charge_map

        Returns:

        """
        merged_charge_map = dict()
        for charge_map in charge_maps:
            for key, val in charge_map.items():
                if merged_charge_map.get(key) is None:
                    merged_charge_map[key] = set(val)
                else:
                    merged_charge_map[key].update(val)
        return merged_charge_map

    def get_charge_types_in_charge_map(self, charge_ids):
        charge_type_list = []
        for charge_id in charge_ids:
            charge = self.get_charge(charge_id)
            charge_type_list.append(charge.type)
        return charge_type_list

    def update_bill_details(self, indexed_charge_dtos, payment_dtos):
        self.delete_all_charges()
        self.delete_all_payments()

        indexed_charges = dict()
        for index, charge_dtos in indexed_charge_dtos.items():
            charges = self.add_charges(charge_dtos, raise_event=False)
            indexed_charges[index] = charges

        if payment_dtos:
            payments = []
            for payment_dto in payment_dtos:
                if payment_dto.is_voucher_payment() and self.get_ref_ids_for_payment(
                    payment_dto
                ):
                    continue
                payments.append(payment_dto)
            self.add_payments(payment_dtos=payments, raise_event=False)

        self.check_invariance()

        payment_event_data = []
        for payment in self.payments:
            payment_event_data.append(
                PaymentEventData(
                    payment.payment_id,
                    payment.paid_by,
                    payment.paid_to,
                    payment.payment_mode,
                    payment.payment_mode_sub_type,
                    payment.amount,
                    payment.date_of_payment,
                    payment.payment_type,
                    payment.amount_in_payment_currency,
                    payment.payer,
                    payment.payment_ref_id,
                    payment.payment_details.get('remarks')
                    if payment.payment_details
                    else "",
                    payment.confirmed,
                    payment_splits=[
                        PaymentSplitEventData(
                            payment_split_id=ps.payment_split_id,
                            billed_entity_account=ps.billed_entity_account,
                            amount=ps.amount,
                        )
                        for ps in payment.payment_splits
                    ],
                    payor_billed_entity_id=payment.payor_billed_entity_id,
                )
            )
        register_event(
            BillReCreatedEvent(
                bill_amount=self.total_posttax_amount(), payments=payment_event_data
            )
        )
        return indexed_charges

    # DO NOT USE: Used for booking migration
    def update_vendor(self, vendor_id, vendor_details):
        self.bill.vendor_id = vendor_id
        self.bill.vendor_details = vendor_details

    def add_reversal_charge(self, invoice_charge, charge_item):
        charge = self.get_charge(invoice_charge.charge_id)
        # TODO: Should this condition be here?
        if self._is_charge_assigned_to_locked_account(charge):
            raise BillingError(error=BillingErrors.ACCOUNT_IS_LOCKED)
        max_charge_id = (
            max([charge.charge_id for charge in self._charges]) if self._charges else 0
        )
        reversed_charge = charge.negate(charge_id=max_charge_id + 1)
        reversed_charge.replace_charge_splits(
            [
                charge_split.negate()
                for charge_split in charge.charge_splits
                if charge_split.charge_split_id in invoice_charge.charge_split_ids
            ],
            override=True,
        )
        if charge.posttax_amount > invoice_charge.posttax_amount:
            reversed_charge.update_amount(
                -invoice_charge.pretax_amount,
                -invoice_charge.tax_amount,
                -invoice_charge.posttax_amount,
                [tax_detail.negate() for tax_detail in invoice_charge.tax_details],
                override=True,
            )
        reversed_charge.update_charge_item(charge_item)
        reversed_charge.clear_invoice_ids()
        old_bill_amount = self.total_posttax_amount()
        self._charges.append(reversed_charge)
        self.charge_dict.update({reversed_charge.charge_id: reversed_charge})
        new_bill_amount = self.total_posttax_amount()

        if old_bill_amount != new_bill_amount:
            register_event(
                BillAmountUpdatedEvent(
                    old_bill_amount=old_bill_amount, new_bill_amount=new_bill_amount
                )
            )

        register_event(ChargeAddedEvent(charges=[reversed_charge]))
        return reversed_charge

    def get_cancellation_no_show_charges(self):
        return [
            charge
            for charge in self.active_charges
            if charge.is_cancellation_no_show() and not charge.is_completely_invoiced()
        ]

    def get_credit_note_amount(self, only_noncredit=False):
        charge_types = (
            ChargeTypes.all() if not only_noncredit else [ChargeTypes.NON_CREDIT]
        )
        amount = Money(0, self.bill.base_currency)
        for ch in self._charges:
            if not ch.is_active:
                continue
            for split in ch.charge_splits:
                if not (
                    split.has_credit_note_allowance or split.is_credit_note_charge_split
                ):
                    continue
                if split.charge_type in charge_types:
                    amount += split.get_credit_note_amount()
        return amount

    def get_invoiced_cancellation_no_show_charges(self):
        return [
            charge
            for charge in self._charges
            if charge.is_active
            and charge.is_completely_invoiced()
            and charge.is_cancellation_no_show()
        ]

    def is_pos_bill(self):
        return self.bill.app_id == BillAppId.POS_APP.value

    def is_allowed_charge_type(self, charge_split, invoice):
        return (
            (charge_split.charge_type not in invoice.allowed_charge_types)
            if not self.is_pos_bill()
            else False
        )

    def is_crs_bill(self):
        return self.bill.app_id == BillAppId.CRS_APP.value

    def add_billed_entity(self, billed_entity_dto: BilledEntityData):
        billed_entity_id = max(
            [
                int(billed_entity.billed_entity_id)
                for billed_entity in self._billed_entities
            ],
            default=0,
        )
        billed_entity_id += 1
        billed_entity = BilledEntity(
            billed_entity_id=billed_entity_id,
            name=billed_entity_dto.name,
            category=billed_entity_dto.category,
            secondary_category=billed_entity_dto.secondary_category,
            status=billed_entity_dto.status,
        )
        billed_entity.add_new_account()
        self._billed_entities.append(billed_entity)
        self.billed_entity_dict[billed_entity.billed_entity_id] = billed_entity
        return billed_entity

    def get_billed_entity(self, billed_entity_id) -> BilledEntity:
        billed_entity = self.billed_entity_dict.get(billed_entity_id)
        if not billed_entity or billed_entity.deleted:
            raise ResourceNotFound(
                "Billed Entity",
                description="BillAggregate:Billed Entity not found in {}:{}".format(
                    self.bill.bill_id, billed_entity
                ),
            )
        return billed_entity

    def get_all_billed_entities_grouped_by_category(self):
        grouped_map = defaultdict(list)
        for key, billed_entity in self.billed_entity_dict.items():
            if not billed_entity.deleted:
                grouped_map[billed_entity.category].append(billed_entity)
        return grouped_map

    def get_payment_billed_entities_grouped_by_category(self, payment_id):
        grouped_map = defaultdict(list)
        payment = self.payment_dict.get(payment_id)
        if payment and payment.payment_splits:
            for payment_split in payment.payment_splits:
                if payment_split.billed_entity_account:
                    billed_entity_id = (
                        payment_split.billed_entity_account.billed_entity_id
                    )
                    billed_entity = self.billed_entity_dict.get(billed_entity_id)
                    if not billed_entity.deleted:
                        grouped_map[billed_entity.category].append(billed_entity)
        return grouped_map

    def get_billed_entity_ids_associated_with_payment(self, payment_id):
        billed_entity_ids = []
        payment = self.payment_dict.get(payment_id)
        if payment and payment.payment_splits:
            for payment_split in payment.payment_splits:
                if payment_split.billed_entity_account:
                    billed_entity_ids.append(
                        payment_split.billed_entity_account.billed_entity_id
                    )
        return billed_entity_ids

    def derive_bill_to_type(
        self, billed_entity_account: BilledEntityAccountVO
    ) -> ChargeBillToTypes:
        if self.get_billed_entity(billed_entity_account.billed_entity_id).category in {
            BilledEntityCategory.BOOKER_COMPANY,
            BilledEntityCategory.TRAVEL_AGENT,
        }:
            return ChargeBillToTypes.COMPANY
        else:
            return ChargeBillToTypes.GUEST

    def derive_bill_to_type_for_group_key(self, group_key) -> ChargeBillToTypes:
        if isinstance(group_key, self.GroupKey):
            if group_key.bill_to_type:
                return group_key.bill_to_type
            return ChargeBillToTypes.GUEST
        billed_entity_account = group_key.billed_entity_account
        if self.get_billed_entity(billed_entity_account.billed_entity_id).category in {
            BilledEntityCategory.BOOKER_COMPANY,
            BilledEntityCategory.TRAVEL_AGENT,
        }:
            return ChargeBillToTypes.COMPANY
        else:
            return ChargeBillToTypes.GUEST

    def _is_billed_entity_used(self, billed_entity_id):
        for charge in self.charges:
            for split in charge.charge_splits:
                if split.billed_entity_account.billed_entity_id == billed_entity_id:
                    return True

                if any(
                    allowance.billed_entity_account.billed_entity_id == billed_entity_id
                    for allowance in split.allowances
                ):
                    return True

        for payment in self.payments:
            if any(
                split.billed_entity_account.billed_entity_id == billed_entity_id
                for split in payment.payment_splits
            ):
                return True

    def delete_billed_entities(self, billed_entities=None):
        billed_entities = (
            self.billed_entities if not billed_entities else billed_entities
        )
        for billed_entity in billed_entities:
            if self._is_billed_entity_used(billed_entity):
                raise BillingError(error=BillingErrors.BILLING_ERROR)
            billed_entity.delete()

    def get_default_billed_entity_account(
        self, billed_entity_category: BilledEntityCategory, charge_type=None
    ):
        assert billed_entity_category in {
            BilledEntityCategory.BOOKER,
            BilledEntityCategory.BOOKER_COMPANY,
            BilledEntityCategory.TRAVEL_AGENT,
            BilledEntityCategory.PRIMARY_GUEST,
        }
        for billed_entity in self.billed_entities:
            if (
                not billed_entity.deleted
                and billed_entity.category == billed_entity_category
            ):
                bea = BilledEntityAccountVO(
                    billed_entity_id=billed_entity.billed_entity_id,
                    account_number=billed_entity.get_account_for_new_assignment(
                        charge_type=charge_type
                    ).account_number,
                )
                self.add_folio_if_not_exists(bea.billed_entity_id, bea.account_number)
                return bea
        return None

    def get_billed_entity_account_for_new_assignment(
        self, billed_entity: BilledEntity, charge_type
    ):
        bea = billed_entity.get_account_for_new_assignment(charge_type)
        self.add_folio_if_not_exists(bea.billed_entity_id, bea.account_number)
        return bea

    def get_billed_entity_for_category(
        self, billed_entity_category: BilledEntityCategory
    ):
        assert billed_entity_category in {
            BilledEntityCategory.BOOKER,
            BilledEntityCategory.BOOKER_COMPANY,
            BilledEntityCategory.TRAVEL_AGENT,
        }
        for billed_entity in self.billed_entities:
            if (
                not billed_entity.deleted
                and billed_entity.category == billed_entity_category
            ):
                return billed_entity
        return None

    def get_billed_entity_accounts(
        self, billed_entity_ids
    ) -> List[BilledEntityAccountVO]:
        billed_entity_accounts = []
        for billed_entity in self.billed_entities:
            if billed_entity.billed_entity_id in billed_entity_ids:
                billed_entity_accounts.extend(
                    [
                        BilledEntityAccountVO(
                            billed_entity.billed_entity_id, account.account_number
                        )
                        for account in billed_entity.accounts
                    ]
                )
        return billed_entity_accounts

    def mark_billed_entity_account_as_invoiced(
        self, billed_entity_account: BilledEntityAccountVO, payment_splits_priority=None
    ):
        self.transfer_surplus_payment_to_new_account(
            billed_entity_account, payment_splits_priority=payment_splits_priority
        )
        self.get_billed_entity(
            billed_entity_account.billed_entity_id
        ).mark_account_as_invoiced(billed_entity_account.account_number)

    def mark_billed_entity_account_as_uninvoiced(
        self,
        billed_entity_account: BilledEntityAccountVO,
    ):
        self.get_billed_entity(
            billed_entity_account.billed_entity_id
        ).mark_account_as_uninvoiced(billed_entity_account.account_number)

    def mark_billed_entity_account_as_allowance_account(
        self, billed_entity_account: BilledEntityAccountVO
    ):
        """
        :param billed_entity_account:
        :return:
        Marking the account as an allowance account for the passed billed entity account

        We've introduced is_allowance_account field in the account entity to ensure that this account only has
        allowances attached to it. The reason for this is that an allowance is a negative line item,
        which can't independently be part of an invoice.  Now adding it to a separate account to that of the
        charge means, it should be a separate invoice (because of 1-1 mapping of account -> invoice).
        So now these allowance accounts with no other charge in that account, can be handled by generating credit note
        """
        self.get_billed_entity(
            billed_entity_account.billed_entity_id
        ).mark_account_as_allowance_account(billed_entity_account.account_number)

    def add_allowance(
        self,
        charge_id,
        charge_split_id,
        allowance_dto,
        posting_date,
        post_allowance_tax_details=None,
    ):
        """
        :param charge_id:
        :param charge_split_id:
        :param allowance_dto:
        :param posting_date:
        :param post_allowance_tax_details: this is the final tax details after allowance is added for invoice
        charges calculation, so that we don't have to do back calculation.
        :return:
        Adding an allowance on a charge split using the allowance_dto

        There are two cases here:
            -> If billed_entity_account is not passed in allowance_dto we are deriving billed entity account
               using three options below:
                   1. On same account that charge split is in, if account is not locked. -> First priority
                   2. On an existing unlocked allowance account. -> Second priority
                   3. On a new account. This new account will be marked as allowance account -> Last priority
            -> If the billed_entity_account is passed in the allowance_dto we are validating the same to check
               if it belongs to one of the above accounts.
        After adding the allowance if the charge_split has a credit offered payment attached to it we update
        the credit entry and decrement the amount with the allowance amount
        """
        charge = self.get_charge(charge_id)
        charge_split = charge.get_split(charge_split_id)
        if not allowance_dto.billed_entity_account:
            account = self._get_default_allowance_account_for_charge_split(charge_split)
            allowance_dto.update_billed_entity_account(
                BilledEntityAccountVO(
                    charge_split.billed_entity_account.billed_entity_id,
                    account.account_number,
                )
            )
        else:
            billed_entity = self.get_billed_entity(
                allowance_dto.billed_entity_account.billed_entity_id
            )
            account = billed_entity.get_account(
                allowance_dto.billed_entity_account.account_number
            )
            if not any(
                [
                    account.account_number
                    == charge_split.billed_entity_account.account_number,
                    account.is_allowance_account(),
                    account.is_new(),
                ]
            ):
                raise BillingError(
                    error=BillingErrors.INVALID_ACCOUNT_NUMBER_FOR_ALLOWANCE_ADDITION
                )

        if account.is_locked():
            raise BillingError(error=BillingErrors.ACCOUNT_IS_LOCKED)
        allowance = charge_split.add_allowance(allowance_dto, posting_date)
        charge_split.update_post_allowance_tax_details(post_allowance_tax_details)
        if allowance_dto.consume_at_creation:
            charge_split.consume_allowances(posting_date)
        register_event(AllowancePassedEvent(charge_split_id, charge, allowance))
        return allowance

    def _get_default_allowance_account_for_charge_split(self, charge_split):
        billed_entity = self.get_billed_entity(
            charge_split.billed_entity_account.billed_entity_id
        )
        account = billed_entity.get_account(
            charge_split.billed_entity_account.account_number
        )
        if not account.is_locked():
            return account
        bea = billed_entity.get_account_for_new_assignment(
            charge_type=charge_split.charge_type, allowance_account=True
        )
        if bea:
            self.add_folio_if_not_exists(bea.billed_entity_id, bea.account_number)
            return billed_entity.get_account(bea.account_number)
        account = billed_entity.add_new_account(charge_type=charge_split.charge_type)
        account.mark_as_allowance_account()
        self.add_folio_if_not_exists(
            billed_entity.billed_entity_id, account.account_number
        )
        return account

    def update_allowance(
        self, charge_id, charge_split_id, allowance_id, edit_data, posting_date
    ):
        charge = self.get_charge(charge_id)
        charge_split = charge.get_split(charge_split_id)
        allowance = charge_split.update_allowance(allowance_id, edit_data, posting_date)
        if edit_data.status == ChargeStatus.CANCELLED:
            register_event(AllowanceCancelledEvent(charge_split_id, charge, allowance))
        if edit_data.status == ChargeStatus.CONSUMED:
            register_event(AllowancePostedEvent(charge_split_id, charge, allowance))
        return allowance

    def update_billing_instructions(
        self, charge_id, billing_instructions: List[BillingInstructionVO]
    ):
        """
        Updating billing instructions on a charge, having any charge split with allowance, will change the allowance
        split, and probably reports of certain dates, if allowances are posted on different dates.

        BillingInstructionUpdate comprises of 3 things:
            - Split Percentage change -> Which can result in change in number of Charge Splits
                -> If all allowances are posted on different business dates -> Cannot Allow
                -> If all allowances are posted on current business date, and night audit has not run -> Can Allow
                -> If one of the account is locked -> Cannot allow

            - Billed Entity Account change:
                -> Cannot change the account which is already locked.
                -> If none of the accounts are locked, then can allow the change of billed entity accounts,
                of existing split

            - Payment Instruction Change:
                -> Cannot change for split, whose account is already locked
        """
        if len(list(filter(lambda a: a.is_spot_credit, billing_instructions))) > 1:
            # if 'is_spot_credit' is true then there can be only one billing instruction with 100 percentage
            # In that case: this method is used to merge the multiple charge splits (subtype spot-credit) having same account
            raise SpotCreditIssueError(description="Invalid billing instruction")
        charge = self.get_charge(charge_id)
        if self._is_charge_assigned_to_locked_account(charge):
            raise BillingError(error=BillingErrors.ACCOUNT_IS_LOCKED)

        for billing_instruction in billing_instructions:
            billed_entity = self.get_billed_entity(
                billing_instruction.billed_entity_account.billed_entity_id
            )
            charge_type = ChargeTypes.from_payment_instruction(
                billing_instruction.payment_instruction
            )
            billing_instruction.set_charge_type(charge_type)
            account = billed_entity.create_account_if_not_exists(
                charge_type, billing_instruction.billed_entity_account.account_number
            )
            self.add_folio_if_not_exists(
                billed_entity.billed_entity_id, account.account_number
            )
            if account.is_locked():
                raise BillingError(error=BillingErrors.ACCOUNT_IS_LOCKED)

            if (
                billing_instruction.payment_instruction
                == PaymentInstruction.PAY_AFTER_CHECKOUT
                and not billed_entity.allow_pay_after_checkout()
                and not billing_instruction.is_spot_credit
            ):
                raise BillingError(
                    error=BillingErrors.CREDIT_CHARGE_ONLY_BILL_TO_COMPANY_ERROR
                )

            if billing_instruction.is_spot_credit and (
                billing_instruction.payment_instruction
                == PaymentInstruction.PAY_AT_CHECKOUT
                or billing_instruction.split_percentage != Decimal('100.0')
            ):
                raise SpotCreditIssueError(description="Invalid billing instruction")

            self._validate_account_for_charge_assignment(charge_type, account)
            self._validate_account_type_for_charge(account, charge_type)

            billing_instruction.set_bill_to_type(
                self.derive_bill_to_type(billing_instruction.billed_entity_account)
            )

        charge.update_billing_instructions(billing_instructions)

        if charge.addon_charge_ids:
            inclusion_charges = self.filter_and_get_charges(charge.addon_charge_ids)
            for inclusion_charge in inclusion_charges:
                inclusion_charge.update_billing_instructions(billing_instructions)

        return charge

    def consume_charges(self, charges, business_date):
        if isinstance(business_date, datetime.datetime):
            posting_date = dateutils.to_date(business_date)
        else:
            posting_date = business_date

        inclusion_charge_ids = []
        for c in charges:
            c.consume(posting_date)

            if c.addon_charge_ids:
                inclusion_charge_ids.extend(c.addon_charge_ids)

        consumed_charges = {c.charge_id for c in charges}
        pending_inclusion_charges_to_consume = {
            cid for cid in inclusion_charge_ids if cid not in consumed_charges
        }

        for c in self.filter_and_get_charges(
            pending_inclusion_charges_to_consume,
            allowed_charge_status=[ChargeStatus.CREATED],
        ):
            c.consume(posting_date)

    def consume_charge(self, charge, operation_datetime):
        business_date = dateutils.to_date(operation_datetime)
        if charge.item.name == 'Transferred Charge':
            charge.consume(
                business_date,
                destination_folio_name_map={
                    BilledEntityAccountVO(
                        folio.billed_entity_id, folio.account_number
                    ): self.get_folio_name(folio.folio_number)
                    for folio in self.folios
                },
            )
        else:
            charge.consume(business_date)

        # Post inclusion charges too
        if charge.addon_charge_ids:
            inclusion_charges = self.filter_and_get_charges(charge.addon_charge_ids)
            for inclusion_charge in inclusion_charges:
                self.consume_charge(inclusion_charge, operation_datetime)

    def get_accounts_attached_with_charge(self, charge):
        accounts = []
        for split in charge.charge_splits:
            if split.billed_entity_account:
                billed_entity = self.get_billed_entity(
                    split.billed_entity_account.billed_entity_id
                )
                accounts.append(
                    billed_entity.get_account(
                        split.billed_entity_account.account_number
                    )
                )
        return accounts

    def get_accounts_attached_with_payment(self, payment):
        accounts = []
        for split in payment.payment_splits:
            billed_entity = self.get_billed_entity(
                split.billed_entity_account.billed_entity_id
            )
            accounts.append(
                billed_entity.get_account(split.billed_entity_account.account_number)
            )
        return accounts

    def unlock_accounts(
        self, billed_entity_accounts: List[BilledEntityAccountVO], mark_uninvoiced=False
    ):
        billed_entity_wise_accounts = group_list(
            billed_entity_accounts, 'billed_entity_id'
        )

        for (
            billed_entity_id,
            billed_entity_accounts,
        ) in billed_entity_wise_accounts.items():
            account_numbers = [
                billed_entity_account.account_number
                for billed_entity_account in billed_entity_accounts
            ]
            self.get_billed_entity(billed_entity_id).unlock_accounts(
                account_numbers, mark_uninvoiced=mark_uninvoiced
            )

    def post_payment(self, payment):
        payment.post()

    def post_eligible_payments(self, current_business_date, billed_entity_ids=None):
        for payment in self.payments:
            if dateutils.to_date(
                payment.date_of_payment
            ) <= current_business_date and payment.status in {
                PaymentStatus.DONE,
                PaymentStatus.UNCONFIRMED,
            }:
                if billed_entity_ids is not None:
                    if any(
                        split
                        for split in payment.payment_splits
                        if split.billed_entity_account.billed_entity_id
                        in billed_entity_ids
                    ):
                        payment.post()
                else:
                    payment.post()

    def post_allowances(self, current_business_date):
        for charge in self.active_charges:
            for charge_split in charge.charge_splits:
                charge_split.consume_allowances(posting_date=current_business_date)

    def billed_entity_account_to_string(
        self, billed_entity_account: BilledEntityAccountVO
    ):
        billed_entity_id = billed_entity_account.billed_entity_id
        account_number = billed_entity_account.account_number
        return "{0} (Account: {1})".format(
            self.get_billed_entity(billed_entity_id).name, account_number
        )

    def total_credit_posttax_amount(self):
        return self.total_posttax_amount({ChargeTypes.CREDIT})

    def total_credit_posttax_amount_post_allowance(self):
        return self.total_posttax_amount({ChargeTypes.CREDIT})

    def add_new_account_for_same_account_type(
        self, billed_entity_account: BilledEntityAccountVO
    ):
        billed_entity = self.get_billed_entity(billed_entity_account.billed_entity_id)
        account_type = billed_entity.get_account(
            billed_entity_account.account_number
        ).get_account_type()
        account = billed_entity.add_new_account(
            charge_type=ChargeTypes.CREDIT
            if account_type == ChargeTypes.CREDIT
            else ChargeTypes.NON_CREDIT
        )
        self.add_folio_if_not_exists(
            billed_entity.billed_entity_id, account.account_number
        )
        return BilledEntityAccountVO(
            billed_entity.billed_entity_id, account.account_number
        )

    def transfer_surplus_payment_to_new_account(
        self, from_billed_entity_account, payment_splits_priority=None
    ):
        billed_entity = self.get_billed_entity(
            from_billed_entity_account.billed_entity_id
        )
        net_balance = self.get_net_balance(from_billed_entity_account)
        if net_balance >= Money("0", self.bill.base_currency):
            return
        net_balance = abs(net_balance)
        account = billed_entity.get_or_add_new_account(
            charge_type=ChargeTypes.NON_CREDIT
        )
        self.add_folio_if_not_exists(
            billed_entity.billed_entity_id, account.account_number
        )
        to_billed_entity_account = BilledEntityAccountVO(
            billed_entity.billed_entity_id, account.account_number
        )

        # consolidate multiple payment splits into one
        self.check_and_consolidate_payment_splits(from_billed_entity_account)

        for payment_id, payment_split in self.get_payment_splits(
            from_billed_entity_account,
            PaymentTypes.PAYMENT,
            payment_splits_priority=payment_splits_priority,
        ).items():
            if net_balance <= Money("0", self.bill.base_currency):
                break

            if payment_split.amount <= net_balance:
                payment_amount = payment_split.amount
                # Since entire payment split has to be moved to new account, we can just update new account
                payment_split.update_billed_entity_account(to_billed_entity_account)
                net_balance -= payment_amount
                continue
            else:
                payment = self.get_payment(payment_id)
                # Now payment split amount is greater than remaining net_balance.
                # Reduce current payment split with remaining net_balance, and add new split in new account
                payment_split.update_amount(payment_split.amount - net_balance)
                payment.add_payment_splits(
                    [
                        PaymentSplitData(
                            billed_entity_account=to_billed_entity_account,
                            amount=net_balance,
                        )
                    ]
                )
                break

        return to_billed_entity_account

    def convert_charge_split_to_spot_credit(
        self, charge_id, charge_split_id, bea_for_spot_credit: BilledEntityAccountVO
    ):
        charge = self.get_charge(charge_id)
        billed_entity = self.get_billed_entity(bea_for_spot_credit.billed_entity_id)
        charge.convert_charge_split_to_spot_credit(
            charge_split_id, bea_for_spot_credit, billed_entity.category
        )

    def create_spot_credit_from_existing_charge_split(
        self,
        charge_id,
        charge_split_id,
        amount_for_spot_credit,
        bea_for_spot_credit: BilledEntityAccountVO,
    ):
        charge = self.get_charge(charge_id)
        charge.create_spot_credit_from_existing_charge_split(
            charge_split_id, amount_for_spot_credit, bea_for_spot_credit
        )

    def transfer_surplus_payment_to_different_account(
        self, from_billed_entity_account, to_billed_entity_account, amount
    ):
        for payment_id, payment_split in self.get_payment_splits(
            from_billed_entity_account, PaymentTypes.PAYMENT
        ).items():
            payment = self.get_payment(payment_id)
            for split in payment.payment_splits:
                if split.billed_entity_account != from_billed_entity_account:
                    continue
                transaction_amount = Money('0', currency=payment_split.amount.currency)
                if split.amount < amount:
                    transaction_amount = split.amount
                else:
                    transaction_amount = amount
                amount -= transaction_amount
                split.update_amount(split.amount - transaction_amount)
                if split.amount == 0:
                    split.mark_deleted()
                payment.add_payment_splits(
                    [
                        PaymentSplitData(
                            billed_entity_account=to_billed_entity_account,
                            amount=transaction_amount,
                        )
                    ]
                )
                if transaction_amount != 0:
                    register_event(
                        PaymentRedistributedEvent(
                            payment_id=payment_id,
                            from_account=self.get_folio_name(
                                billed_entity_account=from_billed_entity_account
                            ),
                            to_account=self.get_folio_name(
                                billed_entity_account=to_billed_entity_account
                            ),
                            transferred_amount=transaction_amount,
                        )
                    )
                if amount == 0:
                    break
            if amount == 0:
                break

    def reverse_payments_in_new_account(
        self, from_billed_entity_account, to_billed_entity_account, payment_date
    ):
        next_payment_id = (
            max([payment.payment_id for payment in self._payments])
            if self._payments
            else 0
        )

        payment_splits = self.get_payment_splits(from_billed_entity_account)
        payments = []
        for payment_id, payment_split in payment_splits.items():
            payment = self.get_payment(payment_id)
            if payment.is_refund():
                continue
            else:
                amount_in_payment_currency = (
                    payment.get_proportionate_amount_in_payment_currency_for_split(
                        payment_split.amount
                    )
                )
                next_payment_id += 1
                payment = Payment(
                    next_payment_id,
                    payment_split.amount,
                    payment_date,
                    payment.payment_mode,
                    PaymentTypes.REFUND,
                    dict(source_payment_id=payment.payment_id),
                    payment.status,
                    payment.paid_by,
                    payment.payment_channel,
                    payment.payment_ref_id,
                    payment.paid_to,
                    payment.comment,
                    amount_in_payment_currency,
                    payment.payment_mode_sub_type,
                    payer=None,
                    confirmed=payment.confirmed,
                )
                payment.add_payment_splits(
                    [
                        PaymentSplitData(
                            billed_entity_account=to_billed_entity_account,
                            amount=payment_split.amount,
                        )
                    ]
                )
                payments.append(payment)

        self._payments.extend(payments)
        self.payment_dict.update({p.payment_id: p for p in payments})
        return payments

    def cancel_payment(self, payment):
        if self._is_payment_assigned_to_locked_account(payment):
            raise BillingError(error=BillingErrors.ACCOUNT_IS_LOCKED)
        payment.cancel()

    def cancel_payment_via_refund(self, payment, payment_date):
        # TODO: What do we do when we already have a refund on the payment before cancelling it? How do we determine
        #  splits here?

        # If payment is already cancelled by refund, then we won't process the flow
        if any(
            p.payment_details
            and p.payment_details.get('source_payment_id') == payment.payment_id
            for p in self.payments
        ):
            raise BillingError(error=BillingErrors.REFUND_ALREADY_PASSED_ON_PAYMENT)

        next_payment_id = (
            max([payment.payment_id for payment in self._payments])
            if self._payments
            else 0
        )

        refundable_payment_splits = []
        for split in payment.payment_splits:
            billed_entity_account = split.billed_entity_account
            billed_entity = self.get_billed_entity(
                billed_entity_account.billed_entity_id
            )
            account = billed_entity.get_account(billed_entity_account.account_number)
            if not account.is_locked():
                refundable_payment_splits.append(
                    PaymentSplitData(
                        billed_entity_account=billed_entity_account, amount=split.amount
                    )
                )
        if not refundable_payment_splits:
            raise BillingError(error=BillingErrors.CANNOT_CANCEL_INVOICED_PAYMENT)

        total_refund_amount_in_base_currency = sum(
            split.amount for split in refundable_payment_splits
        )

        if payment.amount_in_payment_currency:
            amount_in_payment_currency = (
                payment.amount_in_payment_currency
                * total_refund_amount_in_base_currency.amount
                / payment.amount.amount
            )
        else:
            amount_in_payment_currency = None

        payment_ref_id = (
            payment.payment_ref_id
            if payment.payment_ref_id
            else generate_short_random_id(prefix='R', length=9)
        )

        refund = Payment(
            payment_id=next_payment_id + 1,
            amount=total_refund_amount_in_base_currency,
            date_of_payment=payment_date,
            payment_mode=payment.payment_mode,
            payment_type=PaymentTypes.REFUND,
            payment_details=dict(source_payment_id=payment.payment_id),
            status=payment.status,
            paid_to=payment.paid_by,
            payment_channel=payment.payment_channel,
            payment_ref_id=payment_ref_id,
            paid_by=payment.paid_to,
            comment="Refund created for payment with ref_id: {0}".format(
                payment_ref_id
            ),
            amount_in_payment_currency=amount_in_payment_currency,
            payment_mode_sub_type=payment.payment_mode_sub_type,
            payer=None,
            confirmed=payment.confirmed,
            payor_billed_entity_id=payment.payor_billed_entity_id,
            refund_reason='Cancellation - Guest Initiated',
        )

        refund.add_payment_splits(refundable_payment_splits)
        register_event(RefundAddedEvent(payments=[refund]))
        self._payments.append(refund)
        self.payment_dict[refund.payment_id] = refund
        return refund

    def copy_payments_in_new_account(
        self,
        from_billed_entity_account,
        to_billed_entity_account,
        payment_date,
        exclude_payment_types=None,
    ):
        next_payment_id = (
            max([payment.payment_id for payment in self._payments])
            if self._payments
            else 0
        )

        payment_splits = self.get_payment_splits(from_billed_entity_account)
        payments = []
        for payment_id, payment_split in payment_splits.items():
            payment = self.get_payment(payment_id)
            if exclude_payment_types and payment.payment_type in exclude_payment_types:
                continue
            amount_in_payment_currency = (
                payment.get_proportionate_amount_in_payment_currency_for_split(
                    payment_split.amount
                )
            )
            next_payment_id += 1
            payment = Payment(
                next_payment_id,
                payment_split.amount,
                payment_date,
                payment.payment_mode,
                payment.payment_type,
                payment.payment_details,
                payment.status,
                payment.paid_to,
                payment.payment_channel,
                payment.payment_ref_id,
                payment.paid_by,
                payment.comment,
                amount_in_payment_currency,
                payment.payment_mode_sub_type,
                payer=None,
                confirmed=payment.confirmed,
            )
            payment.add_payment_splits(
                [
                    PaymentSplitData(
                        billed_entity_account=to_billed_entity_account,
                        amount=payment_split.amount,
                    )
                ]
            )
            payments.append(payment)

        self._payments.extend(payments)
        self.payment_dict.update({p.payment_id: p for p in payments})
        return payments

    def add_refund_in_new_account_using_credit_shell(
        self,
        from_billed_entity_account,
        to_billed_entity_account,
        payment_date,
        credit_shell_id,
    ):
        next_payment_id = (
            max([payment.payment_id for payment in self._payments])
            if self._payments
            else 0
        )
        net_payable = self.get_net_paid_amount(from_billed_entity_account)
        payments = []
        next_payment_id += 1
        refund_to, refund_by = self.get_paid_by_and_paid_to_of_recent_payment_by_the_be(
            from_billed_entity_account.billed_entity_id
        )
        refund_to = PaymentReceiverTypes.GUEST if refund_to is None else refund_to
        refund_by = PaymentReceiverTypes.TREEBO if refund_by is None else refund_by
        payment = Payment(
            next_payment_id,
            net_payable,
            payment_date,
            PaymentModes.CREDIT_SHELL,
            PaymentTypes.REFUND,
            dict(),
            PaymentStatus.DONE,
            refund_to,
            PaymentChannels.FRONT_DESK,
            credit_shell_id,
            refund_by,
            comment=None,
            amount_in_payment_currency=net_payable,
            payment_mode_sub_type=None,
            payer=None,
            confirmed=True,
            payor_billed_entity_id=to_billed_entity_account.billed_entity_id,
            posting_date=payment_date,
            posted_date=dateutils.current_date(),
            refund_reason='Adjustment - Reissue of invoice',
        )
        payment.add_payment_splits(
            [
                PaymentSplitData(
                    billed_entity_account=to_billed_entity_account, amount=net_payable
                )
            ]
        )
        payments.append(payment)

        self._payments.extend(payments)
        self.payment_dict.update({p.payment_id: p for p in payments})
        return payments, payment

    def get_paid_by_and_paid_to_of_recent_payment_by_the_be(self, billed_entity_id):
        available_payments = (
            [
                p
                for p in self.payments
                if p.payment_type == PaymentTypes.PAYMENT
                and p.is_associated_with_billed_entity(billed_entity_id)
                and p.is_active()
            ]
            if billed_entity_id
            else []
        )
        if not available_payments:
            available_payments = [
                p
                for p in self.payments
                if p.payment_type == PaymentTypes.PAYMENT and p.is_active()
            ]
        available_payments = [
            payment for payment in available_payments if payment.created_at
        ]
        if available_payments:
            available_payments = sorted(
                available_payments, key=lambda x: x.created_at, reverse=True
            )
            return available_payments[0].paid_by, available_payments[0].paid_to
        return None, None

    def add_payment_in_new_account_using_credit_shell(
        self,
        billed_entity_account,
        payment_date,
        credit_shell_id,
        net_payable: Money,
        payor_billed_entity_id=None,
    ):
        next_payment_id = (
            max([payment.payment_id for payment in self._payments])
            if self._payments
            else 0
        )
        payments = []
        next_payment_id += 1
        paid_by, paid_to = self.get_paid_by_and_paid_to_of_recent_payment_by_the_be(
            billed_entity_account.billed_entity_id
        )
        paid_to = PaymentReceiverTypes.TREEBO if paid_to is None else paid_to
        paid_by = PaymentReceiverTypes.GUEST if paid_by is None else paid_by
        payment = Payment(
            next_payment_id,
            net_payable,
            payment_date,
            PaymentModes.CREDIT_SHELL,
            PaymentTypes.PAYMENT,
            dict(),
            PaymentStatus.DONE,
            paid_to,
            PaymentChannels.FRONT_DESK,
            credit_shell_id,
            paid_by,
            comment=None,
            amount_in_payment_currency=net_payable,
            payment_mode_sub_type=None,
            payer=None,
            confirmed=True,
            payor_billed_entity_id=payor_billed_entity_id,
        )
        payment.add_payment_splits(
            [
                PaymentSplitData(
                    billed_entity_account=billed_entity_account, amount=net_payable
                )
            ]
        )
        payments.append(payment)

        self._payments.extend(payments)
        self.payment_dict.update({p.payment_id: p for p in payments})
        return payments

    def create_billed_entities_account_if_not_exist(
        self, billed_entity_accounts: [BilledEntityAccountVO]
    ) -> List[AccountSummaryDomainDto]:
        # Check charge type used for account creation

        # TODO : Check Account Sequence & Override account number for random account number

        entity_accounts = []
        entity_accounts_summary = set()
        for billed_entity_account in billed_entity_accounts:
            account_type = billed_entity_account['account_type']
            billed_entity_account = billed_entity_account['billed_entity_account']
            billed_entity = self.get_billed_entity(
                billed_entity_account.billed_entity_id
            )
            if billed_entity.account_exists(billed_entity_account.account_number):
                entity_accounts.append(billed_entity_account)
                folio = self.add_folio_if_not_exists(
                    billed_entity_account.billed_entity_id,
                    billed_entity_account.account_number,
                )
            else:
                if (
                    billed_entity_account.account_number
                    != billed_entity.latest_account_number()
                ):
                    billed_entity_account.account_number = (
                        billed_entity.latest_account_number() + 1
                    )
                account = billed_entity.add_new_account(charge_type=account_type)
                folio = self.add_folio_if_not_exists(
                    billed_entity_account.billed_entity_id, account.account_number
                )
            entity_accounts.append(billed_entity_account)

            entity_accounts_summary.add(
                AccountSummaryDomainDto(
                    billed_entity_account=billed_entity_account,
                    accounts_summary=self.get_account_summary(billed_entity_account),
                    folio_number=folio.folio_number,
                )
            )
        return entity_accounts_summary

    def update_payment_amount(
        self,
        payment,
        amount=None,
        amount_in_payment_currency=None,
        payment_split_dtos=None,
    ):
        if amount:
            payment.amount = amount
            if amount_in_payment_currency is not None:
                payment.amount_in_payment_currency = amount_in_payment_currency
            else:
                payment.amount_in_payment_currency = amount

            if not payment_split_dtos:
                # If payment splits is not getting updated along with amount update, then reset split to allocate to
                # default accounts. Because sum of existing payment splits won't match new amount
                existing_billed_entity = self.get_billed_entity(
                    payment.payment_splits[0].billed_entity_account.billed_entity_id
                )
                # Delete old payment splits
                for split in payment.payment_splits:
                    split.mark_deleted()

                # Attach default billed entity split to payment as per new amount
                self.attach_default_billed_entity_account_to_payments(
                    existing_billed_entity, payments=[payment]
                )

        if payment_split_dtos:
            self.update_payment_splits(payment, payment_split_dtos)

        if payment.amount != sum(split.amount for split in payment.payment_splits):
            raise BillingError(
                error=BillingErrors.PAYMENT_ALLOCATION_DOES_NOT_SUM_UP_TO_PAYMENT_AMOUNT
            )

    def update_payment_splits(
        self, payment, payment_split_dtos: List[PaymentSplitData]
    ):
        if len(
            {split.billed_entity_account.account_number for split in payment_split_dtos}
        ) != len(payment_split_dtos):
            billed_entity_account_wise_new_splits = dict()
            for split_dto in payment_split_dtos:
                if (
                    split_dto.billed_entity_account
                    not in billed_entity_account_wise_new_splits
                ):
                    billed_entity_account_wise_new_splits[
                        split_dto.billed_entity_account
                    ] = split_dto
                else:
                    billed_entity_account_wise_new_splits[
                        split_dto.billed_entity_account
                    ].add_split(split_dto)

            payment_split_dtos = billed_entity_account_wise_new_splits.values()

        # At this point, all the payment splits are allocated to unique billed entity account

        account_wise_net_balance = {
            split.billed_entity_account: self.get_net_balance(
                split.billed_entity_account
            )
            for split in payment.payment_splits
        }

        account_wise_current_split = {
            split.billed_entity_account: split.amount
            for split in payment.payment_splits
        }

        billed_entity_account_type = ChargeTypes.NON_CREDIT
        if not payment.is_payment_or_refund():
            billed_entity_account_type = ChargeTypes.CREDIT

        for payment_split_dto in payment_split_dtos:
            billed_entity = self.get_billed_entity(
                payment_split_dto.billed_entity_account.billed_entity_id
            )
            billed_entity_account = payment_split_dto.billed_entity_account
            account = billed_entity.create_account_if_not_exists(
                billed_entity_account_type, billed_entity_account.account_number
            )
            self.add_folio_if_not_exists(
                billed_entity.billed_entity_id, account.account_number
            )

            self._validate_account_type(account, payment)
            account = billed_entity.get_account(
                payment_split_dto.billed_entity_account.account_number
            )

            if not account.is_locked():
                continue

            # Payment split update won't be allowed if older net balance of an account was positive, and after
            # re-allocation, it'll become negative, and the account is locked
            # We can take out only that much payment from a locked account, till when the balance remains greater
            # than or equal to 0, in that account
            current_balance = account_wise_net_balance.get(billed_entity_account)
            current_split = account_wise_current_split.get(billed_entity_account)
            if not current_balance or not current_split:
                # If this condition is true, then this payment split update cannot decrease the account balance
                continue

            if (
                current_balance - current_split + payment_split_dto.amount
            ).amount > Decimal("0"):
                raise ValidationException(
                    error=BillingErrors.CAN_REDUCE_ALLOCATION_ONLY_TILL_ZERO_ACCOUNT_BALANCE
                )

        payment.update_payment_splits(payment_split_dtos)

    def attach_default_billed_entity_account_to_charges(
        self, billed_entity=None, charges=None, charge_id_to_billed_entity_map=None
    ):
        assert charge_id_to_billed_entity_map or billed_entity

        if not charges:
            charges = self.charges

        for charge in charges:
            if charge_id_to_billed_entity_map:
                billed_entity = charge_id_to_billed_entity_map.get(charge.charge_id)

            if not charge.charge_splits:
                bea = self.get_billed_entity_account_for_new_assignment(
                    billed_entity, charge.type
                )
                self.add_folio_if_not_exists(bea.billed_entity_id, bea.account_number)
                if charge.bill_to_type is None:
                    charge.bill_to_type = self.derive_bill_to_type(
                        BilledEntityAccountVO(
                            billed_entity.billed_entity_id, bea.account_number
                        )
                    )

                charge.add_new_charge_splits(
                    BilledEntityAccountVO(
                        billed_entity.billed_entity_id, bea.account_number
                    )
                )

            else:
                for split in charge.charge_splits:
                    if split.bill_to_type:
                        if (
                            split.bill_to_type == ChargeBillToTypes.COMPANY
                            and billed_entity.category
                            not in {
                                BilledEntityCategory.BOOKER_COMPANY,
                                BilledEntityCategory.TRAVEL_AGENT,
                            }
                        ):
                            billed_entity = (
                                self.get_billed_entity_for_category(
                                    BilledEntityCategory.BOOKER_COMPANY
                                )
                                or billed_entity
                            )

                        elif (
                            split.bill_to_type == ChargeBillToTypes.GUEST
                            and billed_entity.category
                            not in {
                                BilledEntityCategory.PRIMARY_GUEST,
                                BilledEntityCategory.CONSUMING_GUESTS,
                                BilledEntityCategory.BOOKER,
                            }
                        ):
                            billed_entity = (
                                self.get_billed_entity_for_category(
                                    BilledEntityCategory.BOOKER
                                )
                                or billed_entity
                            )

                    bea = billed_entity.get_account_for_new_assignment(
                        split.charge_type
                    )
                    self.add_folio_if_not_exists(
                        bea.billed_entity_id, bea.account_number
                    )

                    billed_entity_account = BilledEntityAccountVO(
                        billed_entity.billed_entity_id, bea.account_number
                    )
                    split.attach_default_billed_entity(billed_entity_account)
                    if split.bill_to_type is None:
                        split.bill_to_type = self.derive_bill_to_type(
                            billed_entity_account
                        )

    def attach_default_billed_entity_account_to_payments(
        self, billed_entity, payments=None
    ):
        # Payments should go in non-credit account only
        bea = billed_entity.get_account_for_new_assignment(ChargeTypes.NON_CREDIT)
        billed_entity_account = BilledEntityAccountVO(
            billed_entity.billed_entity_id, bea.account_number
        )

        if not payments:
            payments = self.payments

        if payments:
            self.add_folio_if_not_exists(bea.billed_entity_id, bea.account_number)

        for payment in payments:
            if payment.is_refund() or payment.is_payment():
                payment.attach_default_billed_entity(billed_entity_account)
            if payment.is_posted():
                payment.payor_billed_entity_id = billed_entity_account.billed_entity_id

    def _is_active_charge_split_associated_with_billed_entity(self, billed_entity):
        for charge in self.get_active_charges():
            for split in charge.charge_splits:
                if (
                    billed_entity.billed_entity_id
                    == split.billed_entity_account.billed_entity_id
                ):
                    return True
        return False

    def _is_active_payment_split_associated_with_billed_entity(self, billed_entity):
        for payment in self.get_active_payments():
            for split in payment.payment_splits:
                if (
                    billed_entity.billed_entity_id
                    == split.billed_entity_account.billed_entity_id
                ):
                    return True
        return False

    def _is_active_payment_associated_with_billed_entity(self, billed_entity):
        for payment in self.get_active_payments():
            if payment.payor_billed_entity_id == billed_entity.billed_entity_id:
                return True
        return False

    def _can_delete_billed_entity(self, billed_entity):
        return (
            not self._is_active_charge_split_associated_with_billed_entity(
                billed_entity
            )
            and not self._is_active_payment_split_associated_with_billed_entity(
                billed_entity
            )
            and not self._is_active_payment_associated_with_billed_entity(billed_entity)
        )

    def safe_delete_billed_entity(self, billed_entity_id):
        if billed_entity_id:
            billed_entity = self.get_billed_entity(billed_entity_id)
            # delete is only allowed if billed entity has no active charge or payments splits
            if self._can_delete_billed_entity(billed_entity):
                billed_entity.delete()
                return billed_entity_id
        return None

    def get_all_billed_entities_of_given_categories(self, categories):
        return [
            billed_entity
            for billed_entity in self.billed_entity_dict.values()
            if not billed_entity.deleted and billed_entity.category in categories
        ]

    def is_billed_entity_single_in_given_category(self, billed_entity_id, category):
        # this will check if given billed entity category has only one billed entity and
        # it's id is same as given billed_entity_id
        # this is to check if billed entity category has no BE other than given BE
        all_billed_entities_of_given_category = (
            self.get_all_billed_entities_of_given_categories([category])
        )
        return (
            len(all_billed_entities_of_given_category) == 1
            and all_billed_entities_of_given_category[0].billed_entity_id
            == billed_entity_id
        )

    def post_payment_by_id(self, payment_id):
        payment = self.get_payment(payment_id)
        payment.status = PaymentStatus.POSTED
        payment.mark_dirty()

    def update_posting_date_of_payment(self, payment_id, payment_date):
        payment = self.get_payment(payment_id)
        payment.posted_date = dateutils.current_date()
        payment.posting_date = payment_date
        payment.mark_dirty()

    def post_payment_and_update_posting_date(self, payment_id, payment_date):
        payment = self.get_payment(payment_id)
        payment.status = PaymentStatus.POSTED
        payment.posted_date = dateutils.current_date()
        payment.posting_date = payment_date
        payment.mark_dirty()

    def update_consuming_guests(self, charge, consuming_guest_ids):
        charge.update_consuming_guests(consuming_guest_ids)

        inclusion_charges = (
            self.get_active_charges(charge.addon_charge_ids)
            if charge.addon_charge_ids
            else []
        )
        for inc_charge in inclusion_charges:
            inc_charge.update_consuming_guests(consuming_guest_ids)

    def update_charge_item_details(self, charge, details):
        charge.update_charge_item_details(details)
        inclusion_charges = (
            self.get_active_charges(charge.addon_charge_ids)
            if charge.addon_charge_ids
            else []
        )
        for inc_charge in inclusion_charges:
            inc_charge.update_charge_item_details(details)

    def update_rate_plan_info_in_charge_item(self, charge_id, rate_plan):
        charge = self.get_charge(charge_id)
        charge_item_details = RoomChargeItemDetails.from_dict(charge.item.details)
        charge_item_details.update_rate_plan_name(rate_plan.name)
        charge_item_details.update_rate_plan_code(rate_plan.rate_plan_code)
        charge_item_details.update_rate_plan_reference_id(
            rate_plan.rate_plan_reference_id
        )
        charge.update_charge_item_details(charge_item_details)

    def save_invoice_numbers_available_for_reuse(
        self,
        billed_entity_account: BilledEntityAccountVO,
        invoice_number=None,
        invoice_date=None,
        hotel_invoice_number=None,
    ):
        self.get_billed_entity(
            billed_entity_account.billed_entity_id
        ).update_account_invoice_numbers_available_for_use(
            billed_entity_account.account_number,
            invoice_number=invoice_number,
            invoice_date=invoice_date,
            hotel_invoice_number=hotel_invoice_number,
        )

    def pop_invoice_number(
        self, billed_entity_account: BilledEntityAccountVO, current_date
    ):
        return self.get_billed_entity(
            billed_entity_account.billed_entity_id
        ).pop_account_invoice_numbers_available_for_use(
            billed_entity_account.account_number, current_date
        )

    def pop_hotel_invoice_number(
        self, billed_entity_account: BilledEntityAccountVO, current_date
    ):
        return self.get_billed_entity(
            billed_entity_account.billed_entity_id
        ).pop_account_hotel_invoice_numbers_available_for_use(
            billed_entity_account.account_number, current_date
        )

    def reverse_bill_side_effect(self, bill_side_effect):
        self.move_cancelled_charges_to_created_state(
            bill_side_effect.cancelled_charge_ids
        )
        self.move_cancelled_allowances_to_created_state(
            bill_side_effect.cancelled_allowances
        )

    def get_ref_ids_for_payment(self, payment_dto: PaymentData):
        payment_ref_ids = [
            p.payment_ref_id
            for p in self.payments
            if p.payment_ref_id == payment_dto.payment_ref_id
            and p.payment_type == PaymentTypes.PAYMENT
        ]
        return payment_ref_ids

    def is_charge_associated_to_billed_entity_account(
        self, billed_entity_account, booked_charges_to_post
    ):
        for charge in self.charges:
            for split in charge.charge_splits:
                if split.billed_entity_account == billed_entity_account:
                    if charge.status in [ChargeStatus.CONSUMED, ChargeStatus.PREVIEW]:
                        return True
                    if (
                        charge.status == ChargeStatus.CREATED
                        and booked_charges_to_post
                        and charge.charge_id in booked_charges_to_post
                    ):
                        return True
                if any(
                    allowance.billed_entity_account == billed_entity_account
                    for allowance in split.allowances
                ):
                    return True

    def is_payment_associated_to_billed_entity_account(self, billed_entity_account):
        for payment in self.payments:
            if any(
                split.billed_entity_account == billed_entity_account
                for split in payment.payment_splits
            ):
                return True

    def _update_bea_in_charge_split(self, charge_id_to_splits_map, bea):
        for charge_id, split_ids in charge_id_to_splits_map.items():
            charge = self.get_charge(charge_id)
            charge.update_billed_entity_account_in_charge_splits(split_ids, bea)

    def update_bea_in_payment_splits(self, payment_id_to_splits_map, bea):
        for payment_id, split_ids in payment_id_to_splits_map.items():
            payment = self.get_payment(payment_id)
            payment.update_billed_entity_account_in_payment_splits(split_ids, bea)

    def update_billed_entity_secondary_category(
        self, billed_entity_id, secondary_category
    ):
        billed_entity = self.get_billed_entity(billed_entity_id)
        billed_entity.update_secondary_category(secondary_category)

    def _create_be_account_and_folio(
        self,
        billed_entity,
        charge_type=ChargeTypes.NON_CREDIT,
        is_allowance_account=False,
    ):
        account = billed_entity.add_new_account(
            charge_type=charge_type, is_allowance_account=is_allowance_account
        )
        self.add_folio_if_not_exists(
            billed_entity.billed_entity_id, account.account_number
        )
        return BilledEntityAccountVO(
            billed_entity_id=billed_entity.billed_entity_id,
            account_number=account.account_number,
        )

    def _group_charges_associated_with_billed_entity(self, billed_entity):
        bea_to_charge_id_map = defaultdict(lambda: defaultdict(list))
        for charge in self.charges:
            for split in charge.charge_splits:
                if (
                    split.billed_entity_account.billed_entity_id
                    == billed_entity.billed_entity_id
                ):
                    bea_to_charge_id_map[split.billed_entity_account.account_number][
                        charge.charge_id
                    ].append(split.charge_split_id)
        return bea_to_charge_id_map

    def _group_payments_associated_with_billed_entity(self, billed_entity):
        bea_to_payment_id_map = defaultdict(lambda: defaultdict(list))
        for payment in self.payments:
            for split in payment.payment_splits:
                if (
                    split.billed_entity_account.billed_entity_id
                    == billed_entity.billed_entity_id
                ):
                    bea_to_payment_id_map[split.billed_entity_account.account_number][
                        payment.payment_id
                    ].append(split.payment_split_id)
        return bea_to_payment_id_map

    def replace_consuming_guest_from_charges(self, old_customer, new_customer):
        for charge in self.charges:
            charge.replace_consuming_guest(old_customer, new_customer)

    def move_all_charges_and_payments_splits_from_one_billed_entity_to_another(
        self, source_be_id, target_be_id
    ):
        source_be = self.get_billed_entity(source_be_id)
        target_be = self.get_billed_entity(target_be_id)
        bea_to_charge_map = self._group_charges_associated_with_billed_entity(source_be)
        bea_to_payments_map = self._group_payments_associated_with_billed_entity(
            source_be
        )
        for bea in source_be.accounts:
            if bea.is_locked():
                raise BillingError(error=BillingErrors.ACCOUNT_IS_LOCKED)
            charge_id_to_splits_map = bea_to_charge_map.get(bea.account_number)
            payment_id_to_splits_map = bea_to_payments_map.get(bea.account_number)
            account = None
            if charge_id_to_splits_map:
                account = self._create_be_account_and_folio(
                    target_be, charge_type=bea.account_type
                )
                self._update_bea_in_charge_split(charge_id_to_splits_map, account)
            if payment_id_to_splits_map:
                account = (
                    self._create_be_account_and_folio(
                        target_be, charge_type=bea.account_type
                    )
                    if account is None
                    else account
                )
                self.update_bea_in_payment_splits(payment_id_to_splits_map, account)
        self.move_payor_billed_entity_id_of_payment_to_booker(
            source_be_id, target_be_id
        )

    def get_active_charges_for_guests(self, guest_ids):
        charges = []
        for charge in self.get_active_charges():
            if all(x in guest_ids for x in charge.charge_to):
                charges.append(charge)
        return charges

    def get_active_charges_for_billed_entity_accounts(self, billed_entity_accounts):
        charges = []
        for charge in self.get_active_charges():
            for charge_split in charge.charge_splits:
                if charge_split.billed_entity_account in billed_entity_accounts:
                    charges.append(charge)
        return charges

    def get_active_charges_for_billed_entity_id(self, billed_entity_id):
        charges = []
        for charge in self.get_active_charges():
            for charge_split in charge.charge_splits:
                if (
                    charge_split.billed_entity_account.billed_entity_id
                    == billed_entity_id
                ):
                    charges.append(charge)
        return charges

    def add_folio_if_not_exists(self, billed_entity_id, account_number):
        folio = [
            folio
            for folio in self.folios
            if folio.billed_entity_id == billed_entity_id
            and folio.account_number == account_number
        ]
        if folio:
            return folio[0]
        if not folio:
            new_folio_number = (
                max([int(folio.folio_number) for folio in self.folios], default=0) + 1
            )
            folio = Folio(
                self.bill_id, billed_entity_id, new_folio_number, account_number
            )
            self._folios.append(folio)
        return folio

    def get_folio(
        self, folio_number=None, billed_entity_account: BilledEntityAccountVO = None
    ) -> Folio:
        folio = None
        if folio_number:
            folio = [
                folio for folio in self.folios if folio.folio_number == folio_number
            ]
        if billed_entity_account:
            folio = [
                folio
                for folio in self.folios
                if folio.billed_entity_id == billed_entity_account.billed_entity_id
                and folio.account_number == billed_entity_account.account_number
            ]
        return folio[0] if folio else None

    def reset_folio_sequence(self):
        if self.folios:
            for f in self.folios:
                f.delete()

    def delete_folio_of_billed_entities(self, billed_entity_ids):
        for f in self.folios or []:
            if f.billed_entity_id in billed_entity_ids:
                f.delete()

    def safe_inactivate_billed_entities(
        self, billed_entity_ids, status=BilledEntityStatus.CANCEL
    ):
        cancelled_billed_entity_ids = []
        for billed_entity_id in billed_entity_ids or []:
            billed_entity = self.get_billed_entity(billed_entity_id)
            if self._can_delete_billed_entity(billed_entity):
                if status.value == BilledEntityStatus.CANCEL.value:
                    billed_entity.mark_cancel()
                if status.value == BilledEntityStatus.NOSHOW.value:
                    billed_entity.mark_noshow()
                if status.value == BilledEntityStatus.INACTIVE.value:
                    billed_entity.mark_inactive()
                cancelled_billed_entity_ids.append(billed_entity_id)
        return cancelled_billed_entity_ids

    def activate_inactive_billed_entitties(self, billed_entity_ids):
        activated_billed_entity_ids = []
        for billed_entity_id in billed_entity_ids or []:
            billed_entity = self.get_billed_entity(billed_entity_id)
            billed_entity.mark_active()
            activated_billed_entity_ids.append(billed_entity_id)
        return activated_billed_entity_ids

    def get_folio_name(
        self, folio_number=None, billed_entity_account: BilledEntityAccountVO = None
    ):
        if not (folio_number or billed_entity_account):
            raise ValidationException(BillingErrors.FOLIO_NOT_FOUND)
        if billed_entity_account:
            folio_number = self.get_folio(
                billed_entity_account=billed_entity_account
            ).folio_number
        elif folio_number:
            billed_entity_account = self.get_folio(
                folio_number=folio_number
            ).get_billed_entity_account()
        billed_entity = self.get_billed_entity(billed_entity_account.billed_entity_id)
        return (
            "Folio "
            + str(folio_number)
            + " - "
            + billed_entity.category.value
            + " - "
            + billed_entity.name.full_name
        )

    def get_total_amount_paid_by_billed_entity(self, billed_entity_id):
        amount = Money("0", self.bill.base_currency)
        for payment in self.payments:
            if (
                payment.payor_billed_entity_id == billed_entity_id
                and payment.payment_type == PaymentTypes.PAYMENT
            ):
                amount += payment.amount
        return amount

    def get_total_amount_refunded_to_billed_entity(self, billed_entity_id):
        amount = Money("0", self.bill.base_currency)
        for payment in self.payments:
            if (
                payment.payor_billed_entity_id == billed_entity_id
                and payment.payment_type == PaymentTypes.REFUND
            ):
                amount += payment.amount
        return amount

    def get_billed_entity_wise_payment_for_folio(self, folio_number):
        billed_entity_wise_payment_map_for_folio = dict()
        for payment in self.payments:
            for payment_split in payment.payment_splits:
                if (
                    payment_split.billed_entity_account
                    == self.get_folio(folio_number).get_billed_entity_account()
                ):
                    billed_entity_wise_payment_map_for_folio[
                        payment_split.billed_entity_account
                    ] = (
                        payment_split.amount
                        + billed_entity_wise_payment_map_for_folio.get(
                            payment_split.billed_entity_account, 0
                        )
                    )
        return billed_entity_wise_payment_map_for_folio

    def move_payor_billed_entity_id_of_payment_to_booker(
        self, source_be_id, target_be_id
    ):
        for payment in self.payments:
            if payment.payor_billed_entity_id == source_be_id:
                payment.payor_billed_entity_id = target_be_id

    def is_tax_slab_based_charge_splits(self, charge_id, grouped_sku_categories):
        charge = self.get_charge(charge_id)
        if (
            len(charge.charge_splits) > 1
            and grouped_sku_categories[
                charge.item.sku_category_id
            ].has_slab_based_taxation
        ):
            return True
        return False

    def has_any_active_charge_with_tax_slab_based_charge_splits(
        self, grouped_sku_categories
    ):
        charges = self.active_charges
        for charge in charges:
            if (
                len(charge.charge_splits) > 1
                and grouped_sku_categories[
                    charge.item.sku_category_id
                ].has_slab_based_taxation
            ):
                return True
        return False

    def is_reissue_allowed(self, charge_map, grouped_sku_categories):
        for charge in self.get_active_charges(charge_map):
            if self.is_tax_slab_based_charge_splits(
                charge.charge_id, grouped_sku_categories
            ):
                return False
        return True

    def get_payment_amount_paid_to_payer(self, payer, payment_id=None):
        net_paid_amount = Money("0", self.bill.base_currency)
        for payment in self.payments:
            if payment.is_active():
                if (
                    payment.paid_to
                    and payment.paid_to in payer
                    and payment.payment_type == PaymentTypes.PAYMENT
                ):
                    net_paid_amount += payment.amount
                elif (
                    payment.paid_by
                    and payment.paid_by in payer
                    and payment.payment_type == PaymentTypes.REFUND
                ):
                    net_paid_amount -= payment.amount
        if payment_id:
            net_paid_amount += self.get_payment(payment_id).amount
        return net_paid_amount

    def get_paid_by_and_paid_to_for_credit_shell(self, credit_shell_id):
        paid_by, paid_to = None, None
        for p in self.payments:
            if (
                p.payment_ref_id == credit_shell_id
                and p.payment_type == PaymentTypes.REFUND
            ):
                paid_by = p.paid_by
                paid_to = p.paid_to
        return paid_by, paid_to

    def add_refund_for_booking_relocation(
        self,
        billed_entity_account,
        payment_date,
        amount,
        paid_to,
    ):
        next_payment_id = (
            max([payment.payment_id for payment in self._payments], default=0) + 1
        )
        net_payable = amount
        payment_ref_id = generate_short_random_id(prefix='R', length=9)
        payment = Payment(
            next_payment_id,
            net_payable,
            payment_date,
            PaymentModes.TRANSFERRED_CREDIT,
            PaymentTypes.REFUND,
            dict(),
            PaymentStatus.DONE,
            paid_to,
            PaymentChannels.FRONT_DESK,
            payment_ref_id,
            PaymentReceiverTypes.TREEBO,
            comment=None,
            amount_in_payment_currency=net_payable,
            payment_mode_sub_type=None,
            payer=None,
            confirmed=True,
            payor_billed_entity_id=billed_entity_account.billed_entity_id,
            posting_date=payment_date,
            posted_date=dateutils.current_date(),
            refund_reason='Booking Relocated',
        )
        payment.add_payment_splits(
            [
                PaymentSplitData(
                    billed_entity_account=billed_entity_account, amount=net_payable
                )
            ]
        )

        self._payments.append(payment)
        self.payment_dict[next_payment_id] = payment
        return [payment], payment

    def add_refund_for_non_refundable_amount(
        self,
        amount,
        billed_entity_account,
        payment_date,
        reason,
        payment_mode,
        paid_by,
        paid_to,
        payment_ref_id,
    ):
        next_payment_id = (
            max([payment.payment_id for payment in self._payments], default=0) + 1
        )
        net_payable = amount
        payment = Payment(
            next_payment_id,
            net_payable,
            payment_date,
            payment_mode,
            PaymentTypes.REFUND,
            dict(),
            PaymentStatus.DONE,
            paid_to,
            PaymentChannels.FRONT_DESK,
            payment_ref_id,
            paid_by,
            comment=None,
            amount_in_payment_currency=net_payable,
            payment_mode_sub_type=None,
            payer=None,
            confirmed=True,
            payor_billed_entity_id=billed_entity_account.billed_entity_id,
            posting_date=payment_date,
            posted_date=dateutils.current_date(),
            refund_reason=reason,
        )
        payment.add_payment_splits(
            [
                PaymentSplitData(
                    billed_entity_account=billed_entity_account, amount=net_payable
                )
            ]
        )

        self._payments.append(payment)
        self.payment_dict[next_payment_id] = payment
        return [payment], payment

    def get_pah_amount(self, payer, billed_entity_account=None):
        net_paid_amount = Money("0", self.bill.base_currency)

        for payment in self.payments:
            if payment.is_active():
                for split in payment.payment_splits:
                    if self.is_valid_payment(
                        payment, split, payer, billed_entity_account
                    ):
                        net_paid_amount += (
                            split.amount
                            if payment.payment_type == PaymentTypes.PAYMENT
                            else -split.amount
                        )
        return net_paid_amount

    @staticmethod
    def is_valid_payment(payment, split, payer, billed_entity_account):
        return (
            (
                payment.paid_to
                and payment.paid_to in payer
                and payment.payment_type == PaymentTypes.PAYMENT
            )
            or (
                payment.paid_by
                and payment.paid_by in payer
                and payment.payment_type == PaymentTypes.REFUND
            )
        ) and (
            not billed_entity_account
            or split.billed_entity_account == billed_entity_account
        )

    @staticmethod
    def categorize_and_sort_payment_splits(payment_splits, payment_splits_priority):
        high_priority_refund_modes = payment_splits_priority.get(
            'high_priority_refund_modes'
        )
        least_priority_refund_modes = payment_splits_priority.get(
            'least_priority_refund_modes'
        )

        high_priority_splits, normal_priority_splits, least_priority_splits = (
            dict(),
            dict(),
            dict(),
        )

        for payment_id, payment_split in payment_splits.items():
            if payment_split:
                if payment_split.payment_mode in high_priority_refund_modes:
                    high_priority_splits[payment_id] = payment_split
                elif payment_split.payment_mode in least_priority_refund_modes:
                    least_priority_splits[payment_id] = payment_split
                else:
                    normal_priority_splits[payment_id] = payment_split

        sorted_high_priority_splits = dict(
            sorted(
                high_priority_splits.items(),
                key=lambda x: high_priority_refund_modes.index(x[1].payment_mode),
            )
        )
        sorted_least_priority_splits = dict(
            sorted(
                least_priority_splits.items(),
                key=lambda x: least_priority_refund_modes.index(x[1].payment_mode),
            )
        )

        return {
            **sorted_high_priority_splits,
            **normal_priority_splits,
            **sorted_least_priority_splits,
        }

    def cancel_preview_charges(self):
        charge_ids_to_cancel = [
            charge.charge_id
            for charge in self.charges
            if charge.status == ChargeStatus.PREVIEW
        ]
        if charge_ids_to_cancel:
            self.cancel_charges(charge_ids_to_cancel)

    def get_payment_splits_for_invoice_templates(
        self,
        billed_entity_account: BilledEntityAccountVO,
        payment_type=None,
    ) -> Dict[int, PaymentSplit]:
        payment_splits = {
            p.payment_id: p.get_payment_splits(billed_entity_account)
            for p in self.payments
            if p.is_active()
            and (payment_type is None or p.payment_type == payment_type)
        }
        return {
            payment_id: payment_split
            for payment_id, payment_split in payment_splits.items()
            if payment_split
        }

    def check_and_consolidate_payment_splits(self, billed_entity_account):
        for p in self.payments:
            if p.is_active() and p.payment_type == PaymentTypes.PAYMENT:
                payment_splits = [
                    ps
                    for ps in p.payment_splits
                    if ps.billed_entity_account == billed_entity_account
                ]
                if len(payment_splits) > 1:
                    self._consolidate_payment_splits(
                        p, payment_splits, billed_entity_account
                    )

    def _consolidate_payment_splits(
        self, payment, payment_splits, billed_entity_account
    ):
        total_amount = Money(0, self.bill.base_currency)
        for splits in payment_splits:
            total_amount += splits.amount
            splits.mark_deleted()

        payment.add_payment_splits(
            [
                PaymentSplitData(
                    billed_entity_account=billed_entity_account,
                    amount=total_amount,
                )
            ]
        )

    def create_payment_for_funding_room_stay(
        self, bill_aggregate, billed_entity, total_funding_payment_amount
    ):
        next_payment_id = (
            max([payment.payment_id for payment in bill_aggregate.payments], default=0)
            + 1
        )
        net_payable = total_funding_payment_amount
        payment_ref_id = generate_short_random_id(prefix='P', length=9)
        payment = Payment(
            next_payment_id,
            net_payable,
            crs_context.current_business_date,
            PaymentModes.TREEBO_EXPENSE,
            PaymentTypes.PAYMENT,
            dict(),
            PaymentStatus.DONE,
            PaymentReceiverTypes.TREEBO,
            PaymentChannels.FRONT_DESK,
            payment_ref_id,
            PaymentReceiverTypes.TREEBO,
            comment="Added to settle funding folio",
            amount_in_payment_currency=net_payable,
            payment_mode_sub_type=None,
            payer=None,
            confirmed=True,
            payor_billed_entity_id=billed_entity.billed_entity_id,
            posting_date=crs_context.current_business_date,
            posted_date=crs_context.current_business_date,
        )
        payment.add_payment_splits(
            [PaymentSplitData(billed_entity_account=billed_entity, amount=net_payable)]
        )
        self._payments.append(payment)
        self.payment_dict[next_payment_id] = payment
        return [payment], payment

    def get_charges_by_sku_id(self, sku_ids):
        charge_details = []
        for charge in self.charges:
            if charge.item and charge.item.item_id and charge.item.item_id in sku_ids:
                charge_details.append(charge)
        return charge_details

    def average_posttax_room_stay_amount(self, charge_ids=None):
        room_charges_with_addons = [
            self.get_post_tax_room_stay_amount(ch.charge_id)
            for ch in self.charges
            if ch.status in {ChargeStatus.CREATED, ChargeStatus.CONSUMED}
            and ch.is_room_rent()
            and (charge_ids is None or ch.charge_id in charge_ids)
        ]
        return sum(room_charges_with_addons) / len(room_charges_with_addons)

    def get_post_tax_room_stay_amount(self, room_stay_charge_id):
        room_stay_charge = self.get_charge(room_stay_charge_id)
        if not room_stay_charge.is_active:
            base_currency = (
                crs_context.hotel_context.base_currency
                if (
                    crs_context.hotel_context
                    and crs_context.hotel_context.base_currency
                )
                else CurrencyType.INR
            )
            return Money(0, base_currency)
        active_addon_charges = (
            self.get_active_charges(room_stay_charge.addon_charge_ids)
            if room_stay_charge.addon_charge_ids
            else []
        )
        return sum(
            ch.posttax_amount_post_allowance
            for ch in [room_stay_charge] + active_addon_charges
        )
