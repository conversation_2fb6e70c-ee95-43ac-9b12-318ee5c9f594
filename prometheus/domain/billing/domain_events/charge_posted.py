"""
Charge Posted Domain Event
"""
from prometheus.core.base_domain_event import MergeableDomainEvent
from prometheus.domain.billing.domain_events.schema.billing import (
    ChargePostedEventSchema,
)
from prometheus.domain.billing.dto.charge_event_data import ChargeEventData
from ths_common.constants.domain_event_constants import DomainEvent


class ChargePostedEvent(MergeableDomainEvent):
    def __init__(self, charge, destination_folio_name_map=None):
        self.charges = [charge]
        self.charge_event_data = []
        self.destination_folio_name_map = destination_folio_name_map

    def serialize(self):
        serialized = (
            ChargePostedEventSchema(many=True).dump(self.charge_event_data).data
        )
        return serialized

    def update_mapping(self, **kwargs):
        for charge in self.charges:
            guest_ids = charge.charge_to

            _guest_ids = []
            billed_entity_accounts = []
            for cs in charge.charge_splits:
                if cs.charge_to:
                    _guest_ids.append(cs.charge_to)
                if cs.billed_entity_account:
                    billed_entity_accounts.append(cs.billed_entity_account)

            if not guest_ids:
                guest_ids = _guest_ids
            self.charge_event_data.append(
                ChargeEventData(
                    charge_id=charge.charge_id,
                    item_name=charge.item.name,
                    posttax_amount=charge.posttax_amount,
                    guest_ids=guest_ids,
                    applicable_date=charge.applicable_date,
                    charge_type=charge.type,
                    bill_to_type=charge.bill_to_type,
                    sku_category_id=charge.item.sku_category_id,
                    room_stay_id=charge.item.details.get('room_stay_id'),
                    billed_entity_accounts=billed_entity_accounts,
                    room_number=charge.item.details.get('room_no'),
                    posting_date=charge.posting_date,
                )
            )

        customer_map = kwargs.get('customer_map')
        sku_category_map = kwargs.get('sku_category_map')
        folio_name_map = kwargs.get('folio_name_map')
        if self.destination_folio_name_map:
            folio_name_map = self.destination_folio_name_map
        for charge in self.charge_event_data:
            charge.set_guest_names(customer_map)
            charge.set_sku_category(sku_category_map)
            charge.set_billed_entity_names(folio_name_map=folio_name_map)

    def event_type(self):
        return DomainEvent.CHARGE_POSTED

    def can_merge(self, mergeable_domain_event):
        return isinstance(mergeable_domain_event, ChargePostedEvent)

    def merge(self, mergeable_domain_event):
        self.charges.extend(mergeable_domain_event.charges)
