class SpotCreditEventData(object):
    def __init__(
        self,
        source_billed_entity_account,
        unpaid_charges,
        destination_billed_entity_account,
        settlement_type,
        source_billed_entity_name=None,
        destination_billed_entity_name=None,
    ):
        self.source_billed_entity_account = source_billed_entity_account
        self.unpaid_charges = unpaid_charges
        self.destination_billed_entity_account = destination_billed_entity_account
        self.settlement_type = settlement_type
        self.source_billed_entity_name = source_billed_entity_name
        self.destination_billed_entity_name = destination_billed_entity_name

    def set_billed_entity_names(self, folio_name_map):
        self.source_billed_entity_name = folio_name_map.get(
            self.source_billed_entity_account
        )
        self.destination_billed_entity_name = folio_name_map.get(
            self.destination_billed_entity_account
        )
