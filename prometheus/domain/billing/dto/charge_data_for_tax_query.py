class ChargeDataForTaxQuery(object):
    def __init__(
        self,
        id,
        pretax_amount,
        posttax_amount,
        applicable_date,
        charge_item=None,
        sku_category_id=None,
        buyer_gst_details=None,
    ):
        self.id = id
        self.applicable_date = applicable_date
        self.pretax_amount = pretax_amount
        self.posttax_amount = posttax_amount
        self.charge_item = charge_item
        if charge_item:
            self.sku_category_id = charge_item.sku_category_id
        elif sku_category_id:
            self.sku_category_id = sku_category_id
        self.buyer_gst_details = buyer_gst_details
