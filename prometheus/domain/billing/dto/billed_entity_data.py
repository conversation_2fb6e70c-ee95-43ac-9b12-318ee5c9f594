from ths_common.constants.billing_constants import (
    BilledEntityCategory,
    BilledEntityStatus,
)


class BilledEntityData(object):
    def __init__(
        self,
        name,
        category: BilledEntityCategory,
        secondary_category: BilledEntityCategory = None,
        status=BilledEntityStatus.ACTIVE,
    ):
        self.name = name
        self.category = category
        self.secondary_category = secondary_category
        self.status = status
