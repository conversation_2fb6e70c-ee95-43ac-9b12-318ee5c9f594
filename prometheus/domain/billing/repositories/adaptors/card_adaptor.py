from treebo_commons.money import Money
from treebo_commons.money.constants import CurrencyType

from prometheus.domain.billing.entities.card import Card
from prometheus.domain.billing.models import CardModel
from prometheus.infrastructure.database.base_db_to_domain_entity_adaptor import (
    BaseAdaptor,
)
from ths_common.constants.billing_constants import CardTypes


class CardAdaptor(BaseAdaptor):
    def to_db_entity(self, domain_entity: Card, **kwargs):
        # noinspection PyArgumentList
        return CardModel(
            card_id=domain_entity.card_id,
            bill_id=domain_entity.bill_id,
            holder_name=domain_entity.holder_name,
            expiry=domain_entity.expiry,
            card_type=domain_entity.card_type.value
            if domain_entity.card_type
            else None,
            token=domain_entity.token,
            last_digits=domain_entity.last_digits,
            bin=domain_entity.bin,
            brand=domain_entity.brand,
            pre_auth_code=domain_entity.pre_auth_code,
            pre_auth_amount=domain_entity.pre_auth_amount.amount
            if domain_entity.pre_auth_amount
            else None,
            currency=domain_entity.pre_auth_amount.currency.value
            if domain_entity.pre_auth_amount
            else None,
            billed_entity_id=domain_entity.billed_entity_id,
        )

    def to_domain_entity(self, db_entity: CardModel, **kwargs):
        return Card(
            card_id=db_entity.card_id,
            bill_id=db_entity.bill_id,
            holder_name=db_entity.holder_name,
            expiry=db_entity.expiry,
            card_type=CardTypes(db_entity.card_type) if db_entity.card_type else None,
            token=db_entity.token,
            last_digits=db_entity.last_digits,
            bin=db_entity.bin,
            brand=db_entity.brand,
            pre_auth_amount=Money(
                db_entity.pre_auth_amount, CurrencyType(db_entity.currency)
            )
            if db_entity.pre_auth_amount
            else None,
            pre_auth_code=db_entity.pre_auth_code,
            billed_entity_id=db_entity.billed_entity_id,
        )
