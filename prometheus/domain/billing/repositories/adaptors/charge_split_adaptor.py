from collections import defaultdict
from typing import List

from treebo_commons.money import Money
from treebo_commons.money.constants import CurrencyType

from prometheus.domain.billing.entities.billed_entity import BilledEntityAccountVO
from prometheus.domain.billing.entities.charge_split import ChargeSplit
from prometheus.domain.billing.models import ChargeSplitModel
from ths_common.constants.billing_constants import (
    ChargeBillToTypes,
    ChargeSubTypes,
    ChargeTypes,
)


class ChargeSplitDBAdapter(object):
    @staticmethod
    def to_db_model(bill_id, charge_id, charge_splits: List[ChargeSplit]):
        charge_split_models = []
        for charge_split in charge_splits:
            model = ChargeSplitModel(
                charge_split_id=charge_split.charge_split_id,
                charge_id=charge_id,
                bill_id=bill_id,
                charge_to=charge_split.charge_to,
                pre_tax=charge_split.pre_tax.amount,
                tax=charge_split.tax.amount,
                post_tax=charge_split.post_tax.amount,
                tax_details=charge_split.tax_details,
                invoice_id=charge_split.invoice_id,
                credit_note_id=charge_split.credit_note_id,
                percentage=charge_split.percentage,
                billed_entity_id=charge_split.billed_entity_account.billed_entity_id
                if charge_split.billed_entity_account
                else None,
                billed_entity_account_number=charge_split.billed_entity_account.account_number
                if charge_split.billed_entity_account
                else None,
                charge_type=charge_split.charge_type.value
                if charge_split.charge_type
                else None,
                charge_sub_type=charge_split.charge_sub_type.value
                if charge_split.charge_sub_type
                else None,
                bill_to_type=charge_split.bill_to_type.value
                if charge_split.bill_to_type
                else None,
                payment_id=charge_split.payment_id,
                deleted=charge_split.deleted,
                post_allowance_tax_details=charge_split.post_allowance_tax_details,
            )
            charge_split_models.append(model)
        return charge_split_models

    @staticmethod
    def to_entities(charge_split_models, allowance_map, base_currency):
        charge_splits_map = defaultdict(list)
        base_currency = (
            base_currency
            if isinstance(base_currency, CurrencyType)
            else CurrencyType(base_currency)
        )
        for charge_split_model in charge_split_models:
            charge_id = charge_split_model.charge_id
            charge_split = ChargeSplit(
                charge_split_id=charge_split_model.charge_split_id,
                charge_to=charge_split_model.charge_to,
                pre_tax=Money(charge_split_model.pre_tax, base_currency),
                tax=Money(charge_split_model.tax, base_currency),
                post_tax=Money(charge_split_model.post_tax, base_currency),
                tax_details=charge_split_model.tax_details,
                percentage=charge_split_model.percentage,
                invoice_id=charge_split_model.invoice_id,
                credit_note_id=charge_split_model.credit_note_id,
                charge_type=ChargeTypes(charge_split_model.charge_type)
                if charge_split_model.charge_type
                else None,
                charge_sub_type=ChargeSubTypes(charge_split_model.charge_sub_type)
                if charge_split_model.charge_sub_type
                else None,
                bill_to_type=ChargeBillToTypes(charge_split_model.bill_to_type)
                if charge_split_model.bill_to_type
                else None,
                billed_entity_account=BilledEntityAccountVO(
                    billed_entity_id=charge_split_model.billed_entity_id,
                    account_number=charge_split_model.billed_entity_account_number,
                )
                if charge_split_model.billed_entity_id
                else None,
                payment_id=charge_split_model.payment_id,
                deleted=charge_split_model.deleted,
                allowances=allowance_map.get(
                    (charge_split_model.charge_id, charge_split_model.charge_split_id)
                ),
                post_allowance_tax_details=charge_split_model.post_allowance_tax_details,
                dirty=False,
                new=False,
            )
            charge_splits_map[charge_id].append(charge_split)

        return charge_splits_map
