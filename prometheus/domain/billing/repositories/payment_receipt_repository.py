from object_registry import register_instance
from prometheus.domain.billing.aggregates.payment_receipt_aggregate import (
    PaymentReceiptAggregate,
)
from prometheus.domain.billing.models import PaymentReceiptModel
from prometheus.domain.billing.repositories.adaptors.payment_receipt_adapter import (
    PaymentReceiptAdaptor,
)
from prometheus.infrastructure.database.base_repository import BaseRepository
from ths_common.exceptions import AggregateNotFound, ResourceNotFound


@register_instance()
class PaymentReceiptRepository(BaseRepository):
    payment_receipt_adapter = PaymentReceiptAdaptor()

    def to_aggregate(self, **kwargs):
        payment_receipt_model = kwargs.get("payment_receipt_model")
        payment_receipt_entity = self.payment_receipt_adapter.to_domain_entity(
            db_entity=payment_receipt_model
        )
        return PaymentReceiptAggregate(payment_receipt=payment_receipt_entity)

    def from_aggregate(self, aggregate: PaymentReceiptAggregate = None):
        payment_receipt_model = self.payment_receipt_adapter.to_db_entity(
            domain_entity=aggregate.payment_receipt
        )
        return payment_receipt_model

    def save(self, payment_receipt_aggregate: PaymentReceiptAggregate):
        self._save(self.from_aggregate(aggregate=payment_receipt_aggregate))
        self.flush_session()

    def update(self, payment_receipt_aggregate: PaymentReceiptAggregate):
        payment_receipt_models = self.from_aggregate(
            aggregate=payment_receipt_aggregate
        )
        self._update(payment_receipt_models)
        self.flush_session()

    def load_for_bill_id(self, bill_id):
        payment_receipt_models = self.filter(
            PaymentReceiptModel, PaymentReceiptModel.bill_id == bill_id
        ).all()
        aggregates = [
            self.to_aggregate(payment_receipt_model=model)
            for model in payment_receipt_models
        ]
        return aggregates

    def load(self, payment_id, bill_id):
        q = self.query(PaymentReceiptModel).filter(
            PaymentReceiptModel.bill_id == bill_id,
            PaymentReceiptModel.payment_id == payment_id,
        )
        payment_receipt_model = q.one_or_none()
        if payment_receipt_model is None:
            return None
        return self.to_aggregate(payment_receipt_model=payment_receipt_model)

    def load_for_update(self, payment_id, bill_id):
        payment_receipt_model = self.get_for_update(
            PaymentReceiptModel, payment_id=payment_id, bill_id=bill_id
        )
        if payment_receipt_model is None:
            return None
        return self.to_aggregate(payment_receipt_model=payment_receipt_model)
