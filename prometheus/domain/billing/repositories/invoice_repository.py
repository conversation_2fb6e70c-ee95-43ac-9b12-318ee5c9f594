# coding=utf-8
"""
invoice repository
"""
import json
import logging

import jsonpickle
from sqlalchemy import and_, func, or_, tuple_
from sqlalchemy.dialects.postgresql import array
from sqlalchemy.orm import aliased
from treebo_commons.money import Money
from treebo_commons.money.constants import CurrencyType
from treebo_commons.utils import dateutils

from object_registry import register_instance
from prometheus import crs_context
from prometheus.domain.billing.aggregates.invoice_aggregate import InvoiceAggregate
from prometheus.domain.billing.dto.erp_event_details_dto import (
    ErpInvoiceAndCreditNoteDto,
    ErpInvoiceDetailsDto,
)
from prometheus.domain.billing.dto.finance_erp_dtos import InvoiceDetailsDto
from prometheus.domain.billing.entities.billed_entity import BilledEntityAccountVO
from prometheus.domain.billing.entities.invoice import Invoice
from prometheus.domain.billing.entities.invoice_charge import InvoiceCharge
from prometheus.domain.billing.models import (
    AccountModel,
    BillModel,
    FolioModel,
    InvoiceChargeModel,
    InvoiceModel,
)
from prometheus.domain.booking.models import BookingModel
from prometheus.infrastructure.database.base_repository import BaseRepository
from ths_common.constants.billing_constants import (
    ChargeBillToTypes,
    ChargeStatus,
    ChargeTypes,
    InvoiceStatus,
    IssuedByType,
    IssuedToType,
)
from ths_common.constants.expense_constants import ItemCodeTypes
from ths_common.exceptions import AggregateNotFound, OutdatedVersion
from ths_common.utils.common_utils import DateTimeEncoder, group_list, json_dumps
from ths_common.value_objects import (
    Address,
    BookingBillParentInfo,
    ChargeItem,
    GSTDetails,
    InvoiceBillToInfo,
    InvoiceChargeToInfo,
    InvoiceIssuedByInfo,
    ItemCode,
    Name,
    PhoneNumber,
    VendorDetails,
)

logger = logging.getLogger(__name__)


class InvoiceDBAdapter(object):
    @staticmethod
    def to_db_model(invoice):
        user_info_map_dict = None
        if invoice.user_info_map:
            user_info_map_dict = {
                key: val.to_json() for key, val in invoice.user_info_map.items()
            }

        invoice_model = InvoiceModel(
            bill_id=invoice.bill_id,
            invoice_id=invoice.invoice_id,
            invoice_number=invoice.invoice_number,
            parent_info=json.loads(
                json.dumps(invoice.parent_info, cls=DateTimeEncoder)
            ),
            bill_to=invoice.bill_to.to_json() if invoice.bill_to else None,
            status=invoice.status.value,
            generated_by=invoice.generated_by,
            generation_channel=invoice.generation_channel,
            pretax_amount=invoice.pretax_amount.amount,
            tax_amount=invoice.tax_amount.amount,
            posttax_amount=invoice.posttax_amount.amount,
            invoice_url=invoice.invoice_url,
            invoice_date=invoice.invoice_date,
            invoice_due_date=invoice.invoice_due_date,
            vendor_id=invoice.vendor_id,
            allowed_charge_to_ids=invoice.allowed_charge_to_ids,
            bill_to_type=invoice.bill_to_type.value if invoice.bill_to_type else None,
            allowed_charge_types=[
                charge_type.value for charge_type in invoice.allowed_charge_types
            ]
            if invoice.allowed_charge_types
            else None,
            vendor_details=invoice.vendor_details.to_json(),
            user_info_map=user_info_map_dict,
            tax_details_breakup=json.loads(json_dumps(invoice.tax_details_breakup)),
            deleted=invoice.deleted,
            version=invoice.version,
            issued_to_type=invoice.issued_to_type.value,
            issued_by_type=invoice.issued_by_type.value,
            issued_by=invoice.issued_by.to_json() if invoice.issued_by else None,
            hotel_invoice_id=invoice.hotel_invoice_id,
            signed_url=invoice.signed_url,
            signed_url_expiry_time=invoice.signed_url_expiry_time,
            irn=invoice.irn,
            qr_code=invoice.qr_code,
            signed_invoice=invoice.signed_invoice,
            irp_ack_number=invoice.irp_ack_number,
            irp_ack_date=invoice.irp_ack_date,
            is_einvoice=invoice.is_einvoice,
            billed_entity_id=invoice.billed_entity_account.billed_entity_id
            if invoice.billed_entity_account
            else None,
            billed_entity_account_number=invoice.billed_entity_account.account_number
            if invoice.billed_entity_account
            else None,
            is_downloaded=invoice.is_downloaded,
            is_reissue_allowed=invoice.is_reissue_allowed,
            is_spot_credit=invoice.is_spot_credit,
        )
        return invoice_model

    @staticmethod
    def to_entity(invoice_model, base_currency):
        vendor_details, bill_to, user_info_map_dict, parent_info = (
            invoice_model.vendor_details,
            invoice_model.bill_to,
            invoice_model.user_info_map,
            invoice_model.parent_info,
        )
        issued_by = invoice_model.issued_by
        try:
            vendor_details = VendorDetails.from_json(
                json.loads(vendor_details)
                if isinstance(vendor_details, str)
                else vendor_details
            )
        except:
            vendor_details = jsonpickle.decode(
                vendor_details, keys=True, classes=[GSTDetails, PhoneNumber]
            )

        if bill_to:
            try:
                bill_to = InvoiceBillToInfo.from_json(
                    json.loads(bill_to) if isinstance(bill_to, str) else bill_to
                )
            except:
                bill_to = jsonpickle.decode(
                    bill_to, keys=True, classes=[GSTDetails, PhoneNumber, Address, Name]
                )

        user_info_map = dict()
        if user_info_map_dict:
            try:
                user_info_map_dict = (
                    json.loads(user_info_map_dict)
                    if isinstance(user_info_map_dict, str)
                    else user_info_map_dict
                )
                for key, val in user_info_map_dict.items():
                    user_info_map[key] = InvoiceChargeToInfo.from_json(val)
            except:
                user_info_map = jsonpickle.decode(
                    user_info_map, keys=True, classes=[InvoiceChargeToInfo]
                )

        if parent_info:
            try:
                parent_info = (
                    json.loads(parent_info)
                    if isinstance(parent_info, str)
                    else parent_info
                )
            except:
                parent_info = jsonpickle.decode(
                    parent_info, keys=True, classes=[BookingBillParentInfo]
                )

        issued_by = InvoiceIssuedByInfo.from_json(
            json.loads(issued_by) if isinstance(issued_by, str) else issued_by
        )

        invoice = Invoice(
            bill_id=invoice_model.bill_id,
            invoice_id=invoice_model.invoice_id,
            invoice_number=invoice_model.invoice_number,
            vendor_id=invoice_model.vendor_id,
            vendor_details=vendor_details,
            invoice_date=invoice_model.invoice_date,
            invoice_due_date=invoice_model.invoice_due_date,
            parent_info=parent_info,
            bill_to=bill_to,
            status=InvoiceStatus(invoice_model.status),
            generated_by=invoice_model.generated_by,
            generation_channel=invoice_model.generation_channel,
            pretax_amount=Money(invoice_model.pretax_amount, base_currency),
            tax_amount=Money(invoice_model.tax_amount, base_currency),
            posttax_amount=Money(invoice_model.posttax_amount, base_currency),
            invoice_url=invoice_model.invoice_url,
            allowed_charge_to_ids=invoice_model.allowed_charge_to_ids,
            bill_to_type=ChargeBillToTypes(invoice_model.bill_to_type)
            if invoice_model.bill_to_type
            else None,
            user_info_map=user_info_map,
            allowed_charge_types=[
                ChargeTypes(charge_type)
                for charge_type in invoice_model.allowed_charge_types
            ]
            if invoice_model.allowed_charge_types
            else None,
            tax_details_breakup=invoice_model.tax_details_breakup,
            deleted=invoice_model.deleted,
            version=invoice_model.version,
            issued_to_type=IssuedToType(invoice_model.issued_to_type),
            issued_by_type=IssuedByType(invoice_model.issued_by_type),
            issued_by=issued_by,
            hotel_invoice_id=invoice_model.hotel_invoice_id,
            created_at=dateutils.localize_datetime(invoice_model.created_at),
            modified_at=dateutils.localize_datetime(invoice_model.modified_at),
            irn=invoice_model.irn,
            qr_code=invoice_model.qr_code,
            signed_invoice=invoice_model.signed_invoice,
            irp_ack_number=invoice_model.irp_ack_number,
            irp_ack_date=invoice_model.irp_ack_date,
            is_einvoice=invoice_model.is_einvoice,
            signed_url=invoice_model.signed_url,
            signed_url_expiry_time=dateutils.localize_datetime(
                invoice_model.signed_url_expiry_time
            ),
            base_currency=base_currency,
            billed_entity_account=BilledEntityAccountVO(
                invoice_model.billed_entity_id,
                invoice_model.billed_entity_account_number,
            )
            if invoice_model.billed_entity_id
            else None,
            is_downloaded=invoice_model.is_downloaded,
            is_reissue_allowed=invoice_model.is_reissue_allowed
            if invoice_model.is_reissue_allowed is not None
            else True,
            is_spot_credit=invoice_model.is_spot_credit
            if invoice_model.is_spot_credit is not None
            else False,
        )
        return invoice


class InvoiceChargeDBAdapter(object):
    @staticmethod
    def to_db_models(invoice_id, invoice_charges):
        # noinspection PyArgumentList
        return [
            InvoiceChargeModel(
                invoice_charge_id=ic.invoice_charge_id,
                invoice_id=invoice_id,
                charge_id=ic.charge_id,
                charge_type=ic.charge_type.value,
                bill_to_type=ic.bill_to_type.value,
                created_by=ic.created_by,
                pretax_amount=ic.pretax_amount.amount,
                tax_amount=ic.tax_amount.amount,
                posttax_amount=ic.posttax_amount.amount,
                tax_details=ic.tax_details,
                item_name=ic.charge_item.name,
                item_code=ic.charge_item.sku_category_id,
                item_detail=ic.charge_item.details,
                item_hsn_code=ic.charge_item.item_code.value,
                applicable_date=ic.applicable_date,
                comment=ic.comment,
                charge_to_ids=ic.charge_to_ids,
                deleted=ic.deleted,
                charge_status=ic.charge_status.value,
                recorded_time=ic.recorded_time,
                charge_split_ids=ic.charge_split_ids,
                credit_note_generated_amount=ic.credit_note_generated_amount.amount,
            )
            for ic in invoice_charges
        ]

    @staticmethod
    def to_entities(invoice_charge_models, base_currency):
        invoice_charges = []

        for invoice_charge_model in invoice_charge_models:
            charge_item = ChargeItem(
                name=invoice_charge_model.item_name,
                sku_category_id=invoice_charge_model.item_code,
                details=invoice_charge_model.item_detail,
                hsn_code=ItemCode(
                    ItemCodeTypes.HSN, invoice_charge_model.item_hsn_code
                ),
            )
            invoice_charge = InvoiceCharge(
                invoice_charge_id=invoice_charge_model.invoice_charge_id,
                charge_id=invoice_charge_model.charge_id,
                pretax_amount=Money(invoice_charge_model.pretax_amount, base_currency),
                tax_amount=Money(invoice_charge_model.tax_amount, base_currency),
                tax_details=invoice_charge_model.tax_details,
                posttax_amount=Money(
                    invoice_charge_model.posttax_amount, base_currency
                ),
                charge_type=ChargeTypes(invoice_charge_model.charge_type),
                bill_to_type=ChargeBillToTypes(invoice_charge_model.bill_to_type),
                charge_status=ChargeStatus(invoice_charge_model.charge_status),
                recorded_time=invoice_charge_model.recorded_time,
                applicable_date=dateutils.localize_datetime(
                    invoice_charge_model.applicable_date
                ),
                comment=invoice_charge_model.comment,
                created_by=invoice_charge_model.created_by,  # Auth id
                charge_item=charge_item,
                charge_to_ids=invoice_charge_model.charge_to_ids,
                deleted=invoice_charge_model.deleted,
                charge_split_ids=invoice_charge_model.charge_split_ids,
                credit_note_generated_amount=Money(
                    invoice_charge_model.credit_note_generated_amount, base_currency
                )
                if invoice_charge_model.credit_note_generated_amount
                else Money(0, base_currency),
                dirty=False,
                new=False,
            )
            invoice_charges.append(invoice_charge)
        return invoice_charges


@register_instance()
class InvoiceRepository(BaseRepository):
    """
    Invoice repository
    """

    def to_aggregate(self, **kwargs):
        base_currency = (
            CurrencyType(kwargs.get('bill_base_currency'))
            if kwargs.get('bill_base_currency')
            else CurrencyType.INR
        )
        invoice_model, invoice_charge_models = kwargs.get('invoice_model'), kwargs.get(
            'invoice_charge_models'
        )
        # TODO adapter here
        invoice = InvoiceDBAdapter.to_entity(invoice_model, base_currency)
        invoice_charges = (
            InvoiceChargeDBAdapter.to_entities(invoice_charge_models, base_currency)
            if invoice_charge_models
            else None
        )
        return InvoiceAggregate(invoice, invoice_charges)

    def from_aggregate(self, aggregate=None):
        invoice_aggregate = aggregate
        invoice = invoice_aggregate.invoice
        invoice_model = InvoiceDBAdapter.to_db_model(invoice)
        if not invoice_aggregate._invoice_charges:
            return invoice_model, []
        invoice_charge_models = InvoiceChargeDBAdapter.to_db_models(
            invoice.invoice_id, invoice_aggregate._invoice_charges
        )
        return invoice_model, invoice_charge_models

    @staticmethod
    def get_updated_models(aggregate: InvoiceAggregate):
        invoice_aggregate = aggregate
        invoice = invoice_aggregate.invoice
        invoice_model = InvoiceDBAdapter.to_db_model(invoice)
        if not invoice_aggregate._invoice_charges:
            return invoice_model, []
        invoice_charge_models = InvoiceChargeDBAdapter.to_db_models(
            invoice.invoice_id,
            [
                ic
                for ic in invoice_aggregate._invoice_charges
                if ic.has_changes_to_be_saved()
            ],
        )
        return invoice_model, invoice_charge_models

    def verify_version(self, invoice_id, version):
        invoice = self.get(InvoiceModel, invoice_id=invoice_id)
        return version == invoice.version

    def save(self, invoice_aggregate):
        """

        Args:
            invoice_aggregate:

        Returns:

        """
        invoice_model, invoice_charge_models = self.from_aggregate(
            aggregate=invoice_aggregate
        )
        self._save(invoice_model)
        self._save_all(invoice_charge_models)
        self.flush_session()

    def save_all(self, invoice_aggregates):
        """

        Args:
            invoice_aggregates:

        Returns:

        """
        invoice_models = []
        all_invoice_charge_models = []
        for invoice_aggregate in invoice_aggregates:
            invoice_model, invoice_charge_models = self.from_aggregate(
                aggregate=invoice_aggregate
            )
            invoice_models.append(invoice_model)
            all_invoice_charge_models.extend(invoice_charge_models)

        self._save_all(invoice_models)
        self._save_all(all_invoice_charge_models)
        self.flush_session()

    def update(self, invoice_aggregate):
        """

        Args:
            invoice_aggregate:

        Returns:

        """
        invoice_aggregate.increment_version()
        invoice_model, invoice_charge_models = self.get_updated_models(
            aggregate=invoice_aggregate
        )
        self._update(invoice_model)
        if invoice_charge_models:
            self._update_all(invoice_charge_models)
        self.flush_session()

    def update_invoice_charge(self, invoice_aggregate):
        invoice_aggregate.increment_version()
        _, invoice_charge_models = self.get_updated_models(aggregate=invoice_aggregate)
        if invoice_charge_models:
            self._update_all(invoice_charge_models)
        self.flush_session()

    def update_all(self, invoice_aggregates):
        """

        Args:
            invoice_aggregates:

        Returns:

        """
        invoice_models = []
        all_invoice_charge_models = []
        for invoice_aggregate in invoice_aggregates:
            invoice_aggregate.increment_version()
            invoice_model, invoice_charge_models = self.get_updated_models(
                aggregate=invoice_aggregate
            )
            invoice_models.append(invoice_model)
            if invoice_charge_models:
                all_invoice_charge_models.extend(invoice_charge_models)
        self._update_all(invoice_models)
        self._update_all(all_invoice_charge_models)
        self.flush_session()

    def _create_invoice_aggregates(self, invoice_models, load_charges=True):
        invoice_ids = [invoice.invoice_id for invoice in invoice_models]
        grouped_invoice_charge_models = dict()
        if load_charges:
            subq = None
            if len(invoice_ids) > 100:
                subq = (
                    self.session()
                    .query(func.unnest(array(invoice_ids)).label('invoice_ids'))
                    .subquery()
                )

            q = self.query(InvoiceChargeModel)
            if len(invoice_ids) > 100:
                q = q.join(subq, InvoiceChargeModel.invoice_id == subq.c.invoice_ids)
            else:
                q = q.filter(InvoiceChargeModel.invoice_id.in_(invoice_ids))
            invoice_charge_models = q.order_by(InvoiceChargeModel.applicable_date.asc())

            grouped_invoice_charge_models = group_list(
                invoice_charge_models, 'invoice_id'
            )

        if invoice_models:
            bill_base_currency = self.filter(
                BillModel.base_currency, BillModel.bill_id == invoice_models[0].bill_id
            ).one()[0]

        return [
            self.to_aggregate(
                invoice_model=invoice_model,
                invoice_charge_models=grouped_invoice_charge_models.get(
                    invoice_model.invoice_id
                ),
                bill_base_currency=bill_base_currency,
            )
            for invoice_model in invoice_models
        ]

    def load_for_bill_id(
        self,
        bill_id,
        load_charges=True,
        exclude_statuses=None,
        billed_entity_accounts: [BilledEntityAccountVO] = None,
        order_by_created_at=False,
        exclude_issued_to_reseller=True,
    ):
        """
        Load all the invoices for a given bill_id
        Args:
            bill_id: bill id to be used as the filter
            load_charges:
            exclude_statuses: Will return invoices with status other than status provided here
            billed_entity_accounts: Filter invoices according to billed_entity_accounts(include)
            order_by_created_at:

        """
        invoice_models = self.filter(InvoiceModel, InvoiceModel.bill_id == bill_id)

        if exclude_issued_to_reseller:
            invoice_models = invoice_models.filter(
                InvoiceModel.issued_to_type != IssuedToType.RESELLER.value
            )

        if exclude_statuses:
            invoice_models = invoice_models.filter(
                InvoiceModel.status.notin_(exclude_statuses)
            )

        if billed_entity_accounts:
            invoice_models = invoice_models.filter(
                tuple_(
                    InvoiceModel.billed_entity_id,
                    InvoiceModel.billed_entity_account_number,
                ).in_(
                    [
                        (acc.billed_entity_id, acc.account_number)
                        for acc in billed_entity_accounts
                    ]
                )
            )

        if order_by_created_at:
            invoice_models.order_by(InvoiceModel.created_at.desc())

        invoice_models = invoice_models.all()
        return self._create_invoice_aggregates(
            invoice_models, load_charges=load_charges
        )

    def load_for_bill_ids_with_yield_per(
        self, bill_ids, status_to_be_filtered=None, load_charges=True
    ):
        """
        Load all the invoices for given bill_ids
        Args:
            bill_ids: bill ids to be used as the filter
            status_to_be_filtered: List of Status for which the Invoice should not be loaded.
            load_charges: if charges should be loaded

        Returns:

        """
        q = self.query(InvoiceModel).yield_per(1000).enable_eagerloads(False)
        if status_to_be_filtered:
            q = q.filter(InvoiceModel.status.notin_(status_to_be_filtered))
        else:
            q = q.filter(InvoiceModel.status.notin_([InvoiceStatus.PREVIEW.value]))
        if len(bill_ids) > 100:
            subq = (
                self.session()
                .query(func.unnest(array(bill_ids)).label('bill_ids'))
                .subquery()
            )
            invoice_models = q.join(subq, InvoiceModel.bill_id == subq.c.bill_ids)
        else:
            invoice_models = q.filter(InvoiceModel.bill_id.in_(bill_ids))
        return self._create_invoice_aggregates(
            invoice_models.all(), load_charges=load_charges
        )

    def load(self, invoice_id):
        """
        load invoice
        Args:
            invoice_id:

        Returns:

        """
        invoice_model = self.get(InvoiceModel, invoice_id=invoice_id)
        if not invoice_model:
            raise AggregateNotFound("Invoice", invoice_id)
        invoice_charge_models = self.filter(
            InvoiceChargeModel, InvoiceChargeModel.invoice_id == invoice_id
        ).order_by(InvoiceChargeModel.applicable_date.asc())
        bill_base_currency = self.filter(
            BillModel.base_currency, BillModel.bill_id == invoice_model.bill_id
        ).one()[0]
        if not invoice_charge_models:
            logger.warning(
                f"Missing invoice_charge_models for invoice_id: {invoice_model.invoice_id}. Invoice Status: "
                f"{invoice_model.status}. Invoice Created Timestamp: {invoice_model.created_at}"
            )
        return self.to_aggregate(
            invoice_model=invoice_model,
            invoice_charge_models=invoice_charge_models,
            bill_base_currency=bill_base_currency,
        )

    def load_by_invoice_numbers(self, invoice_numbers, load_charges=True):
        invoice_models = self.query(InvoiceModel).filter(
            InvoiceModel.invoice_number.in_(invoice_numbers)
        )
        return self._create_invoice_aggregates(
            invoice_models.all(), load_charges=load_charges
        )

    def load_all(self, invoice_ids, load_charges=True):
        q = self.query(InvoiceModel)
        if len(invoice_ids) > 100:
            subq = (
                self.session()
                .query(func.unnest(array(invoice_ids)).label('invoice_ids'))
                .subquery()
            )
            invoice_models = q.join(subq, InvoiceModel.invoice_id == subq.c.invoice_ids)
        else:
            invoice_models = q.filter(InvoiceModel.invoice_id.in_(invoice_ids))

        return self._create_invoice_aggregates(
            invoice_models.all(), load_charges=load_charges
        )

    def load_all_customer_invoices_by_invoice_numbers(self, invoice_numbers):
        return (
            self.query(InvoiceModel)
            .with_entities(InvoiceModel.invoice_id, InvoiceModel.invoice_number)
            .filter(
                InvoiceModel.issued_to_type == IssuedToType.CUSTOMER.value,
                InvoiceModel.invoice_number.in_(invoice_numbers),
            )
        )

    def load_all_customer_invoices_with_wrong_invoice_strategy(
        self, invoice_from_date, invoice_to_date=None
    ):
        expected_year_prefix = invoice_from_date.year % 2000
        q1 = (
            self.query(InvoiceModel)
            .with_entities(InvoiceModel.invoice_id, InvoiceModel.invoice_number)
            .filter(
                InvoiceModel.issued_by_type == IssuedByType.RESELLER.value,
                InvoiceModel.issued_to_type == IssuedToType.CUSTOMER.value,
                InvoiceModel.invoice_date >= invoice_from_date,
                InvoiceModel.status.in_(
                    [InvoiceStatus.LOCKED.value, InvoiceStatus.GENERATED.value]
                ),
                or_(
                    InvoiceModel.invoice_number.notlike(
                        func.concat(
                            'R', InvoiceModel.vendor_id, expected_year_prefix, '%'
                        )
                    ),
                    func.length(InvoiceModel.invoice_number) != 16,
                ),
            )
        )
        q2 = (
            self.query(InvoiceModel)
            .with_entities(InvoiceModel.invoice_id, InvoiceModel.invoice_number)
            .filter(
                InvoiceModel.issued_by_type == IssuedByType.HOTEL.value,
                InvoiceModel.issued_to_type == IssuedToType.CUSTOMER.value,
                InvoiceModel.invoice_date >= invoice_from_date,
                InvoiceModel.status.in_(
                    [InvoiceStatus.LOCKED.value, InvoiceStatus.GENERATED.value]
                ),
                or_(
                    InvoiceModel.invoice_number.notlike(
                        func.concat(InvoiceModel.vendor_id, expected_year_prefix, '%')
                    ),
                    func.length(InvoiceModel.invoice_number) != 16,
                ),
            )
        )
        if invoice_to_date:
            q1 = q1.filter(InvoiceModel.invoice_date <= invoice_to_date)
            q2 = q2.filter(InvoiceModel.invoice_date <= invoice_to_date)
        return q1.union(q2)

    # TODO: Add separate function to load latest version and make version mandatory here. This need to be done in
    # all repo
    def load_for_update(self, invoice_id, version=None):
        invoice_model = self.get_for_update(InvoiceModel, invoice_id=invoice_id)
        if not invoice_model:
            raise AggregateNotFound("Invoice", invoice_id)
        if version is not None and invoice_model.version != version:
            raise OutdatedVersion('Invoice', version, invoice_model.version)

        invoice_charge_models = self.filter(
            InvoiceChargeModel, InvoiceChargeModel.invoice_id == invoice_id
        ).order_by(InvoiceChargeModel.applicable_date.asc())
        bill_base_currency = self.filter(
            BillModel.base_currency, BillModel.bill_id == invoice_model.bill_id
        ).one()[0]
        if not invoice_charge_models:
            logger.warning(
                f"Missing invoice_charge_models for invoice_id: {invoice_model.invoice_id}. Invoice Status: "
                f"{invoice_model.status}. Invoice Created Timestamp: {invoice_model.created_at}"
            )
        return self.to_aggregate(
            invoice_model=invoice_model,
            invoice_charge_models=invoice_charge_models,
            bill_base_currency=bill_base_currency,
        )

    def load_all_for_update(
        self, invoice_ids: list, invoice_versions: list = None, nowait=True
    ):
        invoice_models = self.filter(
            InvoiceModel,
            InvoiceModel.invoice_id.in_(invoice_ids),
            for_update=True,
            nowait=nowait,
        ).order_by(InvoiceModel.invoice_id)
        invoice_aggregates = self._create_invoice_aggregates(invoice_models.all())

        if invoice_versions:
            grouped_invoices = {
                invoice_aggregate.invoice.invoice_id: invoice_aggregate
                for invoice_aggregate in invoice_aggregates
            }

            for invoice_id, requested_version in zip(invoice_ids, invoice_versions):
                current_invoice = grouped_invoices.get(invoice_id)
                if not current_invoice:
                    raise AggregateNotFound("Invoice", invoice_id)
                if current_invoice.invoice.version != requested_version:
                    raise OutdatedVersion(
                        'Invoice',
                        requested_version,
                        current_invoice.invoice.version,
                        entity_id=invoice_id,
                    )
        return invoice_aggregates

    def load_for_night_audit(
        self,
        vendor_ids,
        exclude_statuses,
        current_business_date,
        offset=None,
        limit=None,
        for_update=False,
    ):
        q = self.filter(InvoiceModel, for_update=for_update)
        q = q.filter(InvoiceModel.vendor_id.in_(vendor_ids))
        q = q.filter(~InvoiceModel.status.in_(exclude_statuses))

        q = q.order_by(InvoiceModel.created_at)

        if limit is not None and offset is not None:
            q = q.limit(limit).offset(offset)

        invoice_models = q.all()
        return self._create_invoice_aggregates(invoice_models)

    def invoice_report_query(
        self,
        start_date,
        end_date,
        hotel_ids,
        bill_ids=None,
        issued_by_type=None,
        issued_to_type=None,
    ):
        q = self.query(InvoiceModel).yield_per(1000).enable_eagerloads(False)

        # Building inner query with or condition
        q1 = []
        if start_date and end_date:
            q1 = [
                func.Date(InvoiceModel.created_at) >= start_date,
                func.Date(InvoiceModel.created_at) <= end_date,
            ]
        if hotel_ids:
            q1.append(InvoiceModel.vendor_id.in_(hotel_ids))

        q1 = and_(*q1)

        or_query = [q1]
        if bill_ids:
            q2 = InvoiceModel.bill_id.in_(bill_ids)
            or_query.append(q2)

        # Attaching inner or condition query to q
        q = q.filter(
            InvoiceModel.status.notin_(
                [InvoiceStatus.PREVIEW.value, InvoiceStatus.CANCELLED.value]
            ),
            or_(*or_query),
        )
        q = q.filter(InvoiceModel.deleted == False)

        if issued_by_type:
            q = q.filter(InvoiceModel.issued_by_type == issued_by_type.value)

        if issued_to_type:
            q = q.filter(InvoiceModel.issued_to_type == issued_to_type.value)

        invoice_models = q.all()
        return self._create_invoice_aggregates(invoice_models)

    def get_hotel_invoice_mapping(self, customer_invoice_ids):
        invoice_model_sale = aliased(InvoiceModel)
        invoice_model_purchase = aliased(InvoiceModel)
        query = self.query(
            invoice_model_sale.invoice_number, invoice_model_purchase.invoice_number
        ).join(
            invoice_model_purchase,
            and_(
                invoice_model_sale.hotel_invoice_id
                == invoice_model_purchase.invoice_id,
                invoice_model_sale.invoice_id.in_(customer_invoice_ids),
            ),
        )
        return {
            customer_cn_number: hotel_cn_number
            for customer_cn_number, hotel_cn_number in query.all()
        }

    def load_all_with_yield_per(self, invoice_ids):
        q = (
            self.query(InvoiceModel)
            .yield_per(1000)
            .enable_eagerloads(False)
            .filter(InvoiceModel.status != InvoiceStatus.PREVIEW.value)
        )

        if len(invoice_ids) > 100:
            subq = (
                self.session()
                .query(func.unnest(array(invoice_ids)).label('invoice_ids'))
                .subquery()
            )
            invoice_models = q.join(subq, InvoiceModel.invoice_id == subq.c.invoice_ids)
        else:
            invoice_models = q.filter(InvoiceModel.invoice_id.in_(invoice_ids))

        return self._create_invoice_aggregates(invoice_models)

    def get_einvoices_without_irn(
        self, invoice_ids=None, hotel_ids=None, bill_ids=None
    ):
        q = self.query(InvoiceModel).yield_per(1000).enable_eagerloads(False)
        q = q.filter((InvoiceModel.irn == '') | (InvoiceModel.irn.is_(None)))
        q = q.filter(InvoiceModel.is_einvoice == True)

        if invoice_ids:
            q = q.filter(InvoiceModel.invoice_id.in_(invoice_ids))

        if hotel_ids:
            q = q.filter(InvoiceModel.vendor_id.in_(hotel_ids))

        if bill_ids:
            q = q.filter(InvoiceModel.bill_id.in_(bill_ids))

        models = q.all()
        return self._create_invoice_aggregates(models)

    def load_bill_ids_by_invoice_ids(self, invoice_ids=None):
        if not invoice_ids:
            return []
        q = self.query(InvoiceModel.bill_id)
        subq = (
            self.session()
            .query(func.unnest(array(invoice_ids)).label('invoice_ids'))
            .subquery()
        )
        invoice_models = q.join(subq, InvoiceModel.invoice_id == subq.c.invoice_ids)

        invoice_models = invoice_models.all()
        return [inv_model.bill_id for inv_model in invoice_models if invoice_models]

    def get_total_invoiced_credit_amount_on_given_business_date(
        self, business_date, hotel_id, booking_statuses=None
    ) -> Money:
        q = (
            self.query(func.sum(InvoiceChargeModel.posttax_amount))
            .filter(
                InvoiceChargeModel.charge_type == ChargeTypes.CREDIT.value,
                InvoiceChargeModel.deleted.is_(False),
            )
            .join(
                InvoiceModel, InvoiceChargeModel.invoice_id == InvoiceModel.invoice_id
            )
        )

        if booking_statuses:
            q = q.join(BookingModel, InvoiceModel.bill_id == BookingModel.bill_id)
            q = q.filter(BookingModel.status.in_(booking_statuses))

        q = q.filter(InvoiceModel.vendor_id == hotel_id)
        q = q.filter(
            InvoiceModel.invoice_date == business_date,
            InvoiceModel.status == InvoiceStatus.LOCKED.value,
            InvoiceModel.deleted.is_(False),
        )
        total_invoice_credit = q.first()[0]
        return (
            Money(total_invoice_credit, crs_context.get_hotel_context().base_currency)
            if total_invoice_credit
            else Money(0, crs_context.get_hotel_context().base_currency)
        )

    def get_invoice_details_for_invoice_ids(self, invoice_ids):
        q = self.query(
            InvoiceModel.invoice_id,
            InvoiceModel.invoice_number,
            InvoiceModel.irn,
            InvoiceModel.irp_ack_number,
            InvoiceModel.irp_ack_date,
            FolioModel.folio_number,
            InvoiceModel.bill_id,
            InvoiceModel.billed_entity_id,
        )
        q = q.join(FolioModel, FolioModel.bill_id == InvoiceModel.bill_id)
        q = q.filter(
            InvoiceModel.invoice_id.in_(invoice_ids),
            InvoiceModel.billed_entity_id == FolioModel.billed_entity_id,
            InvoiceModel.billed_entity_account_number == FolioModel.account_number,
        )
        invoices = q.all()
        invoice_details = [ErpInvoiceDetailsDto(invoice) for invoice in invoices]
        return invoice_details

    def get_credit_invoices_on_given_business_date(self, hotel_id, business_date):
        q = self.query(
            InvoiceModel.posttax_amount,
            InvoiceModel.bill_id,
            InvoiceModel.billed_entity_id,
            FolioModel.folio_number,
        )
        q = q.join(AccountModel, AccountModel.bill_id == InvoiceModel.bill_id)
        q = q.join(FolioModel, FolioModel.bill_id == InvoiceModel.bill_id)
        q = q.filter(
            InvoiceModel.bill_id == AccountModel.bill_id,
            InvoiceModel.billed_entity_id == AccountModel.billed_entity_id,
            InvoiceModel.billed_entity_account_number == AccountModel.account_number,
            InvoiceModel.billed_entity_id == FolioModel.billed_entity_id,
            InvoiceModel.billed_entity_account_number == FolioModel.account_number,
            AccountModel.account_type == ChargeTypes.CREDIT.value,
            InvoiceModel.status != InvoiceStatus.CANCELLED.value,
            InvoiceModel.invoice_date == business_date,
            InvoiceModel.vendor_id == hotel_id,
        )
        invoices = q.all()
        invoice_details = [ErpInvoiceAndCreditNoteDto(invoice) for invoice in invoices]
        return invoice_details

    def get_invoices_on_given_invoice_date(self, hotel_id, invoice_date):
        q = self.query(
            InvoiceModel.invoice_id,
            InvoiceModel.bill_id,
            InvoiceModel.billed_entity_id,
            FolioModel.folio_number,
            InvoiceModel.invoice_number,
            InvoiceModel.billed_entity_account_number,
        )
        q = q.join(FolioModel, FolioModel.bill_id == InvoiceModel.bill_id)
        q = q.filter(
            InvoiceModel.billed_entity_id == FolioModel.billed_entity_id,
            InvoiceModel.billed_entity_account_number == FolioModel.account_number,
            InvoiceModel.status == InvoiceStatus.LOCKED.value,
            InvoiceModel.invoice_date == invoice_date,
            InvoiceModel.vendor_id == hotel_id,
        )
        invoices = q.all()
        invoice_details = [ErpInvoiceAndCreditNoteDto(invoice) for invoice in invoices]
        return invoice_details

    def get_invoice_data_for_given_bills(self, bill_ids):
        q = self.query(
            InvoiceModel.invoice_id,
            InvoiceModel.bill_id,
            InvoiceModel.billed_entity_id,
            InvoiceModel.billed_entity_account_number,
        )
        q = q.filter(
            InvoiceModel.bill_id.in_(bill_ids), InvoiceModel.deleted.is_(False)
        )
        invoices = q.all()
        invoice_details = [InvoiceDetailsDto(invoice) for invoice in invoices]
        return invoice_details
