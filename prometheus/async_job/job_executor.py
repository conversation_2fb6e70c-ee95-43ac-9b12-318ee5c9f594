import logging

from flask.cli import with_appcontext
from sentry_sdk.integrations.serverless import serverless_function

from object_registry import inject
from prometheus.application.decorators import session_manager
from prometheus.async_job.job.repositories.job_repository import JobRepository
from prometheus.async_job.job_registry import JobRegistry
from prometheus.async_job.job_result_dto import JobResultDto
from prometheus.common.decorators import consumer_middleware
from prometheus.easyjoblite.response import EasyResponse
from ths_common.constants.scheduled_job_constants import (
    JOB_FAILURE_CODE,
    JOB_SUCCESS_CODE,
    JobStatus,
)

logger = logging.getLogger(__name__)


@serverless_function
@consumer_middleware
@with_appcontext
@session_manager(commit=True)
@inject(job_registry=JobRegistry, job_repo=JobRepository)
def execute_job(job_data, job_registry, job_repo):
    # TODO: Moving this function will fail the Job already pushed to RMQ to find this method and run.
    #  For moving this method, please ensure that RMQ is empty, and job scheduler is stopped
    logger.info("Executing job with id: %s", job_data['data']['job_id'])
    job_aggregate = job_repo.get_job(job_data['data']['job_id'], with_lock=True)

    if job_aggregate.job_entity.status != JobStatus.PROCESSING:
        logger.info(
            "Job status is not valid for execution: %s. Skipping execution",
            job_aggregate.job_entity.status,
        )
        return EasyResponse(JOB_SUCCESS_CODE)

    mark_picked_for_execution(job_aggregate, job_repo)

    try:
        process_job(job_repo, job_data, job_registry, job_aggregate)

    except Exception as e:
        logger.exception('Error while executing job: %s', job_data)
        failure_message = str(e)
        job_aggregate.failed(failure_message)
        job_repo.update(job_aggregate)

    return (
        EasyResponse(JOB_FAILURE_CODE)
        if job_aggregate.is_failed()
        else EasyResponse(JOB_SUCCESS_CODE)
    )


@session_manager(commit=True)
def mark_picked_for_execution(job_aggregate, job_repo):
    job_aggregate.mark_picked_for_execution()
    job_repo.update(job_aggregate)


@session_manager(commit=True)
def process_job(job_repo, job_data, job_registry, job_aggregate):
    handler_function = job_registry.get_executor(job_data['data']['job_name'])
    job_result_dto: JobResultDto = handler_function(**job_data['data']['job_args'])
    if job_result_dto:
        logger.info(
            "Job execution status for job id: %s -> %s",
            job_aggregate.job_entity.job_id,
            job_result_dto.run_successful,
        )

        if job_result_dto.should_retry:
            job_aggregate.reschedule(
                job_result_dto.retry_at_eta, reason=job_result_dto.remarks
            )
        elif not job_result_dto.run_successful:
            job_aggregate.failed(job_result_dto.remarks)
        else:
            job_aggregate.completed()
    else:
        logger.info(
            "Job execution status for job id: %s -> %s",
            job_aggregate.job_entity.job_id,
            True,
        )
        job_aggregate.completed()

    job_repo.update(job_aggregate)
