class AsyncJobDTO:
    def __init__(self, job_name, data, hotel_id=None, seller_id=None):
        self.job_name = job_name
        self.data = data
        self.hotel_id = hotel_id
        self.seller_id = seller_id


class ScheduledJobDTO(AsyncJobDTO):
    def __init__(self, job_name, hotel_id, data, eta, seller_id=None):
        super().__init__(job_name, data, hotel_id=hotel_id, seller_id=seller_id)
        self.eta = eta
