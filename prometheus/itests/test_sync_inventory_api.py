from prometheus.tests.factories.aggregate_factories import HotelFactory
from ths_common.constants.scheduled_job_constants import JobName

super_admin_user_type = {'X-User-Type': 'super-admin'}


def test_inventory_sync_api_schedules_jobs_for_all_hotels(client, hotel_repo, job_repo):
    hotel2 = HotelFactory(hotel__hotel_id="0016933")
    hotel_repo.save(hotel2)

    registration_key = JobName.BULK_SYNC_INVENTORY_ASYNC_JOB_NAME.value

    response = client.post("/v1/sync-inventories", headers=super_admin_user_type)
    assert response.status_code == 200

    inventory_sync_jobs = job_repo.get_all_jobs()
    assert len(inventory_sync_jobs) == 2
    assert all(
        [
            inventory_sync_job.job_entity.job_name == registration_key
            for inventory_sync_job in inventory_sync_jobs
        ]
    )
