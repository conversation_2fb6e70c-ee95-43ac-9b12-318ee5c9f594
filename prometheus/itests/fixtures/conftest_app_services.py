import pytest

from object_registry import locate_instance
from prometheus.application.booking.helpers.billed_entity_service import (
    BilledEntityService,
)
from prometheus.application.booking.helpers.e_reg_card_template_service import (
    ERegCardTemplateService,
)
from prometheus.application.booking.helpers.room_stay_cancellation_handler import (
    RoomStayCancellationHandler,
)
from prometheus.application.booking.helpers.web_checkin_service import WebCheckinService
from prometheus.application.hotel_settings.tenant_settings import TenantSettings
from prometheus.domain.billing.services.charge_edit_service import ChargeEditService
from prometheus.domain.booking.services.addon_domain_service import AddonDomainService
from prometheus.domain.booking.services.cancellation_charge_calculator import (
    CancellationChargeCalculator,
)


@pytest.fixture(scope='session')
def charge_edit_service():
    return locate_instance(ChargeEditService)


@pytest.fixture(scope='session')
def addon_domain_service():
    return locate_instance(AddonDomainService)


@pytest.fixture(scope='session')
def tenant_settings():
    return locate_instance(TenantSettings)


@pytest.fixture(scope='session')
def eregcard_template_service():
    return locate_instance(ERegCardTemplateService)


@pytest.fixture(scope='session')
def web_checkin_application_service():
    return locate_instance(WebCheckinService)


@pytest.fixture(scope='session')
def room_stay_cancellation_handler():
    return locate_instance(RoomStayCancellationHandler)


@pytest.fixture(scope='session')
def billed_entity_service():
    return locate_instance(BilledEntityService)


@pytest.fixture(scope='session')
def cancellation_charge_calculator():
    return locate_instance(CancellationChargeCalculator)
