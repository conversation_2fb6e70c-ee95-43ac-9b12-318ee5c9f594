import datetime

import pytest
from treebo_commons.utils import dateutils

from prometheus import crs_context
from prometheus.domain.catalog.models import RoomModel
from prometheus.domain.hotel_config.aggregates.hotel_config_aggregate import (
    HotelConfigAggregate,
)
from prometheus.domain.hotel_config.entities.hotel_config import HotelConfig
from prometheus.infrastructure.database import db_engine
from prometheus.tests.factories.aggregate_factories import (
    HotelFactory,
    ResellerGSTAggregateFactory,
    RoomAggregateFactory,
    RoomTypeAggregateFactory,
)
from ths_common.constants.hotel_constants import ManagedBy


@pytest.fixture(scope="function")
def active_hotel_aggregate(app, hotel_repo):
    hotel_aggregate = hotel_repo.load("0016932")
    hotel_aggregate.hotel.current_business_date = dateutils.to_date(
        dateutils.yesterday()
    )
    hotel_repo.update(hotel_aggregate)
    return hotel_aggregate


@pytest.fixture(scope='function', autouse=True)
def hotel_config_crs(active_hotel_aggregate, hotel_repo, hotel_config_repo):
    hotel_config_aggregate = hotel_config_repo.load("0016932")
    crs_context.set_hotel_context(active_hotel_aggregate, hotel_config_aggregate)


@pytest.fixture
def hotel_with_switch_over_time_in_next_10_minutes(active_hotel_aggregate):
    ten_minutes_from_now = dateutils.add(dateutils.current_datetime(), minutes=10)
    ten_minutes_from_now_str = datetime.datetime.strftime(
        ten_minutes_from_now, "%H:%M:%S"
    )
    active_hotel_aggregate.hotel.switch_over_time = ten_minutes_from_now_str
    from prometheus import crs_context

    crs_context.set_hotel_context(active_hotel_aggregate)
    return active_hotel_aggregate


@pytest.fixture
def some_hotel():
    return HotelFactory()


@pytest.fixture
def some_hotel_just_past_switchover_time(active_hotel_aggregate, hotel_repo):
    old_switch_over_time = active_hotel_aggregate.hotel.switch_over_time
    old_free_late_checkout_time = active_hotel_aggregate.hotel.free_late_checkout_time

    switchover_time = str(
        (dateutils.current_datetime() - datetime.timedelta(minutes=30)).time()
    ).split(".")[0]
    free_late_checkout_time = str(
        (dateutils.current_datetime() - datetime.timedelta(minutes=15)).time()
    ).split(".")[0]

    active_hotel_aggregate.hotel.switch_over_time = switchover_time
    active_hotel_aggregate.hotel.free_late_checkout_time = free_late_checkout_time
    hotel_repo.update(active_hotel_aggregate)

    yield active_hotel_aggregate

    active_hotel_aggregate.hotel.switch_over_time = old_switch_over_time
    active_hotel_aggregate.hotel.free_late_checkout_time = old_free_late_checkout_time
    hotel_repo.update(active_hotel_aggregate)


@pytest.fixture(scope='session', autouse=True)
def initialize_crs_db(
    app, hotel_repo, hotel_config_repo, room_type_repo, room_repo, reseller_gst_repo
):
    yesterday_ = dateutils.to_date(dateutils.yesterday())
    hotel_aggregate = HotelFactory(
        hotel__hotel_id="0016932", hotel__current_business_date=yesterday_
    )
    hotel_repo.save(hotel_aggregate)

    hotel_config = HotelConfig(
        hotel_id="0016932",
        migration_start_date=dateutils.current_datetime(),
        migration_end_date=dateutils.current_datetime(),
        live_date=dateutils.current_datetime(),
        managed_by=ManagedBy.CRS,
    )

    hotel_config_aggregate = HotelConfigAggregate(hotel_config=hotel_config)
    hotel_config_repo.save(hotel_config_aggregate)

    room_type_aggregate = RoomTypeAggregateFactory()
    room_type_repo.save(room_type_aggregate)
    room_type_aggregate = RoomTypeAggregateFactory(
        room_type__room_type_id='rt02', room_type__type="OAK"
    )
    room_type_repo.save(room_type_aggregate)

    room_aggregate = RoomAggregateFactory(room__room_id="15", room__room_type_id="rt01")
    room_repo.save(room_aggregate)
    room_aggregate = RoomAggregateFactory(
        room__room_id="160", room__room_type_id="rt02", room__room_number='302'
    )
    room_repo.save(room_aggregate)
    reseller_gst = ResellerGSTAggregateFactory()
    reseller_gst_repo.save(reseller_gst)

    db_engine.get_session(None).commit()


@pytest.fixture()
def temp_create_new_room(request, room_repo):
    room_type_id = (
        request.param.get('room_type_id', 'rt01')
        if hasattr(request, 'param')
        else 'rt01'
    )
    room_aggregate = RoomAggregateFactory(
        room__room_id="16", room__room_type_id=room_type_id
    )
    room_repo.save(room_aggregate)
    db_engine.get_session().commit()

    yield room_aggregate

    room_repo.filter(RoomModel, RoomModel.room_id == "16").delete()
    db_engine.get_session().commit()
