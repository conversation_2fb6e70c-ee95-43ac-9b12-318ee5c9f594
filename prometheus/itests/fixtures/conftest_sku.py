import pytest

from prometheus.tests.factories.aggregate_factories import SkuCategoryAggregateFactory
from prometheus.tests.factories.entity_factories import ExpenseItemFactory
from ths_common.constants.funding_constants import FundingExpenseItem


@pytest.fixture(scope='session', autouse=True)
def expense_item_setup(expense_repo):
    items = [
        ExpenseItemFactory(
            expense_item_id="booking_cancellation",
            name="booking_cancellation",
            sku_category_id="stay",
            sku_id="booking_cancellation",
        ),
        ExpenseItemFactory(
            expense_item_id="no_show",
            name="no_show",
            sku_category_id="stay",
            sku_id="noshow",
        ),
        ExpenseItemFactory(
            expense_item_id="123",
            name="lunch",
            sku_category_id="food",
            sku_id="lunch",
        ),
        ExpenseItemFactory(
            expense_item_id="extra_guest",
            name="Stay: Extra Guest",
            sku_category_id="stay",
            short_name="EXT_GUEST",
            linked=True,
            sku_id="extra_guest",
        ),
        ExpenseItemFactory(
            expense_item_id="booking_modification",
            name="booking_modification",
            sku_category_id="stay",
            sku_id="booking_modification",
        ),
        ExpenseItemFactory(
            expense_item_id="accommodation_charge",
            name="Accommodation",
            description="Accommodation Charges",
            sku_category_id="stay",
            sku_id=None,
            linked=None,
        ),
        ExpenseItemFactory(
            expense_item_id="124",
            name="Breakfast",
            sku_id="377",
            sku_category_id="food",
        ),
        ExpenseItemFactory(
            expense_item_id="125",
            name="Champagne",
            sku_id="378",
            sku_category_id="food",
        ),
        ExpenseItemFactory(
            expense_item_id="126",
            name="Buffet lunch",
            sku_id="391",
            sku_category_id="food",
        ),
        ExpenseItemFactory(
            expense_item_id="127",
            name="Buffet breakfast",
            sku_id="393",
            sku_category_id="food",
        ),
        ExpenseItemFactory(
            expense_item_id=FundingExpenseItem.TREEBO_AUTO_FUNDING.value,
            name="Treebo Funding Auto",
            sku_id="auto_funding",
            sku_category_id="stay",
        ),
        ExpenseItemFactory(
            expense_item_id=FundingExpenseItem.TREEBO_MANUAL_FUNDING.value,
            name="Treebo Funding Manual",
            sku_id="manual_funding",
            sku_category_id="stay",
        ),
    ]

    expense_repo.save_all(items)
    expense_repo.session().commit()


@pytest.fixture(scope='session', autouse=True)
def sku_category_setup(sku_category_repo):
    sku_aggregate = SkuCategoryAggregateFactory()
    sku_category_repo.save(sku_aggregate)
    sku_aggregate = SkuCategoryAggregateFactory(
        sku_category__name="stay", sku_category__sku_category_id="stay"
    )
    sku_category_repo.save(sku_aggregate)
    sku_category_repo.session().commit()


@pytest.fixture()
def cancellation_item(expense_repo):
    yield expense_repo.load('booking_cancellation')


@pytest.fixture()
def noshow_item(expense_repo):
    yield expense_repo.load('no_show')


@pytest.fixture()
def lunch_item(expense_repo):
    yield expense_repo.load('123')


@pytest.fixture()
def extra_guest(expense_repo):
    yield expense_repo.load('extra_guest')
