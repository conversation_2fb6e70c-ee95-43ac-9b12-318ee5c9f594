import pytest

from prometheus.tests.mockers import mock_role_manager

super_admin_user_type = {'X-User-Type': 'super-admin'}


@pytest.mark.skip(
    reason="Need to use booking created via API, rather than Factories. Booking via factory are in "
    "invalid state"
)
@pytest.mark.usefixtures('hotel_with_switch_over_time_in_next_10_minutes')
def test_night_audit_api_does_night_audit_for_hotel(
    client,
    booking_repo,
    bill_repo,
    old_checked_out_booking_and_bill,
    checked_in_booking_and_bill,
    future_booking_and_bill,
):
    old_booking, old_bill = old_checked_out_booking_and_bill
    booking_repo.save(old_booking)
    bill_repo.save(old_bill)

    future_booking, future_bill = future_booking_and_bill
    booking_repo.save(future_booking)
    bill_repo.save(future_bill)

    checked_in_booking, checked_in_bill = checked_in_booking_and_bill
    booking_repo.save(checked_in_booking)
    bill_repo.save(checked_in_bill)
    with mock_role_manager():
        response = client.post(
            '/v1/hotels/0016932/night-audit', headers=super_admin_user_type
        )
    assert response.status_code == 200


@pytest.mark.skip(
    reason="Need to use booking created via API, rather than Factories. Booking via factory are in "
    "invalid state"
)
@pytest.mark.usefixtures('hotel_with_switch_over_time_in_next_10_minutes')
def test_booking_night_audit_api_does_night_audit_for_booking(
    client,
    booking_repo,
    bill_repo,
    old_checked_out_booking_and_bill,
    checked_in_booking_and_bill,
    future_booking_and_bill,
):
    headers = {'X-User-Type': 'super-admin'}
    checked_in_booking, checked_in_bill = checked_in_booking_and_bill
    booking_repo.save(checked_in_booking)
    bill_repo.save(checked_in_bill)

    booking_id = checked_in_booking.booking.booking_id
    with mock_role_manager():
        response = client.post(
            f'/v1/hotels/0016932/night-audit/{booking_id}', headers=headers
        )
    assert response.status_code == 200
