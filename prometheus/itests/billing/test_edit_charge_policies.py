import json
from decimal import Decimal

import pytest
from treebo_commons.utils import dateutils

from prometheus.domain.billing.dto.chargesplit_data import ChargeSplitData
from prometheus.domain.billing.entities.billed_entity import BilledEntityAccountVO
from prometheus.infrastructure.database import db_engine
from prometheus.itests.helpers import roll_over_business_date
from prometheus.tests.mockers import (
    mock_role_manager,
    mock_rule_engine,
    mock_tax_calculator_service,
)
from ths_common.constants.billing_constants import (
    BilledEntityCategory,
    ChargeBillToTypes,
    ChargeSplitType,
    ChargeTypes,
    PaymentInstruction,
)
from ths_common.constants.booking_constants import BookingStatus


def perform_checkin(client, room_repo, booking_aggregate, room_stay, room_type_id):
    room_aggregates = room_repo.load_multiple(
        booking_aggregate.booking.hotel_id, room_type_id
    )
    if not room_aggregates:
        raise Exception("No room found")
    room_aggregate = room_aggregates[0]
    room_id = room_aggregate.room.room_id

    url = "v1/bookings/{booking_id}/actions".format(
        booking_id=booking_aggregate.booking.booking_id
    )
    checkin_request = dict(
        data=dict(
            action_type="checkin",
            payload=dict(
                checkin=dict(
                    room_stays=[
                        dict(
                            guest_stays=[dict(guest_id="1", guest_stay_id=1)],
                            room_allocation=dict(
                                checkin_date=dateutils.isoformat_datetime(
                                    room_stay.checkin_date
                                ),
                                room_id=room_id,
                            ),
                            room_stay_id=room_stay.room_stay_id,
                        )
                    ]
                )
            ),
        ),
        resource_version=booking_aggregate.current_version(),
    )
    headers = {"X-User-Type": "fdm"}

    response = client.post(
        url,
        data=json.dumps(checkin_request),
        content_type='application/json',
        headers=headers,
    )
    assert response.status_code == 200


def add_expense(client, booking_aggregate, room_stay, bill_to_type):
    url = "v1/bookings/{booking_id}/expenses".format(
        booking_id=booking_aggregate.booking.booking_id
    )
    add_expense_request = dict(
        data=dict(
            added_by="hotel",
            assigned_to=["1"],
            comments="Expense Added - Guest ID: 1",
            expense_item_id="123",
            price=dict(
                applicable_date=dateutils.isoformat_datetime(
                    room_stay.actual_checkin_date
                ),
                bill_to_type=bill_to_type,
                type="non-credit",
                posttax_amount="1500",
            ),
            room_stay_id=room_stay.room_stay_id,
        ),
        resource_version=booking_aggregate.current_version(),
    )
    headers = {"X-User-Type": "fdm"}

    with mock_rule_engine():
        response = client.post(
            url,
            data=json.dumps(add_expense_request),
            content_type='application/json',
            headers=headers,
        )
        assert response.status_code == 200


@pytest.fixture
def invoiced_charge_in_bill(bill_repo, booking_and_bill):
    booking_aggregate, bill_aggregate = booking_and_bill

    room_stay = booking_aggregate.get_room_stay(1)
    charge_id = room_stay.charge_ids[0]
    charge = bill_aggregate.get_charge(charge_id)
    charge.update_charge_splits(
        [
            ChargeSplitData(
                charge_type=ChargeTypes.NON_CREDIT,
                bill_to_type=ChargeBillToTypes.COMPANY,
                billed_entity_account=BilledEntityAccountVO(1, 1),
            )
        ],
        charge_split_type=ChargeSplitType.EQUAL_SPLIT,
    )

    charge_splits = charge.charge_splits
    charge_splits[0].update_invoice_id('INV-140818-**********-6294')
    bill_repo.update(bill_aggregate)
    return booking_aggregate, bill_aggregate, room_stay, charge


@pytest.fixture
def closed_booking_and_bill(booking_repo, booking_and_bill):
    booking_aggregate = booking_and_bill[0]
    booking_aggregate.booking.status = BookingStatus.CHECKED_OUT
    booking_repo.update(booking_aggregate)
    return booking_and_bill


def test_cr_team_can_edit_room_stay_non_credit_charge(
    active_hotel_aggregate, client, booking_and_bill, hotel_repo, bill_repo
):
    booking_aggregate = booking_and_bill[0]
    bill_aggregate = booking_and_bill[1]

    room_stay = booking_aggregate.get_room_stay(1)
    charge_id = room_stay.charge_ids[0]
    charge = bill_aggregate.get_charge(charge_id)
    bill_repo.update(bill_aggregate)

    url = "v2/bookings/{booking_id}/room-stays/{room_stay_id}/prices".format(
        booking_id=booking_aggregate.booking.booking_id,
        room_stay_id=room_stay.room_stay_id,
    )
    edit_charge_request = dict(
        data=[dict(charge_id=charge_id, pretax_amount="1000")], resource_version=1
    )

    # Edit of Room stay charge by CR team member should pass
    headers = {"X-User-Type": "cr-team"}

    with mock_role_manager():
        response = client.patch(
            url,
            data=json.dumps(edit_charge_request),
            content_type='application/json',
            headers=headers,
        )
    assert response.status_code == 200
    edit_charge_request['resource_version'] = response.json['resource_version']


def test_cr_team_can_edit_non_room_stay_credit_charge(
    active_hotel_aggregate, client, booking_and_bill, hotel_repo, bill_repo
):
    booking_aggregate = booking_and_bill[0]
    bill_aggregate = booking_and_bill[1]

    room_stay = booking_aggregate.get_room_stay(1)
    charge_id = room_stay.charge_ids[0]
    charge = bill_aggregate.get_charge(charge_id)
    bill_repo.update(bill_aggregate)

    url = "v2/bookings/{booking_id}/room-stays/{room_stay_id}/prices".format(
        booking_id=booking_aggregate.booking.booking_id,
        room_stay_id=room_stay.room_stay_id,
    )
    edit_charge_request = dict(
        data=[dict(charge_id=charge_id, pretax_amount="1000")], resource_version=1
    )
    # Edit of non room stay credit charge by CR team member should pass
    headers = {"X-User-Type": "cr-team"}

    with mock_role_manager():
        response = client.patch(
            url,
            data=json.dumps(edit_charge_request),
            content_type='application/json',
            headers=headers,
        )
    assert response.status_code == 200
    edit_charge_request['resource_version'] = response.json['resource_version']


def test_edit_billing_instructions_of_non_room_stay_charge_updates_charge_splits(
    active_hotel_aggregate,
    client,
    booking_and_bill,
    hotel_repo,
    bill_repo,
    room_repo,
    booking_repo,
):
    booking_aggregate = booking_and_bill[0]
    # Rollover Business Date
    roll_over_business_date(active_hotel_aggregate, hotel_repo)

    room_stay = booking_aggregate.room_stays[0]
    perform_checkin(
        client, room_repo, booking_aggregate, room_stay, room_stay.room_type_id
    )

    booking_aggregate = booking_repo.load(booking_aggregate.booking.booking_id)
    room_stay = booking_aggregate.room_stays[0]
    add_expense(client, booking_aggregate, room_stay, "guest")

    booking_aggregate = booking_repo.load(booking_aggregate.booking.booking_id)
    expenses = booking_aggregate.expenses
    expense = expenses[0]

    bill_aggregate = bill_repo.load(booking_aggregate.bill_id)
    non_credit_account = bill_aggregate.get_billed_entity(
        2
    ).get_account_for_new_assignment(ChargeTypes.NON_CREDIT)
    credit_account = bill_aggregate.get_billed_entity(1).get_account_for_new_assignment(
        ChargeTypes.CREDIT
    )
    bill_aggregate.add_folio_if_not_exists(1, credit_account.account_number)
    bill_repo.update(bill_aggregate)
    db_engine.get_scoped_session().commit()

    url = "v1/bookings/{booking_id}/expenses/{expense_id}".format(
        booking_id=booking_aggregate.booking.booking_id, expense_id=expense.expense_id
    )
    edit_charge_request = dict(
        data=dict(
            billing_instructions=[
                dict(
                    billed_entity_account=dict(
                        billed_entity_id=2,
                        account_number=non_credit_account.account_number,
                    ),
                    payment_instruction=PaymentInstruction.PAY_AT_CHECKOUT.value,
                    split_percentage=40,
                ),
                dict(
                    billed_entity_account=dict(
                        billed_entity_id=1, account_number=credit_account.account_number
                    ),
                    payment_instruction=PaymentInstruction.PAY_AFTER_CHECKOUT.value,
                    split_percentage=60,
                ),
            ]
        ),
        resource_version=booking_aggregate.current_version(),
    )

    headers = {"X-User-Type": "super-admin"}

    with mock_rule_engine():
        response = client.patch(
            url,
            data=json.dumps(edit_charge_request),
            content_type='application/json',
            headers=headers,
        )
        assert response.status_code == 200

    get_charge_url = 'v1/bills/{bill_id}/charges/{charge_id}'.format(
        bill_id=booking_aggregate.bill_id, charge_id=expense.charge_id
    )
    get_charge_response = client.get(get_charge_url)
    assert get_charge_response.status_code == 200

    charge_json = get_charge_response.json['data']
    assert len(charge_json['charge_splits']) == 2

    for split in charge_json['charge_splits']:
        assert split['percentage'] in {Decimal('40'), Decimal('60')}

        if split['percentage'] == Decimal('40'):
            assert split['billed_entity_account'] == dict(
                billed_entity_id=2, account_number=non_credit_account.account_number
            )
            assert split['charge_type'] == ChargeTypes.NON_CREDIT.value
        else:
            assert split['billed_entity_account'] == dict(
                billed_entity_id=1, account_number=credit_account.account_number
            )
            assert split['charge_type'] == ChargeTypes.CREDIT.value


def test_edit_billing_instructions_of_room_stay_charge_updates_charge_splits(
    active_hotel_aggregate,
    client,
    booking_and_bill,
    hotel_repo,
    bill_repo,
    room_repo,
    booking_repo,
):
    booking_aggregate = booking_and_bill[0]

    room_stay = booking_aggregate.room_stays[0]
    booking_aggregate = booking_repo.load(booking_aggregate.booking.booking_id)
    room_stay = booking_aggregate.room_stays[0]

    booking_aggregate = booking_repo.load(booking_aggregate.booking.booking_id)
    bill_aggregate = bill_repo.load(booking_aggregate.bill_id)
    non_credit_account = bill_aggregate.get_billed_entity(
        2
    ).get_account_for_new_assignment(ChargeTypes.NON_CREDIT)
    bill_aggregate.add_folio_if_not_exists(2, non_credit_account.account_number)
    credit_account = bill_aggregate.get_billed_entity(1).get_account_for_new_assignment(
        ChargeTypes.CREDIT
    )
    bill_aggregate.add_folio_if_not_exists(1, credit_account.account_number)
    bill_repo.update(bill_aggregate)
    db_engine.get_scoped_session().commit()

    url = "v2/bookings/{booking_id}/room-stays/{room_stay_id}/prices".format(
        booking_id=booking_aggregate.booking.booking_id,
        room_stay_id=room_stay.room_stay_id,
    )
    edit_charge_request = dict(
        data=[
            dict(
                charge_id=room_stay.charge_ids[0],
                billing_instructions=[
                    dict(
                        billed_entity_account=dict(
                            billed_entity_id=2,
                            account_number=non_credit_account.account_number,
                        ),
                        payment_instruction=PaymentInstruction.PAY_AT_CHECKOUT.value,
                        split_percentage=40,
                    ),
                    dict(
                        billed_entity_account=dict(
                            billed_entity_id=1,
                            account_number=credit_account.account_number,
                        ),
                        payment_instruction=PaymentInstruction.PAY_AFTER_CHECKOUT.value,
                        split_percentage=60,
                    ),
                ],
            )
        ],
        resource_version=booking_aggregate.current_version(),
    )

    headers = {"X-User-Type": "super-admin"}

    with mock_rule_engine():
        response = client.patch(
            url,
            data=json.dumps(edit_charge_request),
            content_type='application/json',
            headers=headers,
        )
        assert response.status_code == 200

    get_charge_url = 'v1/bills/{bill_id}/charges/{charge_id}'.format(
        bill_id=booking_aggregate.bill_id, charge_id=room_stay.charge_ids[0]
    )
    get_charge_response = client.get(get_charge_url)
    assert get_charge_response.status_code == 200

    charge_json = get_charge_response.json['data']
    assert len(charge_json['charge_splits']) == 2

    for split in charge_json['charge_splits']:
        assert split['percentage'] in {Decimal('40'), Decimal('60')}

        if split['percentage'] == Decimal('40'):
            assert split['billed_entity_account'] == dict(
                billed_entity_id=2, account_number=non_credit_account.account_number
            )
            assert split['charge_type'] == ChargeTypes.NON_CREDIT.value
        else:
            assert split['billed_entity_account'] == dict(
                billed_entity_id=1, account_number=credit_account.account_number
            )
            assert split['charge_type'] == ChargeTypes.CREDIT.value


def test_edit_billing_instruction_using_credit_account_for_non_credit_charge_should_fail(
    active_hotel_aggregate,
    client,
    booking_and_bill,
    hotel_repo,
    bill_repo,
    room_repo,
    booking_repo,
):
    roll_over_business_date(active_hotel_aggregate, hotel_repo)
    booking_aggregate = booking_and_bill[0]

    booking_aggregate = booking_repo.load(booking_aggregate.booking.booking_id)
    room_stay = booking_aggregate.room_stays[0]
    # Checkin is required, to be able to consume charge
    perform_checkin(
        client, room_repo, booking_aggregate, room_stay, room_stay.room_type_id
    )
    charge_id = room_stay.charge_ids[0]

    bill_aggregate = bill_repo.load(booking_aggregate.bill_id)
    charge = bill_aggregate.get_charge(charge_id)

    assert all(
        split.charge_type == ChargeTypes.NON_CREDIT for split in charge.charge_splits
    )

    headers = {"X-User-Type": "super-admin"}

    bill_aggregate = bill_repo.load(booking_aggregate.bill_id)
    charge = bill_aggregate.get_charge(charge_id)

    assert all(
        split.charge_type == ChargeTypes.NON_CREDIT for split in charge.charge_splits
    )

    billed_entity_account = bill_aggregate.get_default_billed_entity_account(
        BilledEntityCategory.BOOKER_COMPANY, charge_type=ChargeTypes.CREDIT
    )
    bill_repo.update(bill_aggregate)
    db_engine.get_scoped_session().commit()
    billed_entity_id, account_number = (
        billed_entity_account.billed_entity_id,
        billed_entity_account.account_number,
    )

    edit_charge_request = dict(
        data=dict(
            billing_instructions=[
                dict(
                    billed_entity_account=dict(
                        billed_entity_id=billed_entity_id, account_number=account_number
                    ),
                    payment_instruction=PaymentInstruction.PAY_AT_CHECKOUT.value,
                    split_percentage=40,
                ),
                dict(
                    billed_entity_account=dict(
                        billed_entity_id=billed_entity_id, account_number=account_number
                    ),
                    payment_instruction=PaymentInstruction.PAY_AT_CHECKOUT.value,
                    split_percentage=60,
                ),
            ]
        ),
        resource_version=bill_aggregate.current_version(),
    )

    # Now update the charge account to a credit account
    url = f"v1/bills/{bill_aggregate.bill_id}/charges/{charge_id}"

    headers = {"X-User-Type": "super-admin"}

    response = client.patch(
        url,
        data=json.dumps(edit_charge_request),
        content_type='application/json',
        headers=headers,
    )
    assert response.status_code == 400

    bill_aggregate = bill_repo.load(bill_aggregate.bill_id)
    charge = bill_aggregate.get_charge(charge_id)

    for split in charge.charge_splits:
        actual_billed_entity_account = split.billed_entity_account
        assert actual_billed_entity_account.account_number != account_number
        assert (
            not bill_aggregate.get_billed_entity(
                actual_billed_entity_account.billed_entity_id
            )
            .get_account(actual_billed_entity_account.account_number)
            .is_credit_account()
        )
