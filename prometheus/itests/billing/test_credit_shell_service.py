import json

import pytest
import treebo_commons.utils.dateutils
from treebo_commons.money import Money
from treebo_commons.money.constants import CurrencyType

from object_registry import locate_instance
from prometheus.application.billing.command_handlers.credit_shell.consume_credit_shell import (
    ConsumeCreditShellCommandHandler,
)
from prometheus.application.billing.command_handlers.credit_shell.create_credit_shell import (
    CreateCreditShellCommandHandler,
)
from prometheus.application.billing.command_handlers.credit_shell.credit_balance_to_credit_shell import (
    CreditBalanceToCreditShellCommandHandler,
)
from prometheus.application.billing.query_handlers.credit_shell.get_credit_shells import (
    GetCreditShellsQueryHandler,
)
from prometheus.domain.billing.dto.credit_shell_data import (
    AddBalanceInCreditShellData,
    ConsumeCreditShellData,
    CreditShellData,
)
from prometheus.domain.booking.repositories import BookingRepository
from prometheus.itests.billing.test_invoice_modification import (
    modify_invoice_api_call,
    modify_invoice_payload,
)
from prometheus.itests.billing.test_payment_api import mock_auto_refund_payment_service
from prometheus.tests.mockers import (
    mock_catalog_client,
    mock_credit_shell_refund_payment_modes,
    mock_tenant_config_for_payment_rules,
)
from ths_common.constants.billing_constants import (
    IssuedByType,
    PaymentChannels,
    PaymentModes,
    PaymentReceiverTypes,
    PaymentStatus,
    PaymentTypes,
)
from ths_common.constants.booking_constants import BookingStatus


@pytest.fixture
def get_credit_shell_query_handler():
    return locate_instance(GetCreditShellsQueryHandler)


@pytest.fixture
def credit_balance_to_credit_shell_command_handler():
    return locate_instance(CreditBalanceToCreditShellCommandHandler)


@pytest.fixture
def consume_credit_shell_command_handler():
    return locate_instance(ConsumeCreditShellCommandHandler)


@pytest.fixture
def create_credit_shell_command_handler():
    return locate_instance(CreateCreditShellCommandHandler)


@pytest.fixture
def booking_repo():
    return BookingRepository()


@pytest.fixture
def credit_shell_data():
    credit_shell = CreditShellData(
        bill_id="123",
        billed_entity_id="456",
        issue_date=treebo_commons.utils.dateutils.current_date(),
        credit_note_id="123",
        amount=Money("120.5", CurrencyType("INR")),
        paid_to='treebo',
        paid_by='guest',
    )
    return credit_shell


@pytest.fixture
def consume_credit_shell_data():
    consume_credit_shell_data = ConsumeCreditShellData(
        bill_id="123",
        credit_shell_id="NA",
        debit_amount=Money("12", CurrencyType("INR")),
    )
    return consume_credit_shell_data


@pytest.fixture
def add_balance_in_credit_shell():
    add_balance_in_credit_shell = AddBalanceInCreditShellData(
        credit_amount=Money("12", CurrencyType("INR")), credit_shell_id="NA"
    )
    return add_balance_in_credit_shell


def get_credit_shell_transaction_log(client, booking_id, credit_shell_id):
    url = f"/v1/credit-shells/{credit_shell_id}/transaction-log"
    response = client.get(
        url,
        content_type="application/json",
        headers={"X-User-Type": "super-admin"},
    )
    assert response.status_code == 200
    return response.json


def redeem_credit_shell_payload(credit_shell_id, amount, payment_mode, remarks=None):
    return {
        "data": {
            "credit_shell_id": credit_shell_id,
            "amount": amount,
            "payment_mode": payment_mode,
            "remarks": remarks,
        }
    }


def redeem_credit_shell_api_call(payload, client):
    url = 'v1/credit-shells'
    headers = {'X-User-Type': 'super-admin'}
    response = client.post(
        url, data=json.dumps(payload), content_type='application/json', headers=headers
    )
    return response


def test_create_credit_shell(
    client, credit_shell_data, create_credit_shell_command_handler
):
    aggregate = create_credit_shell_command_handler.handle(
        credit_shell_data=credit_shell_data, booking_reference_number='BOOKINGREF'
    )
    assert aggregate.credit_shell
    assert aggregate.credit_shell.version == 1
    assert aggregate.credit_shell.total_credit == credit_shell_data.amount
    assert aggregate.credit_shell.bill_id == credit_shell_data.bill_id
    assert aggregate.credit_shell.billed_entity_id == credit_shell_data.billed_entity_id


def test_create_and_consume_credit_shell(
    client,
    credit_shell_data,
    consume_credit_shell_data,
    consume_credit_shell_command_handler,
    create_credit_shell_command_handler,
):
    create_credit_shell_aggregate = create_credit_shell_command_handler.handle(
        credit_shell_data=credit_shell_data, booking_reference_number='BOOKINGREF'
    )
    ref_id = create_credit_shell_aggregate.credit_shell.credit_shell_id
    consume_credit_shell_data.credit_shell_id = ref_id
    consume_credit_shell_aggregate = consume_credit_shell_command_handler.handle(
        consume_credit_shell_data=consume_credit_shell_data,
        target_booking_reference_number='BOOKINGREF',
    )
    consumed_credit_shell = consume_credit_shell_aggregate.credit_shell
    created_credit_shell = create_credit_shell_aggregate.credit_shell
    assert created_credit_shell.credit_shell_id == consumed_credit_shell.credit_shell_id
    assert created_credit_shell.total_credit == consumed_credit_shell.total_credit
    assert (
        consumed_credit_shell.remaining_credit
        == created_credit_shell.total_credit - consume_credit_shell_data.debit_amount
    )
    assert consumed_credit_shell.version == created_credit_shell.version + 1


def test_add_balance_in_credit_shell(
    client,
    credit_shell_data,
    consume_credit_shell_data,
    add_balance_in_credit_shell,
    credit_balance_to_credit_shell_command_handler,
    consume_credit_shell_command_handler,
    create_credit_shell_command_handler,
):
    create_credit_shell_aggregate = create_credit_shell_command_handler.handle(
        credit_shell_data=credit_shell_data, booking_reference_number='BOOKINGREF'
    )
    ref_id = create_credit_shell_aggregate.credit_shell.credit_shell_id
    consume_credit_shell_data.credit_shell_id = ref_id
    consume_credit_shell_aggregate = consume_credit_shell_command_handler.handle(
        consume_credit_shell_data=consume_credit_shell_data,
        target_booking_reference_number='BOOKINGREF',
    )
    consumed_credit_shell = consume_credit_shell_aggregate.credit_shell
    created_credit_shell = create_credit_shell_aggregate.credit_shell
    add_balance_in_credit_shell.credit_shell_id = created_credit_shell.credit_shell_id
    edited_credit_shell_aggregate = (
        credit_balance_to_credit_shell_command_handler.handle(
            edit_credit_shell_data=add_balance_in_credit_shell,
            target_booking_reference_number='BOOKINGREF',
        )
    )
    edited_credit_shell = edited_credit_shell_aggregate.credit_shell

    assert edited_credit_shell.version == consumed_credit_shell.version + 1
    assert edited_credit_shell.remaining_credit == edited_credit_shell.total_credit


@pytest.fixture
def open_booking_and_bill(booking_repo, booking_and_bill):
    booking_aggregate = booking_and_bill[0]
    booking_aggregate.booking.status = BookingStatus.CONFIRMED
    booking_repo.update(booking_aggregate)
    return booking_and_bill


def test_add_balance_and_consume_credit_shell_with_cancel_payment(
    client,
    bill_repo,
    open_booking_and_bill,
    cashier_session_repo,
    credit_shell_data,
    get_credit_shell_query_handler,
    hotel_repo,
    hotel_config_repo,
    create_credit_shell_command_handler,
):
    from prometheus import crs_context

    bill_aggregate = open_booking_and_bill[1]
    total_payments = len(bill_aggregate.payments)

    if not crs_context.get_hotel_context():
        hotel_aggregate = hotel_repo.load(bill_aggregate.bill.vendor_id)
        hotel_config_aggregate = hotel_config_repo.load(bill_aggregate.bill.vendor_id)
        crs_context.set_hotel_context(hotel_aggregate, hotel_config_aggregate)

    credit_shell_data.amount = Money("8165.7700", bill_aggregate.base_currency)
    aggregate = create_credit_shell_command_handler.handle(
        credit_shell_data=credit_shell_data,
        booking_reference_number=bill_aggregate.bill.parent_info.get(
            'reference_number'
        ),
    )
    assert aggregate.credit_shell
    assert aggregate.credit_shell.version == 1
    assert aggregate.credit_shell.total_credit == credit_shell_data.amount
    assert aggregate.credit_shell.bill_id == credit_shell_data.bill_id
    assert aggregate.credit_shell.billed_entity_id == credit_shell_data.billed_entity_id

    with mock_catalog_client(), mock_tenant_config_for_payment_rules():
        url = 'v1/bills/' + bill_aggregate.bill.bill_id + '/payments'
        new_payment = dict(
            data=dict(
                amount='8165.7700',
                paid_by=PaymentReceiverTypes.GUEST,
                paid_to=PaymentReceiverTypes.HOTEL,
                payment_channel=PaymentChannels.ONLINE,
                payment_mode=PaymentModes.CREDIT_SHELL,
                payment_type=PaymentTypes.PAYMENT.value,
                status=PaymentStatus.DONE.value,
                payment_ref_id=aggregate.credit_shell.credit_shell_id,
            ),
            resource_version=1,
        )

        headers = {"X-User-Type": "super-admin"}
        response = client.post(
            url,
            data=json.dumps(new_payment),
            content_type='application/json',
            headers=headers,
        )

    payment_id = response.json.get('data').get('payment_id')
    bill_aggregate = bill_repo.load(bill_aggregate.bill.bill_id)
    payment = bill_aggregate.get_payment(int(payment_id))

    created_credit_shell = get_credit_shell_query_handler.handle(
        credit_shell_id=aggregate.credit_shell_id
    )[0]
    assert created_credit_shell.credit_shell.remaining_credit == Money(
        '0', bill_aggregate.base_currency
    )
    assert created_credit_shell.credit_shell.total_credit == payment.amount

    # Post Payment
    patch_payment_url = (
        'v1/bills/' + bill_aggregate.bill.bill_id + '/payments/' + payment_id
    )
    client.patch(
        patch_payment_url,
        data=json.dumps(
            dict(
                data=dict(status=PaymentStatus.POSTED.value),
                resource_version=bill_aggregate.bill.version,
            )
        ),
        headers=headers,
        content_type='application/json',
    )

    # Cancel Payment. Should create refund. Payment Status should still be posted
    client.patch(
        patch_payment_url,
        data=json.dumps(
            dict(
                data=dict(status=PaymentStatus.CANCELLED.value),
                resource_version=bill_aggregate.bill.version,
            )
        ),
        headers=headers,
        content_type='application/json',
    )

    bill_aggregate = bill_repo.load(bill_aggregate.bill.bill_id)
    payment = bill_aggregate.get_payment(int(payment_id))
    assert payment.status == PaymentStatus.POSTED
    # Cancellation of posted payment should internally create a refund, thus increasing len of payments by 1.
    assert len(bill_aggregate.payments) == total_payments + 2

    refund = bill_aggregate.get_payment(int(payment_id) + 1)
    assert refund.payment_type == PaymentTypes.REFUND
    assert refund.amount == payment.amount
    created_credit_shell = get_credit_shell_query_handler.handle(
        credit_shell_id=aggregate.credit_shell_id
    )[0]
    assert created_credit_shell.credit_shell.remaining_credit == refund.amount
    response = get_credit_shell_transaction_log(
        client,
        booking_id=bill_aggregate.bill.parent_reference_number,
        credit_shell_id=aggregate.credit_shell_id,
    )
    assert len(response.get('data')) > 0


def test_add_balance_and_consume_credit_shell_with_edit_payment(
    client,
    bill_repo,
    open_booking_and_bill,
    cashier_session_repo,
    credit_shell_data,
    get_credit_shell_query_handler,
    hotel_repo,
    hotel_config_repo,
    create_credit_shell_command_handler,
):
    from prometheus import crs_context

    bill_aggregate = open_booking_and_bill[1]
    total_payments = len(bill_aggregate.payments)

    if not crs_context.get_hotel_context():
        hotel_aggregate = hotel_repo.load(bill_aggregate.bill.vendor_id)
        hotel_config_aggregate = hotel_config_repo.load(bill_aggregate.bill.vendor_id)
        crs_context.set_hotel_context(hotel_aggregate, hotel_config_aggregate)

    credit_shell_data.amount = Money("10000.0000", bill_aggregate.base_currency)
    aggregate = create_credit_shell_command_handler.handle(
        credit_shell_data=credit_shell_data,
        booking_reference_number=bill_aggregate.bill.parent_info.get(
            'reference_number'
        ),
    )
    response = get_credit_shell_query_handler.handle(bill_id=credit_shell_data.bill_id)
    assert aggregate.credit_shell
    assert aggregate.credit_shell.version == 1
    assert aggregate.credit_shell.total_credit == credit_shell_data.amount
    assert aggregate.credit_shell.bill_id == credit_shell_data.bill_id
    assert aggregate.credit_shell.billed_entity_id == credit_shell_data.billed_entity_id

    with mock_catalog_client():
        url = 'v1/bills/' + bill_aggregate.bill.bill_id + '/payments'
        new_payment = dict(
            data=dict(
                amount='8000.0000',
                paid_by=PaymentReceiverTypes.GUEST,
                paid_to=PaymentReceiverTypes.HOTEL,
                payment_channel=PaymentChannels.FRONT_DESK,
                payment_mode=PaymentModes.CREDIT_SHELL,
                payment_type=PaymentTypes.PAYMENT.value,
                status=PaymentStatus.DONE.value,
                payment_ref_id=aggregate.credit_shell.credit_shell_id,
            ),
            resource_version=1,
        )

        headers = {"X-User-Type": "super-admin"}
        response = client.post(
            url,
            data=json.dumps(new_payment),
            content_type='application/json',
            headers=headers,
        )

    payment_id = response.json.get('data').get('payment_id')
    bill_aggregate = bill_repo.load(bill_aggregate.bill.bill_id)
    payment = bill_aggregate.get_payment(int(payment_id))
    response = get_credit_shell_transaction_log(
        client,
        booking_id=bill_aggregate.bill.parent_reference_number,
        credit_shell_id=aggregate.credit_shell_id,
    )
    assert len(response.get('data')) == 2

    created_credit_shell = get_credit_shell_query_handler.handle(
        credit_shell_id=aggregate.credit_shell_id
    )[0]
    assert created_credit_shell.credit_shell.remaining_credit == Money(
        '2000.000', bill_aggregate.base_currency
    )
    assert created_credit_shell.credit_shell.total_credit == Money(
        '10000.000', bill_aggregate.base_currency
    )

    # Post Payment
    patch_payment_url = (
        'v1/bills/' + bill_aggregate.bill.bill_id + '/payments/' + payment_id
    )
    client.patch(
        patch_payment_url,
        data=json.dumps(
            dict(
                data=dict(status=PaymentStatus.DONE.value, amount='9000.0000'),
                resource_version=bill_aggregate.bill.version,
            )
        ),
        headers=headers,
        content_type='application/json',
    )

    created_credit_shell = get_credit_shell_query_handler.handle(
        credit_shell_id=aggregate.credit_shell_id
    )[0]
    assert created_credit_shell.credit_shell.remaining_credit == Money(
        '1000.000', bill_aggregate.base_currency
    )
    assert created_credit_shell.credit_shell.total_credit == Money(
        '10000.000', bill_aggregate.base_currency
    )
    response = get_credit_shell_transaction_log(
        client,
        booking_id=bill_aggregate.bill.parent_reference_number,
        credit_shell_id=aggregate.credit_shell_id,
    )
    assert len(response.get('data')) == 3

    bill_aggregate = bill_repo.load(bill_aggregate.bill.bill_id)
    client.patch(
        patch_payment_url,
        data=json.dumps(
            dict(
                data=dict(status=PaymentStatus.DONE.value, amount='7000.0000'),
                resource_version=bill_aggregate.bill.version,
            )
        ),
        headers=headers,
        content_type='application/json',
    )

    created_credit_shell = get_credit_shell_query_handler.handle(
        credit_shell_id=aggregate.credit_shell_id
    )[0]
    assert created_credit_shell.credit_shell.remaining_credit == Money(
        '3000.000', bill_aggregate.base_currency
    )
    assert created_credit_shell.credit_shell.total_credit == Money(
        '10000.000', bill_aggregate.base_currency
    )
    response = get_credit_shell_transaction_log(
        client,
        booking_id=bill_aggregate.bill.parent_reference_number,
        credit_shell_id=aggregate.credit_shell_id,
    )
    assert len(response.get('data')) == 4

    bill_aggregate = bill_repo.load(bill_aggregate.bill.bill_id)
    client.patch(
        patch_payment_url,
        data=json.dumps(
            dict(
                data=dict(
                    status=PaymentStatus.DONE.value,
                    amount='9000.0000',
                    payment_mode=PaymentModes.CASH,
                ),
                resource_version=bill_aggregate.bill.version,
            )
        ),
        headers=headers,
        content_type='application/json',
    )

    created_credit_shell = get_credit_shell_query_handler.handle(
        credit_shell_id=aggregate.credit_shell_id
    )[0]
    assert created_credit_shell.credit_shell.remaining_credit == Money(
        '10000.000', bill_aggregate.base_currency
    )
    assert created_credit_shell.credit_shell.total_credit == Money(
        '10000.000', bill_aggregate.base_currency
    )
    response = get_credit_shell_transaction_log(
        client,
        booking_id=bill_aggregate.bill.parent_reference_number,
        credit_shell_id=aggregate.credit_shell_id,
    )
    assert len(response.get('data')) == 5

    bill_aggregate = bill_repo.load(bill_aggregate.bill.bill_id)
    client.patch(
        patch_payment_url,
        data=json.dumps(
            dict(
                data=dict(
                    status=PaymentStatus.DONE.value,
                    amount='9000.0000',
                    payment_mode=PaymentModes.CREDIT_SHELL,
                ),
                resource_version=bill_aggregate.bill.version,
            )
        ),
        headers=headers,
        content_type='application/json',
    )

    created_credit_shell = get_credit_shell_query_handler.handle(
        credit_shell_id=aggregate.credit_shell_id
    )[0]
    assert created_credit_shell.credit_shell.remaining_credit == Money(
        '1000.000', bill_aggregate.base_currency
    )
    assert created_credit_shell.credit_shell.total_credit == Money(
        '10000.000', bill_aggregate.base_currency
    )
    response = get_credit_shell_transaction_log(
        client,
        booking_id=bill_aggregate.bill.parent_reference_number,
        credit_shell_id=aggregate.credit_shell_id,
    )
    assert len(response.get('data')) == 6

    bill_aggregate = bill_repo.load(bill_aggregate.bill.bill_id)
    client.patch(
        patch_payment_url,
        data=json.dumps(
            dict(
                data=dict(
                    status=PaymentStatus.DONE.value,
                    amount='9200.0000',
                    payment_mode=PaymentModes.CREDIT_SHELL,
                ),
                resource_version=bill_aggregate.bill.version,
            )
        ),
        headers=headers,
        content_type='application/json',
    )

    created_credit_shell = get_credit_shell_query_handler.handle(
        credit_shell_id=aggregate.credit_shell_id
    )[0]
    assert created_credit_shell.credit_shell.remaining_credit == Money(
        '800.000', bill_aggregate.base_currency
    )
    assert created_credit_shell.credit_shell.total_credit == Money(
        '10000.000', bill_aggregate.base_currency
    )
    response = get_credit_shell_transaction_log(
        client,
        booking_id=bill_aggregate.bill.parent_reference_number,
        credit_shell_id=aggregate.credit_shell_id,
    )
    assert len(response.get('data')) == 7

    bill_aggregate = bill_repo.load(bill_aggregate.bill.bill_id)
    client.patch(
        patch_payment_url,
        data=json.dumps(
            dict(
                data=dict(status=PaymentStatus.DONE.value, amount='9300.0000'),
                resource_version=bill_aggregate.bill.version,
            )
        ),
        headers=headers,
        content_type='application/json',
    )

    created_credit_shell = get_credit_shell_query_handler.handle(
        credit_shell_id=aggregate.credit_shell_id
    )[0]
    assert created_credit_shell.credit_shell.remaining_credit == Money(
        '700.000', bill_aggregate.base_currency
    )
    assert created_credit_shell.credit_shell.total_credit == Money(
        '10000.000', bill_aggregate.base_currency
    )
    response = get_credit_shell_transaction_log(
        client,
        booking_id=bill_aggregate.bill.parent_reference_number,
        credit_shell_id=aggregate.credit_shell_id,
    )
    assert len(response.get('data')) == 8

    bill_aggregate = bill_repo.load(bill_aggregate.bill.bill_id)
    client.patch(
        patch_payment_url,
        data=json.dumps(
            dict(
                data=dict(
                    status=PaymentStatus.DONE.value,
                    amount='9400.0000',
                    payment_mode=PaymentModes.CASH,
                ),
                resource_version=bill_aggregate.bill.version,
            )
        ),
        headers=headers,
        content_type='application/json',
    )

    created_credit_shell = get_credit_shell_query_handler.handle(
        credit_shell_id=aggregate.credit_shell_id
    )[0]
    assert created_credit_shell.credit_shell.remaining_credit == Money(
        '10000.000', bill_aggregate.base_currency
    )
    assert created_credit_shell.credit_shell.total_credit == Money(
        '10000.000', bill_aggregate.base_currency
    )
    response = get_credit_shell_transaction_log(
        client,
        booking_id=bill_aggregate.bill.parent_reference_number,
        credit_shell_id=aggregate.credit_shell_id,
    )
    assert len(response.get('data')) == 9

    bill_aggregate = bill_repo.load(bill_aggregate.bill.bill_id)
    client.patch(
        patch_payment_url,
        data=json.dumps(
            dict(
                data=dict(status=PaymentStatus.DONE.value, amount='9500.0000'),
                resource_version=bill_aggregate.bill.version,
            )
        ),
        headers=headers,
        content_type='application/json',
    )

    created_credit_shell = get_credit_shell_query_handler.handle(
        credit_shell_id=aggregate.credit_shell_id
    )[0]
    assert created_credit_shell.credit_shell.remaining_credit == Money(
        '10000.000', bill_aggregate.base_currency
    )
    assert created_credit_shell.credit_shell.total_credit == Money(
        '10000.000', bill_aggregate.base_currency
    )
    response = get_credit_shell_transaction_log(
        client,
        booking_id=bill_aggregate.bill.parent_reference_number,
        credit_shell_id=aggregate.credit_shell_id,
    )
    assert len(response.get('data')) == 9

    bill_aggregate = bill_repo.load(bill_aggregate.bill.bill_id)
    client.patch(
        patch_payment_url,
        data=json.dumps(
            dict(
                data=dict(status=PaymentStatus.POSTED.value),
                resource_version=bill_aggregate.bill.version,
            )
        ),
        headers=headers,
        content_type='application/json',
    )

    bill_aggregate = bill_repo.load(bill_aggregate.bill.bill_id)
    payment = bill_aggregate.get_payment(int(payment_id))
    assert payment.status == PaymentStatus.POSTED
    credit_shell_data.amount = Money("5000.0000", bill_aggregate.base_currency)
    aggregate = create_credit_shell_command_handler.handle(
        credit_shell_data=credit_shell_data,
        booking_reference_number=bill_aggregate.bill.parent_info.get(
            'reference_number'
        ),
    )
    response = get_credit_shell_query_handler.handle(bill_id=credit_shell_data.bill_id)
    assert 1 == 1


def test_modify_locked_invoices_generates_credit_shell_logs_with_credit_and_debit_entry(
    locked_invoices, bill_repo, client, get_credit_shell_query_handler
):
    bill_aggregate = bill_repo.load(bill_id=locked_invoices[0].bill_id)

    modify_invoice_request = modify_invoice_payload(
        invoice_aggregate=locked_invoices[0],
        bill_aggregate=bill_aggregate,
        transfer_payment_to_new_invoice_account=True,
    )

    response = modify_invoice_api_call(
        modify_invoice_request, locked_invoices[0].bill_id, client
    )

    assert response.status_code == 200
    response_data = response.json
    created_credit_shell = get_credit_shell_query_handler.handle(
        bill_id=bill_aggregate.bill_id
    )
    assert len(created_credit_shell[0].credit_shell_audit_trails) == 2
    invoice_billed_entity_account = locked_invoices[0].invoice.billed_entity_account
    credit_note_billed_entity_account = response_data['data']['credit_notes'][0][
        'billed_entity_account'
    ]
    assert (
        invoice_billed_entity_account.billed_entity_id
        == credit_note_billed_entity_account['billed_entity_id']
    )
    assert (
        invoice_billed_entity_account.account_number
        != credit_note_billed_entity_account['account_number']
    )


def test_redeem_credit_shell(
    reseller_locked_invoices,
    bill_repo,
    client,
    invoice_repo,
    credit_note_repo,
    credit_shell_repo,
    get_credit_shell_query_handler,
):
    locked_invoices = reseller_locked_invoices
    bill_aggregate = bill_repo.load(bill_id=locked_invoices[0].bill_id)
    all_invoices = invoice_repo.load_for_bill_id(
        bill_id=locked_invoices[0].bill_id, exclude_issued_to_reseller=False
    )
    sell_side_invoice = None
    for invoice_agg in all_invoices:
        if invoice_agg.invoice.issued_by_type == IssuedByType.RESELLER:
            sell_side_invoice = invoice_agg
    modify_invoice_request = modify_invoice_payload(
        invoice_aggregate=sell_side_invoice,
        bill_aggregate=bill_aggregate,
        transfer_payment_to_new_invoice_account=False,
    )

    response = modify_invoice_api_call(
        modify_invoice_request, sell_side_invoice.bill_id, client
    )
    assert response.status_code == 200
    credit_shell_aggregate = credit_shell_repo.load_for_bill_id(
        locked_invoices[0].bill_id
    )[0]
    redeem_credit_shell_request = redeem_credit_shell_payload(
        credit_shell_id=credit_shell_aggregate.credit_shell_id,
        amount="1 INR",
        payment_mode="cash",
        remarks="added remarks",
    )
    with mock_credit_shell_refund_payment_modes(), mock_auto_refund_payment_service():
        response = redeem_credit_shell_api_call(redeem_credit_shell_request, client)
    assert response.status_code == 200

    created_credit_shell = get_credit_shell_query_handler.handle(
        credit_shell_id=credit_shell_aggregate.credit_shell_id
    )[0]
    assert (
        len(
            [
                csat.remarks
                for csat in created_credit_shell.credit_shell_audit_trails
                if csat.remarks
            ]
        )
        == 1
    )
