import json

import pytest

from prometheus.itests.billing.test_invoice_modification import (
    create_locked_invoices,
    modify_invoice_api_call,
    modify_invoice_payload,
)


def get_credit_note(bill_id, credit_note_id, client):
    url = 'v1/bills/' + bill_id + '/credit-note-templates/' + credit_note_id
    headers = {'X-User-Type': 'super-admin'}
    response = client.get(url, content_type='application/json', headers=headers)
    return response


def test_credit_note_template_returns_charge_details_for_line_item(
    locked_invoices, bill_repo, client, credit_note_repo
):
    bill_aggregate = bill_repo.load(bill_id=locked_invoices[0].bill_id)

    modify_invoice_request = modify_invoice_payload(
        invoice_aggregate=locked_invoices[0],
        bill_aggregate=bill_aggregate,
        transfer_payment_to_new_invoice_account=True,
    )

    response = modify_invoice_api_call(
        modify_invoice_request, locked_invoices[0].bill_id, client
    )

    assert response.status_code == 200
    response_data = response.json
    credit_note_dict = response_data['data']['credit_notes'][0]
    response = get_credit_note(
        bill_aggregate.bill_id, credit_note_dict['credit_note_id'], client
    )
    assert response.status_code == 200
    assert (
        response.json.get('data')
        .get('credit_note')
        .get('credit_note_line_items')[0]
        .get('charge_item_detail')
    )
    assert (
        response.json.get('data')
        .get('credit_note')
        .get('credit_note_line_items')[0]
        .get('item_name'),
        'RoomStay',
    )
    assert (
        response.json.get('data')
        .get('credit_note')
        .get('credit_note_line_items')[0]
        .get('sku_category_id'),
        'stay',
    )
    assert (
        response.json.get('data')
        .get('credit_note')
        .get('credit_note_line_items')[0]
        .get('item_code'),
        {'code_type': 'HSN', 'value': '996331'},
    )


@pytest.fixture
def locked_invoices_with_inclusion(
    active_hotel_aggregate,
    create_booking_payload,
    client,
    booking_repo,
    hotel_repo,
    booking_invoice_group_repo,
    invoice_repo,
    seller_repo,
):
    from treebo_commons.utils import dateutils

    create_booking_payload = json.loads(create_booking_payload)
    create_booking_payload['room_stays'][0]["rate_plan_inclusions"] = [
        {
            "sku_id": "377",
            "pretax_amount": "10",
            "start_date": dateutils.current_datetime().isoformat(),
            "end_date": dateutils.current_datetime().isoformat(),
        }
    ]
    from prometheus.itests.booking.test_booking_v2 import make_booking

    booking_id = make_booking(client, {"data": create_booking_payload})
    return create_locked_invoices(
        active_hotel_aggregate,
        booking_id,
        booking_invoice_group_repo,
        booking_repo,
        client,
        hotel_repo,
        invoice_repo,
        seller_repo,
    )


def test_credit_note_template_returns_charge_clubbed_line_items(
    locked_invoices_with_inclusion, bill_repo, client, credit_note_repo
):
    bill_aggregate = bill_repo.load(bill_id=locked_invoices_with_inclusion[0].bill_id)

    modify_invoice_request = modify_invoice_payload(
        invoice_aggregate=locked_invoices_with_inclusion[0],
        bill_aggregate=bill_aggregate,
        transfer_payment_to_new_invoice_account=True,
    )

    response = modify_invoice_api_call(
        modify_invoice_request, locked_invoices_with_inclusion[0].bill_id, client
    )

    assert response.status_code == 200
    response_data = response.json
    credit_note_dict = response_data['data']['credit_notes'][0]
    response = get_credit_note(
        bill_aggregate.bill_id, credit_note_dict['credit_note_id'], client
    )
    assert response.status_code == 200
    assert (
        len(response.json.get('data').get('credit_note').get('credit_note_line_items'))
        == 1
    )
