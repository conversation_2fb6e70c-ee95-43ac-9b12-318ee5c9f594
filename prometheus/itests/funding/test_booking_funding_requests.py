from treebo_commons.utils import dateutils

from prometheus.itests.booking.test_booking_v2 import make_booking
from prometheus.itests.payload_generators.booking_payload_generators import (
    create_new_booking_payload,
)
from prometheus.tests.mockers import (
    mock_catalog_client,
    mock_get_maximum_amount_allowed_for_manual_funding,
    mock_is_booking_funding_enabled,
    mock_rate_manager_client,
    mock_role_manager,
)
from ths_common.constants.booking_constants import BookingStatus
from ths_common.constants.funding_constants import FundingType


def get_booking_funding_request(
    client,
    booking_id,
    expected_status_code=200,
):
    with mock_catalog_client(
        hotel_in_posttax=True
    ), mock_role_manager(), mock_rate_manager_client():
        url = f"/v1/booking-funding?booking_id={booking_id}"
        response = client.get(
            url,
            content_type="application/json",
            headers={"X-User-Type": "super-admin"},
        )
        assert response.status_code == expected_status_code
        if expected_status_code != 200:
            return response
        return response.json['data']


def update_booking_funding_request(
    client,
    booking_id,
    payload,
    expected_status_code=200,
):
    with mock_catalog_client(
        hotel_in_posttax=True
    ), mock_role_manager(), mock_rate_manager_client():
        url = f"/v1/booking-funding/{booking_id}"
        response = client.put(
            url,
            json=payload,
            content_type="application/json",
            headers={"X-User-Type": "super-admin"},
        )
        assert response.status_code == expected_status_code
        if expected_status_code != 200:
            return response
        return response.json['data']


def create_manual_funding_payload(amount, funding_type, reason):
    return {
        "data": {
            "amount": f"{amount} INR",
            "funding_type": funding_type,
            "reason": reason,
        }
    }


def test_add_manual_funding(client, bill_repo, booking_repo):
    checkin_date = dateutils.add(dateutils.current_datetime(), days=10)
    checkout_date = dateutils.add(dateutils.current_datetime(), days=12)

    booking_payload = create_new_booking_payload(
        checkin_date=checkin_date,
        checkout_date=checkout_date,
        number_of_rooms=1,
        number_of_guest_stays=1,
        status=BookingStatus.CONFIRMED,
        reference_number="test-booking",
        discount_value=100,
    )

    with mock_is_booking_funding_enabled():
        response = make_booking(
            client,
            {"data": booking_payload},
            expected_status_code=200,
            return_complete_response=True,
        )
        booking_id = response["data"]["booking_id"]

    payload = create_manual_funding_payload(
        1000, FundingType.MANUAL_FUNDING.value, "test-case"
    )
    response = update_booking_funding_request(client, booking_id, payload)
    assert response["amount"] == "1000.00"
    assert response["funding_type"] == FundingType.MANUAL_FUNDING.value
    assert response["reason"] == "test-case"

    payload = create_manual_funding_payload(3000, "manual_funding", "test-case")
    response = update_booking_funding_request(client, booking_id, payload)
    assert response["amount"] == "3000.00"
    assert response["funding_type"] == FundingType.MANUAL_FUNDING.value
    assert response["reason"] == "test-case"

    payload = create_manual_funding_payload(6000, "manual_funding", "test-case")
    with mock_get_maximum_amount_allowed_for_manual_funding():
        update_booking_funding_request(
            client, booking_id, payload, expected_status_code=400
        )
