import json

import pytest

from prometheus.itests.api_wrappers.billing_wrappers import edit_charge
from prometheus.itests.booking.test_booking_with_rate_plan import (
    add_non_room_night_inclusions,
    create_booking_payload_with_rate_plan,
    create_booking_with_rate_pan,
)
from prometheus.tests.mockers import (
    mock_catalog_client,
    mock_rate_manager_client,
    mock_role_manager,
    mock_tax_calculator_service,
)
from ths_common.constants.catalog_constants import SellerType


def edit_billing_instruction_payload(resource_version, split_percentage):
    return {
        "data": {
            "billing_instructions": [
                {
                    "billed_entity_account": {
                        "account_number": 2,
                        "billed_entity_id": 1,
                    },
                    "payment_instruction": "pay_at_checkout",
                    "split_percentage": split_percentage,
                },
                {
                    "billed_entity_account": {
                        "account_number": 1,
                        "billed_entity_id": 1,
                    },
                    "payment_instruction": "pay_at_checkout",
                    "split_percentage": 100 - split_percentage,
                },
            ]
        },
        "resource_version": resource_version,
    }


def edit_charge_payload(resource_version):
    return {"data": {"pretax_amount": 100}, "resource_version": resource_version}


def test_edit_split_charge_billing_instructions(
    create_booking_payload, client, bill_repo, booking_repo, sku_category_repo
):
    with mock_role_manager(), mock_rate_manager_client():
        with mock_catalog_client(mocked_seller_type=SellerType.MARKETPLACE.value):
            url = 'v1/bookings'
            payload = json.dumps({"data": json.loads(create_booking_payload)})
            response = client.post(
                url,
                data=payload,
                content_type='application/json',
                headers={'X-User-Type': 'super-admin'},
            )
            assert response.status_code == 200
            bookings_response = json.loads(response.data.decode('utf-8'))
            assert len(bookings_response['data']['room_stays']) > 0
            bill_id = bookings_response['data']['bill_id']

    charge_id = '1'
    bill_aggregate = bill_repo.load(bill_id)
    percentage = 50
    payload = edit_billing_instruction_payload(bill_aggregate.version, percentage)
    edit_charge(client, bill_id, charge_id, payload)

    bill_aggregate = bill_repo.load(bill_id)
    for charge in bill_aggregate.charges:
        if charge.charge_id == int(charge_id):
            assert len(charge.charge_splits) > 1


def test_edit_slab_based_split_charge_billing_instructions(
    create_booking_payload, client, bill_repo, booking_repo, sku_category_repo
):
    with mock_role_manager(), mock_rate_manager_client():
        with mock_catalog_client(mocked_seller_type=SellerType.MARKETPLACE.value):
            url = 'v1/bookings'
            payload = json.dumps({"data": json.loads(create_booking_payload)})
            response = client.post(
                url,
                data=payload,
                content_type='application/json',
                headers={'X-User-Type': 'super-admin'},
            )
            assert response.status_code == 200
            bookings_response = json.loads(response.data.decode('utf-8'))
            assert len(bookings_response['data']['room_stays']) > 0
            bill_id = bookings_response['data']['bill_id']
    sku_category_aggregate = sku_category_repo.load_all()[1]
    sku_category_aggregate.sku_category.has_slab_based_taxation = True
    sku_category_repo.update(sku_category_aggregate)
    charge_id = '1'
    bill_aggregate = bill_repo.load(bill_id)
    percentage = 50
    payload = edit_billing_instruction_payload(bill_aggregate.version, percentage)
    edit_charge(client, bill_id, charge_id, payload)

    bill_aggregate = bill_repo.load(bill_id)
    for charge in bill_aggregate.charges:
        if charge.charge_id == int(charge_id):
            assert len(charge.charge_splits) > 1


def test_edit_slab_based_split_charge_prices(
    create_booking_payload, client, bill_repo, booking_repo, sku_category_repo
):
    with mock_role_manager(), mock_rate_manager_client():
        with mock_catalog_client(mocked_seller_type=SellerType.MARKETPLACE.value):
            url = 'v1/bookings'
            payload = json.dumps({"data": json.loads(create_booking_payload)})
            response = client.post(
                url,
                data=payload,
                content_type='application/json',
                headers={'X-User-Type': 'super-admin'},
            )
            assert response.status_code == 200
            bookings_response = json.loads(response.data.decode('utf-8'))
            assert len(bookings_response['data']['room_stays']) > 0
            bill_id = bookings_response['data']['bill_id']
    sku_category_aggregate = sku_category_repo.load_all()[1]
    sku_category_aggregate.sku_category.has_slab_based_taxation = True
    sku_category_repo.update(sku_category_aggregate)
    charge_id = '1'
    bill_aggregate = bill_repo.load(bill_id)
    percentage = 50
    payload = edit_billing_instruction_payload(bill_aggregate.version, percentage)
    edit_charge(client, bill_id, charge_id, payload)

    bill_aggregate = bill_repo.load(bill_id)
    for charge in bill_aggregate.charges:
        if charge.charge_id == int(charge_id):
            assert len(charge.charge_splits) > 1

    payload = edit_charge_payload(bill_aggregate.version)
    response, status = edit_charge(client, bill_id, charge_id, payload)
    assert status == 400
    assert response['errors'][0][
        'message'
    ], "Cannot edit this charge as this has splits and slabbed taxation."


def test_edit_split_charge_billing_instructions_with_rateplan(
    create_booking_payload, client, bill_repo, booking_repo, sku_category_repo
):
    bookings_response = create_booking_with_rate_pan(
        create_booking_payload, client, include_non_room_inclusion_in_rate_plan=True
    )
    booking_id = bookings_response['data']['booking_id']
    bill_id = bookings_response['data']['bill_id']

    add_non_room_night_inclusions(client, booking_id)
    charge_id = '1'
    bill_aggregate = bill_repo.load(bill_id)
    percentage = 50
    payload = edit_billing_instruction_payload(bill_aggregate.version, percentage)
    edit_charge(client, bill_id, charge_id, payload)

    addon_charge_ids = []
    bill_aggregate = bill_repo.load(bill_id)
    for charge in bill_aggregate.charges:
        if charge.charge_id == int(charge_id):
            assert len(charge.charge_splits) > 1
            addon_charge_ids = charge.addon_charge_ids

        if charge.charge_id in addon_charge_ids:
            assert len(charge.charge_splits) > 1
