import json

from treebo_commons.utils import dateutils

from prometheus.itests.api_wrappers.expense_wrappers import add_expenses_v3
from prometheus.itests.api_wrappers.inventory import create_inventory_block
from prometheus.itests.booking.test_booking_v2 import make_booking
from prometheus.itests.booking.test_roomstay_v2_api import reduce_rooms_stay_by_shifting
from prometheus.itests.payload_generators.booking_payload_generators import (
    create_new_booking_payload,
)
from prometheus.itests.payload_generators.expense_payload_generators import (
    create_expenses_v3_request,
)
from ths_common.constants.billing_constants import BilledEntityCategory
from ths_common.constants.booking_constants import BookingStatus, ServiceTypes
from ths_common.constants.inventory_constants import (
    InventoryBlockStatus,
    InventoryBlockType,
)


def fetch_inventory_blocks(
    client,
    hotel_id,
    booking_id,
    expected_status_code=200,
):
    url = f"v1/hotels/{hotel_id}/inventory-blocks?booking_id={booking_id}"
    response = client.get(
        url,
        content_type="application/json",
        headers={"X-User-Type": "super-admin"},
    )
    assert response.status_code == expected_status_code
    if expected_status_code != 200:
        return response
    return response.json["data"]


def test_eci_lco_expenses_and_inventory_blocking(
    client, booking_repo, create_booking_payload
):
    """
    Test adding ECI and LCO expenses and validate inventory blocking.
    """
    # Step 1: Set up booking aggregate
    booking_id = make_booking(client, {"data": json.loads(create_booking_payload)})
    booking_aggregate = booking_repo.load(booking_id)

    # Step 2: Add ECI (Early Check-In) expense
    eci_payload = create_expenses_v3_request(
        service_context=dict(
            service_type=ServiceTypes.EARLY_CHECKIN.value, service_details=dict(hours=3)
        ),
        charge_to=["2"],
        quantity=1,
        dates=[booking_aggregate.booking.checkin_date],
    )
    response_eci = add_expenses_v3(
        client, booking_aggregate.booking.booking_id, eci_payload
    )
    assert (
        response_eci["data"][0]["service_context"]["service_type"]
        == ServiceTypes.EARLY_CHECKIN.value
    )

    # Step 3: Add LCO (Late Check-Out) expense
    lco_payload = create_expenses_v3_request(
        service_context=dict(
            service_type=ServiceTypes.LATE_CHECKOUT.value, service_details=dict(hours=2)
        ),
        charge_to=["2"],
        quantity=1,
        dates=[booking_aggregate.booking.checkout_date],
    )
    response_lco = add_expenses_v3(
        client, booking_aggregate.booking.booking_id, lco_payload
    )
    assert (
        response_lco["data"][0]["service_context"]["service_type"]
        == ServiceTypes.LATE_CHECKOUT.value
    )

    # Step 4: Validate inventory blocking for ECI and LCO
    inventory_data = fetch_inventory_blocks(
        client, booking_aggregate.hotel_id, booking_aggregate.booking.booking_id
    )
    assert len(inventory_data) == 2
    assert any(
        block["block_type"] == InventoryBlockType.EARLY_CHECKIN_BLOCK.value
        for block in inventory_data
    )
    assert any(
        block["block_type"] == InventoryBlockType.LATE_CHECKOUT_BLOCK.value
        for block in inventory_data
    )


def test_eci_lco_expenses_with_invalid_payload_should_raise_error(
    client, booking_repo, create_booking_payload
):
    """
    Test adding ECI and LCO expenses and validate inventory blocking.
    """
    # Step 1: Set up booking aggregate
    booking_id = make_booking(client, {"data": json.loads(create_booking_payload)})
    booking_aggregate = booking_repo.load(booking_id)

    # Test 1: Add ECI (Early Check-In) expense with quantity 2 should raise 400
    eci_payload = create_expenses_v3_request(
        service_context=dict(
            service_type=ServiceTypes.EARLY_CHECKIN.value, service_details=dict(hours=3)
        ),
        charge_to=["2"],
        quantity=2,
    )

    add_expenses_v3(
        client, booking_aggregate.booking.booking_id, eci_payload, status_code=400
    )

    # Test 2: Add ECI (Early Check-In) expense applicablity date other than check-in date should raise 400
    eci_payload = create_expenses_v3_request(
        service_context=dict(
            service_type=ServiceTypes.EARLY_CHECKIN.value, service_details=dict(hours=3)
        ),
        charge_to=["2"],
        quantity=1,
        dates=[booking_aggregate.booking.checkout_date],
    )

    add_expenses_v3(
        client, booking_aggregate.booking.booking_id, eci_payload, status_code=400
    )

    # Test : Add LCO (Late Check-Out) expense with quantity 2 should raise 400
    lco_payload = create_expenses_v3_request(
        service_context=dict(
            service_type=ServiceTypes.LATE_CHECKOUT.value, service_details=dict(hours=2)
        ),
        charge_to=["2"],
        quantity=2,
    )
    add_expenses_v3(
        client,
        booking_aggregate.booking.booking_id,
        lco_payload,
        status_code=400,
    )

    # Test : Add LCO (Late Check-Out) expense applicablity date other than checkout date should raise 400
    lco_payload = create_expenses_v3_request(
        service_context=dict(
            service_type=ServiceTypes.LATE_CHECKOUT.value, service_details=dict(hours=2)
        ),
        charge_to=["2"],
        quantity=1,
        dates=[booking_aggregate.booking.checkin_date],
    )
    add_expenses_v3(
        client,
        booking_aggregate.booking.booking_id,
        lco_payload,
        status_code=400,
    )


def test_eci_lco_with_already_blocked_inventory_should_consume_that_block_only(
    client, booking_repo, create_booking_payload
):
    """
    Test adding ECI and LCO expenses and validate inventory blocking.
    """
    # Step 1: Set up booking aggregate
    booking_id = make_booking(client, {"data": json.loads(create_booking_payload)})
    booking_aggregate = booking_repo.load(booking_id)
    room_stay = booking_aggregate.room_stays[0]

    payload_to_block_inventory = {
        "action": "block",
        "booking_id": booking_aggregate.booking.booking_id,
        "inventory_blocks": [
            {
                "room_type_id": room_stay.room_type_id,
                "start_date": dateutils.date_to_ymd_str(
                    dateutils.subtract(room_stay.checkin_date, days=1)
                ),
                "end_date": dateutils.date_to_ymd_str(room_stay.checkin_date),
            },
            {
                "room_type_id": room_stay.room_type_id,
                "start_date": dateutils.date_to_ymd_str(room_stay.checkout_date),
                "end_date": dateutils.date_to_ymd_str(
                    dateutils.add(room_stay.checkout_date, days=1)
                ),
            },
        ],
    }

    create_inventory_block(
        client, booking_aggregate.hotel_id, payload_to_block_inventory
    )

    inventory_data = fetch_inventory_blocks(
        client, booking_aggregate.hotel_id, booking_aggregate.booking.booking_id
    )
    temp_blocks = [
        block
        for block in inventory_data
        if block["block_type"] == InventoryBlockType.TEMP_BLOCK.value
    ]
    for block in temp_blocks:
        assert block["status"] == InventoryBlockStatus.BLOCKED.value
    assert len(temp_blocks) == 2

    eci_start_date = dateutils.date_to_ymd_str(
        dateutils.subtract(room_stay.checkin_date, days=1)
    )
    block_for_eci = [
        block for block in temp_blocks if block["start_date"] == eci_start_date
    ]
    assert len(block_for_eci) == 1

    lci_end_date = dateutils.date_to_ymd_str(
        dateutils.add(room_stay.checkout_date, days=1)
    )
    block_for_lco = [
        block for block in temp_blocks if block["end_date"] == lci_end_date
    ]
    assert len(block_for_lco) == 1

    # Step 2: Add ECI (Early Check-In) expense using already blocked inventory
    eci_payload = create_expenses_v3_request(
        service_context=dict(
            service_type=ServiceTypes.EARLY_CHECKIN.value,
            service_details=dict(
                hours=3, inventory_block_id=block_for_eci[0]["block_id"]
            ),
        ),
        charge_to=["2"],
        quantity=1,
        dates=[booking_aggregate.booking.checkin_date],
    )
    response_eci = add_expenses_v3(
        client, booking_aggregate.booking.booking_id, eci_payload
    )
    eci_service_context = response_eci["data"][0]["service_context"]
    assert eci_service_context["service_type"] == ServiceTypes.EARLY_CHECKIN.value
    assert eci_service_context["service_details"]["hours"] == 3
    assert (
        eci_service_context["service_details"]["inventory_block_id"]
        == block_for_eci[0]["block_id"]
    )

    # Step 3: Add LCO (Late Check-Out) expense using already blocked inventory
    lco_payload = create_expenses_v3_request(
        service_context=dict(
            service_type=ServiceTypes.LATE_CHECKOUT.value,
            service_details=dict(
                hours=2,
                inventory_block_id=block_for_lco[0]["block_id"],
            ),
        ),
        charge_to=["2"],
        quantity=1,
        dates=[booking_aggregate.booking.checkout_date],
    )
    response_lco = add_expenses_v3(
        client, booking_aggregate.booking.booking_id, lco_payload
    )
    lco_service_context = response_lco["data"][0]["service_context"]

    assert lco_service_context["service_type"] == ServiceTypes.LATE_CHECKOUT.value
    assert lco_service_context["service_details"]["hours"] == 2
    assert (
        lco_service_context["service_details"]["inventory_block_id"]
        == block_for_lco[0]["block_id"]
    )

    # Step 4: Validate inventory blocking for ECI and LCO
    inventory_data = fetch_inventory_blocks(
        client, booking_aggregate.hotel_id, booking_aggregate.booking.booking_id
    )
    # Check if the no more inventory blocks are created
    assert len(inventory_data) == 2
    eci_block = [
        block
        for block in inventory_data
        if block["block_id"] == block_for_eci[0]["block_id"]
    ][0]
    lco_block = [
        block
        for block in inventory_data
        if block["block_id"] == block_for_lco[0]["block_id"]
    ][0]

    assert eci_block["block_type"] == InventoryBlockType.EARLY_CHECKIN_BLOCK.value
    assert eci_block["status"] == InventoryBlockStatus.PROVISIONALLY_CONSUMED.value

    assert lco_block["block_type"] == InventoryBlockType.LATE_CHECKOUT_BLOCK.value
    assert lco_block["status"] == InventoryBlockStatus.PROVISIONALLY_CONSUMED.value


def test_eci_lco_with_invalid_inventory_block_should_raise_error(
    client, booking_repo, create_booking_payload
):
    """
    Test adding ECI and LCO expenses and validate inventory blocking.
    """
    # Step 1: Set up booking aggregate
    booking_id = make_booking(client, {"data": json.loads(create_booking_payload)})
    booking_aggregate = booking_repo.load(booking_id)
    room_stay = booking_aggregate.room_stays[0]

    payload_to_block_inventory = {
        "action": "block",
        "booking_id": booking_aggregate.booking.booking_id,
        "inventory_blocks": [
            {
                "room_type_id": room_stay.room_type_id,
                "start_date": dateutils.date_to_ymd_str(
                    dateutils.subtract(room_stay.checkin_date, days=1)
                ),
                "end_date": dateutils.date_to_ymd_str(room_stay.checkin_date),
            },
            {
                "room_type_id": room_stay.room_type_id,
                "start_date": dateutils.date_to_ymd_str(room_stay.checkout_date),
                "end_date": dateutils.date_to_ymd_str(
                    dateutils.add(room_stay.checkout_date, days=1)
                ),
            },
        ],
    }

    create_inventory_block(
        client, booking_aggregate.hotel_id, payload_to_block_inventory
    )

    inventory_data = fetch_inventory_blocks(
        client, booking_aggregate.hotel_id, booking_aggregate.booking.booking_id
    )
    temp_blocks = [
        block
        for block in inventory_data
        if block["block_type"] == InventoryBlockType.TEMP_BLOCK.value
    ]
    for block in temp_blocks:
        assert block["status"] == InventoryBlockStatus.BLOCKED.value
    assert len(temp_blocks) == 2

    eci_start_date = dateutils.date_to_ymd_str(
        dateutils.subtract(room_stay.checkin_date, days=1)
    )
    block_for_eci = [
        block for block in temp_blocks if block["start_date"] == eci_start_date
    ]
    assert len(block_for_eci) == 1

    lci_end_date = dateutils.date_to_ymd_str(
        dateutils.add(room_stay.checkout_date, days=1)
    )
    block_for_lco = [
        block for block in temp_blocks if block["end_date"] == lci_end_date
    ]
    assert len(block_for_lco) == 1

    # Step 2: Add ECI (Early Check-In) expense using already blocked inventory (invalid case using lco block)
    eci_payload = create_expenses_v3_request(
        service_context=dict(
            service_type=ServiceTypes.EARLY_CHECKIN.value,
            service_details=dict(
                hours=3, inventory_block_id=block_for_lco[0]["block_id"]
            ),
        ),
        charge_to=["2"],
        quantity=1,
        dates=[booking_aggregate.booking.checkin_date],
    )
    add_expenses_v3(
        client,
        booking_aggregate.booking.booking_id,
        eci_payload,
        status_code=400,
    )

    # Step 3: Add LCO (Late Check-Out) expense using already blocked inventory (invalid case using eci block)
    lco_payload = create_expenses_v3_request(
        service_context=dict(
            service_type=ServiceTypes.LATE_CHECKOUT.value,
            service_details=dict(
                hours=2,
                inventory_block_id=block_for_eci[0]["block_id"],
            ),
        ),
        charge_to=["2"],
        quantity=1,
        dates=[booking_aggregate.booking.checkout_date],
    )
    add_expenses_v3(
        client,
        booking_aggregate.booking.booking_id,
        lco_payload,
        status_code=400,
    )

    # Step 4: Validate inventory blocking
    inventory_data = fetch_inventory_blocks(
        client, booking_aggregate.hotel_id, booking_aggregate.booking.booking_id
    )
    # Check if the no more inventory blocks are created
    assert len(inventory_data) == 2
    for block in inventory_data:
        assert block["status"] == InventoryBlockStatus.BLOCKED.value
        assert block["block_type"] == InventoryBlockType.TEMP_BLOCK.value


def test_stay_date_change_after_eci_should_remove_eci(
    client, booking_repo, room_type_inventory_repo
):
    # Step 1: Set up booking aggregate
    checkin_date = dateutils.add(dateutils.current_datetime(), days=13)
    checkout_date = dateutils.add(dateutils.current_datetime(), days=15)
    create_booking_payload = create_new_booking_payload(
        checkin_date,
        checkout_date,
        number_of_rooms=1,
        number_of_guest_stays=2,
        status=BookingStatus.CONFIRMED,
        channel="direct",
        default_billed_entity_category=BilledEntityCategory.BOOKER.value,
        set_travel_agent_details=False,
        set_company_details=False,
    )
    booking_id = make_booking(client, {"data": create_booking_payload})
    booking_aggregate = booking_repo.load(booking_id)

    # Step 2: Add ECI (Early Check-In) expense
    eci_payload = create_expenses_v3_request(
        service_context=dict(
            service_type=ServiceTypes.EARLY_CHECKIN.value, service_details=dict(hours=3)
        ),
        charge_to=["2"],
        quantity=1,
        dates=[booking_aggregate.booking.checkin_date],
        single_charge_split=True,
    )
    response_eci = add_expenses_v3(
        client, booking_aggregate.booking.booking_id, eci_payload
    )
    assert (
        response_eci["data"][0]["service_context"]["service_type"]
        == ServiceTypes.EARLY_CHECKIN.value
    )

    booking_aggregate = booking_repo.load(booking_id)
    start = dateutils.subtract(booking_aggregate.booking.checkin_date, days=1).date()
    end = dateutils.subtract(booking_aggregate.booking.checkout_date, days=1).date()
    inventory_counts = room_type_inventory_repo.load_multiple(
        booking_aggregate.hotel_id,
        from_date=start,
        to_date=end,
        room_type_ids=[booking_aggregate.room_stays[0].room_type_id],
    )
    inventory_counts = inventory_counts[0].availability_grouped_by_date

    # Step 3: shift the bookings checkout one day earlier
    booking_aggregate = booking_repo.load(booking_id)
    reduce_rooms_stay_by_shifting(
        client,
        booking_aggregate.booking_id,
        13,
        15,
        resource_version=booking_aggregate.booking.version,
    )
    booking_aggregate = booking_repo.load(booking_id)
    start = dateutils.subtract(booking_aggregate.booking.checkin_date, days=1).date()
    end = dateutils.subtract(booking_aggregate.booking.checkout_date, days=1).date()
    new_inventory_counts = room_type_inventory_repo.load_multiple(
        booking_aggregate.hotel_id,
        from_date=start,
        to_date=end,
        room_type_ids=[booking_aggregate.room_stays[0].room_type_id],
    )
    new_inventory_counts = new_inventory_counts[0].availability_grouped_by_date

    # inventory blocked by ECI (checkin date - 1) should be released

    assert (
        new_inventory_counts[start].actual_count
        == inventory_counts[start].actual_count + 1
    )


def test_stay_date_change_after_lco_should_remove_lco(
    client, booking_repo, room_type_inventory_repo
):
    # Step 1: Set up booking aggregate
    checkin_date = dateutils.add(dateutils.current_datetime(), days=13)
    checkout_date = dateutils.add(dateutils.current_datetime(), days=15)
    create_booking_payload = create_new_booking_payload(
        checkin_date,
        checkout_date,
        number_of_rooms=1,
        number_of_guest_stays=2,
        status=BookingStatus.CONFIRMED,
        channel="direct",
        default_billed_entity_category=BilledEntityCategory.BOOKER.value,
        set_travel_agent_details=False,
        set_company_details=False,
    )
    booking_id = make_booking(client, {"data": create_booking_payload})
    booking_aggregate = booking_repo.load(booking_id)

    # Step 2: Add LCO (Late Check-Out) expense
    lco_payload = create_expenses_v3_request(
        service_context=dict(
            service_type=ServiceTypes.LATE_CHECKOUT.value, service_details=dict(hours=2)
        ),
        charge_to=["2"],
        quantity=1,
        dates=[booking_aggregate.booking.checkout_date],
    )
    response_lco = add_expenses_v3(
        client, booking_aggregate.booking.booking_id, lco_payload
    )
    assert (
        response_lco["data"][0]["service_context"]["service_type"]
        == ServiceTypes.LATE_CHECKOUT.value
    )

    booking_aggregate = booking_repo.load(booking_id)
    start = booking_aggregate.booking.checkin_date.date()
    end = booking_aggregate.booking.checkout_date.date()
    inventory_counts = room_type_inventory_repo.load_multiple(
        booking_aggregate.hotel_id,
        from_date=start,
        to_date=end,
        room_type_ids=[booking_aggregate.room_stays[0].room_type_id],
    )
    inventory_counts = inventory_counts[0].availability_grouped_by_date

    # Step 3: shift the booking one day earlier
    booking_aggregate = booking_repo.load(booking_id)
    reduce_rooms_stay_by_shifting(
        client,
        booking_aggregate.booking_id,
        13,
        15,
        resource_version=booking_aggregate.booking.version,
    )
    booking_aggregate = booking_repo.load(booking_id)

    new_inventory_counts = room_type_inventory_repo.load_multiple(
        booking_aggregate.hotel_id,
        from_date=start,
        to_date=end,
        room_type_ids=[booking_aggregate.room_stays[0].room_type_id],
    )
    new_inventory_counts = new_inventory_counts[0].availability_grouped_by_date

    # inventory blocked by LCO (checkout date) should be released

    assert (
        new_inventory_counts[end].actual_count == inventory_counts[end].actual_count + 1
    )


def test_pre_pone_stay_should_consume_eci_block_for_the_action(
    client, booking_repo, room_type_inventory_repo
):
    """
    Test adding ECI and LCO expenses and validate inventory blocking.
    """
    # Step 1: Set up booking aggregate
    checkin_date = dateutils.add(dateutils.current_datetime(), days=13)
    checkout_date = dateutils.add(dateutils.current_datetime(), days=15)
    create_booking_payload = create_new_booking_payload(
        checkin_date,
        checkout_date,
        number_of_rooms=1,
        number_of_guest_stays=2,
        status=BookingStatus.CONFIRMED,
        channel="direct",
        default_billed_entity_category=BilledEntityCategory.BOOKER.value,
        set_travel_agent_details=False,
        set_company_details=False,
    )
    booking_id = make_booking(client, {"data": create_booking_payload})
    booking_aggregate = booking_repo.load(booking_id)

    # Step 2: Add ECI (Early Check-In) expense
    eci_payload = create_expenses_v3_request(
        service_context=dict(
            service_type=ServiceTypes.EARLY_CHECKIN.value, service_details=dict(hours=3)
        ),
        charge_to=["2"],
        quantity=1,
        dates=[booking_aggregate.booking.checkin_date],
        single_charge_split=True,
    )
    response_eci = add_expenses_v3(
        client, booking_aggregate.booking.booking_id, eci_payload
    )
    assert (
        response_eci["data"][0]["service_context"]["service_type"]
        == ServiceTypes.EARLY_CHECKIN.value
    )

    booking_aggregate = booking_repo.load(booking_id)
    start = dateutils.subtract(booking_aggregate.booking.checkin_date, days=1).date()
    end = dateutils.subtract(booking_aggregate.booking.checkout_date, days=1).date()
    inventory_counts = room_type_inventory_repo.load_multiple(
        booking_aggregate.hotel_id,
        from_date=start,
        to_date=end,
        room_type_ids=[booking_aggregate.room_stays[0].room_type_id],
    )
    inventory_counts = inventory_counts[0].availability_grouped_by_date

    # Step 3: shift the booking one day earlier
    booking_aggregate = booking_repo.load(booking_id)
    reduce_rooms_stay_by_shifting(
        client,
        booking_aggregate.booking_id,
        13,
        15,
        pre_pone_checkin=True,
        resource_version=booking_aggregate.booking.version,
    )
    booking_aggregate = booking_repo.load(booking_id)

    new_inventory_counts = room_type_inventory_repo.load_multiple(
        booking_aggregate.hotel_id,
        from_date=start,
        to_date=end,
        room_type_ids=[booking_aggregate.room_stays[0].room_type_id],
    )
    new_inventory_counts = new_inventory_counts[0].availability_grouped_by_date

    # Inventory count should be same as the pre shifting canceled the eci block and consumed that inventory
    for date, av in new_inventory_counts.items():
        assert av.actual_count == inventory_counts[date].actual_count
