from treebo_commons.utils import dateutils

from prometheus.itests.api_wrappers.booking_life_cycle_wrappers import make_booking
from prometheus.itests.api_wrappers.overflow_wrappers import swap_room_stay_overflows
from prometheus.itests.payload_generators.booking_payload_generators import (
    create_new_booking_payload,
)
from ths_common.constants.booking_constants import BookingStatus


def test_swap_one_overflowed_room_stay_with_two_confirmed_room_stays(
    active_hotel_aggregate,
    room_type_inventory_repo,
    room_stay_overflow_repo,
    create_booking_payload,
    client,
):
    # d1, d2 - room nights
    checkin_date = dateutils.add(dateutils.current_datetime(), days=20)
    checkout_date = dateutils.add(dateutils.current_datetime(), days=22)
    create_booking_payload = create_new_booking_payload(
        checkin_date=checkin_date,
        checkout_date=checkout_date,
        number_of_rooms=1,
        number_of_guest_stays=1,
        status=BookingStatus.CONFIRMED,
        reference_number='REF-1',
    )
    payload = {"data": create_booking_payload}
    make_booking(client, payload)

    # d3, d4 - room nights
    checkin_date = dateutils.add(dateutils.current_datetime(), days=22)
    checkout_date = dateutils.add(dateutils.current_datetime(), days=24)
    create_booking_payload = create_new_booking_payload(
        checkin_date,
        checkout_date,
        number_of_rooms=1,
        number_of_guest_stays=1,
        status=BookingStatus.CONFIRMED,
        reference_number='REF-2',
    )
    payload = {"data": create_booking_payload}
    make_booking(client, payload)

    # d1, d2, d3, d4 - room nights
    checkin_date = dateutils.add(dateutils.current_datetime(), days=20)
    checkout_date = dateutils.add(dateutils.current_datetime(), days=24)
    create_booking_payload = create_new_booking_payload(
        checkin_date,
        checkout_date,
        number_of_rooms=1,
        number_of_guest_stays=2,
        status=BookingStatus.CONFIRMED,
        reference_number='REF-3',
    )
    payload = {"data": create_booking_payload}
    make_booking(client, payload)

    # d4, d5 - room nights
    checkin_date = dateutils.add(dateutils.current_datetime(), days=23)
    checkout_date = dateutils.add(dateutils.current_datetime(), days=25)
    create_booking_payload = create_new_booking_payload(
        checkin_date,
        checkout_date,
        number_of_rooms=1,
        number_of_guest_stays=2,
        status=BookingStatus.CONFIRMED,
        reference_number='REF-4',
        channel='direct',
    )
    payload = {"data": create_booking_payload}
    d4_d5_booking_id = make_booking(client, payload)

    room_type_inventory_aggregates = room_type_inventory_repo.load_multiple(
        active_hotel_aggregate.hotel.hotel_id,
        dateutils.add(dateutils.current_date(), days=20),
        dateutils.add(dateutils.current_date(), days=25),
        room_type_ids=['rt01'],
    )

    room_type_inventory_aggregates[0].get_availability_for_date(
        dateutils.add(dateutils.current_date(), days=24)
    ).update_count(0)
    room_type_inventory_repo.update(room_type_inventory_aggregates[0])

    # d1, d2, d3 - room nights
    checkin_date = dateutils.add(dateutils.current_datetime(), days=20)
    checkout_date = dateutils.add(dateutils.current_datetime(), days=23)
    create_booking_payload = create_new_booking_payload(
        checkin_date,
        checkout_date,
        number_of_rooms=1,
        number_of_guest_stays=2,
        status=BookingStatus.CONFIRMED,
        reference_number='REF-5',
        channel='direct',
    )
    payload = {"data": create_booking_payload}

    # d2, d3, d4, d5 - room nights
    room_stay_two = payload['data']['room_stays'][0].copy()
    room_stay_two['checkin_date'] = dateutils.add(
        dateutils.current_datetime(), days=21
    ).isoformat()
    room_stay_two['checkout_date'] = dateutils.add(
        dateutils.current_datetime(), days=25
    ).isoformat()
    room_stay_two['prices'] = [
        {
            "applicable_date": dateutils.add(
                dateutils.current_datetime(), days=21
            ).isoformat(),
            "bill_to_type": "company",
            "posttax_amount": 118,
            "type": "non-credit",
        },
        {
            "applicable_date": dateutils.add(
                dateutils.current_datetime(), days=22
            ).isoformat(),
            "bill_to_type": "company",
            "posttax_amount": 118,
            "type": "non-credit",
        },
        {
            "applicable_date": dateutils.add(
                dateutils.current_datetime(), days=23
            ).isoformat(),
            "bill_to_type": "company",
            "posttax_amount": 118,
            "type": "non-credit",
        },
        {
            "applicable_date": dateutils.add(
                dateutils.current_datetime(), days=24
            ).isoformat(),
            "bill_to_type": "company",
            "posttax_amount": 118,
            "type": "non-credit",
        },
    ]
    payload['data']['room_stays'].append(room_stay_two)
    response = make_booking(
        client=client, payload=payload, return_complete_response=True
    )
    latest_booking_id = response['data']['booking_id']
    assert response['data']['room_stays'][0]['is_overflow'] is False
    assert response['data']['room_stays'][1]['is_overflow'] is True
    rs_overflows = room_stay_overflow_repo.get_overflowed_room_stays_for_booking(
        latest_booking_id
    )
    assert len(rs_overflows) == 1
    assert rs_overflows[0].room_stay_overflow.room_stay_id == 2
    assert rs_overflows[0].room_stay_overflow.start_date == dateutils.to_date(
        dateutils.add(dateutils.current_datetime(), days=21)
    )
    assert rs_overflows[0].room_stay_overflow.end_date == dateutils.to_date(
        dateutils.add(dateutils.current_datetime(), days=24)
    )

    swap_payload = {
        "data": {
            "overflows": [
                {
                    "action": "unmark",
                    "booking_id": latest_booking_id,
                    "room_stay_id": 2,
                },
                {"action": "mark", "booking_id": d4_d5_booking_id, "room_stay_id": 1},
                {"action": "mark", "booking_id": latest_booking_id, "room_stay_id": 1},
            ]
        }
    }
    swap_room_stay_overflows(
        client, active_hotel_aggregate.hotel.hotel_id, swap_payload
    )
    rs_overflows = room_stay_overflow_repo.get_overflowed_room_stays_for_booking(
        latest_booking_id
    )
    assert (
        len(rs_overflows) == 1
    ), "There should be only 1 overflow in the latest booking"
    assert (
        rs_overflows[0].room_stay_overflow.room_stay_id == 1
    ), "Room stay with id 1 should be marked overflow"
    rs_overflows = room_stay_overflow_repo.get_overflowed_room_stays_for_booking(
        d4_d5_booking_id
    )
    assert (
        rs_overflows[0].room_stay_overflow.room_stay_id == 1
    ), "Room stay with id 1 should be marked overflow"


def test_swapping_one_overflowed_room_stay_with_one_confirmed_b2b_room_stay_should_error_out(
    active_hotel_aggregate,
    room_type_inventory_repo,
    room_stay_overflow_repo,
    create_booking_payload,
    client,
):
    # d1, d2 - room nights
    checkin_date = dateutils.add(dateutils.current_datetime(), days=30)
    checkout_date = dateutils.add(dateutils.current_datetime(), days=32)
    create_booking_payload = create_new_booking_payload(
        checkin_date=checkin_date,
        checkout_date=checkout_date,
        number_of_rooms=1,
        number_of_guest_stays=1,
        status=BookingStatus.CONFIRMED,
        reference_number='REF-1',
    )
    payload = {"data": create_booking_payload}
    make_booking(client, payload)

    # d3, d4 - room nights
    checkin_date = dateutils.add(dateutils.current_datetime(), days=32)
    checkout_date = dateutils.add(dateutils.current_datetime(), days=34)
    create_booking_payload = create_new_booking_payload(
        checkin_date,
        checkout_date,
        number_of_rooms=1,
        number_of_guest_stays=1,
        status=BookingStatus.CONFIRMED,
        reference_number='REF-2',
    )
    payload = {"data": create_booking_payload}
    make_booking(client, payload)

    # d1, d2, d3, d4 - room nights
    checkin_date = dateutils.add(dateutils.current_datetime(), days=30)
    checkout_date = dateutils.add(dateutils.current_datetime(), days=34)
    create_booking_payload = create_new_booking_payload(
        checkin_date,
        checkout_date,
        number_of_rooms=1,
        number_of_guest_stays=2,
        status=BookingStatus.CONFIRMED,
        reference_number='REF-3',
    )
    payload = {"data": create_booking_payload}
    make_booking(client, payload)

    # d4, d5 - room nights
    checkin_date = dateutils.add(dateutils.current_datetime(), days=33)
    checkout_date = dateutils.add(dateutils.current_datetime(), days=35)
    create_booking_payload = create_new_booking_payload(
        checkin_date,
        checkout_date,
        number_of_rooms=1,
        number_of_guest_stays=2,
        status=BookingStatus.CONFIRMED,
        reference_number='REF-4',
        channel='b2b',
    )
    payload = {"data": create_booking_payload}
    d4_d5_booking_id = make_booking(client, payload)

    room_type_inventory_aggregates = room_type_inventory_repo.load_multiple(
        active_hotel_aggregate.hotel.hotel_id,
        dateutils.add(dateutils.current_date(), days=30),
        dateutils.add(dateutils.current_date(), days=35),
        room_type_ids=['rt01'],
    )

    room_type_inventory_aggregates[0].get_availability_for_date(
        dateutils.add(dateutils.current_date(), days=34)
    ).update_count(0)
    room_type_inventory_repo.update(room_type_inventory_aggregates[0])

    # d1, d2, d3 - room nights
    checkin_date = dateutils.add(dateutils.current_datetime(), days=30)
    checkout_date = dateutils.add(dateutils.current_datetime(), days=33)
    create_booking_payload = create_new_booking_payload(
        checkin_date,
        checkout_date,
        number_of_rooms=1,
        number_of_guest_stays=2,
        status=BookingStatus.CONFIRMED,
        reference_number='REF-5',
        channel='direct',
    )
    payload = {"data": create_booking_payload}

    # d2, d3, d4, d5 - room nights
    room_stay_two = payload['data']['room_stays'][0].copy()
    room_stay_two['checkin_date'] = dateutils.add(
        dateutils.current_datetime(), days=31
    ).isoformat()
    room_stay_two['checkout_date'] = dateutils.add(
        dateutils.current_datetime(), days=35
    ).isoformat()
    room_stay_two['prices'] = [
        {
            "applicable_date": dateutils.add(
                dateutils.current_datetime(), days=31
            ).isoformat(),
            "bill_to_type": "company",
            "posttax_amount": 118,
            "type": "non-credit",
        },
        {
            "applicable_date": dateutils.add(
                dateutils.current_datetime(), days=32
            ).isoformat(),
            "bill_to_type": "company",
            "posttax_amount": 118,
            "type": "non-credit",
        },
        {
            "applicable_date": dateutils.add(
                dateutils.current_datetime(), days=33
            ).isoformat(),
            "bill_to_type": "company",
            "posttax_amount": 118,
            "type": "non-credit",
        },
        {
            "applicable_date": dateutils.add(
                dateutils.current_datetime(), days=34
            ).isoformat(),
            "bill_to_type": "company",
            "posttax_amount": 118,
            "type": "non-credit",
        },
    ]
    payload['data']['room_stays'].append(room_stay_two)
    response = make_booking(
        client=client, payload=payload, return_complete_response=True
    )
    latest_booking_id = response['data']['booking_id']
    assert response['data']['room_stays'][0]['is_overflow'] is False
    assert response['data']['room_stays'][1]['is_overflow'] is True
    rs_overflows = room_stay_overflow_repo.get_overflowed_room_stays_for_booking(
        latest_booking_id
    )
    assert len(rs_overflows) == 1
    assert rs_overflows[0].room_stay_overflow.room_stay_id == 2
    assert rs_overflows[0].room_stay_overflow.start_date == dateutils.to_date(
        dateutils.add(dateutils.current_datetime(), days=31)
    )
    assert rs_overflows[0].room_stay_overflow.end_date == dateutils.to_date(
        dateutils.add(dateutils.current_datetime(), days=34)
    )

    swap_payload = {
        "data": {
            "overflows": [
                {
                    "action": "unmark",
                    "booking_id": latest_booking_id,
                    "room_stay_id": 2,
                },
                {"action": "mark", "booking_id": d4_d5_booking_id, "room_stay_id": 1},
            ]
        }
    }
    response = swap_room_stay_overflows(
        client,
        active_hotel_aggregate.hotel.hotel_id,
        swap_payload,
        expected_response_code=400,
    )
    response['errors'][0][
        'message'
    ] = "Marking overflow is allowed only on DIRECT and OTA channels"
