from datetime import timedelta

import pytest

from object_registry import locate_instance
from prometheus.application.booking.query_handlers.search_bookings import (
    SearchBookingQueryHandler,
)
from prometheus.domain.booking.dtos.booking_search_query import BookingSearchQuery
from prometheus.tests.factories.aggregate_factories import (
    BillFactory,
    BookingAggregateFactory,
)
from prometheus.tests.mockers import mock_role_manager
from ths_common.constants.booking_constants import BookingStatus
from ths_common.constants.user_constants import UserType
from ths_common.value_objects import UserData


@pytest.fixture(scope='module')
def search_booking_query_handler():
    return locate_instance(SearchBookingQueryHandler)


def test_booking_search(search_booking_query_handler, booking_repo, bill_repo):
    user_data = UserData(user_type=UserType.FDM.value)
    bill_aggregate = BillFactory()
    bill_repo.save(bill_aggregate)

    booking_aggregate = BookingAggregateFactory(booking__bill_id=bill_aggregate.bill_id)
    booking_repo.save(booking_aggregate)

    booking = booking_aggregate.booking

    search_start_date = booking.checkin_date - timedelta(days=2)
    search_end_date = booking.checkout_date + timedelta(days=2)
    hotel_id = booking.hotel_id

    house_view_query = BookingSearchQuery(
        hotel_id=hotel_id, from_date=search_start_date, to_date=search_end_date
    )
    with mock_role_manager():
        bookings = search_booking_query_handler.handle(house_view_query, user_data)
    assert len(bookings) == 1


def test_booking_search_by_attributes(
    search_booking_query_handler, booking_repo, bill_repo
):
    user_data = UserData(user_type=UserType.FDM.value)
    # Create a reserved booking
    bill_aggregate = BillFactory()
    bill_repo.save(bill_aggregate)

    reserved_booking = BookingAggregateFactory(booking__bill_id=bill_aggregate.bill_id)
    booking_repo.save(reserved_booking)

    booking = reserved_booking.booking
    hotel_id = booking.hotel_id

    # Query based on status field
    status_query = BookingSearchQuery(hotel_id=hotel_id, status=[booking.status.value])
    with mock_role_manager():
        bookings = search_booking_query_handler.handle(status_query, user_data)
    assert len(bookings) == 1
    assert bookings[0].booking.status == BookingStatus.CONFIRMED

    # Query based on reference_number
    ext_ref_query = BookingSearchQuery(query=booking.reference_number)
    with mock_role_manager():
        bookings = search_booking_query_handler.handle(ext_ref_query, user_data)
    assert len(bookings) == 1
    assert bookings[0].booking.reference_number == booking.reference_number
