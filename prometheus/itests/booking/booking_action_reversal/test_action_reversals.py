import json

import pytest

from prometheus.common.error_codes.errors import ApplicationErrors
from prometheus.itests.api_wrappers.booking_life_cycle_wrappers import make_booking
from prometheus.tests.mockers import mock_tax_calculator_service


@pytest.mark.usefixtures("app", "setup_hotel")
def test_action_does_not_belong_to_booking(
    client,
    create_booking_payload,
):
    payload = {"data": json.loads(create_booking_payload)}
    booking_id = make_booking(client, payload)

    action_id_not_in_booking = "123"
    url = f"v1/bookings/{booking_id}/actions/{action_id_not_in_booking}"
    cancel_response = client.delete(
        url, content_type="application/json", headers={"X-User-Type": "super-admin"}
    )
    assert cancel_response.status_code == 400
    # pylint: disable=no-member
    assert (
        cancel_response.json["errors"][0]["message"]
        == ApplicationErrors.ACTION_DOES_NOT_BELONG_TO_BOOKING.values[1]
    )
