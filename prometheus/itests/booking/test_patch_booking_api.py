import json
from decimal import Decimal

from treebo_commons.utils import dateutils

from prometheus.domain.billing.services import TaxCalculatorService
from prometheus.itests.api_wrappers.billing_wrappers import edit_charge
from prometheus.itests.api_wrappers.booking_life_cycle_wrappers import make_booking
from prometheus.itests.api_wrappers.booking_wrappers import (
    make_booking_v2,
    patch_booking,
)
from prometheus.itests.api_wrappers.expense_wrappers import add_expenses_v3
from prometheus.itests.booking.test_edit_chares import edit_billing_instruction_payload
from prometheus.itests.booking.test_edit_room_stay_price_v2 import (
    patch_room_stay_prices,
)
from prometheus.itests.payload_generators.booking_payload_generators import (
    create_new_booking_payload,
)
from prometheus.itests.payload_generators.expense_payload_generators import (
    create_expenses_v3_request,
)
from prometheus.tests.mockers import (
    mock_catalog_client,
    mock_rate_manager_client,
    mock_role_manager,
    mock_tax_call,
    mock_tax_call_for_mantis_vat,
    mock_tenant_config_club_inclusion,
)
from ths_common.constants.billing_constants import BilledEntity<PERSON>ategory
from ths_common.constants.booking_constants import BookingStatus


def test_update_booking_details_with_old_version_does_not_raise_version_error(
    client, booking_repo
):
    checkin_date = dateutils.add(dateutils.current_datetime(), days=13)
    checkout_date = dateutils.add(dateutils.current_datetime(), days=15)
    group_name = "vacation"
    create_booking_payload = create_new_booking_payload(
        checkin_date,
        checkout_date,
        number_of_rooms=1,
        number_of_guest_stays=2,
        status=BookingStatus.CONFIRMED,
        group_name=group_name,
    )

    response = make_booking(
        client=client,
        payload={"data": create_booking_payload},
        return_complete_response=True,
    )
    booking_id = response["data"]["booking_id"]
    payload = {
        "data": {
            "group_name": "update_vacation",
            "comments": "new comment added",
            "reference_number": "new ref number 123",
            "extra_information": dict(
                internal_remarks="xyz",
                guest_visible_remarks="abc",
                nested_key=dict(nested_key1=123),
                external_remarks="wer",
            ),
        },
        "resource_version": response['data']['version'] - 1,
    }
    response = patch_booking(client, booking_id, payload)
    booking_aggregate = booking_repo.load(booking_id)
    assert booking_aggregate.booking.group_name == payload["data"]["group_name"]
    assert booking_aggregate.booking.comments == payload["data"]["comments"]
    assert (
        booking_aggregate.booking.reference_number
        == payload["data"]["reference_number"]
    )
    assert (
        booking_aggregate.booking.extra_information
        == payload["data"]["extra_information"]
    )


def test_update_booking_company_details_with_old_version_does_not_raise_version_error(
    client, booking_repo, bill_repo
):
    checkin_date = dateutils.add(dateutils.current_datetime(), days=13)
    checkout_date = dateutils.add(dateutils.current_datetime(), days=15)
    create_booking_payload = create_new_booking_payload(
        checkin_date,
        checkout_date,
        number_of_rooms=1,
        number_of_guest_stays=2,
        status=BookingStatus.CONFIRMED,
    )

    response = make_booking(
        client=client,
        payload={"data": create_booking_payload},
        return_complete_response=True,
    )
    booking_id = response["data"]["booking_id"]
    payload = {
        "data": {
            "company_details": {
                "legal_details": {
                    "address": {
                        "city": "city_update",
                        "country": "country_update",
                        "field_1": "field1_update",
                        "field_2": "field2_update",
                        "pincode": "123123",
                        "state": "state_update",
                    },
                    "tin": "12ABCDE0000A1ZMN1",
                    "legal_name": "update_company_legal_name",
                    "client_internal_code": "company-01",
                    "external_reference_id": None,
                    "is_sez": False,
                    "has_lut": False,
                    "email": None,
                    "phone": None,
                }
            }
        },
        "resource_version": response['data']['version'] - 1,
    }
    response = patch_booking(client, booking_id, payload)

    booking_aggregate = booking_repo.load(booking_id)
    bill_aggregate = bill_repo.load(booking_aggregate.booking.bill_id)
    assert any(
        billed_entity.category == BilledEntityCategory.BOOKER_COMPANY
        and billed_entity.name.full_name == booking_aggregate.get_company_legal_name()
        for billed_entity in bill_aggregate.billed_entities
    )
    assert booking_aggregate.get_company_details().billed_entity_id is not None
    assert (
        booking_aggregate.get_company_details().legal_details.to_json()
        == payload["data"]["company_details"]["legal_details"]
    )


def test_update_booking_travel_agent_details_with_old_version_does_not_raise_version_error(
    client, booking_repo, bill_repo
):
    checkin_date = dateutils.add(dateutils.current_datetime(), days=13)
    checkout_date = dateutils.add(dateutils.current_datetime(), days=15)
    create_booking_payload = create_new_booking_payload(
        checkin_date,
        checkout_date,
        number_of_rooms=1,
        number_of_guest_stays=2,
        status=BookingStatus.CONFIRMED,
    )

    response = make_booking(
        client=client,
        payload={"data": create_booking_payload},
        return_complete_response=True,
    )
    booking_id = response["data"]["booking_id"]
    payload = {
        "data": {
            "travel_agent_details": {
                "legal_details": {
                    "address": {
                        "city": "city_update",
                        "country": "country_update",
                        "field_1": "field1_update",
                        "field_2": "field2_update",
                        "pincode": "123123",
                        "state": "state_update",
                    },
                    "tin": "12ABCDE0000A1ZMN1",
                    "legal_name": "update_travel_agent_legal_name",
                    "client_internal_code": "travel_agent-01",
                    "external_reference_id": None,
                    "is_sez": False,
                    "has_lut": False,
                    "email": None,
                    "phone": None,
                },
                "metadata": None,
            }
        },
        "resource_version": response['data']['version'] - 1,
    }
    response = patch_booking(client, booking_id, payload)
    booking_aggregate = booking_repo.load(booking_id)
    bill_aggregate = bill_repo.load(booking_aggregate.booking.bill_id)
    assert any(
        billed_entity.category == BilledEntityCategory.TRAVEL_AGENT
        and billed_entity.name.full_name
        == booking_aggregate.get_travel_agent_legal_name()
        for billed_entity in bill_aggregate.billed_entities
    )
    assert booking_aggregate.get_travel_agent_details().billed_entity_id is not None
    assert (
        booking_aggregate.get_travel_agent_details().legal_details.to_json()
        == payload["data"]["travel_agent_details"]["legal_details"]
    )


def test_update_booking_with_same_details_does_not_change_booking_version(
    client, booking_repo, bill_repo
):
    checkin_date = dateutils.add(dateutils.current_datetime(), days=13)
    checkout_date = dateutils.add(dateutils.current_datetime(), days=15)
    create_booking_payload = create_new_booking_payload(
        checkin_date,
        checkout_date,
        number_of_rooms=1,
        number_of_guest_stays=2,
        status=BookingStatus.CONFIRMED,
    )

    create_booking_payload['extra_information'] = dict(
        guest_visible_remarks="abc",
        internal_remarks="xyz",
        nested_key=dict(nested_key1=123),
        external_remarks="wer",
    )
    response = make_booking(
        client=client,
        payload={"data": create_booking_payload},
        return_complete_response=True,
    )
    booking_id = response["data"]["booking_id"]
    booking_aggregate = booking_repo.load(booking_id)
    pre_api_version = booking_aggregate.current_version()

    payload = {
        "data": {
            "group_name": booking_aggregate.booking.group_name,
            "reference_number": booking_aggregate.booking.reference_number,
            "extra_information": dict(
                internal_remarks="xyz",
                guest_visible_remarks="abc",
                nested_key=dict(nested_key1=123),
                external_remarks="wer",
            ),
            "comments": booking_aggregate.booking.comments,
            "company_details": {
                "legal_details": booking_aggregate.get_company_details().legal_details.to_json()
            },
        },
        "resource_version": booking_aggregate.current_version() - 1,
    }
    patch_booking(client, booking_id, payload)

    booking_aggregate = booking_repo.load(booking_id)
    post_api_version = booking_aggregate.current_version()

    assert pre_api_version == post_api_version


def test_update_booking_with_lut_and_sez_change_should_recalculate_tax(
    client, booking_repo, bill_repo
):
    with mock_tenant_config_club_inclusion():
        checkin_date = dateutils.add(dateutils.current_datetime(), days=13)
        checkout_date = dateutils.add(dateutils.current_datetime(), days=15)
        create_booking_payload = create_new_booking_payload(
            checkin_date,
            checkout_date,
            number_of_rooms=1,
            number_of_guest_stays=2,
            status=BookingStatus.CONFIRMED,
        )
        response = make_booking_v2(
            client=client,
            payload={"data": create_booking_payload},
            return_complete_response=True,
        )
        booking_id = response["data"]["booking_id"]
        booking_aggregate = booking_repo.load(booking_id)
        legal_details = booking_aggregate.get_company_details().legal_details.to_json()
        legal_details['has_lut'] = True
        legal_details['is_sez'] = True
        payload = {
            "data": {
                "company_details": {"legal_details": legal_details},
            },
            "resource_version": booking_aggregate.current_version(),
        }

        patch_booking(client, booking_id, payload)
        bill_aggregate = bill_repo.load(booking_aggregate.booking.bill_id)
        expected_tax_amount = Decimal("0")
        expected_tax_percent = Decimal("0")
        for charge in bill_aggregate.charges:
            assert charge.tax_amount == expected_tax_amount
            for tax_item in charge.tax_details:
                assert tax_item.tax_amount.amount == expected_tax_amount
                assert tax_item.percentage == expected_tax_percent
            for split in charge.charge_splits:
                assert split.tax == expected_tax_amount
                for tax_item in split.tax_details:
                    assert tax_item.tax_amount.amount == expected_tax_amount
                    assert tax_item.percentage == expected_tax_percent


def test_update_booking_having_inclusions_with_lut_and_sez_change_should_recalculate_tax(
    client, booking_repo, bill_repo
):
    with mock_tenant_config_club_inclusion():
        checkin_date = dateutils.add(dateutils.current_datetime(), days=13)
        checkout_date = dateutils.add(dateutils.current_datetime(), days=15)
        create_booking_payload = create_new_booking_payload(
            checkin_date,
            checkout_date,
            number_of_rooms=1,
            number_of_guest_stays=2,
            status=BookingStatus.CONFIRMED,
        )
        create_booking_payload['booking_owner']['gst_details']['has_lut'] = True
        create_booking_payload['booking_owner']['gst_details']['is_sez'] = True
        create_booking_payload['room_stays'][0]["rate_plan_inclusions"] = [
            {
                "sku_id": "377",
                "pretax_amount": "7450",
                "start_date": checkin_date.isoformat(),
                "end_date": dateutils.subtract(checkout_date, days=1).isoformat(),
            }
        ]
        response = make_booking_v2(
            client=client,
            payload={"data": create_booking_payload},
            return_complete_response=True,
        )
        booking_id = response["data"]["booking_id"]
        booking_aggregate = booking_repo.load(booking_id)
        legal_details = booking_aggregate.get_company_details().legal_details.to_json()
        legal_details['has_lut'] = False
        legal_details['is_sez'] = True
        payload = {
            "data": {
                "company_details": {"legal_details": legal_details},
            },
            "resource_version": booking_aggregate.current_version(),
        }
        patch_booking(client, booking_id, payload)
        bill_aggregate = bill_repo.load(booking_aggregate.booking.bill_id)
        expected_tax_percent = Decimal("18")
        for charge in bill_aggregate.charges:
            assert charge.tax_amount != Decimal("0")
            for tax_item in charge.tax_details:
                if tax_item.tax_type == 'igst':
                    assert tax_item.tax_amount.amount != Decimal("0")
                    assert tax_item.percentage == expected_tax_percent
            for split in charge.charge_splits:
                assert split.tax != Decimal("0")
                for tax_item in split.tax_details:
                    if tax_item.tax_type == 'igst':
                        assert tax_item.tax_amount.amount != Decimal("0")
                        assert tax_item.percentage == expected_tax_percent


def test_update_booking_having_linked_charges_should_derive_tax_from_room_stay_w(
    client, booking_repo, bill_repo
):
    with mock_tenant_config_club_inclusion():
        checkin_date = dateutils.add(dateutils.current_datetime(), days=13)
        checkout_date = dateutils.add(dateutils.current_datetime(), days=15)
        create_booking_payload = create_new_booking_payload(
            checkin_date,
            checkout_date,
            number_of_rooms=1,
            number_of_guest_stays=2,
            status=BookingStatus.CONFIRMED,
        )
        create_booking_payload['booking_owner']['gst_details']['has_lut'] = True
        create_booking_payload['booking_owner']['gst_details']['is_sez'] = True
        create_booking_payload['room_stays'][0]["rate_plan_inclusions"] = [
            {
                "sku_id": "377",
                "pretax_amount": "7450",
                "start_date": checkin_date.isoformat(),
                "end_date": dateutils.subtract(checkout_date, days=1).isoformat(),
            }
        ]

        response = make_booking_v2(
            client=client,
            payload={"data": create_booking_payload},
            return_complete_response=True,
        )
        booking_id = response["data"]["booking_id"]
        expense_v3_request = create_expenses_v3_request(
            dates=[checkin_date, dateutils.add(checkin_date, days=1)],
            charge_to=["2", "3"],
            sku_id='extra_guest',
        )
        add_expenses_v3(client, booking_id, expense_v3_request)
        booking_aggregate = booking_repo.load(booking_id)
        legal_details = booking_aggregate.get_company_details().legal_details.to_json()
        legal_details['has_lut'] = False
        legal_details['is_sez'] = True
        payload = {
            "data": {
                "company_details": {"legal_details": legal_details},
            },
            "resource_version": booking_aggregate.current_version(),
        }

        patch_booking(client, booking_id, payload)
        bill_aggregate = bill_repo.load(booking_aggregate.booking.bill_id)
        expected_tax_percent = Decimal("18")
        for charge in bill_aggregate.charges:
            assert charge.tax_amount != Decimal("0")
            for tax_item in charge.tax_details:
                if tax_item.tax_type == 'igst':
                    assert tax_item.tax_amount.amount != Decimal("0")
                    assert tax_item.percentage == expected_tax_percent
            for split in charge.charge_splits:
                assert split.tax != Decimal("0")
                for tax_item in split.tax_details:
                    if tax_item.tax_type == 'igst':
                        assert tax_item.tax_amount.amount != Decimal("0")
                        assert tax_item.percentage == expected_tax_percent


def test_add_expense_v3_for_linked_charges_should_properly_round_tax_details(
    client, booking_repo, bill_repo
):
    with mock_tenant_config_club_inclusion():
        checkin_date = dateutils.add(dateutils.current_datetime(), days=13)
        checkout_date = dateutils.add(dateutils.current_datetime(), days=15)
        create_booking_payload = create_new_booking_payload(
            checkin_date,
            checkout_date,
            number_of_rooms=1,
            number_of_guest_stays=2,
            status=BookingStatus.CONFIRMED,
        )
        create_booking_payload['booking_owner']['gst_details']['has_lut'] = False
        create_booking_payload['booking_owner']['gst_details']['is_sez'] = False
        create_booking_payload['room_stays'][0]["rate_plan_inclusions"] = [
            {
                "sku_id": "377",
                "pretax_amount": "250",
                "start_date": checkin_date.isoformat(),
                "end_date": dateutils.subtract(checkout_date, days=1).isoformat(),
            }
        ]

        response = make_booking_v2(
            client=client,
            payload={"data": create_booking_payload},
            return_complete_response=True,
        )
        booking_id = response["data"]["booking_id"]
        expense_v3_request = create_expenses_v3_request(
            dates=[checkin_date, dateutils.add(checkin_date, days=1)],
            charge_to=["2", "3"],
            sku_id='extra_guest',
            unit_post_tax="21",
        )
        add_expenses_v3(client, booking_id, expense_v3_request)
        booking_aggregate = booking_repo.load(booking_id)
        bill_aggregate = bill_repo.load(booking_aggregate.booking.bill_id)
        for charge in bill_aggregate.charges:
            assert (
                charge.tax_amount.amount + charge.pretax_amount.amount
                == charge.posttax_amount.amount
            )
            sum_of_tax_details = sum(
                [tax_item.tax_amount.amount for tax_item in charge.tax_details]
            )
            assert charge.tax_amount.amount == sum_of_tax_details

            for split in charge.charge_splits:
                assert split.tax.amount + split.pre_tax.amount == split.post_tax.amount
                sum_of_tax_details = sum(
                    [tax_item.tax_amount.amount for tax_item in split.tax_details]
                )
                assert split.tax.amount == sum_of_tax_details


def test_add_patch_booking_for_linked_charges_should_properly_round_tax_details(
    client, booking_repo, bill_repo
):
    with mock_tenant_config_club_inclusion():
        checkin_date = dateutils.add(dateutils.current_datetime(), days=13)
        checkout_date = dateutils.add(dateutils.current_datetime(), days=15)
        create_booking_payload = create_new_booking_payload(
            checkin_date,
            checkout_date,
            number_of_rooms=1,
            number_of_guest_stays=2,
            status=BookingStatus.CONFIRMED,
        )
        create_booking_payload['booking_owner']['gst_details']['has_lut'] = True
        create_booking_payload['booking_owner']['gst_details']['is_sez'] = True
        create_booking_payload['room_stays'][0]["rate_plan_inclusions"] = [
            {
                "sku_id": "377",
                "pretax_amount": "250",
                "start_date": checkin_date.isoformat(),
                "end_date": dateutils.subtract(checkout_date, days=1).isoformat(),
            }
        ]

        response = make_booking_v2(
            client=client,
            payload={"data": create_booking_payload},
            return_complete_response=True,
        )
        booking_id = response["data"]["booking_id"]
        expense_v3_request = create_expenses_v3_request(
            dates=[checkin_date, dateutils.add(checkin_date, days=1)],
            charge_to=["2", "3"],
            sku_id='extra_guest',
            unit_post_tax="21",
        )
        add_expenses_v3(client, booking_id, expense_v3_request)
        booking_aggregate = booking_repo.load(booking_id)

        legal_details = booking_aggregate.get_company_details().legal_details.to_json()
        legal_details['has_lut'] = False
        legal_details['is_sez'] = False
        payload = {
            "data": {
                "company_details": {"legal_details": legal_details},
            },
            "resource_version": booking_aggregate.current_version(),
        }

        patch_booking(client, booking_id, payload)

        booking_aggregate = booking_repo.load(booking_id)
        bill_aggregate = bill_repo.load(booking_aggregate.booking.bill_id)
        for charge in bill_aggregate.charges:
            assert (
                charge.tax_amount.amount + charge.pretax_amount.amount
                == charge.posttax_amount.amount
            )
            sum_of_tax_details = sum(
                [tax_item.tax_amount.amount for tax_item in charge.tax_details]
            )
            assert charge.tax_amount.amount == sum_of_tax_details

            for split in charge.charge_splits:
                assert split.tax.amount + split.pre_tax.amount == split.post_tax.amount
                sum_of_tax_details = sum(
                    [tax_item.tax_amount.amount for tax_item in split.tax_details]
                )
                assert split.tax.amount == sum_of_tax_details


def test_edit_charges_for_linked_charges_should_properly_round_tax_details(
    client, booking_repo, bill_repo
):
    with mock_tenant_config_club_inclusion():
        checkin_date = dateutils.add(dateutils.current_datetime(), days=13)
        checkout_date = dateutils.add(dateutils.current_datetime(), days=15)
        create_booking_payload = create_new_booking_payload(
            checkin_date,
            checkout_date,
            number_of_rooms=1,
            number_of_guest_stays=2,
            status=BookingStatus.CONFIRMED,
        )
        create_booking_payload['booking_owner']['gst_details']['has_lut'] = False
        create_booking_payload['booking_owner']['gst_details']['is_sez'] = False
        create_booking_payload['room_stays'][0]["rate_plan_inclusions"] = [
            {
                "sku_id": "377",
                "pretax_amount": "250",
                "start_date": checkin_date.isoformat(),
                "end_date": dateutils.subtract(checkout_date, days=1).isoformat(),
            }
        ]

        response = make_booking_v2(
            client=client,
            payload={"data": create_booking_payload},
            return_complete_response=True,
        )
        booking_id = response["data"]["booking_id"]
        expense_v3_request = create_expenses_v3_request(
            dates=[checkin_date, dateutils.add(checkin_date, days=1)],
            charge_to=["2", "3"],
            sku_id='extra_guest',
            unit_post_tax="30",
        )
        add_expenses_v3(client, booking_id, expense_v3_request)
        booking_aggregate = booking_repo.load(booking_id)
        bill_aggregate = bill_repo.load(booking_aggregate.booking.bill_id)
        payload = edit_billing_instruction_payload(bill_aggregate.version, 30)
        edit_charge(client, bill_aggregate.bill_id, 7, payload)

        booking_aggregate = booking_repo.load(booking_id)
        bill_aggregate = bill_repo.load(booking_aggregate.booking.bill_id)
        for charge in bill_aggregate.charges:
            assert (
                charge.tax_amount.amount + charge.pretax_amount.amount
                == charge.posttax_amount.amount
            )
            sum_of_tax_details = sum(
                [tax_item.tax_amount.amount for tax_item in charge.tax_details]
            )
            assert charge.tax_amount.amount == sum_of_tax_details

            for split in charge.charge_splits:
                assert split.tax.amount + split.pre_tax.amount == split.post_tax.amount
                sum_of_tax_details = sum(
                    [tax_item.tax_amount.amount for tax_item in split.tax_details]
                )
                assert split.tax.amount == sum_of_tax_details


def test_mantis_booking_tax(client, bill_repo, booking_repo):
    with mock_tenant_config_club_inclusion(), mock_tax_call_for_mantis_vat():
        checkin_date = dateutils.add(dateutils.current_datetime(), days=13)
        checkout_date = dateutils.add(dateutils.current_datetime(), days=14)
        create_booking_payload = create_new_booking_payload(
            checkin_date,
            checkout_date,
            number_of_rooms=1,
            number_of_guest_stays=2,
            status=BookingStatus.CONFIRMED,
        )
        create_booking_payload['room_stays'][0]['prices'][0][
            'posttax_amount'
        ] = "2602.00"
        del create_booking_payload['room_stays'][0]['prices'][0]['pretax_amount']
        create_booking_payload['booking_owner']['gst_details']['has_lut'] = False
        create_booking_payload['booking_owner']['gst_details']['is_sez'] = False
        create_booking_payload['room_stays'][0]["rate_plan_inclusions"] = [
            {
                "sku_id": "377",
                "posttax_amount": "220",
                "start_date": checkin_date.isoformat(),
                "end_date": dateutils.subtract(checkout_date, days=1).isoformat(),
                "quantity": 2,
            }
        ]

        response = make_booking_v2(
            client=client,
            payload={"data": create_booking_payload},
            return_complete_response=True,
        )
        booking_id = response["data"]["booking_id"]
        booking_aggregate = booking_repo.load(booking_id)
        bill_aggregate = bill_repo.load(booking_aggregate.booking.bill_id)
        for charge in bill_aggregate.charges:
            assert (
                charge.tax_amount.amount + charge.pretax_amount.amount
                == charge.posttax_amount.amount
            )
            sum_of_tax_details = sum(
                [tax_item.tax_amount.amount for tax_item in charge.tax_details]
            )
            assert charge.tax_amount.amount == sum_of_tax_details
            if charge.is_inclusion_charge:
                assert charge.posttax_amount.amount == Decimal('220.00')
            else:
                assert charge.posttax_amount.amount == Decimal('2602.00')
            for split in charge.charge_splits:
                assert split.tax.amount + split.pre_tax.amount == split.post_tax.amount
                sum_of_tax_details = sum(
                    [tax_item.tax_amount.amount for tax_item in split.tax_details]
                )
                assert split.tax.amount == sum_of_tax_details
                if charge.is_inclusion_charge:
                    assert split.post_tax.amount == Decimal('220.00')
                else:
                    assert split.post_tax.amount == Decimal('2602.00')


def test_update_gst_details_of_company_with_lut_and_sez_change_should_recalculate_tax(
    client, booking_repo, bill_repo
):
    with mock_tenant_config_club_inclusion():
        checkin_date = dateutils.add(dateutils.current_datetime(), days=13)
        checkout_date = dateutils.add(dateutils.current_datetime(), days=15)
        create_booking_payload = create_new_booking_payload(
            checkin_date,
            checkout_date,
            number_of_rooms=1,
            number_of_guest_stays=2,
            status=BookingStatus.CONFIRMED,
            set_company_details=True,
            default_billed_entity_category=BilledEntityCategory.BOOKER_COMPANY.value,
        )
        create_booking_payload['booking_owner']['gst_details'].update(
            {'is_sez': True, 'has_lut': True}
        )
        create_booking_payload['company_details']['legal_details'].update(
            {'is_sez': False, 'has_lut': False}
        )
        create_booking_payload['room_stays'][0]["rate_plan_inclusions"] = [
            {
                "sku_id": "377",
                "pretax_amount": "7450",
                "start_date": checkin_date.isoformat(),
                "end_date": dateutils.subtract(checkout_date, days=1).isoformat(),
            }
        ]
        response = make_booking_v2(
            client=client,
            payload={"data": create_booking_payload},
            return_complete_response=True,
        )
        booking_id = response["data"]["booking_id"]
        booking_aggregate = booking_repo.load(booking_id)
        legal_details = booking_aggregate.get_company_details().legal_details.to_json()
        legal_details['has_lut'] = False
        legal_details['is_sez'] = True
        payload = {
            "data": {
                "company_details": {"legal_details": legal_details},
            },
            "resource_version": booking_aggregate.current_version(),
        }
        patch_booking(client, booking_id, payload)
        bill_aggregate = bill_repo.load(booking_aggregate.booking.bill_id)
        expected_tax_percent = Decimal("18")
        for charge in bill_aggregate.charges:
            assert charge.tax_amount != Decimal("0")
            for tax_item in charge.tax_details:
                if tax_item.tax_type == 'igst':
                    assert tax_item.tax_amount.amount != Decimal("0")
                    assert tax_item.percentage == expected_tax_percent
            for split in charge.charge_splits:
                assert split.tax != Decimal("0")
                for tax_item in split.tax_details:
                    if tax_item.tax_type == 'igst':
                        assert tax_item.tax_amount.amount != Decimal("0")
                        assert tax_item.percentage == expected_tax_percent


def test_update_gst_details_of_ta_with_lut_and_sez_change_should_recalculate_tax(
    client, booking_repo, bill_repo
):
    with mock_tenant_config_club_inclusion():
        checkin_date = dateutils.add(dateutils.current_datetime(), days=13)
        checkout_date = dateutils.add(dateutils.current_datetime(), days=15)
        create_booking_payload = create_new_booking_payload(
            checkin_date,
            checkout_date,
            number_of_rooms=1,
            number_of_guest_stays=2,
            status=BookingStatus.CONFIRMED,
            set_company_details=True,
            set_travel_agent_details=True,
            default_billed_entity_category=BilledEntityCategory.TRAVEL_AGENT.value,
        )
        create_booking_payload['company_details']['legal_details'].update(
            {'is_sez': True, 'has_lut': True}
        )
        create_booking_payload['travel_agent_details']['legal_details'].update(
            {'is_sez': False, 'has_lut': False}
        )
        create_booking_payload['room_stays'][0]["rate_plan_inclusions"] = [
            {
                "sku_id": "377",
                "pretax_amount": "7450",
                "start_date": checkin_date.isoformat(),
                "end_date": dateutils.subtract(checkout_date, days=1).isoformat(),
            }
        ]
        response = make_booking_v2(
            client=client,
            payload={"data": create_booking_payload},
            return_complete_response=True,
        )
        booking_id = response["data"]["booking_id"]
        booking_aggregate = booking_repo.load(booking_id)
        legal_details = (
            booking_aggregate.get_travel_agent_details().legal_details.to_json()
        )
        legal_details['has_lut'] = True
        legal_details['is_sez'] = True
        payload = {
            "data": {
                "travel_agent_details": {"legal_details": legal_details},
            },
            "resource_version": booking_aggregate.current_version(),
        }
        patch_booking(client, booking_id, payload)
        bill_aggregate = bill_repo.load(booking_aggregate.booking.bill_id)
        for charge in bill_aggregate.charges:
            assert charge.tax_amount == Decimal("0")
            for split in charge.charge_splits:
                assert split.tax == Decimal("0")


def test_update_gst_details_of_ta_with_lut_and_sez_change_should_recalculate_tax_of_only_ta_folio(
    client, booking_repo, bill_repo
):
    with mock_tenant_config_club_inclusion():
        checkin_date = dateutils.add(dateutils.current_datetime(), days=13)
        checkout_date = dateutils.add(dateutils.current_datetime(), days=15)
        create_booking_payload = create_new_booking_payload(
            checkin_date,
            checkout_date,
            number_of_rooms=1,
            number_of_guest_stays=2,
            status=BookingStatus.CONFIRMED,
            set_company_details=True,
            set_travel_agent_details=True,
            default_billed_entity_category=BilledEntityCategory.TRAVEL_AGENT.value,
        )
        create_booking_payload['company_details']['legal_details'].update(
            {'is_sez': False, 'has_lut': False}
        )
        create_booking_payload['travel_agent_details']['legal_details'].update(
            {'is_sez': False, 'has_lut': False}
        )
        create_booking_payload['room_stays'][0]["rate_plan_inclusions"] = [
            {
                "sku_id": "377",
                "pretax_amount": "7450",
                "start_date": checkin_date.isoformat(),
                "end_date": dateutils.subtract(checkout_date, days=1).isoformat(),
            }
        ]
        response = make_booking_v2(
            client=client,
            payload={"data": create_booking_payload},
            return_complete_response=True,
        )
        booking_id = response["data"]["booking_id"]
        booking_aggregate = booking_repo.load(booking_id)
        bill_aggregate = bill_repo.load(booking_aggregate.booking.bill_id)
        billed_entity_of_company = bill_aggregate.get_billed_entity_for_category(
            BilledEntityCategory.BOOKER_COMPANY
        )
        edit_room_stay_price_payload = {
            "data": [
                {
                    "charge_id": 1,
                    "billing_instructions": [
                        {
                            "billed_entity_account": {
                                "billed_entity_id": billed_entity_of_company.billed_entity_id,
                                "account_number": 1,
                            },
                            "payment_instruction": "pay_at_checkout",
                            "split_percentage": 100,
                        },
                    ],
                }
            ],
            "resource_version": booking_aggregate.current_version(),
        }
        patch_room_stay_prices(client, booking_id, edit_room_stay_price_payload)
        booking_aggregate = booking_repo.load(booking_id)
        legal_details = (
            booking_aggregate.get_travel_agent_details().legal_details.to_json()
        )
        legal_details['has_lut'] = True
        legal_details['is_sez'] = True
        payload = {
            "data": {
                "travel_agent_details": {"legal_details": legal_details},
            },
            "resource_version": booking_aggregate.current_version(),
        }
        patch_booking(client, booking_id, payload)
        bill_aggregate = bill_repo.load(booking_aggregate.booking.bill_id)
        # charges billed to company should have tax and on other hand charge billed to TA (default) should be of non tax
        for charge in bill_aggregate.charges:
            for split in charge.charge_splits:
                if (
                    split.billed_entity_account.billed_entity_id
                    == billed_entity_of_company.billed_entity_id
                ):
                    assert split.tax != Decimal("0")
                else:
                    assert split.tax == Decimal("0")
