import enum
import logging
import os

from treebo_commons.service_discovery.service_registry import ServiceRegistry

logger = logging.getLogger(__name__)


class ServiceEndPointNames(enum.Enum):
    ROLE_MANAGER_SERVICE_URL = "role_manager_service_url"
    UPS_SERVICE_URL = "ups_service_url"
    TAX_SERVICE_URL = "tax_service_url"
    CATALOG_SERVICE_URL = "catalog_service_url"
    NOTIFICATION_SERVICE_URL = "notification_service_url"
    REALISATION_SERVICE_URL = "realisation_service_url"
    ACCOUNT_RECEIVABLE_SERVICE_URL = "account_receivable_service_url"
    TEMPLATE_SERVICE_URL = "template_service_url"
    AUTHN_SERVICE_URL = "authn_service_url"
    AUTHZ_SERVICE_URL = "authz_service_url"
    TREEBO_PLATFORM_URL = "treebo_platform_url"
    TREEBO_BACKEND_URL = "treebo_backend_url"
    COMMUNICATION_SERVICE_URL = "communication_service_url"
    POS_SERVICE_URL = "pos_service_url"
    TENANT_SERVICE_URL = "tenant_service_url"
    TENANT_GATEWAY_SERVICE_URL = "tenant_gateway_service_url"

    RATE_MANAGER_SERVICE_URL = "rate_manager_service_url"
    FINANCE_PORTAL_SERVICE_URL = "finance_portal_service_url"
    ATHENA_SERVICE_URL = "athena_service_url"
    COMPANY_PROFILE_SERVICE_URL = "company_profile_service_url"
    PAYMENTS_SERVICE_URL = "payments_service_url"
    CLEARTAX_SERVICE_URL = "clear_tax_service_url"
    AWS_OPEN_SEARCH_SERVICE_ENDPOINT = "aws_open_search_service_endpoint"
    PYGMY_SERVICE_URL = "pygmy_service_url"
    REWARD_SERVICE_URL = "reward_service_url"


class ServiceRegistryClient:
    ALL_API_ENDPOINTS = ServiceRegistry.get_all_service_endpoints()

    @classmethod
    def get_role_manager_service_url(cls):
        service_name = ServiceEndPointNames.ROLE_MANAGER_SERVICE_URL.value
        return cls.ALL_API_ENDPOINTS.get(service_name, os.environ.get(service_name))

    @classmethod
    def get_ups_service_url(cls):
        service_name = ServiceEndPointNames.UPS_SERVICE_URL.value
        return cls.ALL_API_ENDPOINTS.get(service_name, os.environ.get(service_name))

    @classmethod
    def get_tax_service_url(cls):
        service_name = ServiceEndPointNames.TAX_SERVICE_URL.value
        return cls.ALL_API_ENDPOINTS.get(service_name, os.environ.get(service_name))

    @classmethod
    def get_catalog_service_url(cls):
        service_name = ServiceEndPointNames.CATALOG_SERVICE_URL.value
        return cls.ALL_API_ENDPOINTS.get(service_name, os.environ.get(service_name))

    @classmethod
    def get_aws_open_search_service_endpoint(cls):
        service_name = ServiceEndPointNames.AWS_OPEN_SEARCH_SERVICE_ENDPOINT.value
        return cls.ALL_API_ENDPOINTS.get(service_name, os.environ.get(service_name))

    @classmethod
    def get_finance_portal_service_url(cls):
        service_name = ServiceEndPointNames.FINANCE_PORTAL_SERVICE_URL.value
        return cls.ALL_API_ENDPOINTS.get(service_name, os.environ.get(service_name))

    @classmethod
    def get_notification_service_url(cls):
        service_name = ServiceEndPointNames.NOTIFICATION_SERVICE_URL.value
        return cls.ALL_API_ENDPOINTS.get(service_name, os.environ.get(service_name))

    @classmethod
    def get_realisation_service_url(cls):
        service_name = ServiceEndPointNames.REALISATION_SERVICE_URL.value
        return cls.ALL_API_ENDPOINTS.get(service_name, os.environ.get(service_name))

    @classmethod
    def get_account_receivable_service_url(cls):
        service_name = ServiceEndPointNames.ACCOUNT_RECEIVABLE_SERVICE_URL.value
        return cls.ALL_API_ENDPOINTS.get(service_name, os.environ.get(service_name))

    @classmethod
    def get_template_service_url(cls):
        service_name = ServiceEndPointNames.TEMPLATE_SERVICE_URL.value
        return cls.ALL_API_ENDPOINTS.get(service_name, os.environ.get(service_name))

    @classmethod
    def get_authn_service_url(cls):
        service_name = ServiceEndPointNames.AUTHN_SERVICE_URL.value
        return cls.ALL_API_ENDPOINTS.get(service_name, os.environ.get(service_name))

    @classmethod
    def get_authz_service_url(cls):
        service_name = ServiceEndPointNames.AUTHZ_SERVICE_URL.value
        return cls.ALL_API_ENDPOINTS.get(service_name, os.environ.get(service_name))

    @classmethod
    def get_treebo_platform_service_url(cls):
        service_name = ServiceEndPointNames.TREEBO_PLATFORM_URL.value
        return cls.ALL_API_ENDPOINTS.get(service_name, os.environ.get(service_name))

    @classmethod
    def get_treebo_backend_service_url(cls):
        service_name = ServiceEndPointNames.TREEBO_BACKEND_URL.value
        return cls.ALL_API_ENDPOINTS.get(service_name, os.environ.get(service_name))

    @classmethod
    def get_communication_service_url(cls):
        service_name = ServiceEndPointNames.COMMUNICATION_SERVICE_URL.value
        return cls.ALL_API_ENDPOINTS.get(service_name, os.environ.get(service_name))

    @classmethod
    def get_pos_service_url(cls):
        service_name = ServiceEndPointNames.POS_SERVICE_URL.value
        return cls.ALL_API_ENDPOINTS.get(service_name, os.environ.get(service_name))

    @classmethod
    def get_tenant_service_url(cls):
        service_name = ServiceEndPointNames.TENANT_SERVICE_URL.value
        return cls.ALL_API_ENDPOINTS.get(service_name, os.environ.get(service_name))

    @classmethod
    def get_tenant_gateway_service_url(cls):
        service_name = ServiceEndPointNames.TENANT_GATEWAY_SERVICE_URL.value
        return cls.ALL_API_ENDPOINTS.get(service_name, os.environ.get(service_name))

    @classmethod
    def get_rate_manager_service_url(cls):
        service_name = ServiceEndPointNames.RATE_MANAGER_SERVICE_URL.value
        return cls.ALL_API_ENDPOINTS.get(service_name, os.environ.get(service_name))

    @classmethod
    def get_athena_service_url(cls):
        service_name = ServiceEndPointNames.ATHENA_SERVICE_URL.value
        return cls.ALL_API_ENDPOINTS.get(service_name, os.environ.get(service_name))

    @classmethod
    def get_company_profile_service_url(cls):
        service_name = ServiceEndPointNames.COMPANY_PROFILE_SERVICE_URL.value
        return cls.ALL_API_ENDPOINTS.get(service_name, os.environ.get(service_name))

    @classmethod
    def get_payment_service_url(cls):
        service_name = ServiceEndPointNames.PAYMENTS_SERVICE_URL.value
        return cls.ALL_API_ENDPOINTS.get(service_name, os.environ.get(service_name))

    @classmethod
    def get_cleartax_service_url(cls):
        service_name = ServiceEndPointNames.CLEARTAX_SERVICE_URL.value
        return cls.ALL_API_ENDPOINTS.get(service_name, os.environ.get(service_name))

    @classmethod
    def get_pygmy_service_url(cls):
        service_name = ServiceEndPointNames.PYGMY_SERVICE_URL.value
        return cls.ALL_API_ENDPOINTS.get(service_name, os.environ.get(service_name))

    @classmethod
    def get_reward_service_url(cls):
        service_name = ServiceEndPointNames.REWARD_SERVICE_URL.value
        return cls.ALL_API_ENDPOINTS.get(service_name, os.environ.get(service_name))
