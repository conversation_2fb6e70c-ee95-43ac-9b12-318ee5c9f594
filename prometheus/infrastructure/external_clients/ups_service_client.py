from flask import current_app as app

from object_registry import register_instance
from prometheus.infrastructure.external_clients.core.base_client import (
    BaseExternalClient,
)
from prometheus.infrastructure.external_clients.service_registry import (
    ServiceRegistryClient,
)
from ths_common.constants.base_enum import BaseEnum


class UPSProfileTypes(BaseEnum):
    B2B = "b2b"
    B2C = "b2c"


@register_instance()
class UPSServiceClient(BaseExternalClient):
    page_map = {
        'user': dict(
            type=BaseExternalClient.CallTypes.GET, url_regex="/ups/v1/users/{user_id}"
        ),
        'user_profiles': dict(
            type=BaseExternalClient.CallTypes.GET,
            url_regex="/ups/v1/users/{user_id}/profiles",
        ),
    }

    def get_domain(self):
        return ServiceRegistryClient.get_ups_service_url()

    def get_user_details(self, user_id):
        page_name = "user"
        url_params = dict(user_id=user_id)
        response = self.make_call(page_name, url_parameters=url_params)
        if not response.is_success():
            raise Exception(
                "UPS User Details API Error. Status Code: {0}, Errors: {1}".format(
                    response.response_code, response.errors
                )
            )
        return response.json_response

    def get_user_profile_details(self, user_id):
        page_name = "user_profiles"
        url_params = dict(user_id=user_id)
        response = self.make_call(page_name, url_parameters=url_params)
        if not response.is_success():
            raise Exception(
                "UPS User Profile API Error. Status Code: {0}, Errors: {1}".format(
                    response.response_code, response.errors
                )
            )
        return response.json_response
