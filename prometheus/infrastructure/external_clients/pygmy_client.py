import json
import os

from object_registry import register_instance
from prometheus.infrastructure.external_clients.core.base_client import (
    BaseExternalClient,
)
from prometheus.infrastructure.external_clients.service_registry import (
    ServiceRegistryClient,
)
from ths_common.exceptions import DownstreamSystemFailure

environment = os.environ.get('APP_ENV', 'local')


@register_instance()
class PygmyClient(BaseExternalClient):
    page_map = {
        'shorten_url': dict(
            url_regex="/api/shorten", type=BaseExternalClient.CallTypes.POST
        )
    }

    def get_domain(self):
        return ServiceRegistryClient.get_pygmy_service_url()

    def shorten_url(self, url, force_generate=False):
        if environment not in ['production', 'prod'] and not force_generate:
            return url
        page_name = "shorten_url"
        data = dict(long_url=url)
        response = self.make_call(page_name, data=data)
        if not response.is_success():
            raise DownstreamSystemFailure(
                message="Failed to shorten url",
                description=f"Failed to shorten url {url}",
                extra_payload=dict(
                    shorten_url_response=response.errors,
                    shorten_url_request=data,
                ),
            )
        response = response.json_response
        return response["short_url"]
