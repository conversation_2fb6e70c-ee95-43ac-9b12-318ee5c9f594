import logging
import os

from newrelic import agent

from object_registry import register_instance

logger = logging.getLogger(__name__)


@register_instance(arguments=[os.environ.get('APP_ENV', 'local')])
class NewrelicServiceClient:
    def __init__(self, environment):
        self.environment = environment

    def record_event(self, event_type, event_payload):
        if self.environment != "production":
            return
        agent.record_custom_event(event_type, event_payload)

    def record_custom_parameter(self, param_name, value):
        agent.add_custom_parameter(param_name, value)
