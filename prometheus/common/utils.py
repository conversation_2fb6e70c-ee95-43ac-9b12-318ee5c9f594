def split_on_condition(data_list, predicate):
    matching, not_matching = [], []
    for item in data_list:
        if predicate(item):
            matching.append(item)
        else:
            not_matching.append(item)
    return matching, not_matching


def sanitize_phone_number(phone_number):
    # external upstreams like ota send phone numbers in the inconsistent format
    # adding this weird logic to handle that, this method maximise the probability of getting a valid phone number
    from ths_common.utils.common_utils import sanitize_phone_number
    from ths_common.value_objects import PhoneNumber

    local_country_code = "+91"
    if not phone_number or not phone_number.number:
        return None

    country_code, number = sanitize_phone_number(phone_number.number)

    if not number:
        return phone_number

    country_code = country_code or phone_number.country_code or local_country_code
    if not country_code.startswith("+"):
        country_code = f"+{country_code}"

    return PhoneNumber(number, country_code)
