from marshmallow import Schema, fields, pre_dump
from marshmallow.validate import OneOf
from treebo_commons.money.money_field import <PERSON><PERSON>ield

from prometheus.common.serializers import (
    AddressSchema,
    CompanyMetaDataSchema,
    PhoneSchema,
)
from prometheus.core.api_docs import swag_schema
from shared_kernel.serializers.validators import validate_empty_string
from shared_kernel.serializers.value_objects import (
    GSTDetailsSchema,
    LegalDetailsSchema,
    TACommissionDetailsSchema,
)
from ths_common.constants.booking_constants import Salutation


@swag_schema
class NameSchema(Schema):
    salutation = fields.String(validate=OneOf(Salutation.all()))
    first_name = fields.String(allow_none=True, validate=validate_empty_string)
    middle_name = fields.String(allow_none=True, validate=validate_empty_string)
    last_name = fields.String(allow_none=True, validate=validate_empty_string)
    full_name = fields.String(allow_none=True, validate=validate_empty_string)


@swag_schema
class InvoiceBillToInfoSchema(Schema):
    """
    Invoice Bill to schema
    """

    customer_id = fields.String()
    name = fields.String(required=True)
    address = fields.Nested(AddressSchema, allow_none=True)
    gstin_num = fields.String(allow_none=True, validate=validate_empty_string)
    phone = fields.Nested(PhoneSchema, allow_none=True)
    email = fields.String(allow_none=True, validate=validate_empty_string)
    is_sez = fields.Boolean(allow_none=True)
    has_lut = fields.Boolean(allow_none=True)
    external_ref_id = fields.String(allow_none=True)


@swag_schema
class BankDetails(Schema):
    """
    Booking info for a given bill
    """

    account_name = fields.String(required=True)
    account_number = fields.String(required=True)
    bank = fields.String(allow_none=True)
    branch = fields.String(allow_none=True)
    id = fields.String(allow_none=True)
    ifsc_code = fields.String(required=True)
    branch_code = fields.String()
    swift_code = fields.String()
    type = fields.String(required=True)


@swag_schema
class InvoiceIssuedByInfoSchema(Schema):
    gst_details = fields.Nested(GSTDetailsSchema, allow_none=True)
    phone = fields.Nested(PhoneSchema, allow_none=True)
    email = fields.String(allow_none=True, validate=validate_empty_string)
    url = fields.Url(required=True)
    legal_signature = fields.Url()
    bank_details = fields.Nested(BankDetails)


@swag_schema
class BookingBillParentInfoSchema(Schema):
    """
    Booking info for a given bill
    """

    booking_id = fields.String(required=True)
    reference_number = fields.String(required=True)
    creation_date = fields.String(allow_none=True)
    checkin_date = fields.String(allow_none=True)
    checkout_date = fields.String(allow_none=True)


@swag_schema
class CumulativeTaxBreakupSchema(Schema):
    tax_type = fields.String(required=True)
    amount = MoneyField(allow_none=True)


@swag_schema
class ChargeComponentSchema(Schema):
    name = fields.String()
    posttax_amount = MoneyField()
    pretax_amount = MoneyField()
    quantity = fields.Integer()


class CompanyDetailsSchema(Schema):
    legal_details = fields.Nested(LegalDetailsSchema, required=True)
    billed_entity_id = fields.Integer(required=False)
    metadata = fields.Nested(CompanyMetaDataSchema, required=False, allow_none=True)


class AccountDetailsSchema(Schema):
    account_id = fields.String(required=True, validate=validate_empty_string)
    version = fields.Integer(required=True)


class TravelAgentDetailsSchema(CompanyDetailsSchema):
    ta_commission_details = fields.Nested(
        TACommissionDetailsSchema, required=False, allow_none=True
    )

    @pre_dump
    def pre_dump(self, obj, **kwargs):
        if not obj.ta_commission_details:
            obj.ta_commission_details = None
        return obj


class WebCheckInSchema(Schema):
    web_checkin_id = fields.String()
    status = fields.String()


@swag_schema
class FundingAmountSchema(Schema):
    manual_funding_amount = MoneyField()
    auto_funding_amount = MoneyField()


@swag_schema
class FundingSummaryResponseSchema(Schema):
    applicable_funding_amount = MoneyField()
    actual_funded_amount = MoneyField()
    funding_amount_breakup = fields.Nested(
        FundingAmountSchema, required=True, allow_none=False
    )
