from marshmallow import fields

from prometheus.common.serializers.request.attachment import NewAttachmentSchema
from prometheus.core.api_docs import swag_schema
from shared_kernel.serializers.validators import validate_empty_string


@swag_schema
class AttachmentSchema(NewAttachmentSchema):
    created_at = fields.LocalDateTime()
    modified_at = fields.LocalDateTime()
    attachment_id = fields.String(
        required=True,
        description='Unique Identifier generated at the time of creating the Attachment',
        validate=validate_empty_string,
    )
    original_url = fields.String()
    signed_url = fields.String()
    uploaded_by = fields.String()
    source = fields.String()
    status = fields.String()
    rejection_reason = fields.String()
