from marshmallow import Schema, fields, post_dump
from treebo_commons.money.money_field import Money<PERSON>ield

from prometheus.common.serializers.request import (
    CompanyDetailsSchema,
    LoyaltyProgramDetailsSchema,
    TravelAgentDetailsSchema,
    TravelBaseDetailsSchema,
)
from prometheus.common.serializers.request.value_objects import (
    EmploymentDetailsSchema,
    IDProofSchema,
    NameSchema,
    TravelDetailsSchema,
)
from prometheus.common.serializers.response import (
    CustomerVisaDetailsResponseSchema,
    PassportDetailsResponseSchema,
)
from prometheus.core.api_docs import swag_schema
from shared_kernel.serializers.value_objects import AddressSchema, PhoneSchema


@swag_schema
class BaseSchema(Schema):
    SKIP_VALUES = [None]

    @post_dump
    def remove_skip_values(self, data):
        return {
            key: value for key, value in data.items() if value not in self.SKIP_VALUES
        }


@swag_schema
class ERegCardTemplateRoomDetailsSchema(BaseSchema):
    room_stay_id = fields.String()
    room_type = fields.String()
    room_number = fields.String()
    room_charge = fields.String()
    adults = fields.String()
    childs = fields.String()


@swag_schema
class ERegCardTemplateBookingDetailsSchema(BaseSchema):
    arrival_date = fields.String()
    departure_date = fields.String()
    booking_reference_id = fields.String()
    mode_of_payment = fields.String()
    advance_payment = fields.String()
    membership_id = fields.String()
    remarks = fields.String()
    channel_code = fields.String()
    company_details = fields.Nested(CompanyDetailsSchema, allow_none=True)
    travel_agent_details = fields.Nested(
        TravelAgentDetailsSchema, allow_none=True, only=("legal_details",)
    )
    checkin_time = fields.String()
    checkout_time = fields.String()


@swag_schema
class ERegCardTemplateHotelDetailsSchema(BaseSchema):
    hotel_address = fields.String()
    hotel_name = fields.String()
    hotel_reception_landline_number = fields.String()
    hotel_reception_mobile_number = fields.String()
    hotel_email = fields.String()
    hotel_gstin = fields.String()
    hotel_logo = fields.String()
    hotel_pan_number = fields.String()
    hotel_tan_number = fields.String()
    hotel_tin_number = fields.String()
    timezone = fields.String()


@swag_schema
class ERegCardTemplatePrimaryGuestDetailSchema(BaseSchema):
    name = fields.Nested(NameSchema)
    nationality = fields.String()
    date_of_birth = fields.String()
    phone = fields.Nested(PhoneSchema)
    email = fields.String()
    address = fields.Nested(AddressSchema)
    id_proof = fields.Nested(IDProofSchema)
    travel_details = fields.Nested(TravelDetailsSchema)
    employment_details = fields.Nested(EmploymentDetailsSchema)
    verifier_signature = fields.String()
    passport_details = fields.Nested(PassportDetailsResponseSchema)
    visa_details = fields.Nested(CustomerVisaDetailsResponseSchema)
    arrival_details = fields.Nested(TravelBaseDetailsSchema)
    departure_details = fields.Nested(TravelBaseDetailsSchema)
    loyalty_program_details = fields.Nested(LoyaltyProgramDetailsSchema)


@swag_schema
class ERegCardTemplateOtherGuestDetailSchema(BaseSchema):
    name = fields.Nested(NameSchema)
    phone = fields.Nested(PhoneSchema)
    email = fields.String()
    address = fields.Nested(AddressSchema)


@swag_schema
class ERegCardTemplateResponseSchema(BaseSchema):
    hotel_details = fields.Nested(ERegCardTemplateHotelDetailsSchema)
    booking_details = fields.Nested(ERegCardTemplateBookingDetailsSchema)
    room_details = fields.Nested(ERegCardTemplateRoomDetailsSchema)
    primary_guest = fields.Nested(ERegCardTemplatePrimaryGuestDetailSchema)
    other_guests = fields.Nested(ERegCardTemplateOtherGuestDetailSchema, many=True)
    print_rate = fields.Boolean()
    average_room_stay_price = MoneyField()
