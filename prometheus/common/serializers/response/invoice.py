from marshmallow import Schema, fields, post_dump, pre_dump
from marshmallow.validate import OneOf
from treebo_commons.money.money_field import <PERSON>Field

from prometheus.common.serializers.base_schema import ThsBaseSchema
from prometheus.common.serializers.request.value_objects import (
    BilledEntityAccountSchema,
    ChargeItemSchema,
    ItemCodeSchema,
    VendorDetailsSchema,
)
from prometheus.common.serializers.response.billing import (
    BilledEntityAccountSummarySchema,
    BillSummarySchema,
    BookingInvoicePaymentInfoSchema,
    CreditNoteSchema,
    CreditNoteShallowSchema,
    NewBillSummarySchema,
    TemplateChargeItemTaxBreakup,
)
from prometheus.common.serializers.response.value_objects import (
    BankDetails,
    InvoiceBillToInfoSchema,
    InvoiceIssuedByInfoSchema,
    NameSchema,
)
from prometheus.core.api_docs import swag_schema
from schema_instances import register_schema
from shared_kernel.serializers.validators import validate_empty_string
from shared_kernel.serializers.value_objects import (
    CumulativeTaxDetailSchema,
    TaxDetailSchema,
)
from ths_common.constants.billing_constants import (
    ChargeBillToTypes,
    ChargeStatus,
    ChargeTypes,
    InvoiceStatus,
    IssuedByType,
    IssuedToType,
)
from ths_common.value_objects import InvoiceChargeToInfo


@swag_schema
class InvoiceChargeToInfoSchema(Schema):
    """
    The Invoice charge to schema
    """

    customer_id = fields.String(required=True)
    name = fields.Nested(NameSchema, allow_none=True)
    is_primary = fields.Boolean(allow_none=True)


@register_schema(many=True)
@swag_schema
class InvoiceSchema(ThsBaseSchema):
    """
    Invoice Schema
    """

    created_at = fields.LocalDateTime()
    modified_at = fields.LocalDateTime()
    invoice_id = fields.String(required=True)
    bill_id = fields.String(required=True)
    invoice_number = fields.String(
        required=True,
        description='Invoice number in series compliant with the GST Council rules',
    )
    vendor_details = fields.Nested(VendorDetailsSchema, required=True)
    parent_info = fields.Dict(required=True)
    user_info_map = fields.Dict()
    pretax_amount = MoneyField(
        required=True, description='Total amount exclusive of tax'
    )
    tax_amount = MoneyField(required=True, description='Total applicable tax')
    posttax_amount = MoneyField(
        required=True, description='Total amount inclusive of tax'
    )
    credit_payable = MoneyField(
        required=True, description='Total credit charges in the invoice'
    )
    credit_note_amount = MoneyField(
        required=True, description='Total credit note amount on the invoice'
    )
    # TODO: Change to tax_type
    tax_details_breakup = fields.Dict(
        required=True, decription="tax type wise total in a dict format"
    )
    invoice_date = fields.Date(required=True, description='Date of invoice generation')
    invoice_due_date = fields.Date(required=True)
    status = fields.String(validate=OneOf(InvoiceStatus.all()))
    bill_to = fields.Nested(InvoiceBillToInfoSchema, required=True)
    bill_to_type = fields.String(
        required=True, description='bill to type in the invoice'
    )
    allowed_charge_types = fields.List(
        fields.String, description='allowed charge types in the invoice'
    )
    allowed_charge_tos = fields.Nested(
        InvoiceChargeToInfoSchema,
        many=True,
        description='allowed charge tos in the invoice',
    )
    allowed_bill_tos = fields.Nested(
        InvoiceChargeToInfoSchema,
        many=True,
        description="List of customers to which this invoice can be billed to",
    )
    version = fields.Integer(
        required=True,
        description='Version number of the Invoice. Should be provided at the time of editing. '
        'This is used to resolve conflicts, in case the Invoice is changed by '
        'multiple clients',
    )
    invoice_url = fields.Url(required=False)
    signed_url = fields.Url(required=False)
    issued_by = fields.Nested(InvoiceIssuedByInfoSchema)
    issued_to_type = fields.String(validate=OneOf(IssuedToType.all()))
    issued_by_type = fields.String(validate=OneOf(IssuedByType.all()))
    hotel_invoice_id = fields.String(
        description="Invoice id for invoice issued by hotel to treebo in reseller"
    )
    hotel_invoice_number = fields.String(
        required=False,
        description='Invoice number of corresponding buy side invoice',
        dump_only=True,
        allow_none=True,
    )
    irn = fields.String(dump_only=True)
    qr_code = fields.String(dump_only=True)
    is_einvoice = fields.Boolean(dump_only=True)
    tax_breakup = fields.Nested(
        CumulativeTaxDetailSchema,
        many=True,
        dump_only=True,
        decription="Cumulative Tax in Invoice",
        attribute='tax_details',
    )
    billed_entity_account = fields.Nested(BilledEntityAccountSchema, required=True)
    net_payable = MoneyField(
        dump_only=True, description='Total payable amount, to confirm this invoice'
    )
    summary = fields.Nested(NewBillSummarySchema)
    generated_by = fields.String()
    is_downloaded = fields.Boolean()
    is_reissue_allowed = fields.Boolean()
    is_spot_credit = fields.Boolean(dump_only=True)

    @pre_dump
    def load_data(self, invoice_aggregate):
        data = invoice_aggregate.invoice
        data.credit_note_amount = invoice_aggregate.credit_note_amount
        data.credit_payable = invoice_aggregate.get_credit_payable()
        data.hotel_invoice_number = invoice_aggregate.hotel_invoice_number
        data.allowed_charge_tos = None
        data.allowed_bill_tos = None
        data.net_payable = invoice_aggregate.net_payable
        if invoice_aggregate.invoice.allowed_charge_to_ids:
            data.allowed_charge_tos = invoice_aggregate.invoice.get_users(
                invoice_aggregate.invoice.allowed_charge_to_ids
            )
        if invoice_aggregate.allowed_bill_to_ids:
            if invoice_aggregate.invoice.bill_to_type == ChargeBillToTypes.COMPANY:
                data.allowed_bill_tos = [
                    dict(
                        customer_id=invoice_aggregate.bill_to.customer_id,
                        name=invoice_aggregate.bill_to.name,
                    )
                ]
            else:
                data.allowed_bill_tos = invoice_aggregate.invoice.get_users(
                    invoice_aggregate.allowed_bill_to_ids
                )

        if hasattr(invoice_aggregate, 'summary'):
            data.summary = invoice_aggregate.summary
        return data

    @post_dump
    def customise_serialized_data(self, data):
        """
        Sets the nested fields attributes from related aggregates, that are set in the context of this schema object
        :param data:
        :return:
        """

        user_info_map_updated = dict()
        for customer_id, customer_json in data.get('user_info_map').items():
            user_info_map_updated[customer_id] = InvoiceChargeToInfo.to_json(
                customer_json
            )

        data['user_info_map'] = user_info_map_updated
        return data


@swag_schema
class InvoiceChargeSplitSchema(Schema):
    """
    Charge split schema
    """

    charge_split_id = fields.Integer(required=True)
    charge_to = fields.String(required=True)
    pre_tax = MoneyField(required=True)
    tax = MoneyField(required=True)
    post_tax = MoneyField(required=True)
    invoice_id = fields.String(required=False)
    tax_details = fields.Nested(TaxDetailSchema, many=True)
    percentage = fields.Decimal(required=True)


@swag_schema
class InvoiceChargeSchema(Schema):
    """
    Schema for Invoice charges. Used for only testing purposes
    """

    invoice_charge_id = fields.Integer(required=True)
    charge_id = fields.Integer(required=True)
    charge_split_ids = fields.List(fields.String, required=True)
    created_by = fields.String(required=True)
    applicable_date = fields.LocalDateTime(
        required=True,
        description="Date of consumption of the charge. "
        "A datetime string in ISO-8601 format. Timezone information is mandatory.",
    )
    charge_item = fields.Nested(ChargeItemSchema, description='Details about the item')
    pretax_amount = MoneyField(required=True, description='The pretax amount')
    tax_amount = MoneyField(required=True, description='The tax amount')
    posttax_amount = MoneyField(
        required=True, description='The charge amount inclusive of tax'
    )
    tax_details = fields.Nested(TaxDetailSchema, many=True)
    charge_type = fields.String(
        validate=OneOf(ChargeTypes.all()),
        required=True,
        description='credit - Guest can check-out without paying. The expenses will be billed '
        'to the '
        'company non-credit - Guest has to clear the dues at the time of check-out',
    )
    bill_to_type = fields.String(
        validate=OneOf(ChargeBillToTypes.all()),
        required=True,
        description='Whom should the expenses be billed to',
    )
    charge_status = fields.String(
        validate=OneOf(ChargeStatus.all()),
        required=True,
        description='Status of the charge',
    )
    comment = fields.String(description='Any User/Client comment regarding the Charge')
    charge_splits = fields.Nested(
        InvoiceChargeSplitSchema,
        many=True,
        description='Details of the split, in case of multiple invoices',
    )
    charge_to_ids = fields.List(fields.String)
    credit_note_generated_amount = MoneyField(
        description="The amount for which credit note has already been generated"
    )


@swag_schema
class InvoicePaymentSplitSchema(Schema):
    payment_split_id = fields.Integer(required=True)
    payment_id = fields.Integer(required=True)
    amount = MoneyField(required=True)
    payment_type = fields.String(required=True)
    payment_mode = fields.String(required=True)


@register_schema(many=True)
@swag_schema
class InvoiceRawSchema(InvoiceSchema):
    """
    The Invoice schema for raw data
    """

    invoice_charges = fields.Nested(
        InvoiceChargeSchema,
        many=True,
        allow_none=True,
        description='This key will be sent if show_raw is true in the request params',
    )
    payment_splits = fields.Nested(
        InvoicePaymentSplitSchema, many=True, allow_none=True
    )

    @pre_dump
    def load_data(self, invoice_aggregate):
        data = super(InvoiceRawSchema, self).load_data(invoice_aggregate)
        data.invoice_charges = invoice_aggregate.invoice_charges
        data.payment_splits = invoice_aggregate.payment_splits
        return data


@swag_schema
class RoomChargeItemDetailsSchema(Schema):
    """
    Room info for a charge item detail
    """

    occupancy = fields.String(allow_none=True, validate=validate_empty_string)
    room_type = fields.String(allow_none=True, validate=validate_empty_string)
    room_no = fields.String(allow_none=True, validate=validate_empty_string)
    checkin_date = fields.Date()
    checkout_date = fields.Date()


@swag_schema
class HotelBookingInvoiceChargeAmountSchema(Schema):
    applicable_date = fields.Date(required=True)
    services = fields.String()
    other_charge_component_names = fields.List(fields.String())
    item_code = fields.Nested(ItemCodeSchema)
    pretax_amount = MoneyField()
    posttax_amount = MoneyField()
    tax = fields.Nested(TemplateChargeItemTaxBreakup, required=True)
    tax_breakup = fields.Nested(TaxDetailSchema, many=True)
    charged_entity_id = fields.String()
    description = fields.String()
    reference = fields.String()
    charge_category = fields.String()
    posting_date = fields.Date()


@swag_schema
class HotelBookingInvoiceChargeItemSchema(Schema):
    room_info = fields.Nested(RoomChargeItemDetailsSchema)
    guests = fields.Nested(InvoiceChargeToInfoSchema, many=True)
    charges = fields.Nested(HotelBookingInvoiceChargeAmountSchema, many=True)


@swag_schema
class CategoryWiseCumulativeTaxSchema(Schema):
    category = fields.String()
    tax_amount = MoneyField()
    tax_type = fields.String()


@swag_schema
class VendorMetaDetailsSchema(Schema):
    vendor_logo = fields.String()
    vendor_pan_number = fields.String()
    vendor_tan_number = fields.String()
    vendor_tin_number = fields.String()
    vendor_msme_number = fields.String()
    vendor_cin_number = fields.String()


@register_schema(many=True)
@swag_schema
class HotelBookingInvoiceTemplateSchema(ThsBaseSchema):
    vendor_meta = fields.Nested(VendorMetaDetailsSchema)
    invoice = fields.Nested(InvoiceSchema, required=True)
    booking = fields.Dict(required=True)
    charge_items = fields.Nested(HotelBookingInvoiceChargeItemSchema, many=True)
    company_or_ta_details = fields.Nested(InvoiceIssuedByInfoSchema)
    payments = fields.Nested(BookingInvoicePaymentInfoSchema, many=True)
    bank_details = fields.Nested(BankDetails)
    vendor_context = fields.Dict()
    folio_number = fields.Integer()
    category_wise_cummulative_taxes = fields.Nested(
        CategoryWiseCumulativeTaxSchema, many=True
    )
    bill_summary = fields.Nested(BillSummarySchema)
    booking_meta = fields.Dict()
    printed_by = fields.String()
    printed_on = fields.DateTime()
    child_count = fields.Integer()
    adult_count = fields.Integer()
    billed_entity_category = fields.String()
    average_room_stay_price = MoneyField()


@swag_schema
class HotelBookingInvoiceChargeSchema(Schema):
    posting_date = fields.Date(required=True)
    sku_category = fields.String(required=True)
    room_count = fields.Integer()
    room_type = fields.String()
    room_occupancy = fields.String()
    avg_pretax_room_rate = MoneyField()
    posttax_amount = MoneyField(required=True)
    pretax_amount = MoneyField(required=True)
    tax_breakup = fields.Nested(TaxDetailSchema, many=True)


class ChargeItemsSummarySchema(Schema):
    charges = fields.Nested(HotelBookingInvoiceChargeSchema, many=True)


@swag_schema
class ShallowInvoiceSchema(Schema):
    """
    Shallow Invoice Schema
    """

    created_at = fields.LocalDateTime()
    modified_at = fields.LocalDateTime()
    invoice_id = fields.String(required=True)
    bill_id = fields.String(required=True)
    invoice_number = fields.String(
        required=True,
        description='Invoice number in series compliant with the GST Council rules',
    )
    vendor_details = fields.Nested(VendorDetailsSchema, required=True)
    pretax_amount = MoneyField(
        required=True, description='Total amount exclusive of tax'
    )
    tax_amount = MoneyField(required=True, description='Total applicable tax')
    posttax_amount = MoneyField(
        required=True, description='Total amount inclusive of tax'
    )
    credit_payable = MoneyField(
        required=True, description='Total credit charges in the invoice'
    )
    credit_note_amount = MoneyField(
        required=True, description='Total credit note amount on the invoice'
    )
    invoice_date = fields.Date(required=True, description='Date of invoice generation')
    invoice_due_date = fields.Date(required=True)
    status = fields.String(validate=OneOf(InvoiceStatus.all()))
    bill_to = fields.Nested(InvoiceBillToInfoSchema, required=True)
    bill_to_type = fields.String(
        required=True, description='bill to type in the invoice'
    )
    allowed_charge_types = fields.List(
        fields.String, description='allowed charge types in the invoice'
    )
    version = fields.Integer(
        required=True,
        description='Version number of the Invoice. Should be provided at the time of editing. '
        'This is used to resolve conflicts, in case the Invoice is changed by '
        'multiple clients',
    )
    invoice_url = fields.Url(required=False)
    signed_url = fields.Url(required=False)
    issued_by = fields.Nested(InvoiceIssuedByInfoSchema)
    issued_by_type = fields.String(validate=OneOf(IssuedByType.all()))
    hotel_invoice_id = fields.String(
        description="Invoice id for invoice issued by hotel to treebo in reseller"
    )
    irn = fields.String(dump_only=True)
    is_einvoice = fields.Boolean(dump_only=True)
    tax_breakup = fields.Nested(
        CumulativeTaxDetailSchema,
        many=True,
        dump_only=True,
        decription="Cumulative Tax in Invoice",
        attribute='tax_details',
    )
    net_payable = MoneyField(
        dump_only=True, description='Total payable amount, to confirm this invoice'
    )
    summary = fields.Nested(NewBillSummarySchema)
    generated_by = fields.String()
    is_downloaded = fields.Boolean()
    is_reissue_allowed = fields.Boolean()

    @pre_dump
    def load_data(self, invoice_aggregate):
        data = invoice_aggregate.invoice
        data.credit_note_amount = invoice_aggregate.credit_note_amount
        data.credit_payable = invoice_aggregate.get_credit_payable()
        data.net_payable = invoice_aggregate.net_payable

        if hasattr(invoice_aggregate, 'summary'):
            data.summary = invoice_aggregate.summary

        return data


@register_schema(many=True)
class HotelBookingSummaryInvoiceTemplateSchema(ThsBaseSchema):
    vendor_meta = fields.Nested(VendorMetaDetailsSchema)
    invoice = fields.Nested(ShallowInvoiceSchema, required=True)
    booking = fields.Dict(required=True)
    charge_items_summary = fields.Nested(ChargeItemsSummarySchema, many=True)
    payments = fields.Nested(BookingInvoicePaymentInfoSchema, many=True)
    bank_details = fields.Nested(BankDetails)
    vendor_context = fields.Dict()
    booking_meta = fields.Dict()
    printed_by = fields.String()
    printed_on = fields.DateTime()
    guest_name = fields.String()
    total_guests = fields.Integer()
    room_nos = fields.String()
    average_room_stay_price = MoneyField()


class PosInvoiceTemplateChargeItemSchema(Schema):
    applicable_date = fields.Date(required=True)
    item_name = fields.String(required=True)
    hsn_code = fields.String(required=True)
    item_details = fields.Dict(required=False)
    pretax_amount = MoneyField(required=True)
    posttax_amount = MoneyField(required=True)
    tax = fields.Nested(TemplateChargeItemTaxBreakup, required=True)
    tax_breakup = fields.Nested(TaxDetailSchema, many=True, required=True)


@register_schema(many=True)
@swag_schema
class PosBookingInvoiceTemplateSchema(ThsBaseSchema):
    invoice = fields.Nested(InvoiceSchema, required=True)
    parent_info = fields.Dict(required=True)
    charge_items = fields.Nested(
        PosInvoiceTemplateChargeItemSchema, many=True, required=True
    )
    payments = fields.Nested(BookingInvoicePaymentInfoSchema, many=True)
    bank_details = fields.Nested(BankDetails)


@swag_schema
class PosOrderInvoiceTemplateResponse(Schema):
    template = fields.Nested(PosBookingInvoiceTemplateSchema)
    invoice_url = fields.String()
    invoice_signed_url = fields.String()


class InvoiceReIssueResponseSchema(Schema):
    credit_notes = fields.Nested(CreditNoteSchema, many=True)
    invoices = fields.Nested(InvoiceSchema, many=True)


@swag_schema
class ModifyInvoicesResponseSchema(Schema):
    credit_notes = fields.Nested(CreditNoteShallowSchema, many=True)
    accounts_used_for_modified_charges = fields.Nested(
        BilledEntityAccountSummarySchema, many=True
    )
