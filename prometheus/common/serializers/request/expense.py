from marshmallow import Schema, ValidationError, fields, validate
from marshmallow.decorators import post_dump, post_load, validates_schema
from marshmallow.validate import OneOf, Range
from treebo_commons.money.money_field import <PERSON>Field
from treebo_commons.utils import dateutils

from prometheus.common.serializers.request import BillingInstructionSchema
from prometheus.common.serializers.request.value_objects import (
    PriceSchema,
    UpdatePriceSchema,
)
from prometheus.core.api_docs import swag_schema
from shared_kernel.serializers.validators import validate_empty_string
from ths_common.constants.booking_constants import (
    ExpenseAddedBy,
    ExpenseStatus,
    ServiceTypes,
)
from ths_common.utils.math_utils import logical_xor
from ths_common.value_objects import ExpenseServiceContext


@swag_schema
class BaseExpenseSchema(Schema):
    """
    Schema to create a new expense
    """

    expense_item_id = fields.String(required=True, description='')
    room_stay_id = fields.Integer(
        required=True,
        description='Unique identifier of the room to which this expense should be assigned',
        error_messages={
            'null': 'Room stay id may not be null.',
            'required': 'Please provide room stay id.',
            'validator_failed': "'{input}' is not a valid value for room stay id",
        },
    )
    assigned_to = fields.List(
        fields.String,
        required=True,
        description='Unique identifier of the Guest to which this expense should be assigned',
        error_messages={
            'null': 'Assigned to may not be null.',
            'required': 'Please provide assignment.',
            'validator_failed': "'{input}' is not a valid value for assigned to.",
        },
    )
    comments = fields.String(
        allow_none=True,
        validate=validate_empty_string,
        description='Any user/client comments regarding the Expense',
    )
    added_by = fields.String(
        validate=OneOf(
            ExpenseAddedBy.all(),
            error="'{input}' is not a valid choice for Expense Added By",
        ),
        required=False,
        description='Added by Treebo/Hotel',
    )

    @post_load
    def convert_enums(self, data):
        if data.get('added_by'):
            data['added_by'] = ExpenseAddedBy(data['added_by'])


@swag_schema
class NewExpenseSchema(BaseExpenseSchema):
    """
    Schema to create a new expense
    """

    price = fields.Nested(
        PriceSchema,
        required=True,
        description='Price of the Expense (inclusive/exclusive of taxes)',
        error_messages={
            'null': 'Price may not be null.',
            'required': 'Please provide price.',
            'validator_failed': "'{input}' is not a valid value for Price.",
        },
    )
    extra_information = fields.Dict(allow_none=True)
    sku_id = fields.String(
        allow_none=True,
        validate=validate_empty_string,
        description='SKU ID of the expense to be created',
    )

    @post_load
    def convert_to_expense_payload(self, data):
        data['guests'] = data['assigned_to']
        data['applicable_date'] = data['price'].applicable_date
        data['status'] = ExpenseStatus.CONSUMED
        if 'comments' not in data:
            data['comments'] = None
        if 'room_stay_id' not in data:
            data['room_stay_id'] = None


@swag_schema
class EditExpenseSchema(UpdatePriceSchema):
    comments = fields.String(
        allow_none=True,
        validate=validate_empty_string,
        required=False,
        description='Any user/client comments regarding the Expense',
    )

    assigned_to = fields.List(
        fields.String,
        required=False,
        description='Unique identifier of the Guest to which this price should be assigned. Only to be used while '
        'editing charge',
    )


@swag_schema
class ExpensePaginationSchema(Schema):
    expense_id_gt = fields.Integer(required=False)
    limit = fields.Integer(required=False)


@swag_schema
class DateWiseExpensePriceSchema(Schema):
    unit_pretax_price = MoneyField(
        allow_none=True,
        description='Price per extra exclusive of tax',
        missing=None,
        as_string=True,
    )
    unit_posttax_price = MoneyField(
        allow_none=True,
        description='Price per extra inclusive of tax',
        as_string=True,
        missing=None,
    )
    quantity = fields.Integer(
        required=False,
        description='Number of expenses to be created per room. Defaults to 1',
        missing=1,
        error_messages={'null': 'Quantity may not be null.'},
        validate=[Range(min=1, error="Value must be greater than 0")],
    )
    applicable_date = fields.LocalDateTime(
        required=True, description='Should create expense for this date'
    )
    status = fields.String(
        validate=OneOf([ExpenseStatus.CREATED.value, ExpenseStatus.CONSUMED.value]),
        missing=ExpenseStatus.CREATED.value,
    )
    comments = fields.String(
        allow_none=True,
        validate=validate_empty_string,
        required=False,
        description='Any user/client comments regarding the Date wise price Expense',
    )

    @validates_schema
    def validate_data(self, data):
        unit_pretax_price = data.get('unit_pretax_price')
        unit_posttax_price = data.get('unit_posttax_price')

        if not logical_xor(unit_pretax_price, unit_posttax_price):
            raise ValidationError(
                "Please provide either unit_pretax_price or unit_posttax_price"
            )

        if 'applicable_date' in data and dateutils.is_naive(data['applicable_date']):
            raise ValidationError(
                "Timezone information missing", field_names=['applicable_date']
            )

    @post_load
    def set_status(self, data):
        data['status'] = ExpenseStatus(data['status'])


@swag_schema
class SkuExpenseSchema(Schema):
    """
    Schema to create a new expense
    """

    sku_id = fields.String(
        required=True,
        description="Catalog Sku ID for which extra needs to be created",
        error_messages={
            'null': 'sku id may not be null.',
            'required': 'sku id data missing.',
            'validator_failed': 'Invalid sku_id.',
        },
    )
    date_wise_prices = fields.Nested(
        DateWiseExpensePriceSchema,
        many=True,
        required=True,
        validate=[validate.Length(min=1)],
    )
    via_rate_plan = fields.Boolean(required=False)


class ExpenseServiceContextSchema(Schema):
    service_type = fields.String(validate=OneOf(ServiceTypes.all()), required=True)
    service_details = fields.Dict(
        description="Details of the service.",
    )

    @post_dump
    def convert_service_details(self, data):
        service_details = data.get('service_details')
        if service_details and not isinstance(service_details, dict):
            data['service_details'] = service_details.to_dict()
        return data


@swag_schema
class NewExpensesSchemaV2(Schema):
    room_stay_id = fields.Integer(
        required=True,
        description='Unique Identifier of the room to which the charges for the expense to '
        'be associated',
        error_messages={
            'null': 'Room stay id may not be null.',
            'required': 'Room stay id data missing.',
            'validator_failed': 'Invalid value for Room stay id.',
        },
    )
    assigned_to = fields.List(
        fields.String,
        required=True,
        description='Unique identifier of the Guest to which this expense should be assigned',
        error_messages={
            'null': 'Assigned to may not be null.',
            'required': 'Please provide assignment.',
            'validator_failed': "'{input}' is not a valid value for assigned to.",
        },
    )
    billing_instructions = fields.Nested(BillingInstructionSchema, many=True)
    skus = fields.Nested(
        SkuExpenseSchema, many=True, required=True, validate=[validate.Length(min=1)]
    )
    added_by = fields.String(
        validate=OneOf(
            [ExpenseAddedBy.HOTEL.value, ExpenseAddedBy.TREEBO.value],
            error="'{input}' is not a valid " "choice for Expense Added By",
        ),
        missing=ExpenseAddedBy.HOTEL.value,
        required=False,
        description='Added by Treebo/Hotel',
    )

    bill_to = fields.Integer(
        required=False,
        allow_none=True,
        description="If bill_to is passed and billing_instruction is not passed, then derive on the basis of bill_to",
    )
    extra_information = fields.Dict(allow_none=True)
    service_context = fields.Nested(
        ExpenseServiceContextSchema,
        allow_none=True,
        required=False,
        description="Details of the addon service",
    )

    @post_load
    def convert_to_expense_payload(self, data):
        data['guests'] = data['assigned_to']
        data['added_by'] = ExpenseAddedBy(data['added_by'])
        data['service_context'] = (
            ExpenseServiceContext.from_dict(data['service_context'])
            if data.get('service_context')
            else None
        )
