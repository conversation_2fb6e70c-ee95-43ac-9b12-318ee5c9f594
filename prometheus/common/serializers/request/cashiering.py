from marshmallow import (
    Schema,
    ValidationError,
    fields,
    post_load,
    pre_load,
    validate,
    validates_schema,
)
from marshmallow.validate import OneOf
from treebo_commons.money import Money
from treebo_commons.money.money_field import MoneyField
from treebo_commons.utils import dateutils

from prometheus.common.serializers.request.value_objects import IDProofSchema
from prometheus.core.api_docs import swag_schema
from prometheus.domain.billing.value_objects.currency_seller import (
    CurrencySellerDetailVO,
)
from shared_kernel.serializers.validators import validate_empty_string
from shared_kernel.serializers.value_objects import (
    TaxDetailSchemaWithStringifiedDecimal,
)
from ths_common.constants.billing_constants import CashierSessionStatus
from ths_common.value_objects import TaxDetail


@swag_schema
class CashierSessionNewSchema(Schema):
    opening_balance = fields.List(
        MoneyField(
            required=True,
            description='list of opening balance amounts in multiple currencies',
        )
    )
    opening_balance_in_base_currency = MoneyField(
        required=True,
        description='list of opening balance amounts in multiple currencies',
    )
    vendor_id = fields.String(
        required=True,
        description='Unique identifier for the Vendor from Catalog Service',
        error_messages={
            'null': 'Vendor id may not be null.',
            'required': 'Please provide vendor id.',
            'validator_failed': "'{input}' is not a valid choice for vendor id.",
        },
    )

    @validates_schema(skip_on_field_errors=True)
    def validate_data(self, data):
        if (
            data.get('opening_balance') == None
            or data.get('opening_balance_in_base_currency') == None
        ):
            if (
                data.get('opening_balance') == None
                and data.get('opening_balance_in_base_currency') == None
            ):
                return
            if data.get('opening_balance') != None:
                raise ValidationError(
                    "opening balance in base currency is required",
                    field_names=['opening_balance_in_base_currency'],
                )
            if data.get('opening_balance_in_base_currency') != None:
                raise ValidationError(
                    "opening balance is required", field_names=['opening_balance']
                )

    @pre_load
    def convert_money_string_to_money(self, data):
        if data.get('opening_balance'):
            data['opening_balance'] = [
                Money(value.strip()) for value in data.get('opening_balance').split(',')
            ]
        return data


@swag_schema
class CashierSessionUpdateSchema(Schema):
    vendor_id = fields.String(required=True)
    status = fields.String(
        required=True,
        validate=[
            OneOf(
                [CashierSessionStatus.CLOSED, CashierSessionStatus.OPEN],
                error="'{input}' is not a valid choice of 'status'",
            )
        ],
    )


@swag_schema
class CashRegisterNewSchema(Schema):
    default_opening_balance = fields.List(
        MoneyField(
            allow_none=True,
            description='list of opening balance amounts in multiple currencies',
        )
    )

    vendor_id = fields.String(
        required=True,
        description='Unique identifier for the Vendor from Catalog Service',
        error_messages={
            'null': 'Vendor id may not be null.',
            'required': 'Please provide vendor id.',
            'validator_failed': "'{input}' is not a valid choice for vendor id.",
        },
    )
    cash_register_name = fields.String(required=True)
    carry_balance_to_next_session = fields.Boolean()

    @pre_load
    def convert_money_string_to_money(self, data):
        if data.get('default_opening_balance'):
            data['default_opening_balance'] = [
                Money(value.strip())
                for value in data.get('default_opening_balance').split(',')
            ]
        return data


@swag_schema
class CashRegisterSearchSchema(Schema):
    """
    Cash register search schema
    """

    vendor_id = fields.String()


@swag_schema
class CashCounterNewPaymentSchema(Schema):
    """
    Cash Counter New Payment Schema
    """

    amount = MoneyField(required=False)
    amount_in_payment_currency = MoneyField(required=False)
    paid_to = fields.String(allow_none=True)
    status = fields.String(
        required=True,
        error_messages={
            'null': 'Payment status may not be null.',
            'required': 'Please provide payment status.',
            'validator_failed': "'{input}' is not a valid choice for Payment Status",
        },
    )

    date_of_payment = fields.LocalDateTime(
        required=True,
        description="A datetime string in ISO-8601 format. Timezone information is mandatory.",
        error_messages={
            'null': 'Date of Payment may not be null.',
            'required': 'Please provide date of payment.',
            'validator_failed': 'Invalid value for date of payment.',
        },
    )
    payment_mode = fields.String(
        required=True,
        error_messages={
            'null': 'Payment mode may not be null.',
            'required': 'Please provide payment mode.',
            'validator_failed': "'{input}' is not a valid choice for Payment Mode",
        },
    )
    payment_mode_sub_type = fields.String(allow_none=True)
    payment_type = fields.String(
        required=True,
        error_messages={
            'null': 'Payment type may not be null.',
            'required': 'Please provide payment type.',
            'validator_failed': "'{input}' is not a valid choice for Payment Type",
        },
    )
    payment_details = fields.Dict(
        allow_none=True, description='Any details regarding the payment'
    )
    comment = fields.String(
        allow_none=True,
        validate=validate_empty_string,
        description='Optional comment field',
    )
    should_generate_voucher = fields.Boolean()
    booking_reference_number = fields.String()

    @validates_schema(skip_on_field_errors=True)
    def validate_data(self, data):
        if 'date_of_payment' in data:
            if dateutils.is_naive(data['date_of_payment']):
                raise ValidationError(
                    "Timezone information missing", field_names=['date_of_payment']
                )
            data['date_of_payment'] = dateutils.localize_datetime(
                data['date_of_payment']
            )


@swag_schema
class CashierSessionSearchSchema(Schema):
    start_datetime = fields.DateTime(timezone=True)
    end_datetime = fields.DateTime(timezone=True)

    cashier_session_ids = fields.List(fields.String())
    transaction_type = fields.String(required=False)  # cash or non-cash

    sort_by = fields.String(
        description='Specify sort by:'
        '\n -created_at: sort by created_at descending'
        '\n created_at: sort by created_at ascending'
        '\n -start_datetime: sort by start_datetime at descending'
        '\n start_datetime: sort by start_datetime at ascending'
        '\n -session_number: sort by session_number at descending'
        '\n session_number:sort by session_number at ascending'
    )
    limit = fields.Integer(
        description='Number of results required',
        missing='20',
        validate=validate.Range(min=1, max=20),
    )
    offset = fields.Integer(
        description='Offset from which results are required', missing='0'
    )
    include_payments = fields.Boolean()
    status = fields.String(
        validate=[
            OneOf(
                [CashierSessionStatus.CLOSED, CashierSessionStatus.OPEN],
                error="'{input}' is not a valid choice of 'status'",
            )
        ]
    )
    cash_register_ids = fields.List(fields.String())
    vendor_id = fields.String(required=True)

    @validates_schema(skip_on_field_errors=TabError)
    def validate_data(self, data):
        if (data.get('start_datetime') or data.get('end_datetime')) and not (
            data.get('start_datetime') and data.get('end_datetime')
        ):
            raise ValidationError(
                "start_datetime end_datetime mandatory both should be present instead of one has given",
                field_names=['start_datetime', 'end_datetime'],
            )

    @pre_load
    def convert_comma_separated_string_to_list(self, data):
        if data.get('cash_register_ids'):
            data['cash_register_ids'] = [
                cash_register_id.strip()
                for cash_register_id in data.get('cash_register_ids').split(',')
            ]
        if data.get('cashier_session_ids'):
            data['cashier_session_ids'] = [
                cash_register_id.strip()
                for cash_register_id in data.get('cashier_session_ids').split(',')
            ]
        return data


@swag_schema
class CashCounterUpdatePaymentSchema(Schema):
    status = fields.String()


@swag_schema
class UpdateCashRegisterSchema(Schema):
    default_opening_balance = fields.List(
        MoneyField(description='list of opening balance amounts in multiple currencies')
    )
    carry_balance_to_next_session = fields.Boolean()

    @pre_load
    def convert_money_string_to_money(self, data):
        if data.get('default_opening_balance'):
            data['default_opening_balance'] = [
                Money(value.strip())
                for value in data.get('default_opening_balance').split(',')
            ]
        return data


@swag_schema
class CurrencySellerDetailSchema(Schema):
    room_number = fields.String(required=True)
    guest_name = fields.String(required=True)
    booking_id = fields.String()
    id_proof = fields.Nested(IDProofSchema)

    @post_load
    def create_object(self, data):
        return CurrencySellerDetailVO(
            data['room_number'],
            data['guest_name'],
            data.get('booking_id'),
            data.get('id_proof'),
        )


@swag_schema
class NewCurrencyExchangeSchema(Schema):
    currency_seller_detail = fields.Nested(CurrencySellerDetailSchema, required=True)
    amount_in_foreign_currency = MoneyField(required=True)
    foreign_currency_payment_mode = fields.String()
    amount_in_base_currency = MoneyField(required=True)
    taxable_amount = MoneyField()
    tax_amount = MoneyField()
    tax_details = fields.Nested(TaxDetailSchemaWithStringifiedDecimal, many=True)
    round_off = MoneyField(required=True)
    total_payable_in_base_currency = MoneyField(required=True)
    transaction_date = fields.Date(
        required=False,
        description="A date string in ISO-8601 format.",
        error_messages={
            'null': 'Date of transaction may not be null.',
            'validator_failed': 'Invalid value for transaction date.',
        },
    )
    exchange_rate = fields.String(
        required=True,
        description="Rate of exchange in the format: 'x <currency> = y "
        "<currency>'. E.g.: '1 USD = 70 INR'",
    )
    remarks = fields.String(
        allow_none=True,
        validate=validate_empty_string,
        description='Optional comment field',
    )

    @validates_schema(skip_on_field_errors=True)
    def validate_data(self, data):
        if not data.get('amount_in_foreign_currency').currency:
            raise ValidationError(
                "Please pass foreign currency with sold currency amount",
                field_names=["amount_in_foreign_currency"],
            )

    @post_load
    def create_object(self, data):
        tax_details = []
        if data.get('tax_details'):
            for tax_detail in data.get('tax_details'):
                tax_details.append(
                    TaxDetail(
                        tax_type=tax_detail.get('tax_type'),
                        percentage=tax_detail.get('percentage'),
                        amount=tax_detail.get('amount'),
                    )
                )
            data['tax_details'] = tax_details
        return data
