from marshmallow import Schema, ValidationError, fields, post_load, validates_schema
from marshmallow.validate import OneOf
from treebo_commons.money.money_field import MoneyField

from prometheus.core.api_docs import swag_schema
from ths_common.constants.funding_constants import FundingType


@swag_schema
class UpdateFundingRequestSchema(Schema):
    """
    Schema for updating funding_request
    """

    amount = MoneyField(required=False, allow_none=True, description="Funding Amount")
    funding_type = fields.String(
        validate=OneOf(FundingType.all()), required=True, allow_none=False
    )
    reason = fields.String()

    @validates_schema
    def validate_data(self, data):
        if "amount" in data and data["amount"].amount < 0:
            raise ValidationError(
                'funding amount cannot be negative', field_names=['amount']
            )

    @post_load
    def transform(self, data):
        data['funding_type'] = FundingType(data['funding_type'])
        return data


@swag_schema
class FundingSearchSchema(Schema):
    """
    Funding search Schema
    """

    booking_id = fields.String(required=True, allow_none=False)
    funding_type = fields.String(
        validate=OneOf(FundingType.all()), allow_none=True, required=False
    )


@swag_schema
class FundingSummaryRequestSchema(Schema):
    """
    Schema for funding summary.
    """

    booking_id = fields.String(required=True, allow_none=False)
