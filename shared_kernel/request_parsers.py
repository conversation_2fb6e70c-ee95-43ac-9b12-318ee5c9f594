# coding=utf-8
"""
Request Parsers
"""
import functools
import logging

from flask import request

from ths_common.exceptions import UserTypeHeaderInformationMissing
from ths_common.value_objects import UserData

logger = logging.getLogger(__name__)


def with_slave():
    """
    with slave
    """

    def decorator(func):
        """
        decorator
        :param func:
        :return:
        """

        def wrapper(*args, **kwargs):
            """
            wrapper
            :param args:
            :param kwargs:
            :return:
            """
            setattr(request, 'bind_name', 'slave')
            return func(*args, **kwargs)

        return functools.update_wrapper(wrapper, func)

    return decorator


def read_user_data_from_request_header(default=None):
    user_type = request.headers.get("X-User-Type") or default
    if not user_type:
        raise UserTypeHeaderInformationMissing()
    user = request.headers.get("X-User")
    return UserData(user_type=user_type, user=user)


def read_application_from_request_header(request_headers=None):
    application = (
        request.headers.get("X-Application")
        if not request_headers
        else request_headers.get('X-Application')
    )
    return application


def read_thsc_version_from_header(request_headers=None):
    thsc_version = (
        request.headers.get("X-THSC-Version")
        if not request_headers
        else request_headers.get('X-THSC-Version')
    )
    return thsc_version


def read_last_version_from_header():
    last_fetched_version = request.headers.get("If-Version-Mismatch")
    try:
        last_fetched_version = int(last_fetched_version)
    except Exception:
        last_fetched_version = None
    return last_fetched_version
