from marshmallow import Schema, ValidationError, fields, post_load, validates_schema
from marshmallow.validate import <PERSON>O<PERSON>, Range
from treebo_commons.money.money_field import <PERSON><PERSON>ield

from prometheus.core.api_docs import swag_schema
from shared_kernel.serializers.validators import validate_empty_string
from ths_common.constants.booking_constants import TACommissionTypes
from ths_common.value_objects import (
    AccountDetails,
    Address,
    GSTDetails,
    LegalDetails,
    PhoneNumber,
    TACommissionDetails,
)


class AddressSchema(Schema):
    """
    Address schema
    """

    field_1 = fields.String(allow_none=True, validate=validate_empty_string)
    field_2 = fields.String(allow_none=True, validate=validate_empty_string)
    city = fields.String(allow_none=True, validate=validate_empty_string)
    state = fields.String(allow_none=True, validate=validate_empty_string)
    country = fields.String(allow_none=True, validate=validate_empty_string)
    pincode = fields.String(allow_none=True, validate=validate_empty_string)

    @post_load
    def get_value(self, data):
        address = Address(
            data.get('field_1'),
            data.get('field_2'),
            data.get('city'),
            data.get('state'),
            data.get('country'),
            data.get('pincode'),
        )
        return address


class CommissionTaxSchema(Schema):
    tax = fields.Decimal(as_string=True, allow_none=True, validate=Range(min=0))
    tcs = fields.Decimal(as_string=True, allow_none=True, validate=Range(min=0))
    tds = fields.Decimal(as_string=True, allow_none=True, validate=Range(min=0))
    rcm = fields.Decimal(as_string=True, allow_none=True, validate=Range(min=0))

    @validates_schema
    def validate_tax_combinations(self, data, **kwargs):
        tax = data.get("tax")
        tds = data.get("tds")
        tcs = data.get("tcs")
        rcm = data.get("rcm")

        valid_combinations = [
            {"tax": True, "tds": False, "tcs": False, "rcm": False},
            {"tax": True, "tds": True, "tcs": False, "rcm": False},
            {"tax": True, "tds": True, "tcs": True, "rcm": False},
            {"tax": False, "tds": False, "tcs": False, "rcm": True},
        ]

        current_combination = {
            "tax": tax is not None,
            "tds": tds is not None,
            "tcs": tcs is not None,
            "rcm": rcm is not None,
        }

        if current_combination not in valid_combinations:
            raise ValidationError("Invalid tax combination.")


class PhoneSchema(Schema):
    """
    Phone schema
    """

    country_code = fields.String(allow_none=True, validate=validate_empty_string)
    number = fields.String(
        required=True,
        error_messages={
            'null': 'Phone number may not be null.',
            'required': 'Please provide phone number.',
            'validator_failed': "'{input}' is not a valid value for phone number.",
        },
    )

    @post_load
    def get_value(self, data):
        return PhoneNumber(data["number"], data.get('country_code'))


class GSTDetailsSchema(Schema):
    """
    GST Details
    """

    legal_name = fields.String(
        required=True,
        validate=validate_empty_string,
        error_messages={
            'null': 'Legal name may not be null.',
            'required': 'Please provide legal name.',
            'validator_failed': "'{input}' is not a valid value for legal name.",
        },
    )
    address = fields.Nested(
        AddressSchema,
        required=True,
        error_messages={
            'null': 'Address may not be null.',
            'required': 'Please provide legal address.',
            'validator_failed': "'{input}' is not a valid value for address.",
        },
    )
    gstin_num = fields.String(allow_none=True)
    is_sez = fields.Boolean(allow_none=True)
    has_lut = fields.Boolean(allow_none=True)

    @post_load
    def get_value(self, data):
        return GSTDetails(
            legal_name=data['legal_name'],
            gstin_num=data.get('gstin_num'),
            address=data['address'],
            is_sez=data.get("is_sez", False),
            has_lut=data.get("has_lut", False),
        )


class TaxDetailSchema(Schema):
    """
    Tax details
    """

    tax_type = fields.String()
    percentage = fields.Decimal(allow_none=True)
    amount = MoneyField(allow_none=True)


class TaxDetailSchemaWithStringifiedDecimal(Schema):
    """
    Tax details
    """

    tax_type = fields.String()
    percentage = fields.Decimal(allow_none=True, as_string=True)
    amount = MoneyField(allow_none=True)


class CumulativeTaxDetailSchema(Schema):
    """
    Tax details
    """

    tax_type = fields.String()
    amount = MoneyField(allow_none=True)


@swag_schema
class TACommissionDetailsSchema(Schema):
    commission_type = fields.String(
        allow_none=True,
        validate=OneOf(TACommissionTypes.all()),
        default=TACommissionTypes.PERCENT,
    )
    commission_value = fields.Decimal(allow_none=True)
    post_commission_amount = fields.Boolean(allow_none=True, default=True)
    commission_tax = fields.Nested(CommissionTaxSchema, allow_none=True)
    recalculate_commission_on_booking_modification = fields.Boolean(
        allow_none=True, default=True
    )

    @post_load
    def get_value(self, data):
        return TACommissionDetails.from_json(data)


class AccountSchema(Schema):
    """
    B2B account Details
    """

    account_id = fields.String(required=True, allow_none=False)
    version = fields.Integer(required=True, allow_none=False)

    @post_load
    def get_value(self, data):
        return AccountDetails(
            account_id=data['account_id'],
            version=data['version'],
        )


class LegalDetailsSchema(Schema):
    """
    Legal Details
    """

    legal_name = fields.String(
        required=True,
        validate=validate_empty_string,
        error_messages={
            'null': 'Legal name may not be null.',
            'required': 'Please provide legal name.',
            'validator_failed': "'{input}' is not a valid value for legal name.",
        },
    )
    email = fields.String(allow_none=True)
    phone = fields.Nested(PhoneSchema, allow_none=True)
    address = fields.Nested(
        AddressSchema,
        required=True,
        error_messages={
            'null': 'Address may not be null.',
            'required': 'Please provide legal address.',
            'validator_failed': "'{input}' is not a valid value for address.",
        },
    )
    tin = fields.String(allow_none=True)
    is_sez = fields.Boolean(allow_none=True)
    has_lut = fields.Boolean(allow_none=True)
    client_internal_code = fields.String(allow_none=True)
    external_reference_id = fields.String(allow_none=True)

    @post_load
    def get_value(self, data):
        return LegalDetails(
            legal_name=data['legal_name'],
            tin=data.get('tin'),
            email=data.get('email'),
            phone=data.get('phone'),
            address=data['address'],
            is_sez=data.get("is_sez", False),
            has_lut=data.get("has_lut", False),
            client_internal_code=data.get('client_internal_code'),
            external_reference_id=data.get('external_reference_id'),
        )
