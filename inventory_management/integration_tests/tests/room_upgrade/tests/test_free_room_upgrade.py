import pytest

from inventory_management.integration_tests.config.common_config import *
from inventory_management.integration_tests.tests.base_test import BaseTest


class TestFreeRoomUpgrade(BaseTest):
    @pytest.mark.parametrize(
        "test_case_id, previous_actions, tc_description, hotel_id, status_code, user_type, "
        "skip_message",
        [
            (
                "FreeRoomUpgrade_02",
                FILL_ALL_ROOMS_IN_OAK_02,
                "free room upgrade",
                HOTELID_LIST[0],
                200,
                'super-admin',
                "",
            ),
            (
                "FreeRoomUpgrade_03",
                FILL_ALL_ROOMS_IN_OAK_03,
                "free room upgrade",
                HOTELID_LIST[1],
                200,
                'super-admin',
                "",
            ),
        ],
    )
    @pytest.mark.regression
    def test_free_room_upgrade(
        self,
        test_case_id,
        previous_actions,
        tc_description,
        hotel_id,
        status_code,
        user_type,
        skip_message,
    ):
        if skip_message:
            pytest.skip(skip_message)

        if previous_actions:
            self.common_request_caller(previous_actions, hotel_id)
