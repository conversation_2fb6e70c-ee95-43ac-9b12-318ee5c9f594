from treebo_commons.utils import dateutils

from inventory_management.application.dtos.room_type_inventory_dto import (
    RoomTypeInventories,
)
from inventory_management.domain.constants import SlabType, UpgradeType
from inventory_management.domain.entities.room_type import RoomType
from inventory_management.domain.value_objects.bookings_to_pick import BookingsToPick
from inventory_management.domain.value_objects.occupancy_pre_condition import (
    OccupancyPreConditions,
)
from inventory_management.domain.value_objects.room_type_upgrade import RoomTypeUpgrade
from inventory_management.domain.value_objects.time_to_run import TimeToRun


class RoomUpgradeFormula:
    def __init__(
        self,
        formula_id,
        upgrade_type: UpgradeType,
        slab_type: SlabType,
        higher_room_type_fill_percent,
        bookings_to_pick: BookingsToPick,
        time_to_run: TimeToRun,
        occupancy_pre_condition: OccupancyPreConditions,
        room_type_upgrade: RoomTypeUpgrade,
    ):
        self.formula_id = formula_id
        self.upgrade_type = upgrade_type
        self.slab_type = slab_type
        self.higher_room_type_fill_percent = higher_room_type_fill_percent
        self.bookings_to_pick = bookings_to_pick
        self.time_to_run = time_to_run
        self.occupancy_pre_condition = occupancy_pre_condition
        self.room_type_upgrade = room_type_upgrade

    @property
    def lower_room_type(self) -> RoomType:
        return self.room_type_upgrade.lower_room_type

    @property
    def higher_room_type(self) -> RoomType:
        return self.room_type_upgrade.higher_room_type

    @property
    def checkin_start_date(self):
        return (
            dateutils.add(dateutils.current_date(), self.bookings_to_pick.abw_end)
            if self.bookings_to_pick.abw_end
            else dateutils.current_date()
        )

    @property
    def checkin_end_date(self):
        return (
            dateutils.add(dateutils.current_date(), self.bookings_to_pick.abw_start)
            if self.bookings_to_pick.abw_start
            else dateutils.current_date()
        )

    def satisfies_occupancy_pre_condition(
        self, lower_rt_occupancy_percent, higher_rt_occupancy_percent
    ):
        if not self._satisfies_lower_room_type_occupancy_pre_condition(
            lower_rt_occupancy_percent
        ):
            return False

        if not self._satisfies_higher_room_type_occupancy_pre_condition(
            higher_rt_occupancy_percent
        ):
            return False

        return True

    def _satisfies_lower_room_type_occupancy_pre_condition(
        self, lower_rt_occupancy_percent
    ):
        return (
            self.occupancy_pre_condition.lower_room_min_occupancy
            <= lower_rt_occupancy_percent
            <= self.occupancy_pre_condition.lower_room_max_occupancy
        )

    def _satisfies_higher_room_type_occupancy_pre_condition(
        self, higher_rt_occupancy_percent
    ):
        return (
            self.occupancy_pre_condition.higher_room_min_occupancy
            <= higher_rt_occupancy_percent
            <= self.occupancy_pre_condition.higher_room_max_occupancy
        )

    def is_applicable_to_booking(self, booking):
        return (
            self.checkin_start_date
            <= dateutils.to_date(booking.checkin_date)
            <= self.checkin_end_date
        )
