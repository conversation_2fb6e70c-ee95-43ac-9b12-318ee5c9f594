# RENAME
from inventory_management.domain.constants import SlabType
from inventory_management.domain.value_objects.room_type_upgrade import RoomTypeUpgrade


class PaidUpgradePriceSlab:
    def __init__(
        self,
        room_type_upgrade: RoomTypeUpgrade,
        slab_type: SlabType,
        slab_value,
        hotel_id=None,
        created_at=None,
        modified_at=None,
    ):
        self.hotel_id = hotel_id
        self.room_type_upgrade = room_type_upgrade
        self.slab_type = slab_type
        self.slab_value = slab_value
        self.created_at = created_at
        self.modified_at = modified_at
