from inventory_management.domain.constants import UpgradeType
from inventory_management.domain.value_objects.room_type_upgrade import RoomTypeUpgrade


class OptinStatus:
    def __init__(
        self,
        hotel_id,
        room_type_upgrade: RoomTypeUpgrade,
        upgrade_type: UpgradeType,
        optin_status,
        created_at=None,
        modified_at=None,
    ):
        self.hotel_id = hotel_id
        self.room_type_upgrade = room_type_upgrade
        self.upgrade_type = upgrade_type
        self.optin_status = optin_status
        self.created_at = created_at
        self.modified_at = modified_at
