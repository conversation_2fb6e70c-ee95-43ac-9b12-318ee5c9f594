from inventory_management.domain.constants import EmailTemplates, MessageTemplates
from object_registry import register_instance
from prometheus.core.globals import crs_context
from shared_kernel.infrastructure.external_clients.catalog_service_client import (
    CatalogServiceClient,
)
from shared_kernel.infrastructure.external_clients.communication_service_client import (
    CommunicationServiceClient,
)
from thsc.crs.entities.booking import Booking


@register_instance(dependencies=[CommunicationServiceClient, CatalogServiceClient])
class CommunicationService:
    def __init__(self, communication_service_client, catalog_service_client):
        self.communication_service_client = communication_service_client
        self.catalog_service_client = catalog_service_client

    def send_communication_for_free_upgrade(self, booking: Booking):
        name, email, phone_number = self._get_contact_details(booking=booking)
        if not (email or phone_number):
            return
        hotel_name = self.catalog_service_client.get_property_name(
            hotel_id=booking.hotel_id
        )
        context_data = dict(
            guest_name=name, booking_id=booking.reference_number, hotel_name=hotel_name
        )
        if (
            phone_number
            and crs_context.get_hotel_context().is_sms_or_whatsapp_enabled()
        ):
            self.communication_service_client.send_sms_or_whatsapp(
                identifier=MessageTemplates.FREE_UPGRADE.value,
                context_data=context_data,
                receivers=[phone_number],
            )
        # if email:
        #     subject = EmailTemplates.FREE_UPGRADE.subject.format(h_name=hotel_name)
        #     self.communication_service_client.send_email(identifier=EmailTemplates.FREE_UPGRADE.identifier,
        #                                                  context_data=context_data,
        #                                                  to_emails=[email],
        #                                                  subject=subject)

    def _get_contact_details(self, booking: Booking):
        booking_owner = None
        for customer in booking.customers:
            if customer.customer_id == booking.booking_owner_id:
                booking_owner = customer
                break
        if not booking_owner:
            return None, None, None
        email = getattr(booking_owner, "email")
        phone_number = getattr(booking_owner, "phone_number")
        country_code = getattr(booking_owner, "country_code")
        first_name = getattr(booking_owner, "first_name", "Guest")
        if country_code and phone_number:
            phone_number = "+{}{}".format(country_code.replace("+", ""), phone_number)
        else:
            phone_number = None
        return first_name, email, phone_number
