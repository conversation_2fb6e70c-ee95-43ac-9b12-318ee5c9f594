from inventory_management.domain.constants import UpgradeType
from inventory_management.domain.entities.optin_status import OptinStatus
from inventory_management.infrastructure.database.repositories.models import (
    OptinStatusModel,
)


class OptinStatusAdaptor:
    @staticmethod
    def to_db_entity(domain_entity: OptinStatus, **kwargs):
        # noinspection PyArgumentList
        OptinStatusModel(
            hotel_id=domain_entity.hotel_id,
            lower_room_type=domain_entity.room_type_upgrade.lower_room_type.id,
            higher_room_type=domain_entity.room_type_upgrade.higher_room_type.id,
            ugrade_type=domain_entity.upgrade_type.value,
            optin_status=domain_entity.optin_status,
        )
        pass

    @staticmethod
    def to_domain_entity(db_entity: OptinStatusModel, **kwargs):
        return OptinStatus(
            hotel_id=db_entity.hotel_id,
            room_type_upgrade=kwargs.get('room_type_upgrade'),
            upgrade_type=UpgradeType(db_entity.upgrade_type),
            optin_status=db_entity.optin_status,
            created_at=db_entity.created_at,
            modified_at=db_entity.modified_at,
        )
