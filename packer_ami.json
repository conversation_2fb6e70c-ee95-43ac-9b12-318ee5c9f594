{"variables": {"aws_access_key": "", "aws_secret_key": "", "aws_region": "ap-south-1", "vpc_id": "vpc-1f820776", "subnet_id": "subnet-a57bfccc", "distro": "1604"}, "builders": [{"type": "amazon-ebs", "tags": {"BuildUUID": "{{user `build_uuid`}}", "pod": "crs", "env": "production"}, "run_tags": {"pod": "crs", "env": "production", "Name": "crs-p-crsworkers-deployment"}, "vpc_id": "{{user `vpc_id`}}", "subnet_id": "{{user `subnet_id`}}", "access_key": "{{user `aws_access_key`}}", "secret_key": "{{user `aws_secret_key`}}", "region": "{{user `aws_region`}}", "source_ami_filter": {"filters": {"virtualization-type": "hvm", "name": "treebo_base-*", "root-device-type": "ebs"}, "most_recent": true}, "instance_type": "t2.medium", "ssh_username": "ubuntu", "ami_name": "crs-p-crsreports-{{timestamp}}-{{user `distro`}}", "associate_public_ip_address": "true", "spot_price": "auto", "spot_price_auto_product": "Linux/UNIX (Amazon VPC)"}], "provisioners": [{"type": "shell", "inline": ["sudo mkdir -p /opt/crsreports/", "sudo mkdir -p /opt/crsreports/docker", "sudo chmod -R 755 /opt/crsreports && sudo chown -R ubuntu:ubuntu /opt/crsreports"]}, {"type": "file", "source": "en<PERSON><PERSON>", "destination": "/opt/crsreports"}, {"type": "file", "source": "code_repo/prometheus/deployment/setup.sh", "destination": "/opt/crsreports/"}, {"type": "file", "source": "code_repo/docker/compose/crs/prod-compose.yml", "destination": "/opt/crsreports/docker/"}, {"type": "file", "source": "envrepo/prometheus/docker_env/reporting.env", "destination": "/opt/crsreports/docker/"}, {"type": "shell", "environment_vars": ["BUILD_NO={{user `BUILD_NO`}}", "config_branch={{user `config_branch`}}"], "inline": ["sudo chmod -R 755 /opt/crsreports", "sudo  BUILD_DIR=/opt /opt/crsreports/setup.sh production $BUILD_NO crsreports reporting ap-south-1 http://tenants.treebo.pr aps1-cluster apps/crs"]}], "post-processors": [{"type": "manifest", "output": "manifest.json", "strip_path": true}]}